"use strict";exports.id=979,exports.ids=[979],exports.modules={1158:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},3721:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(3210);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},3964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5146:(e,t,r)=>{r.d(t,{B8:()=>I,UC:()=>D,bL:()=>N,l9:()=>M});var n=r(3210),l=r(569),o=r(1273),a=r(2942),i=r(6059),s=r(4163),d=r(43),u=r(5551),c=r(6963),p=r(687),f="Tabs",[h,v]=(0,o.A)(f,[a.RG]),m=(0,a.RG)(),[w,g]=h(f),y=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:l,defaultValue:o,orientation:a="horizontal",dir:i,activationMode:h="automatic",...v}=e,m=(0,d.jH)(i),[g,y]=(0,u.i)({prop:n,onChange:l,defaultProp:o??"",caller:f});return(0,p.jsx)(w,{scope:r,baseId:(0,c.B)(),value:g,onValueChange:y,orientation:a,dir:m,activationMode:h,children:(0,p.jsx)(s.sG.div,{dir:m,"data-orientation":a,...v,ref:t})})});y.displayName=f;var x="TabsList",b=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...l}=e,o=g(x,r),i=m(r);return(0,p.jsx)(a.bL,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:n,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...l,ref:t})})});b.displayName=x;var S="TabsTrigger",C=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...i}=e,d=g(S,r),u=m(r),c=T(d.baseId,n),f=R(d.baseId,n),h=n===d.value;return(0,p.jsx)(a.q7,{asChild:!0,...u,focusable:!o,active:h,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":f,"data-state":h?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...i,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(n)}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(n)}),onFocus:(0,l.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||o||!e||d.onValueChange(n)})})})});C.displayName=S;var j="TabsContent",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:l,forceMount:o,children:a,...d}=e,u=g(j,r),c=T(u.baseId,l),f=R(u.baseId,l),h=l===u.value,v=n.useRef(h);return n.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:o||h,children:({present:r})=>(0,p.jsx)(s.sG.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&a})})});function T(e,t){return`${e}-trigger-${t}`}function R(e,t){return`${e}-content-${t}`}k.displayName=j;var N=y,I=b,M=C,D=k},5891:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7822:(e,t,r)=>{r.d(t,{UC:()=>eL,YJ:()=>eV,In:()=>eE,q7:()=>eG,VF:()=>eF,p4:()=>e_,JU:()=>eB,ZL:()=>eA,bL:()=>eM,wn:()=>eO,PP:()=>eK,wv:()=>eU,l9:()=>eD,WT:()=>eP,LM:()=>eH});var n=r(3210),l=r(1215);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(569),i=r(9510),s=r(8599),d=r(1273),u=r(43),c=r(1355),p=r(1359),f=r(2547),h=r(6963),v=r(5509),m=r(5028),w=r(4163),g=r(8730),y=r(3495),x=r(5551),b=r(6156),S=r(3721),C=r(687),j=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(w.sG.span,{...e,ref:t,style:{...j,...e.style}})).displayName="VisuallyHidden";var k=r(3376),T=r(2247),R=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],I="Select",[M,D,P]=(0,i.N)(I),[E,A]=(0,d.A)(I,[P,v.Bk]),L=(0,v.Bk)(),[H,V]=E(I),[B,G]=E(I),_=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,y=L(t),[b,S]=n.useState(null),[j,k]=n.useState(null),[T,R]=n.useState(!1),N=(0,u.jH)(c),[D,P]=(0,x.i)({prop:l,defaultProp:o??!1,onChange:a,caller:I}),[E,A]=(0,x.i)({prop:i,defaultProp:s,onChange:d,caller:I}),V=n.useRef(null),G=!b||g||!!b.closest("form"),[_,F]=n.useState(new Set),K=Array.from(_).map(e=>e.props.value).join(";");return(0,C.jsx)(v.bL,{...y,children:(0,C.jsxs)(H,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:j,onValueNodeChange:k,valueNodeHasChildren:T,onValueNodeHasChildrenChange:R,contentId:(0,h.B)(),value:E,onValueChange:A,open:D,onOpenChange:P,dir:N,triggerPointerDownPosRef:V,disabled:m,children:[(0,C.jsx)(M.Provider,{scope:t,children:(0,C.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{F(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),G?(0,C.jsxs)(eT,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:E,onChange:e=>A(e.target.value),disabled:m,form:g,children:[void 0===E?(0,C.jsx)("option",{value:""}):null,Array.from(_)]},K):null]})})};_.displayName=I;var F="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=L(r),d=V(F,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=D(r),f=n.useRef("touch"),[h,m,g]=eN(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eI(t,e,r);void 0!==n&&d.onValueChange(n.value)}),y=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(v.Mz,{asChild:!0,...i,children:(0,C.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eR(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&R.includes(e.key)&&(y(),e.preventDefault())})})})});K.displayName=F;var O="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=V(O,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,b.N)(()=>{u(c)},[u,c]),(0,C.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eR(d.value)?(0,C.jsx)(C.Fragment,{children:a}):o})});U.displayName=O;var W=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});W.displayName="SelectIcon";var z=e=>(0,C.jsx)(m.Z,{asChild:!0,...e});z.displayName="SelectPortal";var q="SelectContent",Z=n.forwardRef((e,t)=>{let r=V(q,e.__scopeSelect),[o,a]=n.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,C.jsx)(X,{...e,ref:t}):o?l.createPortal((0,C.jsx)($,{scope:e.__scopeSelect,children:(0,C.jsx)(M.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),o):null});Z.displayName=q;var[$,Y]=E(q),J=(0,g.TL)("SelectContent.RemoveScroll"),X=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S,...j}=e,R=V(q,r),[N,I]=n.useState(null),[M,P]=n.useState(null),E=(0,s.s)(t,e=>I(e)),[A,L]=n.useState(null),[H,B]=n.useState(null),G=D(r),[_,F]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(N)return(0,k.Eq)(N)},[N]),(0,p.Oh)();let O=n.useCallback(e=>{let[t,...r]=G().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&M&&(M.scrollTop=0),r===n&&M&&(M.scrollTop=M.scrollHeight),r?.focus(),document.activeElement!==l))return},[G,M]),U=n.useCallback(()=>O([A,N]),[O,A,N]);n.useEffect(()=>{_&&U()},[_,U]);let{onOpenChange:W,triggerPointerDownPosRef:z}=R;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||W(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,W,z]),n.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[Z,Y]=eN(e=>{let t=G().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eI(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),X=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==R.value&&R.value===t||n)&&(L(e),n&&(K.current=!0))},[R.value]),et=n.useCallback(()=>N?.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==R.value&&R.value===t||n)&&B(e)},[R.value]),en="popper"===l?ee:Q,el=en===ee?{side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)($,{scope:r,content:N,viewport:M,onViewportChange:P,itemRefCallback:X,selectedItem:A,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:U,selectedItemText:H,position:l,isPositioned:_,searchRef:Z,children:(0,C.jsx)(T.A,{as:J,allowPinchZoom:!0,children:(0,C.jsx)(f.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>F(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,a.m)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=G().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>O(t)),e.preventDefault()}})})})})})})});X.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=V(q,r),d=Y(q,r),[u,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.s)(t,e=>f(e)),v=D(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:S,focusSelectedItem:j}=d,k=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&x&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let a=v(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+h+d+parseInt(c.paddingBottom,10)+w,b=Math.min(5*x.offsetHeight,g),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,R=x.offsetHeight/2,N=f+h+(x.offsetTop+R);if(N<=T){let e=a.length>0&&x===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-T,R+(e?k:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+w);u.style.height=N+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;u.style.top="0px";let t=Math.max(T,f+y.offsetTop+(e?j:0)+R);u.style.height=t+(g-N)+"px",y.scrollTop=N-T+y.offsetTop}u.style.margin="10px 0",u.style.minHeight=b+"px",u.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,u,p,y,x,S,i.dir,l]);(0,b.N)(()=>k(),[k]);let[T,R]=n.useState();(0,b.N)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===g.current&&(k(),j?.(),g.current=!1)},[k,j]);return(0,C.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,C.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,C.jsx)(w.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=L(r);return(0,C.jsx)(v.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=E(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Y(en,r),d=er(en,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,C.jsx)(M.Slot,{scope:r,children:(0,C.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=E(eo),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.B)();return(0,C.jsx)(ea,{scope:r,id:l,children:(0,C.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});es.displayName=eo;var ed="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(ed,r);return(0,C.jsx)(w.sG.div,{id:l.id,...n,ref:t})});eu.displayName=ed;var ec="SelectItem",[ep,ef]=E(ec),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=V(ec,r),c=Y(ec,r),p=u.value===l,[f,v]=n.useState(i??""),[m,g]=n.useState(!1),y=(0,s.s)(t,e=>c.itemRefCallback?.(e,l,o)),x=(0,h.B)(),b=n.useRef("touch"),S=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ep,{scope:r,value:l,disabled:o,textId:x,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,C.jsx)(M.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,C.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:y,onFocus:(0,a.m)(d.onFocus,()=>g(!0)),onBlur:(0,a.m)(d.onBlur,()=>g(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{b.current=e.pointerType,o?c.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(N.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ec;var ev="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=V(ev,r),u=Y(ev,r),c=ef(ev,r),p=G(ev,r),[f,h]=n.useState(null),v=(0,s.s)(t,e=>h(e),c.onItemTextChange,e=>u.itemTextRefCallback?.(e,c.value,c.disabled)),m=f?.textContent,g=n.useMemo(()=>(0,C.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=p;return(0,b.N)(()=>(y(g),()=>x(g)),[y,x,g]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(w.sG.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});em.displayName=ev;var ew="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ew,r).isSelected?(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=ew;var ey="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=Y(ey,e.__scopeSelect),l=er(ey,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=ey;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=Y(eb,e.__scopeSelect),l=er(eb,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Y("SelectScrollButton",r),s=n.useRef(null),d=D(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,b.N)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})}),ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})});ej.displayName="SelectSeparator";var ek="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=L(r),o=V(ek,r),a=Y(ek,r);return o.open&&"popper"===a.position?(0,C.jsx)(v.i3,{...l,...n,ref:t}):null}).displayName=ek;var eT=n.forwardRef(({__scopeSelect:e,value:t,...r},l)=>{let o=n.useRef(null),a=(0,s.s)(l,o),i=(0,S.Z)(t);return n.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,C.jsx)(w.sG.select,{...r,style:{...j,...r.style},ref:a,defaultValue:t})});function eR(e){return""===e||void 0===e}function eN(e){let t=(0,y.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eI(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eT.displayName="SelectBubbleInput";var eM=_,eD=K,eP=U,eE=W,eA=z,eL=Z,eH=el,eV=es,eB=eu,eG=eh,e_=em,eF=eg,eK=ex,eO=eS,eU=ej},8148:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(3210),l=r(4163),o=r(687),a=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},8819:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},8869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])}};