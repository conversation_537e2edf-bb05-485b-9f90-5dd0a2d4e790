'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'

interface WeekEvent {
  id: string
  title: string
  startTime: string
  duration: number // in minutes
  customer: string
  address: string
  installer: string
  status: 'assigned' | 'pending' | 'in-progress' | 'completed'
}

interface CalendarWeekViewProps {
  date: Date // The date to center the week around
  events: WeekEvent[]
  onEventClick: (event: WeekEvent) => void
  onTimeSlotClick: (date: Date, time: string) => void
  onNavigate: (direction: 'prev' | 'next') => void
  onNewAppointment: (date: Date) => void
}

export function CalendarWeekView({
  date,
  events,
  onEventClick,
  onTimeSlotClick,
  onNavigate,
  onNewAppointment
}: CalendarWeekViewProps) {
  // Get the start of the week (Sunday)
  const getWeekStart = (date: Date): Date => {
    const d = new Date(date)
    const day = d.getDay()
    const diff = d.getDate() - day
    return new Date(d.setDate(diff))
  }

  // Generate the 7 days of the week
  const getWeekDays = (startDate: Date): Date[] => {
    const days = []
    for (let i = 0; i < 7; i++) {
      const day = new Date(startDate)
      day.setDate(startDate.getDate() + i)
      days.push(day)
    }
    return days
  }

  // Generate time slots from 8 AM to 6 PM
  const generateTimeSlots = (): string[] => {
    const slots = []
    for (let hour = 8; hour <= 18; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`)
      if (hour < 18) {
        slots.push(`${hour.toString().padStart(2, '0')}:30`)
      }
    }
    return slots
  }

  // Calculate event position and height
  const getEventStyle = (startTime: string, duration: number) => {
    const [hours, minutes] = startTime.split(':').map(Number)
    const startMinutes = (hours - 8) * 60 + minutes
    const topPercent = (startMinutes / (10 * 60)) * 100 // 10 hours (8 AM to 6 PM)
    const heightPercent = (duration / (10 * 60)) * 100
    
    return {
      top: `${topPercent}%`,
      height: `${Math.max(heightPercent, 8)}%`, // Minimum height for visibility
    }
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned': return 'bg-blue-500'
      case 'pending': return 'bg-amber-500'
      case 'in-progress': return 'bg-orange-500'
      case 'completed': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  // Filter events by date
  const getEventsForDate = (targetDate: Date): WeekEvent[] => {
    const dateStr = targetDate.toISOString().split('T')[0]
    const today = new Date()
    const todayStr = today.toISOString().split('T')[0]
    
    // Show events only on today for demo purposes
    // In a real app, you'd filter events by the specific date
    if (dateStr === todayStr) {
      return events
    }
    return []
  }

  const weekStart = getWeekStart(date)
  const weekDays = getWeekDays(weekStart)
  const timeSlots = generateTimeSlots()
  const today = new Date()

  return (
    <Card className="bg-white border-gray-200 shadow-sm">
      <CardContent className="p-0">
        {/* Week Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onNavigate('prev')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <h3 className="font-semibold text-slate-700">
            {weekStart.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })} - {' '}
            {weekDays[6].toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
          </h3>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onNavigate('next')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Week Grid */}
        <div className="grid grid-cols-8 min-h-[600px]">
          {/* Time Column */}
          <div className="border-r border-gray-200 bg-gray-50">
            <div className="h-16 border-b border-gray-200"></div> {/* Header spacer */}
            {timeSlots.map((time) => (
              <div
                key={time}
                className="h-12 px-2 py-1 text-xs text-slate-500 border-b border-gray-100 flex items-center justify-end"
              >
                {time}
              </div>
            ))}
          </div>

          {/* Day Columns */}
          {weekDays.map((day) => {
            const dayEvents = getEventsForDate(day)
            const isToday = day.toDateString() === today.toDateString()
            const isWeekend = day.getDay() === 0 || day.getDay() === 6

            return (
              <div
                key={day.toISOString()}
                className={`border-r border-gray-200 relative ${
                  isWeekend ? 'bg-gray-50' : 'bg-white'
                }`}
              >
                {/* Day Header */}
                <div
                  className={`h-16 p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors ${
                    isToday ? 'bg-amber-50' : ''
                  }`}
                  onClick={() => onNewAppointment(day)}
                >
                  <div className="text-xs text-slate-500 uppercase font-medium">
                    {day.toLocaleDateString('en-US', { weekday: 'short' })}
                  </div>
                  <div
                    className={`text-lg font-semibold mt-1 ${
                      isToday ? 'text-amber-600' : 'text-slate-700'
                    }`}
                  >
                    {day.getDate()}
                  </div>
                  {dayEvents.length > 0 && (
                    <div className="text-xs text-slate-500 mt-1">
                      {dayEvents.length} appointment{dayEvents.length > 1 ? 's' : ''}
                    </div>
                  )}
                </div>

                {/* Time Slots */}
                <div className="relative">
                  {timeSlots.map((time) => (
                    <div
                      key={time}
                      className="h-12 border-b border-gray-100 hover:bg-amber-50 cursor-pointer transition-colors group"
                      onClick={() => onTimeSlotClick(day, time)}
                    >
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity p-1">
                        <Plus className="h-3 w-3 text-amber-500" />
                      </div>
                    </div>
                  ))}

                  {/* Events */}
                  {dayEvents.map((event) => {
                    const style = getEventStyle(event.startTime, event.duration)
                    return (
                      <motion.div
                        key={event.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                        className={`absolute left-1 right-1 rounded-lg p-2 cursor-pointer hover:shadow-md transition-shadow z-10 ${getStatusColor(
                          event.status
                        )} text-white text-xs`}
                        style={style}
                        onClick={() => onEventClick(event)}
                      >
                        <div className="font-medium truncate">{event.title}</div>
                        <div className="opacity-90 truncate">{event.customer}</div>
                        <div className="flex items-center mt-1">
                          <Avatar className="h-4 w-4 mr-1">
                            <AvatarFallback className="text-[10px] bg-white/20">
                              {event.installer.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-[10px] opacity-90 truncate">
                            {event.installer}
                          </span>
                        </div>
                      </motion.div>
                    )
                  })}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
