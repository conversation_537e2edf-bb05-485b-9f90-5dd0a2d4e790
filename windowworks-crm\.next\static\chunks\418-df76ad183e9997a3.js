"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[418],{381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},845:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(2115).createContext)(null)},869:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(2115).createContext)({})},1275:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(2115),i=n(2712);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},1285:(e,t,n)=>{n.d(t,{B:()=>l});var r,i=n(2115),o=n(2712),s=(r||(r=n.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function l(e){let[t,n]=i.useState(s());return(0,o.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},1414:(e,t,n)=>{e.exports=n(2436)},1508:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(2115).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},2082:(e,t,n)=>{n.d(t,{xQ:()=>o});var r=n(2115),i=n(845);function o(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return a(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},2085:(e,t,n)=>{n.d(t,{F:()=>s});var r=n(2596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:a}=t,l=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let o=i(t)||i(r);return s[e][o]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...u}[t]):({...a,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2293:(e,t,n)=>{n.d(t,{Oh:()=>o});var r=n(2115),i=0;function o(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:s()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:s()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function s(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2436:(e,t,n)=>{var r=n(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useState,s=r.useEffect,a=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!i(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),i=r[0].inst,c=r[1];return a(function(){i.value=n,i.getSnapshot=t,u(i)&&c({inst:i})},[e,n,t]),s(function(){return u(i)&&c({inst:i}),e(function(){u(i)&&c({inst:i})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},2596:(e,t,n)=>{n.d(t,{$:()=>r});function r(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=function e(t){var n,r,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(n=0;n<o;n++)t[n]&&(r=e(t[n]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}(e))&&(r&&(r+=" "),r+=t);return r}},2664:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let r=n(9991),i=n(7102);function o(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},2712:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(2115),i=globalThis?.document?r.useLayoutEffect:()=>{}},2757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return a},urlObjectKeys:function(){return s}});let r=n(6966)._(n(8859)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:n}=e,o=e.protocol||"",s=e.pathname||"",a=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return o(e)}},2885:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(2115);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},3655:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>a});var r=n(2115),i=n(7650),o=n(9708),s=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,n)=>{n.d(t,{A:()=>K});var r,i,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function s(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(2115)),l="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,h=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,s=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},s=function(){return Promise.resolve().then(o)};s(),r={push:function(e){t.push(e),s()},filter:function(e){return t=t.filter(e),r}}}});return s.options=o({async:!0,ssr:!1},e),s}(),m=function(){},g=a.forwardRef(function(e,t){var n,r,i,l,u=a.useRef(null),f=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),g=f[0],v=f[1],y=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,k=e.enabled,S=e.shards,E=e.sideCar,T=e.noRelative,P=e.noIsolation,A=e.inert,M=e.allowPinchZoom,C=e.as,R=e.gapMode,D=s(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=(n=[u,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(i=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=r,l=i.facade,d(function(){var e=h.get(l);if(e){var t=new Set(e),r=new Set(n),i=l.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,i)})}h.set(l,n)},[n]),l),L=o(o({},D),g);return a.createElement(a.Fragment,null,k&&a.createElement(E,{sideCar:p,removeScrollBar:x,shards:S,noRelative:T,noIsolation:P,inert:A,setCallbacks:v,allowPinchZoom:!!M,lockRef:u,gapMode:R}),y?a.cloneElement(a.Children.only(b),o(o({},L),{ref:j})):a.createElement(void 0===C?"div":C,o({},L,{className:w,ref:j}),b))});g.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},g.classNames={fullWidth:u,zeroRight:l};var v=function(e){var t=e.sideCar,n=s(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,o({},n))};v.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,s;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),s=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(s)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[k(n),k(r),k(i)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},T=w(),P="data-scroll-locked",A=function(e,t,n,r){var i=e.left,o=e.top,s=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(P,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(P,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(P)||"0",10);return isFinite(e)?e:0},C=function(){a.useEffect(function(){return document.body.setAttribute(P,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(P):document.body.setAttribute(P,e.toString())}},[])},R=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,i=void 0===r?"margin":r;C();var o=a.useMemo(function(){return E(i)},[i]);return a.createElement(T,{styles:A(o,!t,i,n?"":"!important")})},D=!1;if("undefined"!=typeof window)try{var j=Object.defineProperty({},"passive",{get:function(){return D=!0,!0}});window.addEventListener("test",j,j),window.removeEventListener("test",j,j)}catch(e){D=!1}var L=!!D&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},V=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),N(e,r)){var i=I(e,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},N=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,i){var o,s=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),a=s*r,l=n.target,u=t.contains(l),c=!1,d=a>0,h=0,f=0;do{if(!l)break;var p=I(e,l),m=p[0],g=p[1]-p[2]-s*m;(m||g)&&N(e,l)&&(h+=g,f+=m);var v=l.parentNode;l=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(i&&1>Math.abs(h)||!i&&a>h)?c=!0:!d&&(i&&1>Math.abs(f)||!i&&-a>f)&&(c=!0),c},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},z=0,W=[];let $=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),i=a.useState(z++)[0],o=a.useState(w)[0],s=a.useRef(e);a.useEffect(function(){s.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var i,o=B(e),a=n.current,l="deltaX"in e?e.deltaX:a[0]-o[0],u="deltaY"in e?e.deltaY:a[1]-o[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var h=V(d,c);if(!h)return!0;if(h?i=d:(i="v"===d?"h":"v",h=V(d,c)),!h)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=i),!i)return!0;var f=r.current||i;return F(f,t,e,"h"===f?l:u,!0)},[]),u=a.useCallback(function(e){if(W.length&&W[W.length-1]===o){var n="deltaY"in e?_(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var i=(s.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!s.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,i){var o={name:e,delta:n,target:r,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=a.useCallback(function(e){n.current=B(e),r.current=void 0},[]),h=a.useCallback(function(t){c(t.type,_(t),t.target,l(t,e.lockRef.current))},[]),f=a.useCallback(function(t){c(t.type,B(t),t.target,l(t,e.lockRef.current))},[]);a.useEffect(function(){return W.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:f}),document.addEventListener("wheel",u,L),document.addEventListener("touchmove",u,L),document.addEventListener("touchstart",d,L),function(){W=W.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,L),document.removeEventListener("touchmove",u,L),document.removeEventListener("touchstart",d,L)}},[]);var p=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?a.createElement(R,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},p.useMedium(r),v);var H=a.forwardRef(function(e,t){return a.createElement(g,o({},e,{ref:t,sideCar:$}))});H.classNames=g.classNames;let K=H},3861:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},4011:(e,t,n)=>{n.d(t,{H4:()=>E,_V:()=>S,bL:()=>k});var r=n(2115),i=n(6081),o=n(9033),s=n(2712),a=n(3655),l=n(1414);function u(){return()=>{}}var c=n(5155),d="Avatar",[h,f]=(0,i.A)(d),[p,m]=h(d),g=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...i}=e,[o,s]=r.useState("idle");return(0,c.jsx)(p,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:s,children:(0,c.jsx)(a.sG.span,{...i,ref:t})})});g.displayName=d;var v="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:i,onLoadingStatusChange:d=()=>{},...h}=e,f=m(v,n),p=function(e,t){let{referrerPolicy:n,crossOrigin:i}=t,o=(0,l.useSyncExternalStore)(u,()=>!0,()=>!1),a=r.useRef(null),c=o?(a.current||(a.current=new window.Image),a.current):null,[d,h]=r.useState(()=>x(c,e));return(0,s.N)(()=>{h(x(c,e))},[c,e]),(0,s.N)(()=>{let e=e=>()=>{h(e)};if(!c)return;let t=e("loaded"),r=e("error");return c.addEventListener("load",t),c.addEventListener("error",r),n&&(c.referrerPolicy=n),"string"==typeof i&&(c.crossOrigin=i),()=>{c.removeEventListener("load",t),c.removeEventListener("error",r)}},[c,i,n]),d}(i,h),g=(0,o.c)(e=>{d(e),f.onImageLoadingStatusChange(e)});return(0,s.N)(()=>{"idle"!==p&&g(p)},[p,g]),"loaded"===p?(0,c.jsx)(a.sG.img,{...h,ref:t,src:i}):null});y.displayName=v;var b="AvatarFallback",w=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:i,...o}=e,s=m(b,n),[l,u]=r.useState(void 0===i);return r.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>u(!0),i);return()=>window.clearTimeout(e)}},[i]),l&&"loaded"!==s.imageLoadingStatus?(0,c.jsx)(a.sG.span,{...o,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=b;var k=g,S=y,E=w},4186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4315:(e,t,n)=>{n.d(t,{jH:()=>o});var r=n(2115);n(5155);var i=r.createContext(void 0);function o(e){let t=r.useContext(i);return e||t||"ltr"}},4378:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(2115),i=n(7650),o=n(3655),s=n(2712),a=n(5155),l=r.forwardRef((e,t)=>{var n,l;let{container:u,...c}=e,[d,h]=r.useState(!1);(0,s.N)(()=>h(!0),[]);let f=u||d&&(null==(l=globalThis)||null==(n=l.document)?void 0:n.body);return f?i.createPortal((0,a.jsx)(o.sG.div,{...c,ref:t}),f):null});l.displayName="Portal"},4395:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},4783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4835:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5152:(e,t,n)=>{n.d(t,{Mz:()=>te,i3:()=>tn,UC:()=>tt,bL:()=>e7,Bk:()=>eG});var r=n(2115);let i=["top","right","bottom","left"],o=Math.min,s=Math.max,a=Math.round,l=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function h(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}let v=new Set(["top","bottom"]);function y(e){return v.has(f(e))?"y":"x"}function b(e){return e.replace(/start|end/g,e=>d[e])}let w=["left","right"],x=["right","left"],k=["top","bottom"],S=["bottom","top"];function E(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function T(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function P(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function A(e,t,n){let r,{reference:i,floating:o}=e,s=y(t),a=m(y(t)),l=g(a),u=f(t),c="y"===s,d=i.x+i.width/2-o.width/2,h=i.y+i.height/2-o.height/2,v=i[l]/2-o[l]/2;switch(u){case"top":r={x:d,y:i.y-o.height};break;case"bottom":r={x:d,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:h};break;case"left":r={x:i.x-o.width,y:h};break;default:r={x:i.x,y:i.y}}switch(p(t)){case"start":r[a]-=v*(n&&c?-1:1);break;case"end":r[a]+=v*(n&&c?-1:1)}return r}let M=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:s}=n,a=o.filter(Boolean),l=await (null==s.isRTL?void 0:s.isRTL(t)),u=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:d}=A(u,r,l),h=r,f={},p=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:v,data:y,reset:b}=await m({x:c,y:d,initialPlacement:r,placement:h,strategy:i,middlewareData:f,rects:u,platform:s,elements:{reference:e,floating:t}});c=null!=g?g:c,d=null!=v?v:d,f={...f,[o]:{...f[o],...y}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(h=b.placement),b.rects&&(u=!0===b.rects?await s.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:c,y:d}=A(u,h,l)),n=-1)}return{x:c,y:d,placement:h,strategy:i,middlewareData:f}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=h(t,e),m=T(p),g=a[f?"floating"===d?"reference":"floating":d],v=P(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),y="floating"===d?{x:r,y:i,width:s.floating.width,height:s.floating.height}:s.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),w=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},x=P(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:b,strategy:l}):y);return{top:(v.top-x.top+m.top)/w.y,bottom:(x.bottom-v.bottom+m.bottom)/w.y,left:(v.left-x.left+m.left)/w.x,right:(x.right-v.right+m.right)/w.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function D(e){return i.some(t=>e[t]>=0)}let j=new Set(["left","top"]);async function L(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),s=f(n),a=p(n),l="y"===y(n),u=j.has(s)?-1:1,c=o&&l?-1:1,d=h(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof v&&(g="end"===a?-1*v:v),l?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function O(){return"undefined"!=typeof window}function V(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function N(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!O()&&(e instanceof Node||e instanceof N(e).Node)}function B(e){return!!O()&&(e instanceof Element||e instanceof N(e).Element)}function _(e){return!!O()&&(e instanceof HTMLElement||e instanceof N(e).HTMLElement)}function U(e){return!!O()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof N(e).ShadowRoot)}let z=new Set(["inline","contents"]);function W(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!z.has(i)}let $=new Set(["table","td","th"]),H=[":popover-open",":modal"];function K(e){return H.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let G=["transform","translate","scale","rotate","perspective"],X=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function q(e){let t=Z(),n=B(e)?ee(e):e;return G.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||X.some(e=>(n.willChange||"").includes(e))||Y.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function Q(e){return J.has(V(e))}function ee(e){return N(e).getComputedStyle(e)}function et(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===V(e))return e;let t=e.assignedSlot||e.parentNode||U(e)&&e.host||I(e);return U(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=en(t);return Q(n)?t.ownerDocument?t.ownerDocument.body:t.body:_(n)&&W(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),s=N(i);if(o){let e=ei(s);return t.concat(s,s.visualViewport||[],W(i)?i:[],e&&n?er(e):[])}return t.concat(i,er(i,[],n))}function ei(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eo(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=_(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:r,l=a(n)!==o||a(r)!==s;return l&&(n=o,r=s),{width:n,height:r,$:l}}function es(e){return B(e)?e:e.contextElement}function ea(e){let t=es(e);if(!_(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=eo(t),s=(o?a(n.width):n.width)/r,l=(o?a(n.height):n.height)/i;return s&&Number.isFinite(s)||(s=1),l&&Number.isFinite(l)||(l=1),{x:s,y:l}}let el=u(0);function eu(e){let t=N(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function ec(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),s=es(e),a=u(1);t&&(r?B(r)&&(a=ea(r)):a=ea(e));let l=(void 0===(i=n)&&(i=!1),r&&(!i||r===N(s))&&i)?eu(s):u(0),c=(o.left+l.x)/a.x,d=(o.top+l.y)/a.y,h=o.width/a.x,f=o.height/a.y;if(s){let e=N(s),t=r&&B(r)?N(r):r,n=e,i=ei(n);for(;i&&r&&t!==n;){let e=ea(i),t=i.getBoundingClientRect(),r=ee(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,s=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,h*=e.x,f*=e.y,c+=o,d+=s,i=ei(n=N(i))}}return P({width:h,height:f,x:c,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:ec(I(e)).left+n}function eh(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ef=new Set(["absolute","fixed"]);function ep(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=N(e),r=I(e),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;let e=Z();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a,y:l}}(e,n);else if("document"===t)r=function(e){let t=I(e),n=et(e),r=e.ownerDocument.body,i=s(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=s(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ed(e),l=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=s(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:l}}(I(e));else if(B(t))r=function(e,t){let n=ec(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=_(e)?ea(e):u(1),s=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:s,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=eu(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return P(r)}function em(e){return"static"===ee(e).position}function eg(e,t){if(!_(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return I(e)===n&&(n=n.ownerDocument.body),n}function ev(e,t){var n;let r=N(e);if(K(e))return r;if(!_(e)){let t=en(e);for(;t&&!Q(t);){if(B(t)&&!em(t))return t;t=en(t)}return r}let i=eg(e,t);for(;i&&(n=i,$.has(V(n)))&&em(i);)i=eg(i,t);return i&&Q(i)&&em(i)&&!q(i)?r:i||function(e){let t=en(e);for(;_(t)&&!Q(t);){if(q(t))return t;if(K(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||ev,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=_(t),i=I(t),o="fixed"===n,s=ec(e,!0,o,t),a={scrollLeft:0,scrollTop:0},l=u(0);if(r||!r&&!o)if(("body"!==V(t)||W(i))&&(a=et(t)),r){let e=ec(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=ed(i));o&&!r&&i&&(l.x=ed(i));let c=!i||r||o?u(0):eh(i,a);return{x:s.left+a.scrollLeft-l.x-c.x,y:s.top+a.scrollTop-l.y-c.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eb={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,s=I(r),a=!!t&&K(t.floating);if(r===s||a&&o)return n;let l={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),h=_(r);if((h||!h&&!o)&&(("body"!==V(r)||W(s))&&(l=et(r)),_(r))){let e=ec(r);c=ea(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let f=!s||h||o?u(0):eh(s,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+f.x,y:n.y*c.y-l.scrollTop*c.y+d.y+f.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?K(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>B(e)&&"body"!==V(e)),i=null,o="fixed"===ee(e).position,s=o?en(e):e;for(;B(s)&&!Q(s);){let t=ee(s),n=q(s);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&ef.has(i.position)||W(s)&&!n&&function e(t,n){let r=en(t);return!(r===n||!B(r)||Q(r))&&("fixed"===ee(r).position||e(r,n))}(e,s))?r=r.filter(e=>e!==s):i=t,s=en(s)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=a[0],u=a.reduce((e,n)=>{let r=ep(t,n,i);return e.top=s(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=s(r.left,e.left),e},ep(t,l,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:ev,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eo(e);return{width:t,height:n}},getScale:ea,isElement:B,isRTL:function(e){return"rtl"===ee(e).direction}};function ew(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:l,elements:u,middlewareData:c}=t,{element:d,padding:f=0}=h(e,t)||{};if(null==d)return{};let v=T(f),b={x:n,y:r},w=m(y(i)),x=g(w),k=await l.getDimensions(d),S="y"===w,E=S?"clientHeight":"clientWidth",P=a.reference[x]+a.reference[w]-b[w]-a.floating[x],A=b[w]-a.reference[w],M=await (null==l.getOffsetParent?void 0:l.getOffsetParent(d)),C=M?M[E]:0;C&&await (null==l.isElement?void 0:l.isElement(M))||(C=u.floating[E]||a.floating[x]);let R=C/2-k[x]/2-1,D=o(v[S?"top":"left"],R),j=o(v[S?"bottom":"right"],R),L=C-k[x]-j,O=C/2-k[x]/2+(P/2-A/2),V=s(D,o(O,L)),N=!c.arrow&&null!=p(i)&&O!==V&&a.reference[x]/2-(O<D?D:j)-k[x]/2<0,I=N?O<D?O-D:O-L:0;return{[w]:b[w]+I,data:{[w]:V,centerOffset:O-V-I,...N&&{alignmentOffset:I}},reset:N}}}),ek=(e,t,n)=>{let r=new Map,i={platform:eb,...n},o={...i.platform,_c:r};return M(e,t,{...i,platform:o})};var eS=n(7650),eE="undefined"!=typeof document?r.useLayoutEffect:function(){};function eT(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eT(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eT(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eP(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eA(e,t){let n=eP(e);return Math.round(t*n)/n}function eM(e){let t=r.useRef(e);return eE(()=>{t.current=e}),t}let eC=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ex({element:n.current,padding:r}).fn(t):{}:n?ex({element:n,padding:r}).fn(t):{}}}),eR=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:s,middlewareData:a}=t,l=await L(t,e);return s===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:s}}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:l=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=h(e,t),d={x:n,y:r},p=await C(t,c),g=y(f(i)),v=m(g),b=d[v],w=d[g];if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+p[e],r=b-p[t];b=s(n,o(b,r))}if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+p[e],r=w-p[t];w=s(n,o(w,r))}let x=u.fn({...t,[v]:b,[g]:w});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[v]:a,[g]:l}}}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=h(e,t),c={x:n,y:r},d=y(i),p=m(d),g=c[p],v=c[d],b=h(a,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(l){let e="y"===p?"height":"width",t=o.reference[p]-o.floating[e]+w.mainAxis,n=o.reference[p]+o.reference[e]-w.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var x,k;let e="y"===p?"width":"height",t=j.has(f(i)),n=o.reference[d]-o.floating[e]+(t&&(null==(x=s.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),r=o.reference[d]+o.reference[e]+(t?0:(null==(k=s.offset)?void 0:k[d])||0)-(t?w.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[p]:g,[d]:v}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,s;let{placement:a,middlewareData:l,rects:u,initialPlacement:c,platform:d,elements:v}=t,{mainAxis:T=!0,crossAxis:P=!0,fallbackPlacements:A,fallbackStrategy:M="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:D=!0,...j}=h(e,t);if(null!=(n=l.arrow)&&n.alignmentOffset)return{};let L=f(a),O=y(c),V=f(c)===c,N=await (null==d.isRTL?void 0:d.isRTL(v.floating)),I=A||(V||!D?[E(c)]:function(e){let t=E(e);return[b(e),t,b(t)]}(c)),F="none"!==R;!A&&F&&I.push(...function(e,t,n,r){let i=p(e),o=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?x:w;return t?w:x;case"left":case"right":return t?k:S;default:return[]}}(f(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(b)))),o}(c,D,R,N));let B=[c,...I],_=await C(t,j),U=[],z=(null==(r=l.flip)?void 0:r.overflows)||[];if(T&&U.push(_[L]),P){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),i=m(y(e)),o=g(i),s="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(s=E(s)),[s,E(s)]}(a,u,N);U.push(_[e[0]],_[e[1]])}if(z=[...z,{placement:a,overflows:U}],!U.every(e=>e<=0)){let e=((null==(i=l.flip)?void 0:i.index)||0)+1,t=B[e];if(t&&("alignment"!==P||O===y(t)||z.every(e=>e.overflows[0]>0&&y(e.placement)===O)))return{data:{index:e,overflows:z},reset:{placement:t}};let n=null==(o=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(M){case"bestFit":{let e=null==(s=z.filter(e=>{if(F){let t=y(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:l,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...g}=h(e,t),v=await C(t,g),b=f(l),w=p(l),x="y"===y(l),{width:k,height:S}=u.floating;"top"===b||"bottom"===b?(i=b,a=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=b,i="end"===w?"top":"bottom");let E=S-v.top-v.bottom,T=k-v.left-v.right,P=o(S-v[i],E),A=o(k-v[a],T),M=!t.middlewareData.shift,R=P,D=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=E),M&&!w){let e=s(v.left,0),t=s(v.right,0),n=s(v.top,0),r=s(v.bottom,0);x?D=k-2*(0!==e||0!==t?e+t:s(v.left,v.right)):R=S-2*(0!==n||0!==r?n+r:s(v.top,v.bottom))}await m({...t,availableWidth:D,availableHeight:R});let j=await c.getDimensions(d.floating);return k!==j.width||S!==j.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eV=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=h(e,t);switch(r){case"referenceHidden":{let e=R(await C(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:D(e)}}}case"escaped":{let e=R(await C(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:D(e)}}}default:return{}}}}}(e),options:[e,t]}),eN=(e,t)=>({...eC(e),options:[e,t]});var eI=n(3655),eF=n(5155),eB=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eF.jsx)(eI.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eB.displayName="Arrow";var e_=n(6101),eU=n(6081),ez=n(9033),eW=n(2712),e$=n(1275),eH="Popper",[eK,eG]=(0,eU.A)(eH),[eX,eY]=eK(eH),eq=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eF.jsx)(eX,{scope:t,anchor:i,onAnchorChange:o,children:n})};eq.displayName=eH;var eZ="PopperAnchor",eJ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,s=eY(eZ,n),a=r.useRef(null),l=(0,e_.s)(t,a);return r.useEffect(()=>{s.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eF.jsx)(eI.sG.div,{...o,ref:l})});eJ.displayName=eZ;var eQ="PopperContent",[e0,e1]=eK(eQ),e2=r.forwardRef((e,t)=>{var n,i,a,u,c,d,h,f;let{__scopePopper:p,side:m="bottom",sideOffset:g=0,align:v="center",alignOffset:y=0,arrowPadding:b=0,avoidCollisions:w=!0,collisionBoundary:x=[],collisionPadding:k=0,sticky:S="partial",hideWhenDetached:E=!1,updatePositionStrategy:T="optimized",onPlaced:P,...A}=e,M=eY(eQ,p),[C,R]=r.useState(null),D=(0,e_.s)(t,e=>R(e)),[j,L]=r.useState(null),O=(0,e$.X)(j),V=null!=(h=null==O?void 0:O.width)?h:0,N=null!=(f=null==O?void 0:O.height)?f:0,F="number"==typeof k?k:{top:0,right:0,bottom:0,left:0,...k},B=Array.isArray(x)?x:[x],_=B.length>0,U={padding:F,boundary:B.filter(e4),altBoundary:_},{refs:z,floatingStyles:W,placement:$,isPositioned:H,middlewareData:K}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:s,floating:a}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,h]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=r.useState(i);eT(f,i)||p(i);let[m,g]=r.useState(null),[v,y]=r.useState(null),b=r.useCallback(e=>{e!==S.current&&(S.current=e,g(e))},[]),w=r.useCallback(e=>{e!==E.current&&(E.current=e,y(e))},[]),x=s||m,k=a||v,S=r.useRef(null),E=r.useRef(null),T=r.useRef(d),P=null!=u,A=eM(u),M=eM(o),C=eM(c),R=r.useCallback(()=>{if(!S.current||!E.current)return;let e={placement:t,strategy:n,middleware:f};M.current&&(e.platform=M.current),ek(S.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==C.current};D.current&&!eT(T.current,t)&&(T.current=t,eS.flushSync(()=>{h(t)}))})},[f,t,n,M,C]);eE(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,h(e=>({...e,isPositioned:!1})))},[c]);let D=r.useRef(!1);eE(()=>(D.current=!0,()=>{D.current=!1}),[]),eE(()=>{if(x&&(S.current=x),k&&(E.current=k),x&&k){if(A.current)return A.current(x,k,R);R()}},[x,k,R,A,P]);let j=r.useMemo(()=>({reference:S,floating:E,setReference:b,setFloating:w}),[b,w]),L=r.useMemo(()=>({reference:x,floating:k}),[x,k]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!L.floating)return e;let t=eA(L.floating,d.x),r=eA(L.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...eP(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,L.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:R,refs:j,elements:L,floatingStyles:O}),[d,R,j,L,O])}({strategy:"fixed",placement:m+("center"!==v?"-"+v:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:h=!1}=r,f=es(e),p=a||u?[...f?er(f):[],...er(t)]:[];p.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=f&&d?function(e,t){let n,r=null,i=I(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let h=e.getBoundingClientRect(),{left:f,top:p,width:m,height:g}=h;if(c||t(),!m||!g)return;let v=l(p),y=l(i.clientWidth-(f+m)),b={rootMargin:-v+"px "+-y+"px "+-l(i.clientHeight-(p+g))+"px "+-l(f)+"px",threshold:s(0,o(1,d))||1},w=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!w)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ew(h,e.getBoundingClientRect())||u(),w=!1}try{r=new IntersectionObserver(x,{...b,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(x,b)}r.observe(e)}(!0),a}(f,n):null,g=-1,v=null;c&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),f&&!h&&v.observe(f),v.observe(t));let y=h?ec(e):null;return h&&function t(){let r=ec(e);y&&!ew(y,r)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,h&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===T})},elements:{reference:M.anchor},middleware:[eR({mainAxis:g+N,alignmentAxis:y}),w&&eD({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?ej():void 0,...U}),w&&eL({...U}),eO({...U,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:s}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(s,"px"))}}),j&&eN({element:j,padding:b}),e6({arrowWidth:V,arrowHeight:N}),E&&eV({strategy:"referenceHidden",...U})]}),[G,X]=e8($),Y=(0,ez.c)(P);(0,eW.N)(()=>{H&&(null==Y||Y())},[H,Y]);let q=null==(n=K.arrow)?void 0:n.x,Z=null==(i=K.arrow)?void 0:i.y,J=(null==(a=K.arrow)?void 0:a.centerOffset)!==0,[Q,ee]=r.useState();return(0,eW.N)(()=>{C&&ee(window.getComputedStyle(C).zIndex)},[C]),(0,eF.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:H?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Q,"--radix-popper-transform-origin":[null==(u=K.transformOrigin)?void 0:u.x,null==(c=K.transformOrigin)?void 0:c.y].join(" "),...(null==(d=K.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(e0,{scope:p,placedSide:G,onArrowChange:L,arrowX:q,arrowY:Z,shouldHideArrow:J,children:(0,eF.jsx)(eI.sG.div,{"data-side":G,"data-align":X,...A,ref:D,style:{...A.style,animation:H?void 0:"none"}})})})});e2.displayName=eQ;var e5="PopperArrow",e3={top:"bottom",right:"left",bottom:"top",left:"right"},e9=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=e1(e5,n),o=e3[i.placedSide];return(0,eF.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eB,{...r,ref:t,style:{...r.style,display:"block"}})})});function e4(e){return null!==e}e9.displayName=e5;var e6=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,s;let{placement:a,rects:l,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,h=c?0:e.arrowHeight,[f,p]=e8(a),m={start:"0%",center:"50%",end:"100%"}[p],g=(null!=(o=null==(r=u.arrow)?void 0:r.x)?o:0)+d/2,v=(null!=(s=null==(i=u.arrow)?void 0:i.y)?s:0)+h/2,y="",b="";return"bottom"===f?(y=c?m:"".concat(g,"px"),b="".concat(-h,"px")):"top"===f?(y=c?m:"".concat(g,"px"),b="".concat(l.floating.height+h,"px")):"right"===f?(y="".concat(-h,"px"),b=c?m:"".concat(v,"px")):"left"===f&&(y="".concat(l.floating.width+h,"px"),b=c?m:"".concat(v,"px")),{data:{x:y,y:b}}}});function e8(e){let[t,n="center"]=e.split("-");return[t,n]}var e7=eq,te=eJ,tt=e2,tn=e9},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5453:(e,t,n)=>{n.d(t,{v:()=>l});var r=n(2115);let i=e=>{let t,n=new Set,r=(e,r)=>{let i="function"==typeof e?e(t):e;if(!Object.is(i,t)){let e=t;t=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},t,i),n.forEach(n=>n(t,e))}},i=()=>t,o={setState:r,getState:i,getInitialState:()=>s,subscribe:e=>(n.add(e),()=>n.delete(e))},s=t=e(r,i,o);return o},o=e=>e?i(e):i,s=e=>e,a=e=>{let t=o(e),n=e=>(function(e,t=s){let n=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},l=e=>e?a(e):a},5695:(e,t,n)=>{var r=n(8999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}})},5845:(e,t,n)=>{n.d(t,{i:()=>a});var r,i=n(2115),o=n(2712),s=(r||(r=n.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,a,l]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),a=i.useRef(t);return s(()=>{a.current=t},[t]),i.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,i.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else a(t)},[u,e,a,l])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>s,q:()=>o});var r=n(2115),i=n(5155);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,s=r.useMemo(()=>o,Object.values(o));return(0,i.jsx)(n.Provider,{value:s,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function s(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return o.scopeName=e,[function(t,o){let s=r.createContext(o),a=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,u=n?.[e]?.[a]||s,c=r.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(n,i){let l=i?.[e]?.[a]||s,u=r.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(o,...t)]}},6101:(e,t,n)=>{n.d(t,{s:()=>s,t:()=>o});var r=n(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function s(...e){return r.useCallback(o(...e),e)}},6408:(e,t,n)=>{let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function o(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function s(e,t,n,r){if("function"==typeof t){let[i,s]=o(r);t=t(void 0!==n?n:e.custom,i,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,s]=o(r);t=t(void 0!==n?n:e.custom,i,s)}return t}function a(e,t,n){let r=e.getProps();return s(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>oA});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function f(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=d.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,o=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(a)}let c={schedule:(e,t=!1,o=!1)=>{let a=o&&i?n:r;return t&&s.add(e),a.has(e)||a.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(a=e,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?n:void 0),e),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:f,update:p,preRender:m,render:g,postRender:v}=s,y=()=>{let o=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),f.process(i),p.process(i),m.process(i),g.process(i),v.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(y))},b=()=>{n=!0,r=!0,i.isProcessing||e(y)};return{schedule:d.reduce((e,t)=>{let r=s[t];return e[t]=(e,t=!1,i=!1)=>(n||b(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)s[d[t]].cancel(e)},state:i,steps:s}}let{schedule:p,cancel:m,state:g,steps:v}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),y=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(y),w=new Set(["width","height","top","left","right","bottom",...y]);function x(e,t){-1===e.indexOf(t)&&e.push(t)}function k(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class S{constructor(){this.subscriptions=[]}add(e){return x(this.subscriptions,e),()=>k(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function E(){r=void 0}let T={now:()=>(void 0===r&&T.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(E)}},P=e=>!isNaN(parseFloat(e)),A={current:void 0};class M{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=T.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=T.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=P(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new S);let n=this.events[e].add(t);return"change"===e?()=>{n(),p.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return A.current&&A.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=T.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function C(e,t){return new M(e,t)}let R=e=>Array.isArray(e),D=e=>!!(e&&e.getVelocity);function j(e,t){let n=e.getValue("willChange");if(D(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let L=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),O="data-"+L("framerAppearId"),V=(e,t)=>n=>t(e(n)),N=(...e)=>e.reduce(V),I=(e,t,n)=>n>t?t:n<e?e:n,F=e=>1e3*e,B=e=>e/1e3,_={layout:0,mainThread:0,waapi:0},U=()=>{},z=()=>{},W=e=>t=>"string"==typeof t&&t.startsWith(e),$=W("--"),H=W("var(--"),K=e=>!!H(e)&&G.test(e.split("/*")[0].trim()),G=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,X={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},Y={...X,transform:e=>I(0,1,e)},q={...X,default:1},Z=e=>Math.round(1e5*e)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Q=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&Q.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,s,a]=r.match(J);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},en=e=>I(0,255,e),er={...X,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Z(Y.transform(r))+")"},eo={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},es=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ea=es("deg"),el=es("%"),eu=es("px"),ec=es("vh"),ed=es("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ef={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(n))+", "+Z(Y.transform(r))+")"},ep={test:e=>ei.test(e)||eo.test(e)||ef.test(e),parse:e=>ei.test(e)?ei.parse(e):ef.test(e)?ef.parse(e):eo.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ef.transform(e),getAnimatableNone:e=>{let t=ep.parse(e);return t.alpha=0,ep.transform(t)}},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ev="color",ey=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eb(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,s=t.replace(ey,e=>(ep.test(e)?(r.color.push(o),i.push(ev),n.push(ep.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(eg),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function ew(e){return eb(e).values}function ex(e){let{split:t,types:n}=eb(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===eg?i+=Z(e[o]):t===ev?i+=ep.transform(e[o]):i+=e[o]}return i}}let ek=e=>"number"==typeof e?0:ep.test(e)?ep.getAnimatableNone(e):e,eS={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(J)?.length||0)+(e.match(em)?.length||0)>0},parse:ew,createTransformer:ex,getAnimatableNone:function(e){let t=ew(e);return ex(e)(t.map(ek))}};function eE(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eT(e,t){return n=>n>0?t:e}let eP=(e,t,n)=>e+(t-e)*n,eA=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eM=[eo,ei,ef],eC=e=>eM.find(t=>t.test(e));function eR(e){let t=eC(e);if(U(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!t)return!1;let n=t.parse(e);return t===ef&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;i=eE(a,r,e+1/3),o=eE(a,r,e),s=eE(a,r,e-1/3)}else i=o=s=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:r}}(n)),n}let eD=(e,t)=>{let n=eR(e),r=eR(t);if(!n||!r)return eT(e,t);let i={...n};return e=>(i.red=eA(n.red,r.red,e),i.green=eA(n.green,r.green,e),i.blue=eA(n.blue,r.blue,e),i.alpha=eP(n.alpha,r.alpha,e),ei.transform(i))},ej=new Set(["none","hidden"]);function eL(e,t){return n=>eP(e,t,n)}function eO(e){return"number"==typeof e?eL:"string"==typeof e?K(e)?eT:ep.test(e)?eD:eI:Array.isArray(e)?eV:"object"==typeof e?ep.test(e)?eD:eN:eT}function eV(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>eO(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eN(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=eO(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eI=(e,t)=>{let n=eS.createTransformer(t),r=eb(e),i=eb(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?ej.has(e)&&!i.values.length||ej.has(t)&&!r.values.length?function(e,t){return ej.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):N(eV(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],s=e.indexes[o][r[o]],a=e.values[s]??0;n[i]=a,r[o]++}return n}(r,i),i.values),n):(U(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),eT(e,t))};function eF(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?eP(e,t,n):eO(e)(e,t)}let eB=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>p.update(t,e),stop:()=>m(t),now:()=>g.isProcessing?g.timestamp:T.now()}},e_=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function eU(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function ez(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let eW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function e$(e,t){return e*Math.sqrt(1-t*t)}let eH=["duration","bounce"],eK=["stiffness","damping","mass"];function eG(e,t){return t.some(t=>void 0!==e[t])}function eX(e=eW.visualDuration,t=eW.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:eW.velocity,stiffness:eW.stiffness,damping:eW.damping,mass:eW.mass,isResolvedFromDuration:!1,...e};if(!eG(e,eK)&&eG(e,eH))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*I(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:eW.mass,stiffness:r,damping:i}}else{let n=function({duration:e=eW.duration,bounce:t=eW.bounce,velocity:n=eW.velocity,mass:r=eW.mass}){let i,o;U(e<=F(eW.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let s=1-t;s=I(eW.minDamping,eW.maxDamping,s),e=I(eW.minDuration,eW.maxDuration,B(e)),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/e$(t,s)*Math.exp(-i)},o=t=>{let r=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=Math.exp(-r),l=e$(Math.pow(t,2),s);return(r*n+n-o)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=F(e),isNaN(a))return{stiffness:eW.stiffness,damping:eW.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:eW.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-B(r.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*d)),v=a-s,y=B(Math.sqrt(u/d)),b=5>Math.abs(v);if(i||(i=b?eW.restSpeed.granular:eW.restSpeed.default),o||(o=b?eW.restDelta.granular:eW.restDelta.default),g<1){let e=e$(y,g);n=t=>a-Math.exp(-g*y*t)*((m+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===g)n=e=>a-Math.exp(-y*e)*(v+(m+y*v)*e);else{let e=y*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*y*t),r=Math.min(e*t,300);return a-n*((m+g*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}let w={calculatedDuration:p&&h||null,next:e=>{let t=n(e);if(p)l.done=e>=h;else{let r=0===e?m:0;g<1&&(r=0===e?F(m):ez(n,e,t));let s=Math.abs(a-t)<=o;l.done=Math.abs(r)<=i&&s}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(eU(w),2e4),t=e_(t=>w.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return w}function eY({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let d,h,f=e[0],p={done:!1,value:f},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l||Math.abs(a-e)<Math.abs(l-e)?a:l,v=n*t,y=f+v,b=void 0===s?y:s(y);b!==y&&(v=b-f);let w=e=>-v*Math.exp(-e/r),x=e=>b+w(e),k=e=>{let t=w(e),n=x(e);p.done=Math.abs(t)<=u,p.value=p.done?b:n},S=e=>{m(p.value)&&(d=e,h=eX({keyframes:[p.value,g(p.value)],velocity:ez(x,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return S(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,k(e),S(e)),void 0!==d&&e>=d)?h.next(e-d):(t||k(e),p)}}}eX.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(eU(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:B(i)}}(e,100,eX);return e.ease=t.ease,e.duration=F(t.duration),e.type="keyframes",e};let eq=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eZ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let o,s,a=0;do(o=eq(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:eq(i(e),t,r)}let eJ=eZ(.42,0,1,1),eQ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e5=e=>t=>1-e(1-t),e3=eZ(.33,1.53,.69,.99),e9=e5(e3),e4=e2(e9),e6=e=>(e*=2)<1?.5*e9(e):.5*(2-Math.pow(2,-10*(e-1))),e8=e=>1-Math.sin(Math.acos(e)),e7=e5(e8),te=e2(e8),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eJ,easeInOut:e0,easeOut:eQ,circIn:e8,circInOut:te,circOut:e7,backIn:e9,backInOut:e4,backOut:e3,anticipate:e6},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){z(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,n,r,i]=e;return eZ(t,n,r,i)}return tr(e)?(z(void 0!==tn[e],`Invalid easing type '${e}'`,"invalid-easing-type"),tn[e]):e},to=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function ts({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=e1(r)?r.map(ti):ti(r),s={done:!1,value:t[0]},a=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(z(o===t.length,"Both input and output ranges must be the same length","range-length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,n){let r=[],i=n||c.mix||eF,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=N(Array.isArray(t)?t[n]||u:t,o)),r.push(o)}return r}(t,r,i),l=a.length,d=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=to(e[r],e[r+1],n);return a[r](i)};return n?t=>d(I(e[0],e[o-1],t)):d}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=to(0,t,r);e.push(eP(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=a(t),s.done=t>=e,s)}}let ta=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(ta),s=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return s&&void 0!==r?r:o[s]}let tu={decay:eY,inertia:eY,tween:ts,keyframes:ts,spring:eX};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tf extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==T.now()&&this.tick(T.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},_.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ts,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:s}=e,a=t||ts;a!==ts&&"number"!=typeof s[0]&&(this.mixKeyframes=N(th,eF(s[0],s[1])),s=[0,100]);let l=a({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=a({...e,keyframes:[...s].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=eU(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:s,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,b=n;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/s)):"mirror"===d&&(b=o)),y=I(0,1,n)*s}let w=v?{done:!1,value:u[0]}:b.next(y);i&&(w.value=i(w.value));let{done:x}=w;v||null===a||(x=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return k&&f!==eY&&(w.value=tl(u,this.options,m,this.speed)),p&&p(w.value),k&&this.finish(),w}then(e,t){return this.finished.then(e,t)}get duration(){return B(this.calculatedDuration)}get time(){return B(this.currentTime)}set time(e){e=F(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(T.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=B(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eB,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(T.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,_.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tp=e=>180*e/Math.PI,tm=e=>tv(tp(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tp(Math.atan(e[1])),skewY:e=>tp(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tv=e=>((e%=360)<0&&(e+=360),e),ty=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tb=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tw={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ty,scaleY:tb,scale:e=>(ty(e)+tb(e))/2,rotateX:e=>tv(tp(Math.atan2(e[6],e[5]))),rotateY:e=>tv(tp(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tp(Math.atan(e[4])),skewY:e=>tp(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tx(e){return+!!e.includes("scale")}function tk(e,t){let n,r;if(!e||"none"===e)return tx(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tw,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=tg,r=t}if(!r)return tx(t);let o=n[t],s=r[1].split(",").map(tE);return"function"==typeof o?o(s):s[o]}let tS=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tk(n,t)};function tE(e){return parseFloat(e.trim())}let tT=e=>e===X||e===eu,tP=new Set(["x","y","z"]),tA=y.filter(e=>!tP.has(e)),tM={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tk(t,"x"),y:(e,{transform:t})=>tk(t,"y")};tM.translateX=tM.x,tM.translateY=tM.y;let tC=new Set,tR=!1,tD=!1,tj=!1;function tL(){if(tD){let e=Array.from(tC).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return tA.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tD=!1,tR=!1,tC.forEach(e=>e.complete(tj)),tC.clear()}function tO(){tC.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tD=!0)})}class tV{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tC.add(this),tR||(tR=!0,p.read(tO),p.resolveKeyframes(tL))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tC.delete(this)}cancel(){"scheduled"===this.state&&(tC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tN=e=>e.startsWith("--");function tI(e){let t;return()=>(void 0===t&&(t=e()),t)}let tF=tI(()=>void 0!==window.ScrollTimeline),tB={},t_=function(e,t){let n=tI(e);return()=>tB[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tU=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,tz={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tU([0,.65,.55,1]),circOut:tU([.55,0,1,.45]),backIn:tU([.31,.01,.66,-.59]),backOut:tU([.33,1.53,.69,.99])};function tW(e){return"function"==typeof e&&"applyToOptions"in e}class t$ extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:s,onComplete:a}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,z("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:e,...t}){return tW(e)&&t_()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let d=function e(t,n){if(t)return"function"==typeof t?t_()?e_(t,n):"ease-out":tt(t)?tU(t):Array.isArray(t)?t.map(t=>e(t,n)||tz.easeOut):tz[t]}(a,i);Array.isArray(d)&&(c.easing=d),h.value&&_.waapi++;let f={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"};u&&(f.pseudoElement=u);let p=e.animate(c,f);return h.value&&p.finished.finally(()=>{_.waapi--}),p}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tN(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return B(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return B(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=F(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tF())?(this.animation.timeline=e,u):t(this)}}let tH={anticipate:e6,backInOut:e4,circInOut:te};class tK extends t${constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tH&&(e.ease=tH[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new tf({...o,autoplay:!1}),a=F(this.finishedTime??this.time);t.setWithVelocity(s.sample(a-10).value,s.sample(a).value,10),s.stop()}}let tG=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eS.test(e)||"0"===e)&&!e.startsWith("url("));var tX,tY,tq=n(7351);let tZ=new Set(["opacity","clipPath","filter","transform"]),tJ=tI(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tQ extends td{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:s,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=T.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:a,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tV;this.keyframeResolver=new h(s,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:s,delay:a,isHandoff:l,onUpdate:d}=n;this.resolvedAt=T.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],s=tG(i,t),a=tG(o,t);return U(s===a,`You are trying to animate ${t} from "${i}" to "${o}". "${s?o:i}" is not an animatable value.`,"value-not-animatable"),!!s&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||tW(n))&&r)}(e,i,o,s)&&((c.instantAnimations||!a)&&d?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},f=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:s}=e;if(!(0,tq.s)(t?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return tJ()&&n&&tZ.has(n)&&("transform"!==n||!l)&&!a&&!r&&"mirror"!==i&&0!==o&&"inertia"!==s}(h)?new tK({...h,element:h.motionValue.owner.current}):new tf(h);f.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tj=!0,tO(),tL(),tj=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t5={type:"keyframes",duration:.8},t3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t9=(e,{keyframes:t})=>t.length>2?t5:b.has(e)?e.startsWith("scale")?t2(t[1]):t1:t3,t4=(e,t,n,r={},i,o)=>s=>{let a=l(r,e)||{},u=a.delay||r.delay||0,{elapsed:d=0}=r;d-=F(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-d,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&Object.assign(h,t9(e,h)),h.duration&&(h.duration=F(h.duration)),h.repeatDelay&&(h.repeatDelay=F(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let f=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(f=!0)),(c.instantAnimations||c.skipAnimations)&&(f=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,f&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t0),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(h.keyframes,a);if(void 0!==e)return void p.update(()=>{h.onUpdate(e),h.onComplete()})}return a.isSync?new tf(h):new tQ(h)};function t6(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...u}=t;r&&(o=r);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(d,t))continue;let s={delay:n,...l(o||{},t)},a=r.get();if(void 0!==a&&!r.isAnimating&&!Array.isArray(i)&&i===a&&!s.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let n=e.props[O];if(n){let e=window.MotionHandoffAnimation(n,t,p);null!==e&&(s.startTime=e,h=!0)}}j(e,t),r.start(t4(t,r,i,e.shouldReduceMotion&&w.has(t)?{type:!1}:s,e,h));let f=r.animation;f&&c.push(f)}return s&&Promise.all(c).then(()=>{p.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=a(e,t)||{};for(let t in i={...i,...n}){var o;let n=R(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,C(n))}}(e,s)})}),c}function t8(e,t,n={}){let r=a(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(t6(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(e,t,n=0,r=0,i=0,o=1,s){let a=[],l=e.variantChildren.size,u=(l-1)*i,c="function"==typeof r,d=c?e=>r(e,l):1===o?(e=0)=>e*i:(e=0)=>u-e*i;return Array.from(e.variantChildren).sort(t7).forEach((e,i)=>{e.notify("AnimationStart",t),a.push(t8(e,t,{...s,delay:n+(c?0:r)+d(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,r,o,s,a,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([o(),s(n.delay)]);{let[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}}function t7(e,t){return e.sortNodePosition(t)}function ne(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nt(e){return"string"==typeof e||Array.isArray(e)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nr=["initial",...nn],ni=nr.length,no=[...nn].reverse(),ns=nn.length;function na(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:na(!0),whileInView:na(),whileHover:na(),whileTap:na(),whileDrag:na(),whileFocus:na(),exit:na()}}class nu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nc extends nu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t8(e,t,n)));else if("string"==typeof t)r=t8(e,t,n);else{let i="function"==typeof t?a(e,t,n.custom):t;r=Promise.all(t6(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nl(),r=!0,o=t=>(n,r)=>{let i=a(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<ni;e++){let r=nr[e],i=t.props[r];(nt(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],d=new Set,h={},f=1/0;for(let t=0;t<ns;t++){var p,m;let a=no[t],g=n[a],v=void 0!==l[a]?l[a]:u[a],y=nt(v),b=a===s?g.isActive:null;!1===b&&(f=t);let w=v===u[a]&&v!==l[a]&&y;if(w&&r&&e.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...h},!g.isActive&&null===b||!v&&!g.prevProp||i(v)||"boolean"==typeof v)continue;let x=(p=g.prevProp,"string"==typeof(m=v)?m!==p:!!Array.isArray(m)&&!ne(m,p)),k=x||a===s&&g.isActive&&!w&&y||t>f&&y,S=!1,E=Array.isArray(v)?v:[v],T=E.reduce(o(a),{});!1===b&&(T={});let{prevResolvedValues:P={}}=g,A={...P,...T},M=t=>{k=!0,d.has(t)&&(S=!0,d.delete(t)),g.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in A){let t=T[e],n=P[e];if(h.hasOwnProperty(e))continue;let r=!1;(R(t)&&R(n)?ne(t,n):t===n)?void 0!==t&&d.has(e)?M(e):g.protectedKeys[e]=!0:null!=t?M(e):d.add(e)}g.prevProp=v,g.prevResolvedValues=T,g.isActive&&(h={...h,...T}),r&&e.blockInitialAnimation&&(k=!1);let C=!(w&&x)||S;k&&C&&c.push(...E.map(e=>({animation:e,options:{type:a}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let n=a(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}d.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let g=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=s(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nl(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nd=0;class nh extends nu{constructor(){super(...arguments),this.id=nd++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let nf={x:!1,y:!1};function np(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nm=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ng(e){return{point:{x:e.pageX,y:e.pageY}}}let nv=e=>t=>nm(t)&&e(t,ng(t));function ny(e,t,n,r){return np(e,t,nv(n),r)}function nb({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nw(e){return e.max-e.min}function nx(e,t,n,r=.5){e.origin=r,e.originPoint=eP(t.min,t.max,e.origin),e.scale=nw(n)/nw(t),e.translate=eP(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nk(e,t,n,r){nx(e.x,t.x,n.x,r?r.originX:void 0),nx(e.y,t.y,n.y,r?r.originY:void 0)}function nS(e,t,n){e.min=n.min+t.min,e.max=e.min+nw(t)}function nE(e,t,n){e.min=t.min-n.min,e.max=e.min+nw(t)}function nT(e,t,n){nE(e.x,t.x,n.x),nE(e.y,t.y,n.y)}let nP=()=>({translate:0,scale:1,origin:0,originPoint:0}),nA=()=>({x:nP(),y:nP()}),nM=()=>({min:0,max:0}),nC=()=>({x:nM(),y:nM()});function nR(e){return[e("x"),e("y")]}function nD(e){return void 0===e||1===e}function nj({scale:e,scaleX:t,scaleY:n}){return!nD(e)||!nD(t)||!nD(n)}function nL(e){return nj(e)||nO(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nO(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nV(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nN(e,t=0,n=1,r,i){e.min=nV(e.min,t,n,r,i),e.max=nV(e.max,t,n,r,i)}function nI(e,{x:t,y:n}){nN(e.x,t.translate,t.scale,t.originPoint),nN(e.y,n.translate,n.scale,n.originPoint)}function nF(e,t){e.min=e.min+t,e.max=e.max+t}function nB(e,t,n,r,i=.5){let o=eP(e.min,e.max,i);nN(e,t,n,o,r)}function n_(e,t){nB(e.x,t.x,t.scaleX,t.scale,t.originX),nB(e.y,t.y,t.scaleY,t.scale,t.originY)}function nU(e,t){return nb(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nz=({current:e})=>e?e.ownerDocument.defaultView:null;function nW(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let n$=(e,t)=>Math.abs(e-t);class nH{constructor(e,t,{transformPagePoint:n,contextWindow:r=window,dragSnapToOrigin:i=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nX(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(n$(e.x,t.x)**2+n$(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=g;this.history.push({...r,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nK(t,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=nX("pointercancel"===e.type?this.lastMoveEventInfo:nK(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!nm(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=r||window;let s=nK(ng(e),this.transformPagePoint),{point:a}=s,{timestamp:l}=g;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,nX(s,this.history)),this.removeListeners=N(ny(this.contextWindow,"pointermove",this.handlePointerMove),ny(this.contextWindow,"pointerup",this.handlePointerUp),ny(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function nK(e,t){return t?{point:t(e.point)}:e}function nG(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nX({point:e},t){return{point:e,delta:nG(e,nY(t)),offset:nG(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nY(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>F(.1)));)n--;if(!r)return{x:0,y:0};let o=B(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function nY(e){return e[e.length-1]}function nq(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nZ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nJ(e,t,n){return{min:nQ(e,t),max:nQ(e,n)}}function nQ(e,t){return"number"==typeof e?e:e[t]||0}let n0=new WeakMap;class n1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nC(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:n}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new nH(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ng(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(nf[e])return null;else return nf[e]=!0,()=>{nf[e]=!1};return nf.x||nf.y?null:(nf.x=nf.y=!0,()=>{nf.x=nf.y=!1})}(n),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nR(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nw(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&p.postRender(()=>i(e,t)),j(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>nR(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:n,contextWindow:nz(this.visualElement)})}stop(e,t){let n=e||this.latestPointerEvent,r=t||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!r||!n)return;let{velocity:o}=r;this.startAnimation(o);let{onDragEnd:s}=this.getProps();s&&p.postRender(()=>s(n,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!n2(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?eP(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?eP(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&nW(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nq(e.x,n,i),y:nq(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nJ(e,"left","right"),y:nJ(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nR(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nW(t))return!1;let r=t.current;z(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=nU(e,n),{scroll:i}=t;return i&&(nF(r.x,i.offset.x),nF(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:nZ(e.x,o.x),y:nZ(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nb(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(nR(s=>{if(!n2(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return j(this.visualElement,e),n.start(t4(e,n,0,t,this.visualElement,!1))}stopAnimation(){nR(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nR(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nR(t=>{let{drag:n}=this.getProps();if(!n2(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-eP(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nW(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nR(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nw(e),i=nw(t);return i>r?n=to(t.min,t.max-r,e.min):r>i&&(n=to(e.min,e.max-i,t.min)),I(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nR(t=>{if(!n2(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(eP(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let e=ny(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nW(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),p.read(t);let i=np(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nR(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function n2(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n5 extends nu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n3=e=>(t,n)=>{e&&p.postRender(()=>e(t,n))};class n9 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new nH(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nz(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n3(e),onStart:n3(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&p.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=ny(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n4=n(5155);let{schedule:n6}=f(queueMicrotask,!1);var n8=n(2115),n7=n(2082),re=n(869);let rt=(0,n8.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ri={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rr(e,t.target.x),r=rr(e,t.target.y);return`${n}% ${r}%`}},ro={},rs=!1;class ra extends n8.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in ru)ro[e]=ru[e],$(e)&&(ro[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),rs&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,rs=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||p.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n6.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rl(e){let[t,n]=(0,n7.xQ)(),r=(0,n8.useContext)(re.L);return(0,n4.jsx)(ra,{...e,layoutGroup:r,switchLayoutGroup:(0,n8.useContext)(rt),isPresent:t,safeToRemove:n})}let ru={borderRadius:{...ri,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ri,borderTopRightRadius:ri,borderBottomLeftRadius:ri,borderBottomRightRadius:ri,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eS.parse(e);if(r.length>5)return e;let i=eS.createTransformer(e),o=+("number"!=typeof r[0]),s=n.x.scale*t.x,a=n.y.scale*t.y;r[0+o]/=s,r[1+o]/=a;let l=eP(s,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};var rc=n(6983);function rd(e){return(0,rc.G)(e)&&"ownerSVGElement"in e}let rh=(e,t)=>e.depth-t.depth;class rf{constructor(){this.children=[],this.isDirty=!1}add(e){x(this.children,e),this.isDirty=!0}remove(e){k(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rh),this.isDirty=!1,this.children.forEach(e)}}function rp(e){return D(e)?e.get():e}let rm=["TopLeft","TopRight","BottomLeft","BottomRight"],rg=rm.length,rv=e=>"string"==typeof e?parseFloat(e):e,ry=e=>"number"==typeof e||eu.test(e);function rb(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rw=rk(0,.5,e7),rx=rk(.5,.95,u);function rk(e,t,n){return r=>r<e?0:r>t?1:n(to(e,t,r))}function rS(e,t){e.min=t.min,e.max=t.max}function rE(e,t){rS(e.x,t.x),rS(e.y,t.y)}function rT(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rP(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rA(e,t,[n,r,i],o,s){!function(e,t=0,n=1,r=.5,i,o=e,s=e){if(el.test(t)&&(t=parseFloat(t),t=eP(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=eP(o.min,o.max,r);e===o&&(a-=t),e.min=rP(e.min,t,n,a,i),e.max=rP(e.max,t,n,a,i)}(e,t[n],t[r],t[i],t.scale,o,s)}let rM=["x","scaleX","originX"],rC=["y","scaleY","originY"];function rR(e,t,n,r){rA(e.x,t,rM,n?n.x:void 0,r?r.x:void 0),rA(e.y,t,rC,n?n.y:void 0,r?r.y:void 0)}function rD(e){return 0===e.translate&&1===e.scale}function rj(e){return rD(e.x)&&rD(e.y)}function rL(e,t){return e.min===t.min&&e.max===t.max}function rO(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rV(e,t){return rO(e.x,t.x)&&rO(e.y,t.y)}function rN(e){return nw(e.x)/nw(e.y)}function rI(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rF{constructor(){this.members=[]}add(e){x(this.members,e),e.scheduleRender()}remove(e){if(k(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rB={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},r_=["","X","Y","Z"],rU=0;function rz(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function rW({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rU++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(rB.nodes=rB.calculatedTargetDeltas=rB.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(rQ),this.nodes.forEach(r0),this.nodes.forEach(rG),h.addProjectionMetrics&&h.addProjectionMetrics(rB)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rf)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new S),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rd(t)&&!(rd(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=0,i=()=>this.root.updateBlockedByResize=!1;p.read(()=>{r=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=T.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&(m(r),e(o-t))};return p.setup(r,!0),()=>m(r)}(i,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rJ)))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||r4,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=i.getProps(),u=!this.targetLayout||!rV(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(o,"layout"),onPlay:s,onComplete:a};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rJ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[O];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",p,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rY);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rq);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(rZ),this.nodes.forEach(r$),this.nodes.forEach(rH)):this.nodes.forEach(rq),this.clearAllSnapshots();let e=T.now();g.delta=I(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,v.update.process(g),v.preRender.process(g),v.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n6.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rX),this.sharedNodes.forEach(r2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nw(this.snapshot.measuredBox.x)||nw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nC(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rj(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nL(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r7((t=r).x),r7(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nC();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(nF(t.x,e.offset.x),nF(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nC();if(rE(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&rE(t,e),nF(t.x,i.offset.x),nF(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nC();rE(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&n_(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nL(r.latestValues)&&n_(n,r.latestValues)}return nL(this.latestValues)&&n_(n,this.latestValues),n}removeTransform(e){let t=nC();rE(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nL(n.latestValues))continue;nj(n.latestValues)&&n.updateSnapshot();let r=nC();rE(r,n.measurePageBox()),rR(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nL(this.latestValues)&&rR(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nC(),this.relativeTargetOrigin=nC(),nT(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rE(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nC(),this.targetWithTransforms=nC()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,s,a;this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,a=this.relativeParent.target,nS(o.x,s.x,a.x),nS(o.y,s.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rE(this.target,this.layout.layoutBox),nI(this.target,this.targetDelta)):rE(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nC(),this.relativeTargetOrigin=nC(),nT(this.relativeTargetOrigin,this.target,e.target),rE(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&rB.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nj(this.parent.latestValues)||nO(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rE(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,o,s=n.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=n[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&n_(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,nI(e,o)),r&&nL(i.latestValues)&&n_(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nC());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rT(this.prevProjectionDelta.x,this.projectionDelta.x),rT(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nk(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&rI(this.projectionDelta.x,this.prevProjectionDelta.x)&&rI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),h.value&&rB.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nA(),this.projectionDelta=nA(),this.projectionDeltaWithTransform=nA()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},s=nA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=nC(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r9));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r5(s.x,e.x,r),r5(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,g;nT(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=a,g=r,r3(f.x,p.x,m.x,g),r3(f.y,p.y,m.y,g),n&&(u=this.relativeTarget,h=n,rL(u.x,h.x)&&rL(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=nC()),rE(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=eP(0,n.opacity??1,rw(r)),e.opacityExit=eP(t.opacity??1,0,rx(r))):o&&(e.opacity=eP(t.opacity??1,n.opacity??1,r));for(let i=0;i<rg;i++){let o=`border${rm[i]}Radius`,s=rb(t,o),a=rb(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||ry(s)===ry(a)?(e[o]=Math.max(eP(rv(s),rv(a),r),0),(el.test(a)||el.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=eP(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{rn.hasAnimatedSinceResize=!0,_.layout++,this.motionValue||(this.motionValue=C(0)),this.currentAnimation=function(e,t,n){let r=D(e)?e:C(e);return r.start(t4("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{_.layout--},onComplete:()=>{_.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ie(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nC();let t=nw(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nw(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rE(t,n),n_(t,i),nk(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rF),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rz("z",e,r,this.animationValues);for(let t=0;t<r_.length;t++)rz(`rotate${r_[t]}`,e,r,this.animationValues),rz(`skew${r_[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let n=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=rp(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rp(t?.pointerEvents)||""),this.hasProjected&&!nL(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1);return}e.visibility="";let i=r.animationValues||r.latestValues;this.applyTransformsToTarget();let o=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,s=n?.z||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:s,skewY:a}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);n&&(o=n(i,o)),e.transform=o;let{x:s,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,r.animationValues?e.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ro){if(void 0===i[t])continue;let{correct:n,applyTo:s,isCSSVariable:a}=ro[t],l="none"===o?i[t]:n(i[t],r);if(s){let t=s.length;for(let n=0;n<t;n++)e[s[n]]=l}else a?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?rp(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rY),this.root.sharedNodes.clear()}}}function r$(e){e.updateLayout()}function rH(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?nR(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=nw(r);r.min=n[e].min,r.max=r.min+i}):ie(i,t.layoutBox,n)&&nR(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],s=nw(n[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=nA();nk(s,n,t.layoutBox);let a=nA();o?nk(a,e.applyTransform(r,!0),t.measuredBox):nk(a,n,t.layoutBox);let l=!rj(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let s=nC();nT(s,t.layoutBox,i.layoutBox);let a=nC();nT(a,n,o.layoutBox),rV(s,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rK(e){h.value&&rB.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rG(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rX(e){e.clearSnapshot()}function rY(e){e.clearMeasurements()}function rq(e){e.isLayoutDirty=!1}function rZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rJ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rQ(e){e.resolveTargetDelta()}function r0(e){e.calcProjection()}function r1(e){e.resetSkewAndRotation()}function r2(e){e.removeLeadSnapshot()}function r5(e,t,n){e.translate=eP(t.translate,0,n),e.scale=eP(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r3(e,t,n,r){e.min=eP(t.min,n.min,r),e.max=eP(t.max,n.max,r)}function r9(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r4={duration:.45,ease:[.4,0,.1,1]},r6=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r8=r6("applewebkit/")&&!r6("chrome/")?Math.round:u;function r7(e){e.min=r8(e.min),e.max=r8(e.max)}function ie(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rN(t)-rN(n)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=rW({attachResizeListener:(e,t)=>np(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},io=rW({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function is(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function ia(e){return!("touch"===e.pointerType||nf.x||nf.y)}function il(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&p.postRender(()=>i(t,ng(t)))}class iu extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=is(e,n),s=e=>{if(!ia(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{ia(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),o}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=N(np(this.node.current,"focus",()=>this.onFocus()),np(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let id=(e,t)=>!!t&&(e===t||id(e,t.parentElement)),ih=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function ig(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iv=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=im(()=>{if(ip.has(n))return;ig(n,"down");let e=im(()=>{ig(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>ig(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function iy(e){return nm(e)&&!(nf.x||nf.y)}function ib(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&p.postRender(()=>i(t,ng(t)))}class iw extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=is(e,n),s=e=>{let r=e.currentTarget;if(!iy(e))return;ip.add(r);let o=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ip.has(r)&&ip.delete(r),iy(e)&&"function"==typeof o&&o(e,{success:t})},a=e=>{s(e,r===window||r===document||n.useGlobalTarget||id(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),(0,tq.s)(e))&&(e.addEventListener("focus",e=>iv(e,i)),ih.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(ib(this.node,t,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ix=new WeakMap,ik=new WeakMap,iS=e=>{let t=ix.get(e.target);t&&t(e)},iE=e=>{e.forEach(iS)},iT={some:0,all:1};class iP extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:iT[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ik.has(n)||ik.set(n,{});let r=ik.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iE,{root:e,...t})),r[i]}(t);return ix.set(e,n),r.observe(e),()=>{ix.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iA=(0,n8.createContext)({strict:!1});var iM=n(1508);let iC=(0,n8.createContext)({});function iR(e){return i(e.animate)||nr.some(t=>nt(e[t]))}function iD(e){return!!(iR(e)||e.variants)}function ij(e){return Array.isArray(e)?e.join(" "):e}var iL=n(8972);let iO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iV={};for(let e in iO)iV[e]={isEnabled:t=>iO[e].some(e=>!!t[e])};let iN=Symbol.for("motionComponentSymbol");var iI=n(845),iF=n(7494);function iB(e,{layout:t,layoutId:n}){return b.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ro[e]||"opacity"===e)}let i_=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iU={...X,transform:Math.round},iz={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:ea,rotateX:ea,rotateY:ea,rotateZ:ea,scale:q,scaleX:q,scaleY:q,scaleZ:q,skew:ea,skewX:ea,skewY:ea,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:Y,originX:eh,originY:eh,originZ:eu,zIndex:iU,fillOpacity:Y,strokeOpacity:Y,numOctaves:iU},iW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},i$=y.length;function iH(e,t,n){let{style:r,vars:i,transformOrigin:o}=e,s=!1,a=!1;for(let e in t){let n=t[e];if(b.has(e)){s=!0;continue}if($(e)){i[e]=n;continue}{let t=i_(n,iz[e]);e.startsWith("origin")?(a=!0,o[e]=t):r[e]=t}}if(!t.transform&&(s||n?r.transform=function(e,t,n){let r="",i=!0;for(let o=0;o<i$;o++){let s=y[o],a=e[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!s.startsWith("scale"):0===parseFloat(a))||n){let e=i_(a,iz[s]);if(!l){i=!1;let t=iW[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;r.transformOrigin=`${e} ${t} ${n}`}}let iK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iG(e,t,n){for(let r in t)D(t[r])||iB(r,n)||(e[r]=t[r])}let iX={offset:"stroke-dashoffset",array:"stroke-dasharray"},iY={offset:"strokeDashoffset",array:"strokeDasharray"};function iq(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:s=0,...a},l,u,c){if(iH(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?iX:iY;e[o.offset]=eu.transform(-r);let s=eu.transform(t),a=eu.transform(n);e[o.array]=`${s} ${a}`}(d,i,o,s,!1)}let iZ=()=>({...iK(),attrs:{}}),iJ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iQ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iQ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i5(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i3=n(2885);let i9=e=>(t,n)=>{let r=(0,n8.useContext)(iC),o=(0,n8.useContext)(iI.t),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:function(e,t,n,r){let o={},a=r(e,{});for(let e in a)o[e]=rp(a[e]);let{initial:l,animate:u}=e,c=iR(e),d=iD(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!n&&!1===n.initial,f=(h=h||!1===l)?u:l;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=s(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(n,r,o,e),renderState:t()}})(e,t,r,o);return n?a():(0,i3.M)(a)};function i4(e,t,n){let{style:r}=e,i={};for(let o in r)(D(r[o])||t.style&&D(t.style[o])||iB(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}let i6={useVisualState:i9({scrapeMotionValuesFromProps:i4,createRenderState:iK})};function i8(e,t,n){let r=i4(e,t,n);for(let n in e)(D(e[n])||D(t[n]))&&(r[-1!==y.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i7={useVisualState:i9({scrapeMotionValuesFromProps:i8,createRenderState:iZ})},oe=e=>t=>t.test(e),ot=[X,eu,el,ea,ed,ec,{test:e=>"auto"===e,parse:e=>e}],on=e=>ot.find(oe(e)),or=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),oi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,oo=e=>/^0[^.\s]+$/u.test(e),os=new Set(["brightness","contrast","saturate","opacity"]);function oa(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(J)||[];if(!r)return e;let i=n.replace(r,""),o=+!!os.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let ol=/\b([a-z-]*)\(.*?\)/gu,ou={...eS,getAnimatableNone:e=>{let t=e.match(ol);return t?t.map(oa).join(" "):e}},oc={...iz,color:ep,backgroundColor:ep,outlineColor:ep,fill:ep,stroke:ep,borderColor:ep,borderTopColor:ep,borderRightColor:ep,borderBottomColor:ep,borderLeftColor:ep,filter:ou,WebkitFilter:ou},od=e=>oc[e];function oh(e,t){let n=od(e);return n!==ou&&(n=eS),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let of=new Set(["auto","none","0"]);class op extends tV{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&K(r=r.trim())){let i=function e(t,n,r=1){z(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[i,o]=function(e){let t=oi.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return or(e)?parseFloat(e):e}return K(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!w.has(n)||2!==e.length)return;let[r,i]=e,o=on(r),s=on(i);if(o!==s)if(tT(o)&&tT(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tM[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||oo(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!of.has(t)&&eb(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=oh(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tM[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=tM[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let om=[...ot,ep,eS],og=e=>om.find(oe(e)),ov={current:null},oy={current:!1},ob=new WeakMap,ow=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ox{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tV,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=T.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,p.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=iR(t),this.isVariantNode=iD(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==a[e]&&D(t)&&t.set(a[e],!1)}}mount(e){this.current=e,ob.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),oy.current||function(){if(oy.current=!0,iL.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ov.current=e.matches;e.addEventListener("change",t),t()}else ov.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ov.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=b.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&p.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iV){let t=iV[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nC()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ow.length;t++){let n=ow[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(D(i))e.addValue(r,i);else if(D(o))e.addValue(r,C(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,C(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=C(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(or(n)||oo(n))?n=parseFloat(n):!og(n)&&eS.test(t)&&(n=oh(e,t)),this.setBaseTarget(e,D(n)?n.get():n)),D(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=s(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||D(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new S),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class ok extends ox{constructor(){super(...arguments),this.KeyframeResolver=op}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;D(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function oS(e,{style:t,vars:n},r,i){let o,s=e.style;for(o in t)s[o]=t[o];for(o in i?.applyProjectionStyles(s,r),n)s.setProperty(o,n[o])}class oE extends ok{constructor(){super(...arguments),this.type="html",this.renderInstance=oS}readValueFromInstance(e,t){if(b.has(t))return this.projection?.isProjecting?tx(t):tS(e,t);{let n=window.getComputedStyle(e),r=($(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nU(e,t)}build(e,t,n){iH(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i4(e,t,n)}}let oT=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oP extends ok{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nC}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(b.has(t)){let e=od(t);return e&&e.default||0}return t=oT.has(t)?t:L(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i8(e,t,n)}build(e,t,n){iq(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in oS(e,t,void 0,r),t.attrs)e.setAttribute(oT.has(n)?n:L(n),t.attrs[n])}mount(e){this.isSVGTag=iJ(e.tagName),super.mount(e)}}let oA=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((tX={animation:{Feature:nc},exit:{Feature:nh},inView:{Feature:iP},tap:{Feature:iw},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:n9},drag:{Feature:n5,ProjectionNode:io,MeasureLayout:rl},layout:{ProjectionNode:io,MeasureLayout:rl}},tY=(e,t)=>i5(e)?new oP(t):new oE(t,{allowProjection:e!==n8.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function(e){var t,n;let{preloadedFeatures:r,createVisualElement:i,useRender:o,useVisualState:s,Component:a}=e;function l(e,t){var n,r,l;let u,c={...(0,n8.useContext)(iM.Q),...e,layoutId:function(e){let{layoutId:t}=e,n=(0,n8.useContext)(re.L).id;return n&&void 0!==t?n+"-"+t:t}(e)},{isStatic:d}=c,h=function(e){let{initial:t,animate:n}=function(e,t){if(iR(e)){let{initial:t,animate:n}=e;return{initial:!1===t||nt(t)?t:void 0,animate:nt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n8.useContext)(iC));return(0,n8.useMemo)(()=>({initial:t,animate:n}),[ij(t),ij(n)])}(e),f=s(e,d);if(!d&&iL.B){r=0,l=0,(0,n8.useContext)(iA).strict;let e=function(e){let{drag:t,layout:n}=iV;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,h.visualElement=function(e,t,n,r,i){let{visualElement:o}=(0,n8.useContext)(iC),s=(0,n8.useContext)(iA),a=(0,n8.useContext)(iI.t),l=(0,n8.useContext)(iM.Q).reducedMotion,u=(0,n8.useRef)(null);r=r||s.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let c=u.current,d=(0,n8.useContext)(rt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&nW(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,d);let h=(0,n8.useRef)(!1);(0,n8.useInsertionEffect)(()=>{c&&h.current&&c.update(n,a)});let f=n[O],p=(0,n8.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,iF.E)(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n6.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,n8.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),c}(a,f,c,i,e.ProjectionNode)}return(0,n4.jsxs)(iC.Provider,{value:h,children:[u&&h.visualElement?(0,n4.jsx)(u,{visualElement:h.visualElement,...c}):null,o(a,e,(n=h.visualElement,(0,n8.useCallback)(e=>{e&&f.onMount&&f.onMount(e),n&&(e?n.mount(e):n.unmount()),t&&("function"==typeof t?t(e):nW(t)&&(t.current=e))},[n])),f,d,h.visualElement)]})}r&&function(e){for(let t in e)iV[t]={...iV[t],...e[t]}}(r),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(n=null!=(t=a.displayName)?t:a.name)?n:"",")"));let u=(0,n8.forwardRef)(l);return u[iN]=a,u}({...i5(e)?i7:i6,preloadedFeatures:tX,useRender:function(e=!1){return(t,n,r,{latestValues:i},o)=>{let s=(i5(t)?function(e,t,n,r){let i=(0,n8.useMemo)(()=>{let n=iZ();return iq(n,t,iJ(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iG(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iG(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n8.useMemo)(()=>{let n=iK();return iH(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,o,t),a=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===n&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n8.Fragment?{...a,...s,ref:r}:{},{children:u}=n,c=(0,n8.useMemo)(()=>D(u)?u.get():u,[u]);return(0,n8.createElement)(t,{...l,children:c})}}(t),createVisualElement:tY,Component:e})}))},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(2115);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=o(e,r)),t&&(i.current=o(t,r))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6786:(e,t,n)=>{n.d(t,{Zr:()=>d,lt:()=>l});let r=new Map,i=e=>{let t=r.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},o=(e,t,n)=>{if(void 0===e)return{type:"untracked",connection:t.connect(n)};let i=r.get(n.name);if(i)return{type:"tracked",store:e,...i};let o={connection:t.connect(n),stores:{}};return r.set(n.name,o),{type:"tracked",store:e,...o}},s=(e,t)=>{if(void 0===t)return;let n=r.get(e);n&&(delete n.stores[t],0===Object.keys(n.stores).length&&r.delete(e))},a=e=>{var t,n;if(!e)return;let r=e.split("\n"),i=r.findIndex(e=>e.includes("api.setState"));if(i<0)return;let o=(null==(t=r[i+1])?void 0:t.trim())||"";return null==(n=/.+ (.+) .+/.exec(o))?void 0:n[1]},l=(e,t={})=>(n,r,l)=>{let c,{enabled:d,anonymousActionType:h,store:f,...p}=t;try{c=(null==d||d)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!c)return e(n,r,l);let{connection:m,...g}=o(f,c,p),v=!0;l.setState=(e,t,o)=>{let s=n(e,t);if(!v)return s;let u=void 0===o?{type:h||a(Error().stack)||"anonymous"}:"string"==typeof o?{type:o}:o;return void 0===f?null==m||m.send(u,r()):null==m||m.send({...u,type:`${f}/${u.type}`},{...i(p.name),[f]:l.getState()}),s},l.devtools={cleanup:()=>{m&&"function"==typeof m.unsubscribe&&m.unsubscribe(),s(p.name,f)}};let y=(...e)=>{let t=v;v=!1,n(...e),v=t},b=e(l.setState,r,l);if("untracked"===g.type?null==m||m.init(b):(g.stores[g.store]=l,null==m||m.init(Object.fromEntries(Object.entries(g.stores).map(([e,t])=>[e,e===g.store?b:t.getState()])))),l.dispatchFromDevtools&&"function"==typeof l.dispatch){let e=!1,t=l.dispatch;l.dispatch=(...n)=>{"__setState"!==n[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...n)}}return m.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return u(e.payload,e=>{if("__setState"===e.type){if(void 0===f)return void y(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[f];return void(null==t||JSON.stringify(l.getState())!==JSON.stringify(t)&&y(t))}l.dispatchFromDevtools&&"function"==typeof l.dispatch&&l.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(y(b),void 0===f)return null==m?void 0:m.init(l.getState());return null==m?void 0:m.init(i(p.name));case"COMMIT":if(void 0===f){null==m||m.init(l.getState());break}return null==m?void 0:m.init(i(p.name));case"ROLLBACK":return u(e.state,e=>{if(void 0===f){y(e),null==m||m.init(l.getState());return}y(e[f]),null==m||m.init(i(p.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return u(e.state,e=>{if(void 0===f)return void y(e);JSON.stringify(l.getState())!==JSON.stringify(e[f])&&y(e[f])});case"IMPORT_STATE":{let{nextLiftedState:n}=e.payload,r=null==(t=n.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;void 0===f?y(r):y(r[f]),null==m||m.send(null,n);break}case"PAUSE_RECORDING":return v=!v}return}}),b},u=(e,t)=>{let n;try{n=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==n&&t(n)},c=e=>t=>{try{let n=e(t);if(n instanceof Promise)return n;return{then:e=>c(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>c(t)(e)}}},d=(e,t)=>(n,r,i)=>{let o,s={storage:function(e,t){let n;try{n=e()}catch(e){return}return{getItem:e=>{var t;let r=e=>null===e?null:JSON.parse(e,void 0),i=null!=(t=n.getItem(e))?t:null;return i instanceof Promise?i.then(r):r(i)},setItem:(e,t)=>n.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>n.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},a=!1,l=new Set,u=new Set,d=s.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...e)},r,i);let h=()=>{let e=s.partialize({...r()});return d.setItem(s.name,{state:e,version:s.version})},f=i.setState;i.setState=(e,t)=>{f(e,t),h()};let p=e((...e)=>{n(...e),h()},r,i);i.getInitialState=()=>p;let m=()=>{var e,t;if(!d)return;a=!1,l.forEach(e=>{var t;return e(null!=(t=r())?t:p)});let i=(null==(t=s.onRehydrateStorage)?void 0:t.call(s,null!=(e=r())?e:p))||void 0;return c(d.getItem.bind(d))(s.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===s.version)return[!1,e.state];else{if(s.migrate){let t=s.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[i,a]=e;if(n(o=s.merge(a,null!=(t=r())?t:p),!0),i)return h()}).then(()=>{null==i||i(o,void 0),o=r(),a=!0,u.forEach(e=>e(o))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{s={...s,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>m(),hasHydrated:()=>a,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},s.skipHydration||m(),o||p}},6874:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return y}});let r=n(6966),i=n(5155),o=r._(n(2115)),s=n(2757),a=n(5227),l=n(9818),u=n(6654),c=n(9991),d=n(5929);n(3230);let h=n(4930),f=n(2664),p=n(6634);function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){let t,n,r,[s,g]=(0,o.useOptimistic)(h.IDLE_LINK_STATUS),y=(0,o.useRef)(null),{href:b,as:w,children:x,prefetch:k=null,passHref:S,replace:E,shallow:T,scroll:P,onClick:A,onMouseEnter:M,onTouchStart:C,legacyBehavior:R=!1,onNavigate:D,ref:j,unstable_dynamicOnHover:L,...O}=e;t=x,R&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let V=o.default.useContext(a.AppRouterContext),N=!1!==k,I=null===k?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:F,as:B}=o.default.useMemo(()=>{let e=m(b);return{href:e,as:w?m(w):e}},[b,w]);R&&(n=o.default.Children.only(t));let _=R?n&&"object"==typeof n&&n.ref:j,U=o.default.useCallback(e=>(null!==V&&(y.current=(0,h.mountLinkInstance)(e,F,V,I,N,g)),()=>{y.current&&((0,h.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,h.unmountPrefetchableInstance)(e)}),[N,F,V,I,g]),z={ref:(0,u.useMergedRef)(U,_),onClick(e){R||"function"!=typeof A||A(e),R&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),V&&(e.defaultPrevented||function(e,t,n,r,i,s,a){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),o.default.startTransition(()=>{if(a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(n||t,i?"replace":"push",null==s||s,r.current)})}}(e,F,B,y,E,P,D))},onMouseEnter(e){R||"function"!=typeof M||M(e),R&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),V&&N&&(0,h.onNavigationIntent)(e.currentTarget,!0===L)},onTouchStart:function(e){R||"function"!=typeof C||C(e),R&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),V&&N&&(0,h.onNavigationIntent)(e.currentTarget,!0===L)}};return(0,c.isAbsoluteUrl)(B)?z.href=B:R&&!S&&("a"!==n.type||"href"in n.props)||(z.href=(0,d.addBasePath)(B)),r=R?o.default.cloneElement(n,z):(0,i.jsx)("a",{...O,...z,children:t}),(0,i.jsx)(v.Provider,{value:s,children:r})}n(3180);let v=(0,o.createContext)(h.IDLE_LINK_STATUS),y=()=>(0,o.useContext)(v);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6983:(e,t,n)=>{n.d(t,{G:()=>r});function r(e){return"object"==typeof e&&null!==e}},7328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function i(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function o(e,t,n){var i=r(e,t,"set");if(i.set)i.set.call(e,n);else{if(!i.writable)throw TypeError("attempted to set read only private field");i.value=n}return n}n.d(t,{N:()=>h});var s,a=n(2115),l=n(6081),u=n(6101),c=n(9708),d=n(5155);function h(e){let t=e+"CollectionProvider",[n,r]=(0,l.A)(t),[i,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=a.useRef(null),o=a.useRef(new Map).current;return(0,d.jsx)(i,{scope:t,itemMap:o,collectionRef:r,children:n})};s.displayName=t;let h=e+"CollectionSlot",f=(0,c.TL)(h),p=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=o(h,n),s=(0,u.s)(t,i.collectionRef);return(0,d.jsx)(f,{ref:s,children:r})});p.displayName=h;let m=e+"CollectionItemSlot",g="data-radix-collection-item",v=(0,c.TL)(m),y=a.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,s=a.useRef(null),l=(0,u.s)(t,s),c=o(m,n);return a.useEffect(()=>(c.itemMap.set(s,{ref:s,...i}),()=>void c.itemMap.delete(s))),(0,d.jsx)(v,{...{[g]:""},ref:l,children:r})});return y.displayName=m,[{Provider:s,Slot:p,ItemSlot:y},function(t){let n=o(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(g,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var f=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=m(t),i=r>=0?r:n+r;return i<0||i>=n?-1:i}(e,t);return -1===n?void 0:e[n]}function m(e){return e!=e||0===e?0:Math.trunc(e)}s=new WeakMap},7340:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7351:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(6983);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},7494:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(2115);let i=n(8972).B?r.useLayoutEffect:r.useEffect},7580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),i=n(6101),o=n(3655),s=n(9033),a=n(5155),l="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:g,onUnmountAutoFocus:v,...y}=e,[b,w]=r.useState(null),x=(0,s.c)(g),k=(0,s.c)(v),S=r.useRef(null),E=(0,i.s)(t,e=>w(e)),T=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(T.paused||!b)return;let t=e.target;b.contains(t)?S.current=t:p(S.current,{select:!0})},t=function(e){if(T.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||p(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,b,T.paused]),r.useEffect(()=>{if(b){m.add(T);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(l,c);b.addEventListener(l,x),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(p(r,{select:t}),document.activeElement!==n)return}(h(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(b))}return()=>{b.removeEventListener(l,x),setTimeout(()=>{let t=new CustomEvent(u,c);b.addEventListener(u,k),b.dispatchEvent(t),t.defaultPrevented||p(null!=e?e:document.body,{select:!0}),b.removeEventListener(u,k),m.remove(T)},0)}}},[b,x,k,T]);let P=r.useCallback(e=>{if(!n&&!d||T.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[i,o]=function(e){let t=h(e);return[f(t,e),f(t.reverse(),e)]}(t);i&&o?e.shiftKey||r!==o?e.shiftKey&&r===i&&(e.preventDefault(),n&&p(o,{select:!0})):(e.preventDefault(),n&&p(i,{select:!0})):r===t&&e.preventDefault()}},[n,d,T.paused]);return(0,a.jsx)(o.sG.div,{tabIndex:-1,...y,ref:E,onKeyDown:P})});function h(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function f(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function p(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=g(e,t)).unshift(t)},remove(t){var n;null==(n=(e=g(e,t))[0])||n.resume()}}}();function g(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},7924:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8168:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},i=new WeakMap,o=new WeakMap,s={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});s[n]||(s[n]=new WeakMap);var c=s[n],d=[],h=new Set,f=new Set(u),p=function(e){!e||h.has(e)||(h.add(e),p(e.parentNode))};u.forEach(p);var m=function(e){!e||f.has(e)||Array.prototype.forEach.call(e.children,function(e){if(h.has(e))m(e);else try{var t=e.getAttribute(r),s=null!==t&&"false"!==t,a=(i.get(e)||0)+1,l=(c.get(e)||0)+1;i.set(e,a),c.set(e,l),d.push(e),1===a&&s&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),s||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),h.clear(),a++,function(){d.forEach(function(e){var t=i.get(e)-1,s=c.get(e)-1;i.set(e,t),c.set(e,s),t||(o.has(e)||e.removeAttribute(r),o.delete(e)),s||e.removeAttribute(n)}),--a||(i=new WeakMap,i=new WeakMap,o=new WeakMap,s={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),o=t||r(e);return o?(i.push.apply(i,Array.from(o.querySelectorAll("[aria-live], script"))),u(i,o,n,"aria-hidden")):function(){return null}}},8698:(e,t,n)=>{n.d(t,{UC:()=>eG,q7:()=>eX,ZL:()=>eK,bL:()=>e$,wv:()=>eY,l9:()=>eH});var r=n(2115),i=n(5185),o=n(6101),s=n(6081),a=n(5845),l=n(3655),u=n(7328),c=n(4315),d=n(9178),h=n(2293),f=n(7900),p=n(1285),m=n(5152),g=n(4378),v=n(8905),y=n(9196),b=n(9708),w=n(9033),x=n(8168),k=n(3795),S=n(5155),E=["Enter"," "],T=["ArrowUp","PageDown","End"],P=["ArrowDown","PageUp","Home",...T],A={ltr:[...E,"ArrowRight"],rtl:[...E,"ArrowLeft"]},M={ltr:["ArrowLeft"],rtl:["ArrowRight"]},C="Menu",[R,D,j]=(0,u.N)(C),[L,O]=(0,s.A)(C,[j,m.Bk,y.RG]),V=(0,m.Bk)(),N=(0,y.RG)(),[I,F]=L(C),[B,_]=L(C),U=e=>{let{__scopeMenu:t,open:n=!1,children:i,dir:o,onOpenChange:s,modal:a=!0}=e,l=V(t),[u,d]=r.useState(null),h=r.useRef(!1),f=(0,w.c)(s),p=(0,c.jH)(o);return r.useEffect(()=>{let e=()=>{h.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>h.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,S.jsx)(m.bL,{...l,children:(0,S.jsx)(I,{scope:t,open:n,onOpenChange:f,content:u,onContentChange:d,children:(0,S.jsx)(B,{scope:t,onClose:r.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:h,dir:p,modal:a,children:i})})})};U.displayName=C;var z=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,i=V(n);return(0,S.jsx)(m.Mz,{...i,...r,ref:t})});z.displayName="MenuAnchor";var W="MenuPortal",[$,H]=L(W,{forceMount:void 0}),K=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:i}=e,o=F(W,t);return(0,S.jsx)($,{scope:t,forceMount:n,children:(0,S.jsx)(v.C,{present:n||o.open,children:(0,S.jsx)(g.Z,{asChild:!0,container:i,children:r})})})};K.displayName=W;var G="MenuContent",[X,Y]=L(G),q=r.forwardRef((e,t)=>{let n=H(G,e.__scopeMenu),{forceMount:r=n.forceMount,...i}=e,o=F(G,e.__scopeMenu),s=_(G,e.__scopeMenu);return(0,S.jsx)(R.Provider,{scope:e.__scopeMenu,children:(0,S.jsx)(v.C,{present:r||o.open,children:(0,S.jsx)(R.Slot,{scope:e.__scopeMenu,children:s.modal?(0,S.jsx)(Z,{...i,ref:t}):(0,S.jsx)(J,{...i,ref:t})})})})}),Z=r.forwardRef((e,t)=>{let n=F(G,e.__scopeMenu),s=r.useRef(null),a=(0,o.s)(t,s);return r.useEffect(()=>{let e=s.current;if(e)return(0,x.Eq)(e)},[]),(0,S.jsx)(ee,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=F(G,e.__scopeMenu);return(0,S.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=(0,b.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:s=!1,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:p,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:w,onDismiss:x,disableOutsideScroll:E,...A}=e,M=F(G,n),C=_(G,n),R=V(n),j=N(n),L=D(n),[O,I]=r.useState(null),B=r.useRef(null),U=(0,o.s)(t,B,M.onContentChange),z=r.useRef(0),W=r.useRef(""),$=r.useRef(0),H=r.useRef(null),K=r.useRef("right"),Y=r.useRef(0),q=E?k.A:r.Fragment,Z=e=>{var t,n;let r=W.current+e,i=L().filter(e=>!e.disabled),o=document.activeElement,s=null==(t=i.find(e=>e.ref.current===o))?void 0:t.textValue,a=function(e,t,n){var r;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=n?e.indexOf(n):-1,s=(r=Math.max(o,0),e.map((t,n)=>e[(r+n)%e.length]));1===i.length&&(s=s.filter(e=>e!==n));let a=s.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return a!==n?a:void 0}(i.map(e=>e.textValue),r,s),l=null==(n=i.find(e=>e.textValue===a))?void 0:n.ref.current;!function e(t){W.current=t,window.clearTimeout(z.current),""!==t&&(z.current=window.setTimeout(()=>e(""),1e3))}(r),l&&setTimeout(()=>l.focus())};r.useEffect(()=>()=>window.clearTimeout(z.current),[]),(0,h.Oh)();let J=r.useCallback(e=>{var t,n;return K.current===(null==(t=H.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e],a=t[o],l=s.x,u=s.y,c=a.x,d=a.y;u>r!=d>r&&n<(c-l)*(r-u)/(d-u)+l&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,null==(n=H.current)?void 0:n.area)},[]);return(0,S.jsx)(X,{scope:n,searchRef:W,onItemEnter:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:r.useCallback(e=>{var t;J(e)||(null==(t=B.current)||t.focus(),I(null))},[J]),onTriggerLeave:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:$,onPointerGraceIntentChange:r.useCallback(e=>{H.current=e},[]),children:(0,S.jsx)(q,{...E?{as:Q,allowPinchZoom:!0}:void 0,children:(0,S.jsx)(f.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,i.m)(l,e=>{var t;e.preventDefault(),null==(t=B.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,S.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:w,onDismiss:x,children:(0,S.jsx)(y.bL,{asChild:!0,...j,dir:C.dir,orientation:"vertical",loop:s,currentTabStopId:O,onCurrentTabStopIdChange:I,onEntryFocus:(0,i.m)(p,e=>{C.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,S.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eP(M.open),"data-radix-menu-content":"",dir:C.dir,...R,...A,ref:U,style:{outline:"none",...A.style},onKeyDown:(0,i.m)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Z(e.key));let i=B.current;if(e.target!==i||!P.includes(e.key))return;e.preventDefault();let o=L().filter(e=>!e.disabled).map(e=>e.ref.current);T.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(z.current),W.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,eC(e=>{let t=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(t)&&n&&(K.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});q.displayName=G;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,S.jsx)(l.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,S.jsx)(l.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",ei="menu.itemSelect",eo=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:s,...a}=e,u=r.useRef(null),c=_(er,e.__scopeMenu),d=Y(er,e.__scopeMenu),h=(0,o.s)(t,u),f=r.useRef(!1);return(0,S.jsx)(es,{...a,ref:h,disabled:n,onClick:(0,i.m)(e.onClick,()=>{let e=u.current;if(!n&&e){let t=new CustomEvent(ei,{bubbles:!0,cancelable:!0});e.addEventListener(ei,e=>null==s?void 0:s(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?f.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),f.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{var t;f.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||E.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=er;var es=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:s=!1,textValue:a,...u}=e,c=Y(er,n),d=N(n),h=r.useRef(null),f=(0,o.s)(t,h),[p,m]=r.useState(!1),[g,v]=r.useState("");return r.useEffect(()=>{let e=h.current;if(e){var t;v((null!=(t=e.textContent)?t:"").trim())}},[u.children]),(0,S.jsx)(R.ItemSlot,{scope:n,disabled:s,textValue:null!=a?a:g,children:(0,S.jsx)(y.q7,{asChild:!0,...d,focusable:!s,children:(0,S.jsx)(l.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":s||void 0,"data-disabled":s?"":void 0,...u,ref:f,onPointerMove:(0,i.m)(e.onPointerMove,eC(e=>{s?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eC(e=>c.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>m(!0)),onBlur:(0,i.m)(e.onBlur,()=>m(!1))})})})}),ea=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,S.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,S.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eA(n)?"mixed":n,...o,ref:t,"data-state":eM(n),onSelect:(0,i.m)(o.onSelect,()=>null==r?void 0:r(!!eA(n)||!n),{checkForDefaultPrevented:!1})})})});ea.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[eu,ec]=L(el,{value:void 0,onValueChange:()=>{}}),ed=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...i}=e,o=(0,w.c)(r);return(0,S.jsx)(eu,{scope:e.__scopeMenu,value:n,onValueChange:o,children:(0,S.jsx)(et,{...i,ref:t})})});ed.displayName=el;var eh="MenuRadioItem",ef=r.forwardRef((e,t)=>{let{value:n,...r}=e,o=ec(eh,e.__scopeMenu),s=n===o.value;return(0,S.jsx)(em,{scope:e.__scopeMenu,checked:s,children:(0,S.jsx)(eo,{role:"menuitemradio","aria-checked":s,...r,ref:t,"data-state":eM(s),onSelect:(0,i.m)(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});ef.displayName=eh;var ep="MenuItemIndicator",[em,eg]=L(ep,{checked:!1}),ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...i}=e,o=eg(ep,n);return(0,S.jsx)(v.C,{present:r||eA(o.checked)||!0===o.checked,children:(0,S.jsx)(l.sG.span,{...i,ref:t,"data-state":eM(o.checked)})})});ev.displayName=ep;var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,S.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ey.displayName="MenuSeparator";var eb=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,i=V(n);return(0,S.jsx)(m.i3,{...i,...r,ref:t})});eb.displayName="MenuArrow";var[ew,ex]=L("MenuSub"),ek="MenuSubTrigger",eS=r.forwardRef((e,t)=>{let n=F(ek,e.__scopeMenu),s=_(ek,e.__scopeMenu),a=ex(ek,e.__scopeMenu),l=Y(ek,e.__scopeMenu),u=r.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=l,h={__scopeMenu:e.__scopeMenu},f=r.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return r.useEffect(()=>f,[f]),r.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,S.jsx)(z,{asChild:!0,...h,children:(0,S.jsx)(es,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":eP(n.open),...e,ref:(0,o.t)(t,a.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,eC(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eC(e=>{var t,r;f();let i=null==(t=n.content)?void 0:t.getBoundingClientRect();if(i){let t=null==(r=n.content)?void 0:r.dataset.side,o="right"===t,s=i[o?"left":"right"],a=i[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:s,y:i.top},{x:a,y:i.top},{x:a,y:i.bottom},{x:s,y:i.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let r=""!==l.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&A[s.dir].includes(t.key)){var i;n.onOpenChange(!0),null==(i=n.content)||i.focus(),t.preventDefault()}})})})});eS.displayName=ek;var eE="MenuSubContent",eT=r.forwardRef((e,t)=>{let n=H(G,e.__scopeMenu),{forceMount:s=n.forceMount,...a}=e,l=F(G,e.__scopeMenu),u=_(G,e.__scopeMenu),c=ex(eE,e.__scopeMenu),d=r.useRef(null),h=(0,o.s)(t,d);return(0,S.jsx)(R.Provider,{scope:e.__scopeMenu,children:(0,S.jsx)(v.C,{present:s||l.open,children:(0,S.jsx)(R.Slot,{scope:e.__scopeMenu,children:(0,S.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:h,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=M[u.dir].includes(e.key);if(t&&n){var r;l.onOpenChange(!1),null==(r=c.trigger)||r.focus(),e.preventDefault()}})})})})})});function eP(e){return e?"open":"closed"}function eA(e){return"indeterminate"===e}function eM(e){return eA(e)?"indeterminate":e?"checked":"unchecked"}function eC(e){return t=>"mouse"===t.pointerType?e(t):void 0}eT.displayName=eE;var eR="DropdownMenu",[eD,ej]=(0,s.A)(eR,[O]),eL=O(),[eO,eV]=eD(eR),eN=e=>{let{__scopeDropdownMenu:t,children:n,dir:i,open:o,defaultOpen:s,onOpenChange:l,modal:u=!0}=e,c=eL(t),d=r.useRef(null),[h,f]=(0,a.i)({prop:o,defaultProp:null!=s&&s,onChange:l,caller:eR});return(0,S.jsx)(eO,{scope:t,triggerId:(0,p.B)(),triggerRef:d,contentId:(0,p.B)(),open:h,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,S.jsx)(U,{...c,open:h,onOpenChange:f,dir:i,modal:u,children:n})})};eN.displayName=eR;var eI="DropdownMenuTrigger",eF=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...s}=e,a=eV(eI,n),u=eL(n);return(0,S.jsx)(z,{asChild:!0,...u,children:(0,S.jsx)(l.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...s,ref:(0,o.t)(t,a.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eI;var eB=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eL(t);return(0,S.jsx)(K,{...r,...n})};eB.displayName="DropdownMenuPortal";var e_="DropdownMenuContent",eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,s=eV(e_,n),a=eL(n),l=r.useRef(!1);return(0,S.jsx)(q,{id:s.contentId,"aria-labelledby":s.triggerId,...a,...o,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;l.current||null==(t=s.triggerRef.current)||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!s.modal||r)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eU.displayName=e_,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(et,{...i,...r,ref:t})}).displayName="DropdownMenuGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(en,{...i,...r,ref:t})}).displayName="DropdownMenuLabel";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(eo,{...i,...r,ref:t})});ez.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(ea,{...i,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(ed,{...i,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(ef,{...i,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(ev,{...i,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(ey,{...i,...r,ref:t})});eW.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(eb,{...i,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(eS,{...i,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eL(n);return(0,S.jsx)(eT,{...i,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e$=eN,eH=eF,eK=eB,eG=eU,eX=ez,eY=eW},8859:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function o(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},8905:(e,t,n)=>{n.d(t,{C:()=>s});var r=n(2115),i=n(6101),o=n(2712),s=e=>{let{present:t,children:n}=e,s=function(e){var t,n;let[i,s]=r.useState(),l=r.useRef(null),u=r.useRef(e),c=r.useRef("none"),[d,h]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(l.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=l.current,n=u.current;if(n!==e){let r=c.current,i=a(t);e?h("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?h("UNMOUNT"):n&&r!==i?h("ANIMATION_OUT"):h("UNMOUNT"),u.current=e}},[e,h]),(0,o.N)(()=>{if(i){var e;let t,n=null!=(e=i.ownerDocument.defaultView)?e:window,r=e=>{let r=a(l.current).includes(e.animationName);if(e.target===i&&r&&(h("ANIMATION_END"),!u.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},o=e=>{e.target===i&&(c.current=a(l.current))};return i.addEventListener("animationstart",o),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",o),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}h("ANIMATION_END")},[i,h]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,s(e)},[])}}(t),l="function"==typeof n?n({present:s.isPresent}):r.Children.only(n),u=(0,i.s)(s.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||s.isPresent?r.cloneElement(l,{ref:u}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}s.displayName="Presence"},8972:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},9033:(e,t,n)=>{n.d(t,{c:()=>i});var r=n(2115);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9178:(e,t,n)=>{n.d(t,{qW:()=>h});var r,i=n(2115),o=n(5185),s=n(3655),a=n(6101),l=n(9033),u=n(5155),c="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=i.forwardRef((e,t)=>{var n,h;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:b,onDismiss:w,...x}=e,k=i.useContext(d),[S,E]=i.useState(null),T=null!=(h=null==S?void 0:S.ownerDocument)?h:null==(n=globalThis)?void 0:n.document,[,P]=i.useState({}),A=(0,a.s)(t,e=>E(e)),M=Array.from(k.layers),[C]=[...k.layersWithOutsidePointerEventsDisabled].slice(-1),R=M.indexOf(C),D=S?M.indexOf(S):-1,j=k.layersWithOutsidePointerEventsDisabled.size>0,L=D>=R,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),o=i.useRef(!1),s=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){p("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...k.branches].some(e=>e.contains(t));L&&!n&&(null==v||v(e),null==b||b(e),e.defaultPrevented||null==w||w())},T),V=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...k.branches].some(e=>e.contains(t))&&(null==y||y(e),null==b||b(e),e.defaultPrevented||null==w||w())},T);return!function(e,t=globalThis?.document){let n=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D===k.layers.size-1&&(null==g||g(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},T),i.useEffect(()=>{if(S)return m&&(0===k.layersWithOutsidePointerEventsDisabled.size&&(r=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),k.layersWithOutsidePointerEventsDisabled.add(S)),k.layers.add(S),f(),()=>{m&&1===k.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=r)}},[S,T,m,k]),i.useEffect(()=>()=>{S&&(k.layers.delete(S),k.layersWithOutsidePointerEventsDisabled.delete(S),f())},[S,k]),i.useEffect(()=>{let e=()=>P({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(s.sG.div,{...x,ref:A,style:{pointerEvents:j?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,V.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,V.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});function f(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,s.hO)(o,a):o.dispatchEvent(a)}h.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(d),r=i.useRef(null),o=(0,a.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(s.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},9196:(e,t,n)=>{n.d(t,{RG:()=>x,bL:()=>R,q7:()=>D});var r=n(2115),i=n(5185),o=n(7328),s=n(6101),a=n(6081),l=n(1285),u=n(3655),c=n(9033),d=n(5845),h=n(4315),f=n(5155),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[v,y,b]=(0,o.N)(g),[w,x]=(0,a.A)(g,[b]),[k,S]=w(g),E=r.forwardRef((e,t)=>(0,f.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(T,{...e,ref:t})})}));E.displayName=g;var T=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:o,loop:a=!1,dir:l,currentTabStopId:v,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:S=!1,...E}=e,T=r.useRef(null),P=(0,s.s)(t,T),A=(0,h.jH)(l),[M,R]=(0,d.i)({prop:v,defaultProp:null!=b?b:null,onChange:w,caller:g}),[D,j]=r.useState(!1),L=(0,c.c)(x),O=y(n),V=r.useRef(!1),[N,I]=r.useState(0);return r.useEffect(()=>{let e=T.current;if(e)return e.addEventListener(p,L),()=>e.removeEventListener(p,L)},[L]),(0,f.jsx)(k,{scope:n,orientation:o,dir:A,loop:a,currentTabStopId:M,onItemFocus:r.useCallback(e=>R(e),[R]),onItemShiftTab:r.useCallback(()=>j(!0),[]),onFocusableItemAdd:r.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>I(e=>e-1),[]),children:(0,f.jsx)(u.sG.div,{tabIndex:D||0===N?-1:0,"data-orientation":o,...E,ref:P,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{V.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!V.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=O().filter(e=>e.focusable);C([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),S)}}V.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>j(!1))})})}),P="RovingFocusGroupItem",A=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:o=!0,active:s=!1,tabStopId:a,children:c,...d}=e,h=(0,l.B)(),p=a||h,m=S(P,n),g=m.currentTabStopId===p,b=y(n),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:k}=m;return r.useEffect(()=>{if(o)return w(),()=>x()},[o,w,x]),(0,f.jsx)(v.ItemSlot,{scope:n,id:p,focusable:o,active:s,children:(0,f.jsx)(u.sG.span,{tabIndex:g?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let i=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return M[i]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>C(n))}}),children:"function"==typeof c?c({isCurrentTabStop:g,hasTabStop:null!=k}):c})})});A.displayName=P;var M={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function C(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var R=E,D=A},9688:(e,t,n)=>{n.d(t,{QP:()=>eu});let r=e=>{let t=a(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),i(n,t)||s(e)},getConflictingClassGroupIds:(e,t)=>{let i=n[e]||[];return t&&r[e]?[...i,...r[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),o=r?i(e.slice(1),r):void 0;if(o)return o;if(0===t.validators.length)return;let s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},o=/^\[(.+)\]$/,s=e=>{if(o.test(e)){let t=o.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},a=e=>{let{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(let e in n)l(n[e],r,e,t);return r},l=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=n;return}if("function"==typeof e)return c(e)?void l(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),n,r)})})},u=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,i=(i,o)=>{n.set(i,o),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(i(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):i(e,t)}}},h=e=>{let{prefix:t,experimentalParseClassName:n}=e,r=e=>{let t,n=[],r=0,i=0,o=0;for(let s=0;s<e.length;s++){let a=e[s];if(0===r&&0===i){if(":"===a){n.push(e.slice(o,s)),o=s+1;continue}if("/"===a){t=s;continue}}"["===a?r++:"]"===a?r--:"("===a?i++:")"===a&&i--}let s=0===n.length?e:e.substring(o),a=f(s);return{modifiers:n,hasImportantModifier:a!==s,baseClassName:a,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){let e=r;r=t=>n({className:t,parseClassName:e})}return r},f=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,p=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let n=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},m=e=>({cache:d(e.cacheSize),parseClassName:h(e),sortModifiers:p(e),...r(e)}),g=/\s+/,v=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:o}=t,s=[],a=e.trim().split(g),l="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:f}=n(t);if(u){l=t+(l.length>0?" "+l:l);continue}let p=!!f,m=r(p?h.substring(0,f):h);if(!m){if(!p||!(m=r(h))){l=t+(l.length>0?" "+l:l);continue}p=!1}let g=o(c).join(":"),v=d?g+"!":g,y=v+m;if(s.includes(y))continue;s.push(y);let b=i(m,p);for(let e=0;e<b.length;++e){let t=b[e];s.push(v+t)}l=t+(l.length>0?" "+l:l)}return l};function y(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=b(e))&&(r&&(r+=" "),r+=t);return r}let b=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=b(e[r]))&&(n&&(n+=" "),n+=t);return n},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,S=/^\d+\/\d+$/,E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,T=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,P=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,M=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=e=>S.test(e),R=e=>!!e&&!Number.isNaN(Number(e)),D=e=>!!e&&Number.isInteger(Number(e)),j=e=>e.endsWith("%")&&R(e.slice(0,-1)),L=e=>E.test(e),O=()=>!0,V=e=>T.test(e)&&!P.test(e),N=()=>!1,I=e=>A.test(e),F=e=>M.test(e),B=e=>!U(e)&&!G(e),_=e=>ee(e,ei,N),U=e=>x.test(e),z=e=>ee(e,eo,V),W=e=>ee(e,es,R),$=e=>ee(e,en,N),H=e=>ee(e,er,F),K=e=>ee(e,el,I),G=e=>k.test(e),X=e=>et(e,eo),Y=e=>et(e,ea),q=e=>et(e,en),Z=e=>et(e,ei),J=e=>et(e,er),Q=e=>et(e,el,!0),ee=(e,t,n)=>{let r=x.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},et=(e,t,n=!1)=>{let r=k.exec(e);return!!r&&(r[1]?t(r[1]):n)},en=e=>"position"===e||"percentage"===e,er=e=>"image"===e||"url"===e,ei=e=>"length"===e||"size"===e||"bg-size"===e,eo=e=>"length"===e,es=e=>"number"===e,ea=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let n,r,i,o=function(a){return r=(n=m(t.reduce((e,t)=>t(e),e()))).cache.get,i=n.cache.set,o=s,s(a)};function s(e){let t=r(e);if(t)return t;let o=v(e,n);return i(e,o),o}return function(){return o(y.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),n=w("text"),r=w("font-weight"),i=w("tracking"),o=w("leading"),s=w("breakpoint"),a=w("container"),l=w("spacing"),u=w("radius"),c=w("shadow"),d=w("inset-shadow"),h=w("text-shadow"),f=w("drop-shadow"),p=w("blur"),m=w("perspective"),g=w("aspect"),v=w("ease"),y=w("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),G,U],S=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],T=()=>[G,U,l],P=()=>[C,"full","auto",...T()],A=()=>[D,"none","subgrid",G,U],M=()=>["auto",{span:["full",D,G,U]},D,G,U],V=()=>[D,"auto",G,U],N=()=>["auto","min","max","fr",G,U],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...T()],et=()=>[C,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],en=()=>[e,G,U],er=()=>[...x(),q,$,{position:[G,U]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],eo=()=>["auto","cover","contain",Z,_,{size:[G,U]}],es=()=>[j,X,z],ea=()=>["","none","full",u,G,U],el=()=>["",R,X,z],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[R,j,q,$],eh=()=>["","none",p,G,U],ef=()=>["none",R,G,U],ep=()=>["none",R,G,U],em=()=>[R,G,U],eg=()=>[C,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[L],breakpoint:[L],color:[O],container:[L],"drop-shadow":[L],ease:["in","out","in-out"],font:[B],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[L],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[L],shadow:[L],spacing:["px",R],text:[L],"text-shadow":[L],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",C,U,G,g]}],container:["container"],columns:[{columns:[R,U,G,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:P()}],"inset-x":[{"inset-x":P()}],"inset-y":[{"inset-y":P()}],start:[{start:P()}],end:[{end:P()}],top:[{top:P()}],right:[{right:P()}],bottom:[{bottom:P()}],left:[{left:P()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",G,U]}],basis:[{basis:[C,"full","auto",a,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[R,C,"auto","initial","none",U]}],grow:[{grow:["",R,G,U]}],shrink:[{shrink:["",R,G,U]}],order:[{order:[D,"first","last","none",G,U]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:M()}],"col-start":[{"col-start":V()}],"col-end":[{"col-end":V()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:M()}],"row-start":[{"row-start":V()}],"row-end":[{"row-end":V()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":N()}],"auto-rows":[{"auto-rows":N()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[s]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",n,X,z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,G,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",j,U]}],"font-family":[{font:[Y,U,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,G,U]}],"line-clamp":[{"line-clamp":[R,"none",G,W]}],leading:[{leading:[o,...T()]}],"list-image":[{"list-image":["none",G,U]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,U]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:en()}],"text-color":[{text:en()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[R,"from-font","auto",G,z]}],"text-decoration-color":[{decoration:en()}],"underline-offset":[{"underline-offset":[R,"auto",G,U]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,U]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,U]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:eo()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,G,U],radial:["",G,U],conic:[D,G,U]},J,H]}],"bg-color":[{bg:en()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:en()}],"gradient-via":[{via:en()}],"gradient-to":[{to:en()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:en()}],"border-color-x":[{"border-x":en()}],"border-color-y":[{"border-y":en()}],"border-color-s":[{"border-s":en()}],"border-color-e":[{"border-e":en()}],"border-color-t":[{"border-t":en()}],"border-color-r":[{"border-r":en()}],"border-color-b":[{"border-b":en()}],"border-color-l":[{"border-l":en()}],"divide-color":[{divide:en()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[R,G,U]}],"outline-w":[{outline:["",R,X,z]}],"outline-color":[{outline:en()}],shadow:[{shadow:["","none",c,Q,K]}],"shadow-color":[{shadow:en()}],"inset-shadow":[{"inset-shadow":["none",d,Q,K]}],"inset-shadow-color":[{"inset-shadow":en()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:en()}],"ring-offset-w":[{"ring-offset":[R,z]}],"ring-offset-color":[{"ring-offset":en()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":en()}],"text-shadow":[{"text-shadow":["none",h,Q,K]}],"text-shadow-color":[{"text-shadow":en()}],opacity:[{opacity:[R,G,U]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[R]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":en()}],"mask-image-linear-to-color":[{"mask-linear-to":en()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":en()}],"mask-image-t-to-color":[{"mask-t-to":en()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":en()}],"mask-image-r-to-color":[{"mask-r-to":en()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":en()}],"mask-image-b-to-color":[{"mask-b-to":en()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":en()}],"mask-image-l-to-color":[{"mask-l-to":en()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":en()}],"mask-image-x-to-color":[{"mask-x-to":en()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":en()}],"mask-image-y-to-color":[{"mask-y-to":en()}],"mask-image-radial":[{"mask-radial":[G,U]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":en()}],"mask-image-radial-to-color":[{"mask-radial-to":en()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[R]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":en()}],"mask-image-conic-to-color":[{"mask-conic-to":en()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:eo()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,U]}],filter:[{filter:["","none",G,U]}],blur:[{blur:eh()}],brightness:[{brightness:[R,G,U]}],contrast:[{contrast:[R,G,U]}],"drop-shadow":[{"drop-shadow":["","none",f,Q,K]}],"drop-shadow-color":[{"drop-shadow":en()}],grayscale:[{grayscale:["",R,G,U]}],"hue-rotate":[{"hue-rotate":[R,G,U]}],invert:[{invert:["",R,G,U]}],saturate:[{saturate:[R,G,U]}],sepia:[{sepia:["",R,G,U]}],"backdrop-filter":[{"backdrop-filter":["","none",G,U]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[R,G,U]}],"backdrop-contrast":[{"backdrop-contrast":[R,G,U]}],"backdrop-grayscale":[{"backdrop-grayscale":["",R,G,U]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[R,G,U]}],"backdrop-invert":[{"backdrop-invert":["",R,G,U]}],"backdrop-opacity":[{"backdrop-opacity":[R,G,U]}],"backdrop-saturate":[{"backdrop-saturate":[R,G,U]}],"backdrop-sepia":[{"backdrop-sepia":["",R,G,U]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,U]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[R,"initial",G,U]}],ease:[{ease:["linear","initial",v,G,U]}],delay:[{delay:[R,G,U]}],animate:[{animate:["none",y,G,U]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,G,U]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ef()}],"rotate-x":[{"rotate-x":ef()}],"rotate-y":[{"rotate-y":ef()}],"rotate-z":[{"rotate-z":ef()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[G,U,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:en()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:en()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,U]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,U]}],fill:[{fill:["none",...en()]}],"stroke-w":[{stroke:[R,X,z,W]}],stroke:[{stroke:["none",...en()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,t,n)=>{n.d(t,{DX:()=>a,TL:()=>s});var r=n(2115),i=n(6101),o=n(5155);function s(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var s;let e,a,l=(s=n,(a=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(a=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),u=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?(0,i.t)(t,l):l),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...s}=e,a=r.Children.toArray(i),l=a.find(u);if(l){let e=l.props.children,i=a.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...s,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...s,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var a=s("Slot"),l=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9946:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(2115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),s=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:h,...f}=e;return(0,r.createElement)("svg",{ref:t,...u,width:i,height:i,stroke:n,strokeWidth:s?24*Number(o)/Number(i):o,className:a("lucide",c),...!d&&!l(f)&&{"aria-hidden":"true"},...f},[...h.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:l,...u}=n;return(0,r.createElement)(c,{ref:o,iconNode:t,className:a("lucide-".concat(i(s(e))),"lucide-".concat(e),l),...u})});return n.displayName=s(e),n}},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return v},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return a},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);