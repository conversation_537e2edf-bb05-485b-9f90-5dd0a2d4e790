(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[893],{2905:(e,s,t)=>{"use strict";t.d(s,{D:()=>d,ThemeProvider:()=>n});var a=t(5155),r=t(2115);let l=(0,r.createContext)(void 0),i=()=>{try{let e=localStorage.getItem("windowworks-theme");if(e&&["light","dark"].includes(e))return e;return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}catch(e){return"light"}};function n(e){let{children:s}=e,[t,n]=(0,r.useState)("light"),[d,c]=(0,r.useState)("#D97706"),[o,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{m(!0);let e=i();n(e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark");let s=localStorage.getItem("windowworks-accent-color");s&&c(s)},[]);let x=(0,r.useCallback)(e=>{n(e);try{localStorage.setItem("windowworks-theme",e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}catch(e){console.error("Failed to set theme in localStorage",e)}},[]),u=(0,r.useCallback)(e=>{c(e);try{localStorage.setItem("windowworks-accent-color",e);let s=document.documentElement,t="oklch(from ".concat(e," l c h)");s.style.setProperty("--primary",t),s.style.setProperty("--color-brand-primary",e)}catch(e){console.error("Failed to set accent color in localStorage",e)}},[]);return(0,a.jsx)(l.Provider,{value:{theme:t,setTheme:x,accentColor:d,setAccentColor:u,mounted:o},children:s})}function d(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},4165:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>o,L3:()=>x,c7:()=>m,lG:()=>n});var a=t(5155);t(2115);var r=t(5452),l=t(4416),i=t(9434);function n(e){let{...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"dialog",...s})}function d(e){let{...s}=e;return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...s})}function c(e){let{className:s,...t}=e;return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...t})}function o(e){let{className:s,children:t,showCloseButton:n=!0,...o}=e;return(0,a.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,a.jsx)(c,{}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...o,children:[t,n&&(0,a.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(l.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",s),...t})}function x(e){let{className:s,...t}=e;return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",s),...t})}},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>i});var a=t(5155);t(2115);var r=t(968),l=t(9434);function i(e){let{className:s,...t}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}},6521:(e,s,t)=>{Promise.resolve().then(t.bind(t,871)),Promise.resolve().then(t.bind(t,9541))},7262:(e,s,t)=>{"use strict";t.d(s,{S:()=>d});var a=t(5155),r=t(2115),l=t(6981),i=t(5196),n=t(9434);let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.bL,{ref:s,className:(0,n.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...r,children:(0,a.jsx)(l.C1,{className:(0,n.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})})});d.displayName=l.bL.displayName},7313:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>c,j7:()=>n,tU:()=>i});var a=t(5155);t(2115);var r=t(704),l=t(9434);function i(e){let{className:s,...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",s),...t})}},8539:(e,s,t)=>{"use strict";t.d(s,{T:()=>i});var a=t(5155),r=t(2115),l=t(9434);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...r})});i.displayName="Textarea"},9409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>x,eb:()=>j,gC:()=>p,l6:()=>o,yv:()=>m});var a=t(5155),r=t(2115),l=t(8715),i=t(6474),n=t(7863),d=t(5196),c=t(9434);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef((e,s)=>{let{className:t,children:r,...n}=e;return(0,a.jsxs)(l.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...n,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.l9.displayName;let u=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});u.displayName=l.PP.displayName;let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,position:i="popper",...n}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,a.jsx)(u,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=l.JU.displayName;let j=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});j.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=l.wv.displayName},9541:(e,s,t)=>{"use strict";t.d(s,{default:()=>es});var a=t(5155),r=t(2115),l=t(6408),i=t(6874),n=t.n(i),d=t(2650),c=t(8833),o=t(5339),m=t(6561),x=t(646),u=t(1007),h=t(4186),p=t(4616),j=t(7924),g=t(5968),f=t(4653),N=t(1788),v=t(9074),b=t(5623),y=t(2657),w=t(3717),C=t(2318),A=t(2525),k=t(2138),D=t(285),S=t(2523),T=t(6695),P=t(6126),I=t(1394),L=t(4838),E=t(9409),R=t(7262),_=t(760),z=t(7434),W=t(7340),q=t(381),V=t(5868),B=t(1154),J=t(4229),M=t(5057),Z=t(8539),$=t(7313),F=t(4165),U=t(2905);let O=[{value:"low",label:"Low",color:"bg-gray-100 text-gray-700"},{value:"medium",label:"Medium",color:"bg-blue-100 text-blue-700"},{value:"high",label:"High",color:"bg-orange-100 text-orange-700"},{value:"urgent",label:"Urgent",color:"bg-red-100 text-red-700"}],K=[{value:"blinds",label:"Blinds"},{value:"shutters",label:"Shutters"},{value:"shades",label:"Shades"},{value:"curtains",label:"Curtains"},{value:"drapes",label:"Drapes"}],X=["Wood","Vinyl","Aluminum","Fabric","Bamboo","Faux Wood","Composite"],H=["Motorized","Blackout","Light Filtering","UV Protection","Energy Efficient","Cordless","Top Down Bottom Up"],G=[{name:"Living Room",windows:2},{name:"Bedroom",windows:1},{name:"Kitchen",windows:1},{name:"Dining Room",windows:2},{name:"Office",windows:1},{name:"Bathroom",windows:1}];function Q(e){let{isOpen:s,onClose:t,onSave:i,customers:n,installers:d}=e,{accentColor:c}=(0,U.D)(),[m,x]=(0,r.useState)("overview"),[j,g]=(0,r.useState)(!1),[f,N]=(0,r.useState)({}),[b,y]=(0,r.useState)({customerId:"",title:"",description:"",priority:"medium",estimatedCost:0,estimatedDuration:5,startDate:"",installerId:"",rooms:[],notes:""}),w=()=>"temp_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),C=(0,r.useCallback)(e=>{if(!e.width||!e.height||!e.treatmentType)return 0;let s=Number(e.width)*Number(e.height)*({blinds:15,shutters:35,shades:25,curtains:20,drapes:30})[e.treatmentType];return s*=({Wood:1.3,Vinyl:.8,Aluminum:.9,Fabric:1.1,Bamboo:1.2,"Faux Wood":1,Composite:1.1})[e.material]||1,Math.round(s+=50*e.features.length)},[]),k=(0,r.useCallback)(()=>{let e=b.rooms.reduce((e,s)=>e+s.windows.reduce((e,s)=>e+C(s),0),0);y(s=>({...s,estimatedCost:e}))},[b.rooms,C]),L=(0,r.useCallback)((e,s)=>{y(t=>{let a=e.split("."),r={...t},l=r;for(let e=0;e<a.length-1;e++)void 0===l[a[e]]&&(l[a[e]]={}),l=l[a[e]];return l[a[a.length-1]]=s,r}),N(s=>{if(s[e]){let t={...s};return delete t[e],t}return s})},[]),Q=e=>{let s={id:w(),name:e||"Room ".concat(b.rooms.length+1),windows:[]};if(e){let t=G.find(s=>s.name===e);if(t)for(let e=0;e<t.windows;e++)s.windows.push({id:w(),width:"",height:"",treatmentType:"",color:"White",material:"Vinyl",installationType:"inside-mount",features:[],cost:0})}y(e=>({...e,rooms:[...e.rooms,s]}))},Y=e=>{y(s=>({...s,rooms:s.rooms.filter(s=>s.id!==e)}))},ee=e=>{let s={id:w(),width:"",height:"",treatmentType:"",color:"White",material:"Vinyl",installationType:"inside-mount",features:[],cost:0};y(t=>({...t,rooms:t.rooms.map(t=>t.id===e?{...t,windows:[...t.windows,s]}:t)}))},es=(e,s)=>{y(t=>({...t,rooms:t.rooms.map(t=>t.id===e?{...t,windows:t.windows.filter(e=>e.id!==s)}:t)}))},et=(e,s,t,a)=>{y(r=>({...r,rooms:r.rooms.map(r=>r.id===e?{...r,windows:r.windows.map(e=>e.id===s?{...e,[t]:a}:e)}:r)}))},ea=(0,r.useCallback)(()=>{let e={};return b.title.trim()||(e.title="Project title is required"),b.customerId||(e.customerId="Customer selection is required"),b.description.trim()||(e.description="Project description is required"),0===b.rooms.length&&(e.rooms="At least one room is required"),b.rooms.forEach((s,t)=>{s.name.trim()||(e["rooms.".concat(t,".name")]="Room name is required"),0===s.windows.length&&(e["rooms.".concat(t,".windows")]="At least one window is required per room"),s.windows.forEach((s,a)=>{(!s.width||s.width<=0)&&(e["rooms.".concat(t,".windows.").concat(a,".width")]="Valid width is required"),(!s.height||s.height<=0)&&(e["rooms.".concat(t,".windows.").concat(a,".height")]="Valid height is required"),s.treatmentType||(e["rooms.".concat(t,".windows.").concat(a,".treatmentType")]="Treatment type is required")})}),N(e),0===Object.keys(e).length},[b]),er=async()=>{if(!ea())return void(f.title||f.customerId||f.description?x("overview"):Object.keys(f).some(e=>e.startsWith("rooms"))&&x("rooms"));g(!0);try{let e=b.rooms.map(e=>({name:e.name,windows:e.windows.filter(e=>""!==e.treatmentType).map(e=>({id:e.id,width:Number(e.width),height:Number(e.height),treatmentType:e.treatmentType,color:e.color,material:e.material,installationType:e.installationType,features:e.features,cost:C(e)}))})),s={customerId:b.customerId,title:b.title,description:b.description,priority:b.priority,estimatedCost:b.estimatedCost,estimatedDuration:b.estimatedDuration,startDate:b.startDate||void 0,installerId:b.installerId||void 0,rooms:e,notes:b.notes||void 0};await i(s),t(),y({customerId:"",title:"",description:"",priority:"medium",estimatedCost:0,estimatedDuration:5,startDate:"",installerId:"",rooms:[],notes:""}),x("overview")}catch(e){console.error("Error saving project:",e)}finally{g(!1)}};return r.useEffect(()=>{k()},[k]),(0,a.jsx)(F.lG,{open:s,onOpenChange:t,children:(0,a.jsx)(F.Cf,{className:"max-w-6xl sm:max-w-6xl w-[95vw] max-h-[70vh] overflow-hidden p-0",children:(0,a.jsxs)(l.P.div,{className:"flex flex-col h-full",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.2},children:[(0,a.jsx)(F.c7,{className:"px-6 py-4 border-b border-gray-200 flex-shrink-0",children:(0,a.jsx)(F.L3,{className:"text-xl font-semibold text-slate-700",children:"Create New Project"})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,a.jsxs)($.tU,{value:m,onValueChange:x,className:"space-y-6",children:[(0,a.jsxs)($.j7,{className:"grid w-full grid-cols-4",children:[(0,a.jsxs)($.Xi,{value:"overview",className:"flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-4 w-4"}),"Overview"]}),(0,a.jsxs)($.Xi,{value:"rooms",className:"flex items-center gap-2",children:[(0,a.jsx)(W.A,{className:"h-4 w-4"}),"Rooms"]}),(0,a.jsxs)($.Xi,{value:"workflow",className:"flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"h-4 w-4"}),"Workflow"]}),(0,a.jsxs)($.Xi,{value:"notes",className:"flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-4 w-4"}),"Notes"]})]}),(0,a.jsx)($.av,{value:"overview",className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(T.Zp,{children:[(0,a.jsx)(T.aR,{children:(0,a.jsxs)(T.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),"Basic Information"]})}),(0,a.jsxs)(T.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{htmlFor:"title",className:"text-slate-600",children:"Project Title *"}),(0,a.jsx)(S.p,{id:"title",value:b.title,onChange:e=>L("title",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(f.title?"border-red-500":""),placeholder:"Enter project title"}),f.title&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"h-3 w-3"}),f.title]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{htmlFor:"description",className:"text-slate-600",children:"Description *"}),(0,a.jsx)(Z.T,{id:"description",value:b.description,onChange:e=>L("description",e.target.value),className:"min-h-[100px] border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(f.description?"border-red-500":""),placeholder:"Describe the project details..."}),f.description&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"h-3 w-3"}),f.description]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{className:"text-slate-600",children:"Priority"}),(0,a.jsx)("div",{className:"flex gap-2",children:O.map(e=>(0,a.jsx)(P.E,{variant:b.priority===e.value?"default":"outline",className:"cursor-pointer transition-colors ".concat(b.priority===e.value?e.color:"border-gray-300 text-gray-600 hover:bg-gray-50"),onClick:()=>L("priority",e.value),children:e.label},e.value))})]})]})]}),(0,a.jsxs)(T.Zp,{children:[(0,a.jsx)(T.aR,{children:(0,a.jsxs)(T.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),"Customer & Assignment"]})}),(0,a.jsxs)(T.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{className:"text-slate-600",children:"Customer *"}),(0,a.jsxs)(E.l6,{value:b.customerId,onValueChange:e=>L("customerId",e),children:[(0,a.jsx)(E.bq,{className:"border-gray-200 ".concat(f.customerId?"border-red-500":""),children:(0,a.jsx)(E.yv,{placeholder:"Select customer"})}),(0,a.jsx)(E.gC,{children:n.map(e=>(0,a.jsx)(E.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(I.eu,{className:"h-6 w-6",children:(0,a.jsxs)(I.q5,{className:"text-xs",children:[e.firstName[0],e.lastName[0]]})}),e.firstName," ",e.lastName]})},e.id))})]}),f.customerId&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"h-3 w-3"}),f.customerId]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{className:"text-slate-600",children:"Installer"}),(0,a.jsxs)(E.l6,{value:b.installerId,onValueChange:e=>L("installerId",e),children:[(0,a.jsx)(E.bq,{className:"border-gray-200",children:(0,a.jsx)(E.yv,{placeholder:"Select installer (optional)"})}),(0,a.jsx)(E.gC,{children:d.map(e=>{var s;return(0,a.jsx)(E.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(I.eu,{className:"h-6 w-6",children:[(0,a.jsx)(I.BK,{src:null==(s=e.profile)?void 0:s.avatar}),(0,a.jsxs)(I.q5,{className:"text-xs",children:[e.profile.firstName[0],e.profile.lastName[0]]})]}),e.profile.firstName," ",e.profile.lastName]})},e.id)})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(M.J,{htmlFor:"estimatedDuration",className:"text-slate-600 flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Duration (days)"]}),(0,a.jsx)(S.p,{id:"estimatedDuration",type:"number",min:"1",value:b.estimatedDuration,onChange:e=>L("estimatedDuration",Number(e.target.value)),className:"border-gray-200 focus:border-[var(--color-brand-primary)]"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(M.J,{htmlFor:"startDate",className:"text-slate-600 flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),"Start Date"]}),(0,a.jsx)(S.p,{id:"startDate",type:"date",value:b.startDate,onChange:e=>L("startDate",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)]"})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-amber-50 rounded-lg border border-amber-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-amber-800",children:[(0,a.jsx)(V.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"font-medium",children:"Estimated Cost"})]}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-amber-900 mt-1",children:["$",b.estimatedCost.toLocaleString()]}),(0,a.jsx)("p",{className:"text-sm text-amber-700 mt-1",children:"Auto-calculated from room specifications"})]})]})]})]})}),(0,a.jsxs)($.av,{value:"rooms",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-700",children:"Room Specifications"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(E.l6,{onValueChange:Q,children:[(0,a.jsx)(E.bq,{className:"w-48 border-gray-200",children:(0,a.jsx)(E.yv,{placeholder:"Add room template"})}),(0,a.jsx)(E.gC,{children:G.map(e=>(0,a.jsxs)(E.eb,{value:e.name,children:[e.name," (",e.windows," window",1!==e.windows?"s":"",")"]},e.name))})]}),(0,a.jsxs)(D.$,{onClick:()=>Q(),className:"text-white",style:{backgroundColor:c,borderColor:c},children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Add Room"]})]})]}),f.rooms&&(0,a.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),f.rooms]})}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)(_.N,{children:b.rooms.map((e,s)=>(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.2},children:(0,a.jsxs)(T.Zp,{children:[(0,a.jsxs)(T.aR,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(W.A,{className:"h-5 w-5 text-slate-600"}),(0,a.jsx)(S.p,{value:e.name,onChange:e=>L("rooms.".concat(s,".name"),e.target.value),className:"font-medium border-none focus:border-[var(--color-brand-primary)] bg-transparent p-0 text-lg ".concat(f["rooms.".concat(s,".name")]?"border-red-500":""),placeholder:"Room name"})]}),(0,a.jsx)(D.$,{variant:"ghost",size:"sm",onClick:()=>Y(e.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,a.jsx)(A.A,{className:"h-4 w-4"})})]}),f["rooms.".concat(s,".name")]&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"h-3 w-3"}),f["rooms.".concat(s,".name")]]})]}),(0,a.jsx)(T.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-slate-600",children:"Windows"}),(0,a.jsxs)(D.$,{variant:"outline",size:"sm",onClick:()=>ee(e.id),className:"border-gray-200",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Add Window"]})]}),f["rooms.".concat(s,".windows")]&&(0,a.jsx)("div",{className:"p-2 bg-red-50 border border-red-200 rounded",children:(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"h-3 w-3"}),f["rooms.".concat(s,".windows")]]})}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsx)(_.N,{children:e.windows.map((t,r)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},className:"p-4 border border-gray-200 rounded-lg space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"font-medium text-slate-700",children:["Window ",r+1]}),(0,a.jsx)(D.$,{variant:"ghost",size:"sm",onClick:()=>es(e.id,t.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,a.jsx)(A.A,{className:"h-3 w-3"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(M.J,{className:"text-xs text-slate-600",children:"Width (inches) *"}),(0,a.jsx)(S.p,{type:"number",min:"1",step:"0.25",value:t.width,onChange:s=>et(e.id,t.id,"width",Number(s.target.value)),className:"text-sm border-gray-200 ".concat(f["rooms.".concat(s,".windows.").concat(r,".width")]?"border-red-500":""),placeholder:"Width"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(M.J,{className:"text-xs text-slate-600",children:"Height (inches) *"}),(0,a.jsx)(S.p,{type:"number",min:"1",step:"0.25",value:t.height,onChange:s=>et(e.id,t.id,"height",Number(s.target.value)),className:"text-sm border-gray-200 ".concat(f["rooms.".concat(s,".windows.").concat(r,".height")]?"border-red-500":""),placeholder:"Height"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(M.J,{className:"text-xs text-slate-600",children:"Treatment Type *"}),(0,a.jsxs)(E.l6,{value:t.treatmentType,onValueChange:s=>et(e.id,t.id,"treatmentType",s),children:[(0,a.jsx)(E.bq,{className:"text-sm border-gray-200 ".concat(f["rooms.".concat(s,".windows.").concat(r,".treatmentType")]?"border-red-500":""),children:(0,a.jsx)(E.yv,{placeholder:"Type"})}),(0,a.jsx)(E.gC,{children:K.map(e=>(0,a.jsx)(E.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(M.J,{className:"text-xs text-slate-600",children:"Color"}),(0,a.jsx)(S.p,{value:t.color,onChange:s=>et(e.id,t.id,"color",s.target.value),className:"text-sm border-gray-200",placeholder:"Color"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(M.J,{className:"text-xs text-slate-600",children:"Material"}),(0,a.jsxs)(E.l6,{value:t.material,onValueChange:s=>et(e.id,t.id,"material",s),children:[(0,a.jsx)(E.bq,{className:"text-sm border-gray-200",children:(0,a.jsx)(E.yv,{})}),(0,a.jsx)(E.gC,{children:X.map(e=>(0,a.jsx)(E.eb,{value:e,children:e},e))})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(M.J,{className:"text-xs text-slate-600",children:"Installation"}),(0,a.jsxs)(E.l6,{value:t.installationType,onValueChange:s=>et(e.id,t.id,"installationType",s),children:[(0,a.jsx)(E.bq,{className:"text-sm border-gray-200",children:(0,a.jsx)(E.yv,{})}),(0,a.jsxs)(E.gC,{children:[(0,a.jsx)(E.eb,{value:"inside-mount",children:"Inside Mount"}),(0,a.jsx)(E.eb,{value:"outside-mount",children:"Outside Mount"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(M.J,{className:"text-xs text-slate-600",children:"Estimated Cost"}),(0,a.jsxs)("div",{className:"text-sm font-medium text-green-600 bg-green-50 px-3 py-2 rounded border",children:["$",C(t).toLocaleString()]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{className:"text-xs text-slate-600",children:"Features"}),(0,a.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-2",children:H.map(s=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(R.S,{id:"".concat(t.id,"-").concat(s),checked:t.features.includes(s),onCheckedChange:a=>{let r=a?[...t.features,s]:t.features.filter(e=>e!==s);et(e.id,t.id,"features",r)},className:"border-gray-300"}),(0,a.jsx)(M.J,{htmlFor:"".concat(t.id,"-").concat(s),className:"text-xs text-slate-600",children:s})]},s))})]})]},t.id))})})]})})]})},e.id))})}),0===b.rooms.length&&(0,a.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,a.jsx)(W.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,a.jsx)("p",{className:"text-lg font-medium mb-2",children:"No rooms added yet"}),(0,a.jsx)("p",{className:"text-sm",children:"Add rooms to start defining your project specifications"})]})]}),(0,a.jsx)($.av,{value:"workflow",className:"space-y-6",children:(0,a.jsxs)(T.Zp,{children:[(0,a.jsx)(T.aR,{children:(0,a.jsxs)(T.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"h-5 w-5"}),"Project Workflow"]})}),(0,a.jsx)(T.Wu,{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.J,{className:"text-slate-600",children:"Assigned Installer"}),(0,a.jsxs)(E.l6,{value:b.installerId,onValueChange:e=>L("installerId",e),children:[(0,a.jsx)(E.bq,{className:"border-gray-200",children:(0,a.jsx)(E.yv,{placeholder:"Select installer"})}),(0,a.jsx)(E.gC,{children:d.map(e=>{var s;return(0,a.jsx)(E.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(I.eu,{className:"h-8 w-8",children:[(0,a.jsx)(I.BK,{src:null==(s=e.profile)?void 0:s.avatar}),(0,a.jsxs)(I.q5,{children:[e.profile.firstName[0],e.profile.lastName[0]]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.profile.firstName," ",e.profile.lastName]}),(0,a.jsx)("div",{className:"text-sm text-slate-500",children:e.email})]})]})},e.id)})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(M.J,{htmlFor:"startDate",className:"text-slate-600 flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),"Planned Start Date"]}),(0,a.jsx)(S.p,{id:"startDate",type:"date",value:b.startDate,onChange:e=>L("startDate",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)]"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(M.J,{htmlFor:"estimatedDuration",className:"text-slate-600 flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Estimated Duration (days)"]}),(0,a.jsx)(S.p,{id:"estimatedDuration",type:"number",min:"1",value:b.estimatedDuration,onChange:e=>L("estimatedDuration",Number(e.target.value)),className:"border-gray-200 focus:border-[var(--color-brand-primary)]"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"Project Timeline"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-blue-700",children:"Start Date:"}),(0,a.jsx)("span",{className:"text-blue-900",children:b.startDate||"Not set"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-blue-700",children:"Duration:"}),(0,a.jsxs)("span",{className:"text-blue-900",children:[b.estimatedDuration," day",1!==b.estimatedDuration?"s":""]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-blue-700",children:"Estimated Completion:"}),(0,a.jsx)("span",{className:"text-blue-900",children:b.startDate?new Date(new Date(b.startDate).getTime()+24*b.estimatedDuration*36e5).toLocaleDateString():"Not calculated"})]})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800 mb-2",children:"Cost Summary"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-green-700",children:"Total Rooms:"}),(0,a.jsx)("span",{className:"text-green-900",children:b.rooms.length})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-green-700",children:"Total Windows:"}),(0,a.jsx)("span",{className:"text-green-900",children:b.rooms.reduce((e,s)=>e+s.windows.length,0)})]}),(0,a.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,a.jsx)("span",{className:"text-green-700",children:"Estimated Cost:"}),(0,a.jsxs)("span",{className:"text-green-900 text-lg",children:["$",b.estimatedCost.toLocaleString()]})]})]})]})]})]})})]})}),(0,a.jsx)($.av,{value:"notes",className:"space-y-6",children:(0,a.jsxs)(T.Zp,{children:[(0,a.jsx)(T.aR,{children:(0,a.jsxs)(T.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),"Project Notes"]})}),(0,a.jsx)(T.Wu,{children:(0,a.jsx)(Z.T,{value:b.notes,onChange:e=>L("notes",e.target.value),className:"min-h-[300px] border-gray-200 focus:border-[var(--color-brand-primary)]",placeholder:"Add any additional notes, special requirements, customer preferences, installation considerations, or other relevant information for this project..."})})]})})]})}),(0,a.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 flex-shrink-0",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(D.$,{variant:"outline",onClick:t,disabled:j,className:"border-gray-200",children:"Cancel"}),(0,a.jsx)(D.$,{onClick:er,disabled:j,className:"text-white min-w-[140px]",style:{backgroundColor:c,borderColor:c},children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(B.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Create Project"]})})]})})]})})})}let Y=[{id:"proj_001",name:"Living Room Shutters Installation",customer:{id:"cust_001",name:"Sarah Johnson",email:"<EMAIL>",phone:"(*************"},status:"In-Progress",installer:{id:"inst_001",name:"Mike Rodriguez",avatar:null},dueDate:"2025-07-15",productType:["Shutters"],description:"Custom wood shutters for living room bay windows",address:"123 Maple Street, Springfield, IL 62704",totalValue:2840,createdAt:"2025-07-01",completionTime:3},{id:"proj_002",name:"Office Blinds Setup",customer:{id:"cust_002",name:"Robert Chen",email:"<EMAIL>",phone:"(*************"},status:"Assigned",installer:{id:"inst_002",name:"Lisa Anderson",avatar:null},dueDate:"2025-07-18",productType:["Blinds"],description:"Motorized blinds for home office windows",address:"456 Oak Avenue, Springfield, IL 62701",totalValue:1560,createdAt:"2025-07-03"},{id:"proj_003",name:"Bedroom Shades Installation",customer:{id:"cust_003",name:"Emma Davis",email:"<EMAIL>",phone:"(*************"},status:"Pending",installer:null,dueDate:"2025-07-12",productType:["Shades"],description:"Blackout shades for master bedroom",address:"789 Pine Road, Springfield, IL 62702",totalValue:890,createdAt:"2025-07-08"},{id:"proj_004",name:"Kitchen Window Treatments",customer:{id:"cust_004",name:"Michael Williams",email:"<EMAIL>",phone:"(*************"},status:"Completed",installer:{id:"inst_001",name:"Mike Rodriguez",avatar:null},dueDate:"2025-07-10",productType:["Blinds","Shades"],description:"Water-resistant blinds and caf\xe9 shades",address:"321 Elm Street, Springfield, IL 62703",totalValue:1250,createdAt:"2025-06-28",completionTime:4},{id:"proj_005",name:"Sunroom Shutters",customer:{id:"cust_005",name:"David Thompson",email:"<EMAIL>",phone:"(*************"},status:"In-Progress",installer:{id:"inst_003",name:"Carlos Martinez",avatar:null},dueDate:"2025-07-20",productType:["Shutters"],description:"Full-height shutters for sunroom",address:"654 Cedar Lane, Springfield, IL 62705",totalValue:3200,createdAt:"2025-07-05",completionTime:6}],ee=[{title:"Total Projects",value:"156",change:"-1%",icon:d.A,trend:"down"},{title:"Active Projects",value:"24",change:"+5%",icon:c.A,trend:"up"},{title:"Overdue",value:"3",change:"",icon:o.A,trend:"alert"},{title:"Avg Completion Time",value:"5 days",change:"+2%",icon:m.A,trend:"up"}];function es(){let[e,s]=(0,r.useState)(""),[t,i]=(0,r.useState)("all"),[d,m]=(0,r.useState)("all"),[_,z]=(0,r.useState)("dueDate"),[W,q]=(0,r.useState)([]),[V,B]=(0,r.useState)("table"),[J,M]=(0,r.useState)(1),[Z,$]=(0,r.useState)(!1),F=async e=>{try{console.log("Creating new project:",e),$(!1)}catch(e){throw console.error("Error creating project:",e),e}},U=Y.filter(s=>{let a=s.name.toLowerCase().includes(e.toLowerCase())||s.customer.name.toLowerCase().includes(e.toLowerCase()),r="all"===t||s.status.toLowerCase()===t.toLowerCase(),l="all"===d||s.productType.some(e=>e.toLowerCase()===d.toLowerCase());return a&&r&&l}).sort((e,s)=>{switch(_){case"dueDate":return new Date(e.dueDate).getTime()-new Date(s.dueDate).getTime();case"name":return e.name.localeCompare(s.name);case"customer":return e.customer.name.localeCompare(s.customer.name);default:return 0}}),O=Math.ceil(U.length/10),K=U.slice((J-1)*10,10*J),X=e=>{q(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},H=e=>{switch(e.toLowerCase()){case"completed":return"default";case"in-progress":return"secondary";case"assigned":default:return"outline";case"pending":return"destructive"}},G=e=>{switch(e.toLowerCase()){case"completed":return x.A;case"in-progress":return c.A;case"assigned":return u.A;default:return h.A}},es=e=>new Date(e)<new Date&&!Y.find(s=>s.dueDate===e&&"Completed"===s.status),et={Pending:U.filter(e=>"Pending"===e.status),Assigned:U.filter(e=>"Assigned"===e.status),"In-Progress":U.filter(e=>"In-Progress"===e.status),Completed:U.filter(e=>"Completed"===e.status)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Projects"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track installations, workflows, and statuses"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(P.E,{variant:"secondary",className:"text-xs",children:"Saturday, July 12, 2025"}),(0,a.jsxs)(D.$,{className:"bg-primary hover:bg-primary/90",onClick:()=>$(!0),children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"New Project"]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:ee.map((e,s)=>(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:(0,a.jsxs)(T.Zp,{className:"hover:border-border/80 transition-colors",children:[(0,a.jsx)(T.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"p-2 rounded-md ".concat("alert"===e.trend?"bg-destructive/10":"bg-primary/10"),children:(0,a.jsx)(e.icon,{className:"h-5 w-5 ".concat("alert"===e.trend?"text-destructive":"text-primary")})}),e.change&&(0,a.jsx)(P.E,{variant:"up"===e.trend?"default":"down"===e.trend||"alert"===e.trend?"destructive":"secondary",className:"text-xs",children:e.change})]})}),(0,a.jsx)(T.Wu,{children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-foreground",children:e.value}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title})]})})]})},e.title))}),(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,a.jsx)(T.Zp,{children:(0,a.jsx)(T.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-1 items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,a.jsx)(S.p,{placeholder:"Search projects, customers...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(E.l6,{value:t,onValueChange:i,children:[(0,a.jsx)(E.bq,{className:"w-32",children:(0,a.jsx)(E.yv,{placeholder:"Status"})}),(0,a.jsxs)(E.gC,{children:[(0,a.jsx)(E.eb,{value:"all",children:"All Status"}),(0,a.jsx)(E.eb,{value:"pending",children:"Pending"}),(0,a.jsx)(E.eb,{value:"assigned",children:"Assigned"}),(0,a.jsx)(E.eb,{value:"in-progress",children:"In-Progress"}),(0,a.jsx)(E.eb,{value:"completed",children:"Completed"})]})]}),(0,a.jsxs)(E.l6,{value:d,onValueChange:m,children:[(0,a.jsx)(E.bq,{className:"w-36",children:(0,a.jsx)(E.yv,{placeholder:"Product"})}),(0,a.jsxs)(E.gC,{children:[(0,a.jsx)(E.eb,{value:"all",children:"All Products"}),(0,a.jsx)(E.eb,{value:"blinds",children:"Blinds"}),(0,a.jsx)(E.eb,{value:"shutters",children:"Shutters"}),(0,a.jsx)(E.eb,{value:"shades",children:"Shades"})]})]}),(0,a.jsxs)(E.l6,{value:_,onValueChange:z,children:[(0,a.jsx)(E.bq,{className:"w-32",children:(0,a.jsx)(E.yv,{placeholder:"Sort by"})}),(0,a.jsxs)(E.gC,{children:[(0,a.jsx)(E.eb,{value:"dueDate",children:"Due Date"}),(0,a.jsx)(E.eb,{value:"name",children:"Name"}),(0,a.jsx)(E.eb,{value:"customer",children:"Customer"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex items-center bg-accent rounded-md p-1",children:[(0,a.jsx)(D.$,{variant:"table"===V?"default":"ghost",size:"sm",onClick:()=>B("table"),className:"h-8 px-3",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})}),(0,a.jsx)(D.$,{variant:"kanban"===V?"default":"ghost",size:"sm",onClick:()=>B("kanban"),className:"h-8 px-3",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)(D.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Export"]}),W.length>0&&(0,a.jsxs)(P.E,{variant:"secondary",children:[W.length," selected"]})]})]})})})}),"table"===V?(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,a.jsx)(T.Zp,{children:(0,a.jsxs)(T.Wu,{className:"p-0",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"border-b border-border",children:(0,a.jsxs)("tr",{className:"bg-accent/20",children:[(0,a.jsx)("th",{className:"text-left p-4 w-12",children:(0,a.jsx)(R.S,{checked:W.length===K.length&&K.length>0,onCheckedChange:()=>{W.length===K.length?q([]):q(K.map(e=>e.id))}})}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Project"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Customer"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Status"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Installer"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Due Date"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Value"}),(0,a.jsx)("th",{className:"text-right p-4 w-16"})]})}),(0,a.jsx)("tbody",{children:K.map((e,s)=>{let t=G(e.status),r=es(e.dueDate);return(0,a.jsxs)(l.P.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},className:"border-b border-border hover:bg-accent/30 transition-colors cursor-pointer",children:[(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)(R.S,{checked:W.includes(e.id),onCheckedChange:()=>X(e.id),onClick:e=>e.stopPropagation()})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:"/projects/".concat(e.id),className:"font-medium text-foreground hover:text-primary transition-colors",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.productType.join(", ")})]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:"/customers/".concat(e.customer.id),className:"font-medium text-foreground hover:text-primary transition-colors",children:e.customer.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.customer.phone})]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)(P.E,{variant:H(e.status),className:"text-xs",children:[(0,a.jsx)(t,{className:"h-3 w-3 mr-1"}),e.status]})}),(0,a.jsx)("td",{className:"p-4",children:e.installer?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(I.eu,{className:"h-6 w-6",children:[(0,a.jsx)(I.BK,{src:e.installer.avatar||void 0}),(0,a.jsx)(I.q5,{className:"bg-primary/10 text-primary text-xs",children:e.installer.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsx)("span",{className:"text-sm text-foreground",children:e.installer.name})]}):(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Unassigned"})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center text-sm ".concat(r?"text-destructive":"text-muted-foreground"),children:[(0,a.jsx)(v.A,{className:"h-3 w-3 mr-2"}),new Date(e.dueDate).toLocaleDateString(),r&&(0,a.jsx)(o.A,{className:"h-3 w-3 ml-1 text-destructive"})]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("span",{className:"font-medium text-foreground",children:["$",e.totalValue.toLocaleString()]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)(L.rI,{children:[(0,a.jsx)(L.ty,{asChild:!0,onClick:e=>e.stopPropagation(),children:(0,a.jsx)(D.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(L.SQ,{align:"end",children:[(0,a.jsxs)(L._2,{children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),(0,a.jsxs)(L._2,{children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Edit Project"]}),(0,a.jsxs)(L._2,{children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Assign Installer"]}),(0,a.jsx)(L.mB,{}),(0,a.jsxs)(L._2,{className:"text-destructive",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})})]},e.id)})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-border",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",(J-1)*10+1," to ",Math.min(10*J,U.length)," of ",U.length," projects"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(D.$,{variant:"outline",size:"sm",onClick:()=>M(e=>Math.max(1,e-1)),disabled:1===J,children:"Previous"}),(0,a.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,O)},(e,s)=>{let t=s+1;return(0,a.jsx)(D.$,{variant:J===t?"default":"outline",size:"sm",onClick:()=>M(t),className:"w-8 h-8 p-0",children:t},t)})}),(0,a.jsx)(D.$,{variant:"outline",size:"sm",onClick:()=>M(e=>Math.min(O,e+1)),disabled:J===O,children:"Next"})]})]})]})})}):(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Object.entries(et).map(e=>{let[s,t]=e;return(0,a.jsxs)(T.Zp,{className:"h-fit",children:[(0,a.jsx)(T.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"font-semibold text-foreground",children:s}),(0,a.jsx)(P.E,{variant:"secondary",className:"text-xs",children:t.length})]})}),(0,a.jsx)(T.Wu,{className:"space-y-3",children:t.map(e=>{let s=G(e.status),t=es(e.dueDate);return(0,a.jsx)(l.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},whileHover:{scale:1.02},className:"p-3 bg-background border border-border rounded-md cursor-pointer hover:border-primary/50 transition-colors",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-foreground leading-tight",children:e.name}),(0,a.jsx)(P.E,{variant:H(e.status),className:"text-xs",children:(0,a.jsx)(s,{className:"h-3 w-3"})})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.customer.name}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center ".concat(t?"text-destructive":"text-muted-foreground"),children:[(0,a.jsx)(v.A,{className:"h-3 w-3 mr-1"}),new Date(e.dueDate).toLocaleDateString()]}),(0,a.jsxs)("span",{className:"font-medium text-foreground",children:["$",e.totalValue.toLocaleString()]})]}),e.installer&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(I.eu,{className:"h-5 w-5",children:[(0,a.jsx)(I.BK,{src:e.installer.avatar||void 0}),(0,a.jsx)(I.q5,{className:"bg-primary/10 text-primary text-xs",children:e.installer.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.installer.name})]})]})},e.id)})})]},s)})}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(T.Zp,{className:"lg:col-span-2",children:[(0,a.jsx)(T.aR,{children:(0,a.jsx)(T.ZB,{children:"Upcoming Schedules"})}),(0,a.jsx)(T.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:Y.filter(e=>"Completed"!==e.status).sort((e,s)=>new Date(e.dueDate).getTime()-new Date(s.dueDate).getTime()).slice(0,5).map(e=>{let s=es(e.dueDate);return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-md bg-accent/20",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(s?"bg-destructive":"bg-primary")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-foreground",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.customer.name})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium ".concat(s?"text-destructive":"text-foreground"),children:new Date(e.dueDate).toLocaleDateString()}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.installer?e.installer.name:"Unassigned"})]})]},e.id)})})})]}),(0,a.jsxs)(T.Zp,{children:[(0,a.jsx)(T.aR,{children:(0,a.jsx)(T.ZB,{children:"AI Insights"})}),(0,a.jsx)(T.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-3 rounded-md bg-destructive/5 border border-destructive/20",children:[(0,a.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Assignment Alert"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"2 overdue projects need immediate installer assignment"}),(0,a.jsxs)(D.$,{size:"sm",variant:"outline",className:"mt-2 h-7 text-xs",children:["Assign Now ",(0,a.jsx)(k.A,{className:"h-3 w-3 ml-1"})]})]}),(0,a.jsxs)("div",{className:"p-3 rounded-md bg-primary/5 border border-primary/20",children:[(0,a.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Efficiency Tip"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Mike Rodriguez has 20% faster completion time. Consider more assignments."})]})]})})]})]}),(0,a.jsx)(Q,{isOpen:Z,onClose:()=>$(!1),onSave:F,customers:[{id:"cust_001",firstName:"Sarah",lastName:"Johnson",email:"<EMAIL>",phone:"(*************",address:{street:"123 Maple Street",city:"Springfield",state:"IL",zipCode:"62704",country:"United States"},preferences:{windowTreatmentTypes:["shutters","blinds"],preferredColors:["white","beige"],budget:"2500-5000",communication:"email"},createdAt:"2025-07-01T00:00:00Z",updatedAt:"2025-07-01T00:00:00Z",createdBy:"admin"}],installers:[{id:"inst_001",email:"<EMAIL>",role:"installer",profile:{firstName:"Mike",lastName:"Rodriguez",phone:"(*************"},createdAt:"2025-07-01T00:00:00Z",updatedAt:"2025-07-01T00:00:00Z"}]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[418,0,663,271,205,441,684,358],()=>s(6521)),_N_E=e.O()}]);