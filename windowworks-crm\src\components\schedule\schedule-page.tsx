'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Calendar as CalendarIcon, 
  Clock,
  Search,
  Plus,
  Download,
  Filter,
  MapPin,
  BarChart3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Timer
} from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { CalendarMonthView } from '@/components/schedule/calendar-month-view'
import { CalendarDayView } from '@/components/schedule/calendar-day-view'
import { CalendarWeekView } from '@/components/schedule/calendar-week-view'
import { NewAppointmentModal } from '@/components/schedule/new-appointment-modal'
import { useTheme } from '@/contexts/theme-context'

// Types
interface AppointmentData {
  id: string
  projectId: string
  customerId: string
  date: string
  startTime: string
  endTime: string
  duration: number
  installerId: string
  location: string
  productTypes: string[]
  notes: string
  estimatedDuration: number
  sendNotification: boolean
  autoReminder: boolean
  smsReminder: boolean
  emailReminder: boolean
}

// Mock data
const mockProjects = [
  {
    id: 'project-1',
    name: 'Kitchen Window Installation',
    customer: 'Sarah Johnson',
    address: '123 Oak Street, Portland, OR',
    productTypes: ['Windows', 'Blinds'],
    status: 'active'
  },
  {
    id: 'project-2',
    name: 'Bathroom Renovation',
    customer: 'Mike Chen',
    address: '456 Pine Avenue, Seattle, WA',
    productTypes: ['Shutters', 'Blinds'],
    status: 'active'
  },
  {
    id: 'project-3',
    name: 'Living Room Makeover',
    customer: 'Emily Davis',
    address: '789 Maple Drive, Vancouver, WA',
    productTypes: ['Curtains', 'Shades'],
    status: 'active'
  }
]

const mockInstallers = [
  {
    id: 'installer-1',
    name: 'Alex Thompson',
    avatar: null,
    availability: 'available' as const,
    utilization: 65
  },
  {
    id: 'installer-2',
    name: 'Jordan Martinez',
    avatar: null,
    availability: 'busy' as const,
    utilization: 85
  },
  {
    id: 'installer-3',
    name: 'Sam Wilson',
    avatar: null,
    availability: 'available' as const,
    utilization: 45
  }
]

const mockCustomers = [
  {
    id: 'customer-1',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Oak Street, Portland, OR'
  },
  {
    id: 'customer-2',
    name: 'Mike Chen',
    email: '<EMAIL>',
    phone: '(*************',
    address: '456 Pine Avenue, Seattle, WA'
  },
  {
    id: 'customer-3',
    name: 'Emily Davis',
    email: '<EMAIL>',
    phone: '(*************',
    address: '789 Maple Drive, Vancouver, WA'
  }
]

// Today's date for demo
const today = new Date('2025-07-12')

const mockAppointments = [
  {
    id: '1',
    title: 'Kitchen Window Installation',
    date: '2025-07-12',
    time: '09:00',
    status: 'assigned' as const,
    customer: 'Sarah Johnson',
    installer: 'Alex Thompson'
  },
  {
    id: '2',
    title: 'Bathroom Consultation',
    date: '2025-07-12',
    time: '14:00',
    status: 'pending' as const,
    customer: 'Mike Chen',
    installer: 'Jordan Martinez'
  },
  {
    id: '3',
    title: 'Living Room Curtains',
    date: '2025-07-13',
    time: '10:00',
    status: 'in-progress' as const,
    customer: 'Emily Davis',
    installer: 'Sam Wilson'
  },
  {
    id: '4',
    title: 'Window Maintenance',
    date: '2025-07-14',
    time: '15:00',
    status: 'completed' as const,
    customer: 'Sarah Johnson',
    installer: 'Alex Thompson'
  }
]

const mockDayViewEvents = [
  {
    id: '1',
    title: 'Kitchen Window Installation',
    startTime: '09:00',
    duration: 180,
    customer: 'Sarah Johnson',
    address: '123 Oak Street',
    installer: 'Alex Thompson',
    status: 'assigned' as const
  },
  {
    id: '2',
    title: 'Bathroom Consultation',
    startTime: '14:00',
    duration: 120,
    customer: 'Mike Chen',
    address: '456 Pine Avenue',
    installer: 'Jordan Martinez',
    status: 'pending' as const
  }
]

export default function SchedulePage() {
  const { accentColor } = useTheme()
  const [currentView, setCurrentView] = useState<'month' | 'week' | 'day'>('month')
  const [selectedDate, setSelectedDate] = useState(today)
  const [showNewAppointment, setShowNewAppointment] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
    setCurrentView('day')
  }

  const handleNewAppointment = (date?: Date) => {
    if (date) setSelectedDate(date)
    setShowNewAppointment(true)
  }

  const handleSaveAppointment = (appointment: AppointmentData) => {
    console.log('Saving appointment:', appointment)
    // Here you would typically save to your backend
    setShowNewAppointment(false)
  }

  const handleNavigate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate)
    if (currentView === 'month') {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1))
    } else if (currentView === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7))
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1))
    }
    setSelectedDate(newDate)
  }

  const todaysAppointments = mockAppointments.filter(apt => apt.date === today.toISOString().split('T')[0])
  const upcomingAppointments = mockAppointments.filter(apt => 
    new Date(apt.date) > today
  ).slice(0, 5)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-slate-700">Schedule</h1>
          <p className="text-slate-600 mt-1">
            Manage installer appointments and project timelines
          </p>
          <p className="text-sm font-medium mt-1" style={{ color: accentColor }}>
            {today.toLocaleDateString('en-US', { 
              weekday: 'long', 
              month: 'long', 
              day: 'numeric', 
              year: 'numeric' 
            })}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button 
            onClick={() => handleNewAppointment()}
            className="text-white"
            style={{ 
              backgroundColor: accentColor,
              borderColor: accentColor
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Appointment
          </Button>
        </div>
      </div>

      {/* Stats Row */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">Upcoming Appointments</CardTitle>
            <CalendarIcon className="h-4 w-4" style={{ color: accentColor }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-700">15</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              +10% from last week
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">Installer Utilization</CardTitle>
            <BarChart3 className="h-4 w-4" style={{ color: accentColor }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-700">75%</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              +5% from last month
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">Conflicts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-700">2</div>
            <p className="text-xs text-red-600 flex items-center mt-1">
              Requires attention
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">Avg Job Duration</CardTitle>
            <Timer className="h-4 w-4" style={{ color: accentColor }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-700">4h</div>
            <p className="text-xs text-slate-500 flex items-center mt-1">
              Per appointment
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Calendar Area */}
        <div className="xl:col-span-3 space-y-6">
          {/* View Controls */}
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Button
                    variant={currentView === 'month' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setCurrentView('month')}
                    className={currentView === 'month' ? 'text-white' : ''}
                    style={currentView === 'month' ? { 
                      backgroundColor: accentColor,
                      borderColor: accentColor
                    } : {}}
                  >
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    Month
                  </Button>
                  <Button
                    variant={currentView === 'week' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setCurrentView('week')}
                    className={currentView === 'week' ? 'text-white' : ''}
                    style={currentView === 'week' ? { 
                      backgroundColor: accentColor,
                      borderColor: accentColor
                    } : {}}
                  >
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    Week
                  </Button>
                  <Button
                    variant={currentView === 'day' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setCurrentView('day')}
                    className={currentView === 'day' ? 'text-white' : ''}
                    style={currentView === 'day' ? { 
                      backgroundColor: accentColor,
                      borderColor: accentColor
                    } : {}}
                  >
                    <Clock className="h-4 w-4 mr-2" />
                    Day
                  </Button>
                </div>

                <div className="text-center">
                  <h2 className="font-semibold text-slate-700">
                    {currentView === 'month' 
                      ? selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
                      : currentView === 'week'
                      ? (() => {
                          const weekStart = new Date(selectedDate)
                          const day = weekStart.getDay()
                          const diff = weekStart.getDate() - day
                          weekStart.setDate(diff)
                          const weekEnd = new Date(weekStart)
                          weekEnd.setDate(weekStart.getDate() + 6)
                          return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`
                        })()
                      : selectedDate.toLocaleDateString('en-US', { 
                          weekday: 'long', 
                          month: 'long', 
                          day: 'numeric',
                          year: 'numeric'
                        })
                    }
                  </h2>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedDate(today)}
                >
                  Today
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Calendar Views */}
          <motion.div
            key={currentView}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {currentView === 'month' ? (
              <CalendarMonthView
                selectedDate={selectedDate}
                events={mockAppointments}
                onDateSelect={handleDateSelect}
                onEventClick={(event) => console.log('Event clicked:', event)}
                onNavigate={handleNavigate}
                onNewAppointment={handleNewAppointment}
              />
            ) : currentView === 'week' ? (
              <CalendarWeekView
                date={selectedDate}
                events={mockDayViewEvents}
                onEventClick={(event) => console.log('Event clicked:', event)}
                onTimeSlotClick={(date) => handleNewAppointment(date)}
                onNavigate={handleNavigate}
                onNewAppointment={handleNewAppointment}
              />
            ) : (
              <CalendarDayView
                date={selectedDate}
                events={selectedDate.toDateString() === today.toDateString() ? mockDayViewEvents : []}
                onEventClick={(event) => console.log('Event clicked:', event)}
                onTimeSlotClick={() => handleNewAppointment(selectedDate)}
              />
            )}
          </motion.div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Search */}
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-slate-700">Quick Search</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search appointments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 border-gray-200"
                />
              </div>
            </CardContent>
          </Card>

          {/* Today's Appointments */}
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-slate-700">Today&apos;s Schedule</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {todaysAppointments.length > 0 ? (
                todaysAppointments.map((appointment) => (
                  <div key={appointment.id} className="p-3 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-sm text-slate-700">{appointment.title}</h4>
                      <Badge 
                        variant={appointment.status === 'completed' ? 'default' : 'secondary'}
                        className={
                          appointment.status === 'assigned' ? 'bg-blue-100 text-blue-700' :
                          appointment.status === 'pending' ? 'bg-opacity-20 text-slate-700' :
                          appointment.status === 'in-progress' ? 'bg-orange-100 text-orange-700' :
                          appointment.status === 'completed' ? 'bg-green-100 text-green-700' :
                          'bg-gray-100 text-gray-700'
                        }
                        style={appointment.status === 'pending' ? { 
                          backgroundColor: `${accentColor}33`,
                          color: accentColor
                        } : {}}
                      >
                        {appointment.status}
                      </Badge>
                    </div>
                    <div className="space-y-1 text-xs text-slate-500">
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {appointment.time}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {appointment.customer}
                      </div>
                    </div>
                    <div className="flex items-center mt-2">
                      <Avatar className="h-5 w-5 mr-2">
                        <AvatarFallback 
                          className="text-xs bg-opacity-20 text-slate-700"
                          style={{ 
                            backgroundColor: `${accentColor}33`,
                            color: accentColor
                          }}
                        >
                          {appointment.installer.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-slate-600">{appointment.installer}</span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-slate-500">No appointments today</p>
              )}
            </CardContent>
          </Card>

          {/* Upcoming Appointments */}
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-slate-700">Upcoming</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {upcomingAppointments.map((appointment) => (
                <div key={appointment.id} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-sm text-slate-700">{appointment.title}</h4>
                    <Badge variant="secondary" className="bg-gray-100 text-gray-700">
                      {appointment.status}
                    </Badge>
                  </div>
                  <div className="space-y-1 text-xs text-slate-500">
                    <div className="flex items-center">
                      <CalendarIcon className="h-3 w-3 mr-1" />
                      {new Date(appointment.date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {appointment.time}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* New Appointment Modal */}
      <NewAppointmentModal
        isOpen={showNewAppointment}
        onClose={() => setShowNewAppointment(false)}
        onSave={handleSaveAppointment}
        selectedDate={selectedDate}
        projects={mockProjects}
        installers={mockInstallers}
        customers={mockCustomers}
      />
    </div>
  )
}
