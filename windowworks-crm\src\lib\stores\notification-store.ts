import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { Notification } from '@/types'

interface NotificationState {
  notifications: Notification[]
  unreadCount: number
}

interface NotificationActions {
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  removeNotification: (id: string) => void
  setNotifications: (notifications: Notification[]) => void
}

export const useNotificationStore = create<NotificationState & NotificationActions>()(
  devtools(
    (set) => ({
      notifications: [],
      unreadCount: 0,
      addNotification: (notification) => {
        const newNotification: Notification = {
          ...notification,
          id: crypto.randomUUID(),
          createdAt: new Date().toISOString(),
        }
        set(
          (state) => ({
            notifications: [newNotification, ...state.notifications],
            unreadCount: state.unreadCount + 1,
          }),
          false,
          'addNotification'
        )
      },
      markAsRead: (id) =>
        set(
          (state) => ({
            notifications: state.notifications.map((notification) =>
              notification.id === id
                ? { ...notification, read: true }
                : notification
            ),
            unreadCount: Math.max(0, state.unreadCount - 1),
          }),
          false,
          'markAsRead'
        ),
      markAllAsRead: () =>
        set(
          (state) => ({
            notifications: state.notifications.map((notification) => ({
              ...notification,
              read: true,
            })),
            unreadCount: 0,
          }),
          false,
          'markAllAsRead'
        ),
      removeNotification: (id) =>
        set(
          (state) => {
            const notification = state.notifications.find((n) => n.id === id)
            const wasUnread = notification && !notification.read
            return {
              notifications: state.notifications.filter((n) => n.id !== id),
              unreadCount: wasUnread
                ? Math.max(0, state.unreadCount - 1)
                : state.unreadCount,
            }
          },
          false,
          'removeNotification'
        ),
      setNotifications: (notifications) =>
        set(
          {
            notifications,
            unreadCount: notifications.filter((n) => !n.read).length,
          },
          false,
          'setNotifications'
        ),
    }),
    {
      name: 'notification-store',
    }
  )
)
