'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Clock, User, MapPin, Plus } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'

interface DayViewEvent {
  id: string
  title: string
  startTime: string
  duration: number // in minutes
  customer: string
  installer?: string
  address: string
  status: 'pending' | 'assigned' | 'in-progress' | 'completed' | 'conflict'
}

interface CalendarDayViewProps {
  date: Date
  events: DayViewEvent[]
  onEventClick: (event: DayViewEvent) => void
  onTimeSlotClick: (time: string) => void
}

export function CalendarDayView({
  date,
  events,
  onEventClick,
  onTimeSlotClick
}: CalendarDayViewProps) {
  const timeSlots = []
  const startHour = 8
  const endHour = 18
  
  // Generate time slots from 8 AM to 6 PM
  for (let hour = startHour; hour <= endHour; hour++) {
    timeSlots.push({
      time: `${hour.toString().padStart(2, '0')}:00`,
      hour
    })
  }

  const getCurrentTimePosition = () => {
    const now = new Date()
    const isToday = now.toDateString() === date.toDateString()
    
    if (!isToday) return null
    
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    
    if (currentHour < startHour || currentHour > endHour) return null
    
    const position = ((currentHour - startHour) * 60 + currentMinute) / (endHour - startHour) / 60 * 100
    return Math.min(Math.max(position, 0), 100)
  }

  const getEventPosition = (event: DayViewEvent) => {
    const [hours, minutes] = event.startTime.split(':').map(Number)
    const startMinutes = (hours - startHour) * 60 + minutes
    const top = (startMinutes / ((endHour - startHour) * 60)) * 100
    const height = (event.duration / ((endHour - startHour) * 60)) * 100
    
    return {
      top: `${Math.max(0, top)}%`,
      height: `${Math.min(height, 100 - top)}%`
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-amber-500 border-amber-600'
      case 'assigned': return 'bg-blue-500 border-blue-600'
      case 'in-progress': return 'bg-orange-500 border-orange-600'
      case 'completed': return 'bg-green-500 border-green-600'
      case 'conflict': return 'bg-red-500 border-red-600 animate-pulse'
      default: return 'bg-gray-400 border-gray-500'
    }
  }

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number)
    const ampm = hours >= 12 ? 'PM' : 'AM'
    const displayHour = hours % 12 || 12
    return `${displayHour}:${minutes.toString().padStart(2, '0')} ${ampm}`
  }

  const currentTimePosition = getCurrentTimePosition()

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div>
          <h2 className="text-lg font-semibold text-slate-700">
            {date.toLocaleDateString('en-US', { 
              weekday: 'long', 
              month: 'long', 
              day: 'numeric', 
              year: 'numeric' 
            })}
          </h2>
          <p className="text-sm text-slate-500">
            {events.length} {events.length === 1 ? 'appointment' : 'appointments'} scheduled
          </p>
        </div>
        <Button 
          onClick={() => onTimeSlotClick('09:00')}
          className="bg-amber-500 hover:bg-amber-600 text-white"
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Appointment
        </Button>
      </div>

      {/* Time Grid */}
      <div className="relative">
        {/* Time slots */}
        <div className="flex">
          {/* Time labels */}
          <div className="w-20 flex-shrink-0">
            {timeSlots.map(({ time }) => (
              <div key={time} className="h-16 flex items-start pt-2">
                <span className="text-xs text-slate-500 font-medium">
                  {formatTime(time)}
                </span>
              </div>
            ))}
          </div>

          {/* Calendar column */}
          <div className="flex-1 relative border-l border-gray-200">
            {/* Time slot grid */}
            {timeSlots.map(({ time }) => (
              <motion.div
                key={time}
                className="h-16 border-b border-gray-100 hover:bg-gray-50 cursor-pointer group relative"
                whileHover={{ backgroundColor: '#f9fafb' }}
                onClick={() => onTimeSlotClick(time)}
              >
                {/* Add appointment button on hover */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs h-6 px-2 border-amber-200 text-amber-600 hover:bg-amber-50"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add
                  </Button>
                </div>
              </motion.div>
            ))}

            {/* Events */}
            {events.map((event) => {
              const position = getEventPosition(event)
              
              return (
                <motion.div
                  key={event.id}
                  className={`
                    absolute left-2 right-2 rounded-md border-2 cursor-pointer
                    ${getStatusColor(event.status)} text-white p-2 shadow-sm
                    overflow-hidden z-10
                  `}
                  style={position}
                  whileHover={{ scale: 1.02, zIndex: 20 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => onEventClick(event)}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="space-y-1">
                    <div className="font-medium text-sm truncate">
                      {event.title}
                    </div>
                    
                    <div className="flex items-center space-x-1 text-xs opacity-90">
                      <Clock className="h-3 w-3" />
                      <span>
                        {formatTime(event.startTime)} ({event.duration}m)
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1 text-xs opacity-90">
                      <User className="h-3 w-3" />
                      <span className="truncate">{event.customer}</span>
                    </div>
                    
                    {event.installer && (
                      <div className="flex items-center space-x-2 mt-2">
                        <Avatar className="h-5 w-5">
                          <AvatarImage src={`/avatars/${event.installer.toLowerCase().replace(' ', '-')}.jpg`} />
                          <AvatarFallback className="bg-white text-slate-700 text-xs">
                            {event.installer.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs truncate">{event.installer}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-1 text-xs opacity-90">
                      <MapPin className="h-3 w-3" />
                      <span className="truncate">{event.address}</span>
                    </div>
                  </div>
                </motion.div>
              )
            })}

            {/* Current time indicator */}
            {currentTimePosition !== null && (
              <motion.div
                className="absolute left-0 right-0 z-30 flex items-center"
                style={{ top: `${currentTimePosition}%` }}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="w-3 h-3 bg-red-500 rounded-full border-2 border-white shadow-md" />
                <div className="flex-1 h-0.5 bg-red-500" />
                <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-md ml-2 shadow-md">
                  Now
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Footer with stats */}
      <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-4 text-sm text-slate-600">
          <span>
            <span className="font-medium">{events.length}</span> appointments
          </span>
          <span>
            <span className="font-medium">
              {events.reduce((total, event) => total + event.duration, 0)}
            </span> minutes total
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {[
            { status: 'pending', count: events.filter(e => e.status === 'pending').length },
            { status: 'assigned', count: events.filter(e => e.status === 'assigned').length },
            { status: 'in-progress', count: events.filter(e => e.status === 'in-progress').length },
            { status: 'completed', count: events.filter(e => e.status === 'completed').length },
            { status: 'conflict', count: events.filter(e => e.status === 'conflict').length }
          ].filter(({ count }) => count > 0).map(({ status, count }) => (
            <div key={status} className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(status).split(' ')[0]}`} />
              <span className="text-xs">{count}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
