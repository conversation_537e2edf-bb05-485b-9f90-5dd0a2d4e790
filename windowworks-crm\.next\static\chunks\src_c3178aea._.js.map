{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/stores/auth-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, persist } from 'zustand/middleware'\r\nimport type { User } from '@/types'\r\n\r\ninterface AuthState {\r\n  user: User | null\r\n  isLoading: boolean\r\n  isAuthenticated: boolean\r\n}\r\n\r\ninterface AuthActions {\r\n  setUser: (user: User | null) => void\r\n  setLoading: (loading: boolean) => void\r\n  clearAuth: () => void\r\n}\r\n\r\nexport const useAuthStore = create<AuthState & AuthActions>()(\r\n  devtools(\r\n    persist(\r\n      (set) => ({\r\n        user: null,\r\n        isLoading: true,\r\n        isAuthenticated: false,\r\n        setUser: (user) =>\r\n          set(\r\n            {\r\n              user,\r\n              isAuthenticated: !!user,\r\n              isLoading: false,\r\n            },\r\n            false,\r\n            'setUser'\r\n          ),\r\n        setLoading: (isLoading) =>\r\n          set({ isLoading }, false, 'setLoading'),\r\n        clearAuth: () =>\r\n          set(\r\n            {\r\n              user: null,\r\n              isAuthenticated: false,\r\n              isLoading: false,\r\n            },\r\n            false,\r\n            'clearAuth'\r\n          ),\r\n      }),\r\n      {\r\n        name: 'auth-storage',\r\n        partialize: (state) => ({\r\n          user: state.user,\r\n          isAuthenticated: state.isAuthenticated,\r\n        }),\r\n      }\r\n    ),\r\n    {\r\n      name: 'auth-store',\r\n    }\r\n  )\r\n)\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAeO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,WAAW;QACX,iBAAiB;QACjB,SAAS,CAAC,OACR,IACE;gBACE;gBACA,iBAAiB,CAAC,CAAC;gBACnB,WAAW;YACb,GACA,OACA;QAEJ,YAAY,CAAC,YACX,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B,WAAW,IACT,IACE;gBACE,MAAM;gBACN,iBAAiB;gBACjB,WAAW;YACb,GACA,OACA;IAEN,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH,IAEF;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/stores/notification-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools } from 'zustand/middleware'\r\nimport type { Notification } from '@/types'\r\n\r\ninterface NotificationState {\r\n  notifications: Notification[]\r\n  unreadCount: number\r\n}\r\n\r\ninterface NotificationActions {\r\n  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void\r\n  markAsRead: (id: string) => void\r\n  markAllAsRead: () => void\r\n  removeNotification: (id: string) => void\r\n  setNotifications: (notifications: Notification[]) => void\r\n}\r\n\r\nexport const useNotificationStore = create<NotificationState & NotificationActions>()(\r\n  devtools(\r\n    (set) => ({\r\n      notifications: [],\r\n      unreadCount: 0,\r\n      addNotification: (notification) => {\r\n        const newNotification: Notification = {\r\n          ...notification,\r\n          id: crypto.randomUUID(),\r\n          createdAt: new Date().toISOString(),\r\n        }\r\n        set(\r\n          (state) => ({\r\n            notifications: [newNotification, ...state.notifications],\r\n            unreadCount: state.unreadCount + 1,\r\n          }),\r\n          false,\r\n          'addNotification'\r\n        )\r\n      },\r\n      markAsRead: (id) =>\r\n        set(\r\n          (state) => ({\r\n            notifications: state.notifications.map((notification) =>\r\n              notification.id === id\r\n                ? { ...notification, read: true }\r\n                : notification\r\n            ),\r\n            unreadCount: Math.max(0, state.unreadCount - 1),\r\n          }),\r\n          false,\r\n          'markAsRead'\r\n        ),\r\n      markAllAsRead: () =>\r\n        set(\r\n          (state) => ({\r\n            notifications: state.notifications.map((notification) => ({\r\n              ...notification,\r\n              read: true,\r\n            })),\r\n            unreadCount: 0,\r\n          }),\r\n          false,\r\n          'markAllAsRead'\r\n        ),\r\n      removeNotification: (id) =>\r\n        set(\r\n          (state) => {\r\n            const notification = state.notifications.find((n) => n.id === id)\r\n            const wasUnread = notification && !notification.read\r\n            return {\r\n              notifications: state.notifications.filter((n) => n.id !== id),\r\n              unreadCount: wasUnread\r\n                ? Math.max(0, state.unreadCount - 1)\r\n                : state.unreadCount,\r\n            }\r\n          },\r\n          false,\r\n          'removeNotification'\r\n        ),\r\n      setNotifications: (notifications) =>\r\n        set(\r\n          {\r\n            notifications,\r\n            unreadCount: notifications.filter((n) => !n.read).length,\r\n          },\r\n          false,\r\n          'setNotifications'\r\n        ),\r\n    }),\r\n    {\r\n      name: 'notification-store',\r\n    }\r\n  )\r\n)\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBO,MAAM,uBAAuB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACvC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAC,MAAQ,CAAC;QACR,eAAe,EAAE;QACjB,aAAa;QACb,iBAAiB,CAAC;YAChB,MAAM,kBAAgC;gBACpC,GAAG,YAAY;gBACf,IAAI,OAAO,UAAU;gBACrB,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,IACE,CAAC,QAAU,CAAC;oBACV,eAAe;wBAAC;2BAAoB,MAAM,aAAa;qBAAC;oBACxD,aAAa,MAAM,WAAW,GAAG;gBACnC,CAAC,GACD,OACA;QAEJ;QACA,YAAY,CAAC,KACX,IACE,CAAC,QAAU,CAAC;oBACV,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eACtC,aAAa,EAAE,KAAK,KAChB;4BAAE,GAAG,YAAY;4BAAE,MAAM;wBAAK,IAC9B;oBAEN,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;gBAC/C,CAAC,GACD,OACA;QAEJ,eAAe,IACb,IACE,CAAC,QAAU,CAAC;oBACV,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eAAiB,CAAC;4BACxD,GAAG,YAAY;4BACf,MAAM;wBACR,CAAC;oBACD,aAAa;gBACf,CAAC,GACD,OACA;QAEJ,oBAAoB,CAAC,KACnB,IACE,CAAC;gBACC,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAC9D,MAAM,YAAY,gBAAgB,CAAC,aAAa,IAAI;gBACpD,OAAO;oBACL,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAC1D,aAAa,YACT,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG,KAChC,MAAM,WAAW;gBACvB;YACF,GACA,OACA;QAEJ,kBAAkB,CAAC,gBACjB,IACE;gBACE;gBACA,aAAa,cAAc,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,IAAI,EAAE,MAAM;YAC1D,GACA,OACA;IAEN,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/dashboard/dashboard-layout.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ReactNode } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { \r\n  Bell, \r\n  Home, \r\n  Users, \r\n  FolderOpen, \r\n  Calendar,\r\n  Settings,\r\n  LogOut,\r\n  Menu,\r\n  Search\r\n} from 'lucide-react'\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\nimport { useAuthStore } from '@/lib/stores/auth-store'\r\nimport { useNotificationStore } from '@/lib/stores/notification-store'\r\n\r\ninterface DashboardLayoutProps {\r\n  children: ReactNode\r\n}\r\n\r\nconst navigationItems = [\r\n  { icon: Home, label: 'Dashboard', href: '/', badge: null },\r\n  { icon: Users, label: 'Customers', href: '/customers', badge: null },\r\n  { icon: FolderOpen, label: 'Projects', href: '/projects', badge: null },\r\n  { icon: Calendar, label: 'Schedule', href: '/schedule', badge: null },\r\n  { icon: Settings, label: 'Settings', href: '/settings', badge: null },\r\n]\r\n\r\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\r\n  const { user, clearAuth } = useAuthStore()\r\n  const { unreadCount } = useNotificationStore()\r\n  const pathname = usePathname()\r\n\r\n  const handleLogout = () => {\r\n    clearAuth()\r\n    // In a real app, you'd also call Supabase signOut here\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-screen bg-background flex overflow-hidden\">\r\n      {/* Sidebar */}\r\n      <motion.aside\r\n        initial={{ x: -300 }}\r\n        animate={{ x: 0 }}\r\n        className=\"w-64 bg-card border-r border-border flex flex-col fixed left-0 top-0 h-full z-30\"\r\n      >\r\n        {/* Logo */}\r\n        <div className=\"p-6 border-b border-border flex-shrink-0\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.2 }}\r\n            className=\"flex items-center space-x-3\"\r\n          >\r\n            <div className=\"w-8 h-8 bg-[var(--color-brand-primary)] rounded-md flex items-center justify-center\">\r\n              <span className=\"text-white font-bold text-sm\">W</span>\r\n            </div>\r\n            <div>\r\n              <h1 className=\"text-xl font-bold text-foreground\">WindowWorks</h1>\r\n              <p className=\"text-xs text-muted-foreground\">CRM Platform</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <nav className=\"flex-1 p-4 space-y-2 overflow-y-auto\">\r\n          {navigationItems.map((item, index) => {\r\n            const isActive = pathname === item.href\r\n            return (\r\n              <motion.div\r\n                key={item.href}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                transition={{ delay: 0.1 * (index + 1) }}\r\n              >\r\n                <Link href={item.href}>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className={`w-full justify-start rounded-md ${\r\n                      isActive \r\n                        ? 'bg-primary/10 text-primary border-primary/20' \r\n                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'\r\n                    }`}\r\n                  >\r\n                    <item.icon className=\"mr-3 h-5 w-5\" />\r\n                    {item.label}\r\n                    {item.badge && (\r\n                      <Badge variant=\"secondary\" className=\"ml-auto\">\r\n                        {item.badge}\r\n                      </Badge>\r\n                    )}\r\n                  </Button>\r\n                </Link>\r\n              </motion.div>\r\n            )\r\n          })}\r\n        </nav>\r\n\r\n        {/* User Profile - Sticky at bottom */}\r\n        <div className=\"p-4 border-t border-border flex-shrink-0 sticky bottom-0 bg-card\">\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" className=\"w-full justify-start p-3 hover:bg-accent\">\r\n                <Avatar className=\"h-8 w-8 mr-3\">\r\n                  <AvatarImage src={user?.profile?.avatar} />\r\n                  <AvatarFallback className=\"bg-primary/10 text-primary\">\r\n                    {user?.profile?.firstName?.[0]}{user?.profile?.lastName?.[0]}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"text-left\">\r\n                  <p className=\"text-sm font-medium text-foreground\">\r\n                    {user?.profile?.firstName} {user?.profile?.lastName}\r\n                  </p>\r\n                  <p className=\"text-xs text-muted-foreground capitalize\">{user?.role}</p>\r\n                </div>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"start\" className=\"w-56\">\r\n              <DropdownMenuItem>\r\n                <Settings className=\"mr-2 h-4 w-4\" />\r\n                Account Settings\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                Sign Out\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </motion.aside>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 flex flex-col bg-background ml-64\">\r\n        {/* Top Header - Sticky */}\r\n        <motion.header\r\n          initial={{ y: -50, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"bg-card border-b border-border px-6 py-4 sticky top-0 z-20 flex-shrink-0\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\r\n                <Menu className=\"h-5 w-5\" />\r\n              </Button>\r\n              \r\n              {/* Search */}\r\n              <div className=\"relative w-96 hidden md:block\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\r\n                <Input\r\n                  placeholder=\"Search customers, projects...\"\r\n                  className=\"pl-10 bg-accent/50 border-border focus:bg-background\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Notifications */}\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\r\n                <Bell className=\"h-5 w-5\" />\r\n                {unreadCount > 0 && (\r\n                  <motion.div\r\n                    initial={{ scale: 0 }}\r\n                    animate={{ scale: 1 }}\r\n                    className=\"absolute -top-1 -right-1 h-5 w-5 bg-destructive rounded-full flex items-center justify-center\"\r\n                  >\r\n                    <span className=\"text-xs text-destructive-foreground font-medium\">\r\n                      {unreadCount > 9 ? '9+' : unreadCount}\r\n                    </span>\r\n                  </motion.div>\r\n                )}\r\n              </Button>\r\n\r\n              {/* Quick Actions */}\r\n              <Button size=\"sm\" className=\"bg-primary hover:bg-primary/90\">\r\n                + New Project\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content - Scrollable */}\r\n        <main className=\"flex-1 overflow-y-auto bg-background\">\r\n          <div className=\"p-6\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.3 }}\r\n            >\r\n              {children}\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AAMA;AACA;;;AA5BA;;;;;;;;;;;;AAkCA,MAAM,kBAAkB;IACtB;QAAE,MAAM,sMAAA,CAAA,OAAI;QAAE,OAAO;QAAa,MAAM;QAAK,OAAO;IAAK;IACzD;QAAE,MAAM,uMAAA,CAAA,QAAK;QAAE,OAAO;QAAa,MAAM;QAAc,OAAO;IAAK;IACnE;QAAE,MAAM,qNAAA,CAAA,aAAU;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;IACtE;QAAE,MAAM,6MAAA,CAAA,WAAQ;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;IACpE;QAAE,MAAM,6MAAA,CAAA,WAAQ;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;CACrE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB;IACA,uDAAuD;IACzD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;kCAMnD,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM;4BAC1B,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,CAAC,QAAQ,CAAC;gCAAE;0CAEvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,IAAI;8CACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAW,CAAC,gCAAgC,EAC1C,WACI,iDACA,+DACJ;;0DAEF,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,KAAK;4CACV,KAAK,KAAK,kBACT,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,KAAK,KAAK;;;;;;;;;;;;;;;;;+BAlBd,KAAK,IAAI;;;;;wBAyBpB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;;0DAChC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDAAC,KAAK,MAAM,SAAS;;;;;;kEACjC,6LAAC,qIAAA,CAAA,iBAAc;wDAAC,WAAU;;4DACvB,MAAM,SAAS,WAAW,CAAC,EAAE;4DAAE,MAAM,SAAS,UAAU,CAAC,EAAE;;;;;;;;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DACV,MAAM,SAAS;4DAAU;4DAAE,MAAM,SAAS;;;;;;;kEAE7C,6LAAC;wDAAE,WAAU;kEAA4C,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAIrE,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAQ,WAAU;;sDAC3C,6LAAC,+IAAA,CAAA,mBAAgB;;8DACf,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BAAE,GAAG,CAAC;4BAAI,SAAS;wBAAE;wBAC9B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAIlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;;8DAC1C,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,cAAc,mBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO;oDAAE;oDACpB,WAAU;8DAEV,cAAA,6LAAC;wDAAK,WAAU;kEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;sDAOlC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;;;;;;kCAQnE,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAExB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAvKgB;;QACc,wIAAA,CAAA,eAAY;QAChB,gJAAA,CAAA,uBAAoB;QAC3B,qIAAA,CAAA,cAAW;;;KAHd", "debugId": null}}, {"offset": {"line": 1186, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm transition-colors\",\n        \"border-border dark:border-muted-foreground/50 dark:hover:border-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,oFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAZS;AAcT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1301, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1516, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { Check } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n))\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1568, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1602, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/textarea.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nTextarea.displayName = 'Textarea'\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1638, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1714, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/customers/new-customer-modal.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState, useCallback } from 'react'\r\nimport { \r\n  User,\r\n  Mail,\r\n  Phone,\r\n  MapPin,\r\n  Heart,\r\n  DollarSign,\r\n  MessageSquare,\r\n  Sparkles,\r\n  Save,\r\n  Loader2,\r\n  AlertCircle\r\n} from 'lucide-react'\r\n\r\nimport { <PERSON><PERSON> } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Textarea } from '@/components/ui/textarea'\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { useTheme } from '@/contexts/theme-context'\r\n\r\n// Types\r\ninterface CustomerFormData {\r\n  firstName: string\r\n  lastName: string\r\n  email: string\r\n  phone: string\r\n  address: {\r\n    street: string\r\n    city: string\r\n    state: string\r\n    zipCode: string\r\n    country: string\r\n  }\r\n  preferences: {\r\n    windowTypes: string[]\r\n    preferredColors: string[]\r\n    budgetRange: string\r\n    communicationMethod: string\r\n  }\r\n  notes: string\r\n}\r\n\r\ninterface NewCustomerModalProps {\r\n  isOpen: boolean\r\n  onClose: () => void\r\n  onSave: (customer: CustomerFormData) => Promise<void>\r\n}\r\n\r\nconst windowTypes = [\r\n  { id: 'blinds', label: 'Blinds', popular: true },\r\n  { id: 'shutters', label: 'Shutters', popular: true },\r\n  { id: 'shades', label: 'Shades', popular: true },\r\n  { id: 'curtains', label: 'Curtains', popular: false },\r\n  { id: 'drapes', label: 'Drapes', popular: false },\r\n  { id: 'valances', label: 'Valances', popular: false }\r\n]\r\n\r\nconst colorOptions = [\r\n  { id: 'white', label: 'White', hex: '#FFFFFF' },\r\n  { id: 'cream', label: 'Cream', hex: '#F5F5DC' },\r\n  { id: 'beige', label: 'Beige', hex: '#F5F5DC' },\r\n  { id: 'gray', label: 'Gray', hex: '#808080' },\r\n  { id: 'brown', label: 'Brown', hex: '#A52A2A' },\r\n  { id: 'black', label: 'Black', hex: '#000000' }\r\n]\r\n\r\nconst budgetRanges = [\r\n  { value: 'under-500', label: 'Under $500' },\r\n  { value: '500-1000', label: '$500 - $1,000' },\r\n  { value: '1000-2500', label: '$1,000 - $2,500' },\r\n  { value: '2500-5000', label: '$2,500 - $5,000' },\r\n  { value: 'over-5000', label: 'Over $5,000' }\r\n]\r\n\r\nconst communicationMethods = [\r\n  { value: 'email', label: 'Email' },\r\n  { value: 'phone', label: 'Phone' },\r\n  { value: 'text', label: 'Text/SMS' },\r\n  { value: 'app', label: 'In-App Notifications' }\r\n]\r\n\r\nexport function NewCustomerModal({ isOpen, onClose, onSave }: NewCustomerModalProps) {\r\n  const { accentColor } = useTheme()\r\n  const [activeTab, setActiveTab] = useState('details')\r\n  const [isLoading, setIsLoading] = useState(false)\r\n  const [errors, setErrors] = useState<Record<string, string>>({})\r\n  const [aiSuggesting, setAiSuggesting] = useState(false)\r\n\r\n  const [formData, setFormData] = useState<CustomerFormData>({\r\n    firstName: '',\r\n    lastName: '',\r\n    email: '',\r\n    phone: '',\r\n    address: {\r\n      street: '',\r\n      city: '',\r\n      state: '',\r\n      zipCode: '',\r\n      country: 'United States'\r\n    },\r\n    preferences: {\r\n      windowTypes: [],\r\n      preferredColors: [],\r\n      budgetRange: '',\r\n      communicationMethod: 'email'\r\n    },\r\n    notes: ''\r\n  })\r\n\r\n  // Validation\r\n  const validateForm = useCallback(() => {\r\n    const newErrors: Record<string, string> = {}\r\n\r\n    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required'\r\n    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required'\r\n    if (!formData.email.trim()) {\r\n      newErrors.email = 'Email is required'\r\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\r\n      newErrors.email = 'Please enter a valid email address'\r\n    }\r\n    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required'\r\n    if (!formData.address.street.trim()) newErrors.street = 'Street address is required'\r\n    if (!formData.address.city.trim()) newErrors.city = 'City is required'\r\n    if (!formData.address.state.trim()) newErrors.state = 'State is required'\r\n    if (!formData.address.zipCode.trim()) newErrors.zipCode = 'ZIP code is required'\r\n\r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }, [formData])\r\n\r\n  // AI Suggestion Simulation\r\n  const suggestPreferences = useCallback(async () => {\r\n    setAiSuggesting(true)\r\n    \r\n    // Simulate AI processing\r\n    await new Promise(resolve => setTimeout(resolve, 1500))\r\n    \r\n    // Mock suggestions based on \"trends\"\r\n    const suggestions = {\r\n      windowTypes: ['blinds', 'shutters'],\r\n      preferredColors: ['white', 'gray'],\r\n      budgetRange: '1000-2500',\r\n      communicationMethod: 'email'\r\n    }\r\n\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      preferences: {\r\n        ...prev.preferences,\r\n        ...suggestions\r\n      }\r\n    }))\r\n    \r\n    setAiSuggesting(false)\r\n  }, [])\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async () => {\r\n    if (!validateForm()) {\r\n      // Switch to the tab with errors\r\n      if (errors.firstName || errors.lastName || errors.email || errors.phone) {\r\n        setActiveTab('details')\r\n      } else if (errors.street || errors.city || errors.state || errors.zipCode) {\r\n        setActiveTab('address')\r\n      }\r\n      return\r\n    }\r\n\r\n    setIsLoading(true)\r\n    try {\r\n      await onSave(formData)\r\n      onClose()\r\n      // Reset form\r\n      setFormData({\r\n        firstName: '',\r\n        lastName: '',\r\n        email: '',\r\n        phone: '',\r\n        address: {\r\n          street: '',\r\n          city: '',\r\n          state: '',\r\n          zipCode: '',\r\n          country: 'United States'\r\n        },\r\n        preferences: {\r\n          windowTypes: [],\r\n          preferredColors: [],\r\n          budgetRange: '',\r\n          communicationMethod: 'email'\r\n        },\r\n        notes: ''\r\n      })\r\n    } catch (error) {\r\n      console.error('Error saving customer:', error)\r\n    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }\r\n\r\n  // Update form data\r\n  const updateFormData = (path: string, value: string | string[]) => {\r\n    setFormData(prev => {\r\n      const keys = path.split('.')\r\n      const newData = { ...prev }\r\n      let current: Record<string, unknown> = newData\r\n      \r\n      for (let i = 0; i < keys.length - 1; i++) {\r\n        if (current[keys[i]] === undefined) current[keys[i]] = {}\r\n        current = current[keys[i]] as Record<string, unknown>\r\n      }\r\n      \r\n      current[keys[keys.length - 1]] = value\r\n      return newData\r\n    })\r\n    \r\n    // Clear error for this field\r\n    if (errors[path]) {\r\n      setErrors(prev => ({ ...prev, [path]: '' }))\r\n    }\r\n  }\r\n\r\n  // Handle window type selection\r\n  const toggleWindowType = (typeId: string) => {\r\n    const current = formData.preferences.windowTypes\r\n    const updated = current.includes(typeId)\r\n      ? current.filter(t => t !== typeId)\r\n      : [...current, typeId]\r\n    updateFormData('preferences.windowTypes', updated)\r\n  }\r\n\r\n  // Handle color selection\r\n  const toggleColor = (colorId: string) => {\r\n    const current = formData.preferences.preferredColors\r\n    const updated = current.includes(colorId)\r\n      ? current.filter(c => c !== colorId)\r\n      : [...current, colorId]\r\n    updateFormData('preferences.preferredColors', updated)\r\n  }\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-6xl sm:max-w-6xl max-h-[70vh] overflow-hidden p-0\">\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Header */}\r\n          <DialogHeader className=\"px-6 py-4 border-b border-gray-200 flex-shrink-0\">\r\n            <DialogTitle className=\"text-xl font-semibold text-slate-700\">\r\n              Add New Customer\r\n            </DialogTitle>\r\n          </DialogHeader>\r\n\r\n          {/* Content */}\r\n          <div className=\"flex-1 overflow-y-auto p-6\">\r\n            <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\r\n              <TabsList className=\"grid w-full grid-cols-2\">\r\n                <TabsTrigger value=\"details\" className=\"flex items-center gap-2\">\r\n                  <User className=\"h-4 w-4\" />\r\n                  Details & Address\r\n                </TabsTrigger>\r\n                <TabsTrigger value=\"preferences\" className=\"flex items-center gap-2\">\r\n                  <Heart className=\"h-4 w-4\" />\r\n                  Preferences & Notes\r\n                </TabsTrigger>\r\n              </TabsList>\r\n\r\n              {/* Details & Address Tab */}\r\n              <TabsContent value=\"details\" className=\"space-y-6\">\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                  {/* Personal Information */}\r\n                  <Card>\r\n                    <CardHeader>\r\n                      <CardTitle className=\"text-lg text-slate-700 flex items-center gap-2\">\r\n                        <User className=\"h-5 w-5\" />\r\n                        Personal Information\r\n                      </CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent className=\"space-y-4\">\r\n                      <div className=\"grid grid-cols-2 gap-4\">\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"firstName\" className=\"text-slate-600\">\r\n                            First Name *\r\n                          </Label>\r\n                          <Input\r\n                            id=\"firstName\"\r\n                            value={formData.firstName}\r\n                            onChange={(e) => updateFormData('firstName', e.target.value)}\r\n                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                              errors.firstName ? 'border-red-500' : ''\r\n                            }`}\r\n                            placeholder=\"Enter first name\"\r\n                          />\r\n                          {errors.firstName && (\r\n                            <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                              <AlertCircle className=\"h-3 w-3\" />\r\n                              {errors.firstName}\r\n                            </p>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"lastName\" className=\"text-slate-600\">\r\n                            Last Name *\r\n                          </Label>\r\n                          <Input\r\n                            id=\"lastName\"\r\n                            value={formData.lastName}\r\n                            onChange={(e) => updateFormData('lastName', e.target.value)}\r\n                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                              errors.lastName ? 'border-red-500' : ''\r\n                            }`}\r\n                            placeholder=\"Enter last name\"\r\n                          />\r\n                          {errors.lastName && (\r\n                            <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                              <AlertCircle className=\"h-3 w-3\" />\r\n                              {errors.lastName}\r\n                            </p>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <Label htmlFor=\"email\" className=\"text-slate-600\">\r\n                          Email Address *\r\n                        </Label>\r\n                        <div className=\"relative\">\r\n                          <Mail className=\"absolute left-3 top-3 h-4 w-4 text-slate-400\" />\r\n                          <Input\r\n                            id=\"email\"\r\n                            type=\"email\"\r\n                            value={formData.email}\r\n                            onChange={(e) => updateFormData('email', e.target.value)}\r\n                            className={`pl-10 border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                              errors.email ? 'border-red-500' : ''\r\n                            }`}\r\n                            placeholder=\"<EMAIL>\"\r\n                          />\r\n                        </div>\r\n                        {errors.email && (\r\n                          <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                            <AlertCircle className=\"h-3 w-3\" />\r\n                            {errors.email}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <Label htmlFor=\"phone\" className=\"text-slate-600\">\r\n                          Phone Number *\r\n                        </Label>\r\n                        <div className=\"relative\">\r\n                          <Phone className=\"absolute left-3 top-3 h-4 w-4 text-slate-400\" />\r\n                          <Input\r\n                            id=\"phone\"\r\n                            type=\"tel\"\r\n                            value={formData.phone}\r\n                            onChange={(e) => updateFormData('phone', e.target.value)}\r\n                            className={`pl-10 border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                              errors.phone ? 'border-red-500' : ''\r\n                            }`}\r\n                            placeholder=\"(*************\"\r\n                          />\r\n                        </div>\r\n                        {errors.phone && (\r\n                          <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                            <AlertCircle className=\"h-3 w-3\" />\r\n                            {errors.phone}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n\r\n                  {/* Address Information */}\r\n                  <Card>\r\n                    <CardHeader>\r\n                      <CardTitle className=\"text-lg text-slate-700 flex items-center gap-2\">\r\n                        <MapPin className=\"h-5 w-5\" />\r\n                        Address Information\r\n                      </CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent className=\"space-y-4\">\r\n                      <div className=\"space-y-2\">\r\n                        <Label htmlFor=\"street\" className=\"text-slate-600\">\r\n                          Street Address *\r\n                        </Label>\r\n                        <Input\r\n                          id=\"street\"\r\n                          value={formData.address.street}\r\n                          onChange={(e) => updateFormData('address.street', e.target.value)}\r\n                          className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                            errors.street ? 'border-red-500' : ''\r\n                          }`}\r\n                          placeholder=\"123 Main Street\"\r\n                        />\r\n                        {errors.street && (\r\n                          <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                            <AlertCircle className=\"h-3 w-3\" />\r\n                            {errors.street}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"grid grid-cols-2 gap-4\">\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"city\" className=\"text-slate-600\">\r\n                            City *\r\n                          </Label>\r\n                          <Input\r\n                            id=\"city\"\r\n                            value={formData.address.city}\r\n                            onChange={(e) => updateFormData('address.city', e.target.value)}\r\n                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                              errors.city ? 'border-red-500' : ''\r\n                            }`}\r\n                            placeholder=\"Springfield\"\r\n                          />\r\n                          {errors.city && (\r\n                            <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                              <AlertCircle className=\"h-3 w-3\" />\r\n                              {errors.city}\r\n                            </p>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"state\" className=\"text-slate-600\">\r\n                            State *\r\n                          </Label>\r\n                          <Input\r\n                            id=\"state\"\r\n                            value={formData.address.state}\r\n                            onChange={(e) => updateFormData('address.state', e.target.value)}\r\n                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                              errors.state ? 'border-red-500' : ''\r\n                            }`}\r\n                            placeholder=\"IL\"\r\n                          />\r\n                          {errors.state && (\r\n                            <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                              <AlertCircle className=\"h-3 w-3\" />\r\n                              {errors.state}\r\n                            </p>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"grid grid-cols-2 gap-4\">\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"zipCode\" className=\"text-slate-600\">\r\n                            ZIP Code *\r\n                          </Label>\r\n                          <Input\r\n                            id=\"zipCode\"\r\n                            value={formData.address.zipCode}\r\n                            onChange={(e) => updateFormData('address.zipCode', e.target.value)}\r\n                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                              errors.zipCode ? 'border-red-500' : ''\r\n                            }`}\r\n                            placeholder=\"62704\"\r\n                          />\r\n                          {errors.zipCode && (\r\n                            <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                              <AlertCircle className=\"h-3 w-3\" />\r\n                              {errors.zipCode}\r\n                            </p>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"country\" className=\"text-slate-600\">\r\n                            Country\r\n                          </Label>\r\n                          <Select \r\n                            value={formData.address.country} \r\n                            onValueChange={(value) => updateFormData('address.country', value)}\r\n                          >\r\n                            <SelectTrigger className=\"border-gray-200\">\r\n                              <SelectValue />\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                              <SelectItem value=\"United States\">United States</SelectItem>\r\n                              <SelectItem value=\"Canada\">Canada</SelectItem>\r\n                              <SelectItem value=\"Mexico\">Mexico</SelectItem>\r\n                            </SelectContent>\r\n                          </Select>\r\n                        </div>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              {/* Preferences & Notes Tab */}\r\n              <TabsContent value=\"preferences\" className=\"space-y-6\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <h3 className=\"text-lg font-semibold text-slate-700\">Customer Preferences & Notes</h3>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    onClick={suggestPreferences}\r\n                    disabled={aiSuggesting}\r\n                    className=\"border-gray-200\"\r\n                    style={{ borderColor: accentColor, color: accentColor }}\r\n                  >\r\n                    {aiSuggesting ? (\r\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                    ) : (\r\n                      <Sparkles className=\"h-4 w-4 mr-2\" />\r\n                    )}\r\n                    {aiSuggesting ? 'Analyzing...' : 'AI Suggest'}\r\n                  </Button>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                  {/* Left Column - Preferences */}\r\n                  <div className=\"space-y-6\">\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle className=\"text-base text-slate-700\">Window Types</CardTitle>\r\n                        <p className=\"text-sm text-slate-500\">Select preferred window treatment types</p>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <div className=\"grid grid-cols-1 gap-3\">\r\n                          {windowTypes.map((type) => (\r\n                            <div key={type.id} className=\"flex items-center space-x-2\">\r\n                              <Checkbox\r\n                                id={type.id}\r\n                                checked={formData.preferences.windowTypes.includes(type.id)}\r\n                                onCheckedChange={() => toggleWindowType(type.id)}\r\n                                className=\"border-gray-300\"\r\n                              />\r\n                              <Label htmlFor={type.id} className=\"text-sm font-normal text-slate-600 flex items-center gap-2\">\r\n                                {type.label}\r\n                                {type.popular && (\r\n                                  <Badge variant=\"secondary\" className=\"text-xs px-1.5 py-0.5\">\r\n                                    Popular\r\n                                  </Badge>\r\n                                )}\r\n                              </Label>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle className=\"text-base text-slate-700\">Preferred Colors</CardTitle>\r\n                        <p className=\"text-sm text-slate-500\">Select preferred color schemes</p>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <div className=\"grid grid-cols-2 gap-3\">\r\n                          {colorOptions.map((color) => (\r\n                            <div key={color.id} className=\"flex items-center space-x-2\">\r\n                              <Checkbox\r\n                                id={color.id}\r\n                                checked={formData.preferences.preferredColors.includes(color.id)}\r\n                                onCheckedChange={() => toggleColor(color.id)}\r\n                                className=\"border-gray-300\"\r\n                              />\r\n                              <Label htmlFor={color.id} className=\"text-sm font-normal text-slate-600 flex items-center gap-2\">\r\n                                <div \r\n                                  className=\"w-3 h-3 rounded-full border border-gray-300\"\r\n                                  style={{ backgroundColor: color.hex }}\r\n                                />\r\n                                {color.label}\r\n                              </Label>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  </div>\r\n\r\n                  {/* Right Column - Budget, Communication & Notes */}\r\n                  <div className=\"space-y-6\">\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle className=\"text-base text-slate-700 flex items-center gap-2\">\r\n                          <DollarSign className=\"h-4 w-4\" />\r\n                          Budget Range\r\n                        </CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <Select \r\n                          value={formData.preferences.budgetRange} \r\n                          onValueChange={(value) => updateFormData('preferences.budgetRange', value)}\r\n                        >\r\n                          <SelectTrigger className=\"border-gray-200\">\r\n                            <SelectValue placeholder=\"Select budget range\" />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            {budgetRanges.map((range) => (\r\n                              <SelectItem key={range.value} value={range.value}>\r\n                                {range.label}\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectContent>\r\n                        </Select>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle className=\"text-base text-slate-700\">Communication</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <Select \r\n                          value={formData.preferences.communicationMethod} \r\n                          onValueChange={(value) => updateFormData('preferences.communicationMethod', value)}\r\n                        >\r\n                          <SelectTrigger className=\"border-gray-200\">\r\n                            <SelectValue />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            {communicationMethods.map((method) => (\r\n                              <SelectItem key={method.value} value={method.value}>\r\n                                {method.label}\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectContent>\r\n                        </Select>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle className=\"text-lg text-slate-700 flex items-center gap-2\">\r\n                          <MessageSquare className=\"h-5 w-5\" />\r\n                          Additional Notes\r\n                        </CardTitle>\r\n                        <p className=\"text-sm text-slate-500\">\r\n                          Add any additional information about the customer\r\n                        </p>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <Textarea\r\n                          value={formData.notes}\r\n                          onChange={(e) => updateFormData('notes', e.target.value)}\r\n                          className=\"min-h-[200px] border-gray-200 focus:border-[var(--color-brand-primary)]\"\r\n                          placeholder=\"Special requirements, previous interactions, design preferences, installation considerations...\"\r\n                        />\r\n                      </CardContent>\r\n                    </Card>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n            </Tabs>\r\n          </div>\r\n\r\n          {/* Footer */}\r\n          <div className=\"px-6 py-4 border-t border-gray-200 flex-shrink-0\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={onClose}\r\n                disabled={isLoading}\r\n                className=\"border-gray-200\"\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                onClick={handleSubmit}\r\n                disabled={isLoading}\r\n                className=\"text-white min-w-[120px]\"\r\n                style={{ \r\n                  backgroundColor: accentColor,\r\n                  borderColor: accentColor\r\n                }}\r\n              >\r\n                {isLoading ? (\r\n                  <>\r\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                    Saving...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Save className=\"h-4 w-4 mr-2\" />\r\n                    Save Customer\r\n                  </>\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAMA;;;AAtCA;;;;;;;;;;;;;;AAoEA,MAAM,cAAc;IAClB;QAAE,IAAI;QAAU,OAAO;QAAU,SAAS;IAAK;IAC/C;QAAE,IAAI;QAAY,OAAO;QAAY,SAAS;IAAK;IACnD;QAAE,IAAI;QAAU,OAAO;QAAU,SAAS;IAAK;IAC/C;QAAE,IAAI;QAAY,OAAO;QAAY,SAAS;IAAM;IACpD;QAAE,IAAI;QAAU,OAAO;QAAU,SAAS;IAAM;IAChD;QAAE,IAAI;QAAY,OAAO;QAAY,SAAS;IAAM;CACrD;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAS,OAAO;QAAS,KAAK;IAAU;IAC9C;QAAE,IAAI;QAAS,OAAO;QAAS,KAAK;IAAU;IAC9C;QAAE,IAAI;QAAS,OAAO;QAAS,KAAK;IAAU;IAC9C;QAAE,IAAI;QAAQ,OAAO;QAAQ,KAAK;IAAU;IAC5C;QAAE,IAAI;QAAS,OAAO;QAAS,KAAK;IAAU;IAC9C;QAAE,IAAI;QAAS,OAAO;QAAS,KAAK;IAAU;CAC/C;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAa,OAAO;IAAa;IAC1C;QAAE,OAAO;QAAY,OAAO;IAAgB;IAC5C;QAAE,OAAO;QAAa,OAAO;IAAkB;IAC/C;QAAE,OAAO;QAAa,OAAO;IAAkB;IAC/C;QAAE,OAAO;QAAa,OAAO;IAAc;CAC5C;AAED,MAAM,uBAAuB;IAC3B;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAQ,OAAO;IAAW;IACnC;QAAE,OAAO;QAAO,OAAO;IAAuB;CAC/C;AAEM,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAyB;;IACjF,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,SAAS;YACP,QAAQ;YACR,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;QACX;QACA,aAAa;YACX,aAAa,EAAE;YACf,iBAAiB,EAAE;YACnB,aAAa;YACb,qBAAqB;QACvB;QACA,OAAO;IACT;IAEA,aAAa;IACb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC/B,MAAM,YAAoC,CAAC;YAE3C,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI,UAAU,SAAS,GAAG;YACtD,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;YACpD,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;gBAC1B,UAAU,KAAK,GAAG;YACpB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;gBAC7D,UAAU,KAAK,GAAG;YACpB;YACA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;YAC9C,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,UAAU,MAAM,GAAG;YACxD,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;YACpD,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;YACtD,IAAI,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO,GAAG;YAE1D,UAAU;YACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;QAC3C;qDAAG;QAAC;KAAS;IAEb,2BAA2B;IAC3B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,gBAAgB;YAEhB,yBAAyB;YACzB,MAAM,IAAI;oEAAQ,CAAA,UAAW,WAAW,SAAS;;YAEjD,qCAAqC;YACrC,MAAM,cAAc;gBAClB,aAAa;oBAAC;oBAAU;iBAAW;gBACnC,iBAAiB;oBAAC;oBAAS;iBAAO;gBAClC,aAAa;gBACb,qBAAqB;YACvB;YAEA;oEAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,aAAa;4BACX,GAAG,KAAK,WAAW;4BACnB,GAAG,WAAW;wBAChB;oBACF,CAAC;;YAED,gBAAgB;QAClB;2DAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI,CAAC,gBAAgB;YACnB,gCAAgC;YAChC,IAAI,OAAO,SAAS,IAAI,OAAO,QAAQ,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,EAAE;gBACvE,aAAa;YACf,OAAO,IAAI,OAAO,MAAM,IAAI,OAAO,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,OAAO,EAAE;gBACzE,aAAa;YACf;YACA;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,OAAO;YACb;YACA,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,SAAS;oBACP,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,SAAS;gBACX;gBACA,aAAa;oBACX,aAAa,EAAE;oBACf,iBAAiB,EAAE;oBACnB,aAAa;oBACb,qBAAqB;gBACvB;gBACA,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC,MAAc;QACpC,YAAY,CAAA;YACV,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,MAAM,UAAU;gBAAE,GAAG,IAAI;YAAC;YAC1B,IAAI,UAAmC;YAEvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;gBACxC,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,WAAW,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC;gBACxD,UAAU,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B;YAEA,OAAO,CAAC,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG;YACjC,OAAO;QACT;QAEA,6BAA6B;QAC7B,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAU,SAAS,WAAW,CAAC,WAAW;QAChD,MAAM,UAAU,QAAQ,QAAQ,CAAC,UAC7B,QAAQ,MAAM,CAAC,CAAA,IAAK,MAAM,UAC1B;eAAI;YAAS;SAAO;QACxB,eAAe,2BAA2B;IAC5C;IAEA,yBAAyB;IACzB,MAAM,cAAc,CAAC;QACnB,MAAM,UAAU,SAAS,WAAW,CAAC,eAAe;QACpD,MAAM,UAAU,QAAQ,QAAQ,CAAC,WAC7B,QAAQ,MAAM,CAAC,CAAA,IAAK,MAAM,WAC1B;eAAI;YAAS;SAAQ;QACzB,eAAe,+BAA+B;IAChD;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;sBACvB,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,qIAAA,CAAA,eAAY;wBAAC,WAAU;kCACtB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAuC;;;;;;;;;;;kCAMhE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,6LAAC,mIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,WAAU;;8DACrC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG9B,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAc,WAAU;;8DACzC,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;8CAMjC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIhC,6LAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAY,WAAU;0FAAiB;;;;;;0FAGtD,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,OAAO,SAAS,SAAS;gFACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;gFAC3D,WAAW,CAAC,0DAA0D,EACpE,OAAO,SAAS,GAAG,mBAAmB,IACtC;gFACF,aAAY;;;;;;4EAEb,OAAO,SAAS,kBACf,6LAAC;gFAAE,WAAU;;kGACX,6LAAC,uNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFACtB,OAAO,SAAS;;;;;;;;;;;;;kFAIvB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAW,WAAU;0FAAiB;;;;;;0FAGrD,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,OAAO,SAAS,QAAQ;gFACxB,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;gFAC1D,WAAW,CAAC,0DAA0D,EACpE,OAAO,QAAQ,GAAG,mBAAmB,IACrC;gFACF,aAAY;;;;;;4EAEb,OAAO,QAAQ,kBACd,6LAAC;gFAAE,WAAU;;kGACX,6LAAC,uNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFACtB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;0EAMxB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAQ,WAAU;kFAAiB;;;;;;kFAGlD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,MAAK;gFACL,OAAO,SAAS,KAAK;gFACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gFACvD,WAAW,CAAC,gEAAgE,EAC1E,OAAO,KAAK,GAAG,mBAAmB,IAClC;gFACF,aAAY;;;;;;;;;;;;oEAGf,OAAO,KAAK,kBACX,6LAAC;wEAAE,WAAU;;0FACX,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EACtB,OAAO,KAAK;;;;;;;;;;;;;0EAKnB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAQ,WAAU;kFAAiB;;;;;;kFAGlD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,MAAK;gFACL,OAAO,SAAS,KAAK;gFACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gFACvD,WAAW,CAAC,gEAAgE,EAC1E,OAAO,KAAK,GAAG,mBAAmB,IAClC;gFACF,aAAY;;;;;;;;;;;;oEAGf,OAAO,KAAK,kBACX,6LAAC;wEAAE,WAAU;;0FACX,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EACtB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;0DAQvB,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIlC,6LAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAS,WAAU;kFAAiB;;;;;;kFAGnD,6LAAC,oIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,OAAO,SAAS,OAAO,CAAC,MAAM;wEAC9B,UAAU,CAAC,IAAM,eAAe,kBAAkB,EAAE,MAAM,CAAC,KAAK;wEAChE,WAAW,CAAC,0DAA0D,EACpE,OAAO,MAAM,GAAG,mBAAmB,IACnC;wEACF,aAAY;;;;;;oEAEb,OAAO,MAAM,kBACZ,6LAAC;wEAAE,WAAU;;0FACX,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EACtB,OAAO,MAAM;;;;;;;;;;;;;0EAKpB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAO,WAAU;0FAAiB;;;;;;0FAGjD,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,OAAO,SAAS,OAAO,CAAC,IAAI;gFAC5B,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;gFAC9D,WAAW,CAAC,0DAA0D,EACpE,OAAO,IAAI,GAAG,mBAAmB,IACjC;gFACF,aAAY;;;;;;4EAEb,OAAO,IAAI,kBACV,6LAAC;gFAAE,WAAU;;kGACX,6LAAC,uNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFACtB,OAAO,IAAI;;;;;;;;;;;;;kFAIlB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAQ,WAAU;0FAAiB;;;;;;0FAGlD,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,OAAO,SAAS,OAAO,CAAC,KAAK;gFAC7B,UAAU,CAAC,IAAM,eAAe,iBAAiB,EAAE,MAAM,CAAC,KAAK;gFAC/D,WAAW,CAAC,0DAA0D,EACpE,OAAO,KAAK,GAAG,mBAAmB,IAClC;gFACF,aAAY;;;;;;4EAEb,OAAO,KAAK,kBACX,6LAAC;gFAAE,WAAU;;kGACX,6LAAC,uNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFACtB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;0EAMrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAAiB;;;;;;0FAGpD,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,OAAO,SAAS,OAAO,CAAC,OAAO;gFAC/B,UAAU,CAAC,IAAM,eAAe,mBAAmB,EAAE,MAAM,CAAC,KAAK;gFACjE,WAAW,CAAC,0DAA0D,EACpE,OAAO,OAAO,GAAG,mBAAmB,IACpC;gFACF,aAAY;;;;;;4EAEb,OAAO,OAAO,kBACb,6LAAC;gFAAE,WAAU;;kGACX,6LAAC,uNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFACtB,OAAO,OAAO;;;;;;;;;;;;;kFAIrB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAAiB;;;;;;0FAGpD,6LAAC,qIAAA,CAAA,SAAM;gFACL,OAAO,SAAS,OAAO,CAAC,OAAO;gFAC/B,eAAe,CAAC,QAAU,eAAe,mBAAmB;;kGAE5D,6LAAC,qIAAA,CAAA,gBAAa;wFAAC,WAAU;kGACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kGAEd,6LAAC,qIAAA,CAAA,gBAAa;;0GACZ,6LAAC,qIAAA,CAAA,aAAU;gGAAC,OAAM;0GAAgB;;;;;;0GAClC,6LAAC,qIAAA,CAAA,aAAU;gGAAC,OAAM;0GAAS;;;;;;0GAC3B,6LAAC,qIAAA,CAAA,aAAU;gGAAC,OAAM;0GAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAW3C,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAc,WAAU;;sDACzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,UAAU;oDACV,WAAU;oDACV,OAAO;wDAAE,aAAa;wDAAa,OAAO;oDAAY;;wDAErD,6BACC,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAErB,eAAe,iBAAiB;;;;;;;;;;;;;sDAIrC,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mIAAA,CAAA,OAAI;;8EACH,6LAAC,mIAAA,CAAA,aAAU;;sFACT,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;sFAA2B;;;;;;sFAChD,6LAAC;4EAAE,WAAU;sFAAyB;;;;;;;;;;;;8EAExC,6LAAC,mIAAA,CAAA,cAAW;8EACV,cAAA,6LAAC;wEAAI,WAAU;kFACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;gFAAkB,WAAU;;kGAC3B,6LAAC,uIAAA,CAAA,WAAQ;wFACP,IAAI,KAAK,EAAE;wFACX,SAAS,SAAS,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE;wFAC1D,iBAAiB,IAAM,iBAAiB,KAAK,EAAE;wFAC/C,WAAU;;;;;;kGAEZ,6LAAC,oIAAA,CAAA,QAAK;wFAAC,SAAS,KAAK,EAAE;wFAAE,WAAU;;4FAChC,KAAK,KAAK;4FACV,KAAK,OAAO,kBACX,6LAAC,oIAAA,CAAA,QAAK;gGAAC,SAAQ;gGAAY,WAAU;0GAAwB;;;;;;;;;;;;;+EAVzD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;sEAqBzB,6LAAC,mIAAA,CAAA,OAAI;;8EACH,6LAAC,mIAAA,CAAA,aAAU;;sFACT,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;sFAA2B;;;;;;sFAChD,6LAAC;4EAAE,WAAU;sFAAyB;;;;;;;;;;;;8EAExC,6LAAC,mIAAA,CAAA,cAAW;8EACV,cAAA,6LAAC;wEAAI,WAAU;kFACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,6LAAC;gFAAmB,WAAU;;kGAC5B,6LAAC,uIAAA,CAAA,WAAQ;wFACP,IAAI,MAAM,EAAE;wFACZ,SAAS,SAAS,WAAW,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE;wFAC/D,iBAAiB,IAAM,YAAY,MAAM,EAAE;wFAC3C,WAAU;;;;;;kGAEZ,6LAAC,oIAAA,CAAA,QAAK;wFAAC,SAAS,MAAM,EAAE;wFAAE,WAAU;;0GAClC,6LAAC;gGACC,WAAU;gGACV,OAAO;oGAAE,iBAAiB,MAAM,GAAG;gGAAC;;;;;;4FAErC,MAAM,KAAK;;;;;;;;+EAZN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAsB5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mIAAA,CAAA,OAAI;;8EACH,6LAAC,mIAAA,CAAA,aAAU;8EACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wEAAC,WAAU;;0FACnB,6LAAC,qNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;8EAItC,6LAAC,mIAAA,CAAA,cAAW;8EACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEACL,OAAO,SAAS,WAAW,CAAC,WAAW;wEACvC,eAAe,CAAC,QAAU,eAAe,2BAA2B;;0FAEpE,6LAAC,qIAAA,CAAA,gBAAa;gFAAC,WAAU;0FACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,6LAAC,qIAAA,CAAA,gBAAa;0FACX,aAAa,GAAG,CAAC,CAAC,sBACjB,6LAAC,qIAAA,CAAA,aAAU;wFAAmB,OAAO,MAAM,KAAK;kGAC7C,MAAM,KAAK;uFADG,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAStC,6LAAC,mIAAA,CAAA,OAAI;;8EACH,6LAAC,mIAAA,CAAA,aAAU;8EACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wEAAC,WAAU;kFAA2B;;;;;;;;;;;8EAElD,6LAAC,mIAAA,CAAA,cAAW;8EACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEACL,OAAO,SAAS,WAAW,CAAC,mBAAmB;wEAC/C,eAAe,CAAC,QAAU,eAAe,mCAAmC;;0FAE5E,6LAAC,qIAAA,CAAA,gBAAa;gFAAC,WAAU;0FACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0FAEd,6LAAC,qIAAA,CAAA,gBAAa;0FACX,qBAAqB,GAAG,CAAC,CAAC,uBACzB,6LAAC,qIAAA,CAAA,aAAU;wFAAoB,OAAO,OAAO,KAAK;kGAC/C,OAAO,KAAK;uFADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;sEASvC,6LAAC,mIAAA,CAAA,OAAI;;8EACH,6LAAC,mIAAA,CAAA,aAAU;;sFACT,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;;8FACnB,6LAAC,2NAAA,CAAA,gBAAa;oFAAC,WAAU;;;;;;gFAAY;;;;;;;sFAGvC,6LAAC;4EAAE,WAAU;sFAAyB;;;;;;;;;;;;8EAIxC,6LAAC,mIAAA,CAAA,cAAW;8EACV,cAAA,6LAAC,uIAAA,CAAA,WAAQ;wEACP,OAAO,SAAS,KAAK;wEACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;wEACvD,WAAU;wEACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,aAAa;oCACf;8CAEC,0BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD;GA5lBgB;;QACU,uIAAA,CAAA,WAAQ;;;KADlB", "debugId": null}}, {"offset": {"line": 3454, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/customers/customers-page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport { \r\n  Users, \r\n  ArrowUp, \r\n  Star, \r\n  DollarSign,\r\n  Search,\r\n  MoreHorizontal,\r\n  Edit,\r\n  ExternalLink,\r\n  Trash2,\r\n  MapPin,\r\n  Phone,\r\n  Mail,\r\n  Calendar,\r\n  Plus,\r\n  Download,\r\n  Eye,\r\n  CheckCircle,\r\n  Clock,\r\n  X\r\n} from 'lucide-react'\r\n\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuSeparator\r\n} from '@/components/ui/dropdown-menu'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { NewCustomerModal } from '@/components/customers/new-customer-modal'\r\n\r\n// Types\r\ninterface CustomerFormData {\r\n  firstName: string\r\n  lastName: string\r\n  email: string\r\n  phone: string\r\n  address: {\r\n    street: string\r\n    city: string\r\n    state: string\r\n    zipCode: string\r\n    country: string\r\n  }\r\n  preferences: {\r\n    windowTypes: string[]\r\n    preferredColors: string[]\r\n    budgetRange: string\r\n    communicationMethod: string\r\n  }\r\n  notes: string\r\n}\r\n\r\n// Types\r\ninterface Customer {\r\n  id: string\r\n  name: string\r\n  email: string\r\n  phone: string\r\n  address: string\r\n  status: string\r\n  lastProject: string | null\r\n  totalRevenue: number\r\n  projectCount: number\r\n  preferences: string[]\r\n  avatar: string | null\r\n}\r\n\r\n// Mock data for customers\r\nconst mockCustomers: Customer[] = [\r\n  {\r\n    id: '1',\r\n    name: 'Sarah Johnson',\r\n    email: '<EMAIL>',\r\n    phone: '(*************',\r\n    address: '123 Maple Street, Springfield, IL 62704',\r\n    status: 'Active',\r\n    lastProject: '2024-12-15',\r\n    totalRevenue: 2840,\r\n    projectCount: 3,\r\n    preferences: ['Blinds', 'Shutters'],\r\n    avatar: null\r\n  },\r\n  {\r\n    id: '2',\r\n    name: 'Robert Chen',\r\n    email: '<EMAIL>',\r\n    phone: '(*************',\r\n    address: '456 Oak Avenue, Springfield, IL 62701',\r\n    status: 'Active',\r\n    lastProject: '2025-01-08',\r\n    totalRevenue: 1560,\r\n    projectCount: 2,\r\n    preferences: ['Shades'],\r\n    avatar: null\r\n  },\r\n  {\r\n    id: '3',\r\n    name: 'Emma Davis',\r\n    email: '<EMAIL>',\r\n    phone: '(*************',\r\n    address: '789 Pine Road, Springfield, IL 62702',\r\n    status: 'Pending',\r\n    lastProject: null,\r\n    totalRevenue: 0,\r\n    projectCount: 0,\r\n    preferences: ['Shutters', 'Shades'],\r\n    avatar: null\r\n  },\r\n  {\r\n    id: '4',\r\n    name: 'Michael Williams',\r\n    email: '<EMAIL>',\r\n    phone: '(*************',\r\n    address: '321 Elm Street, Springfield, IL 62703',\r\n    status: 'Active',\r\n    lastProject: '2024-11-22',\r\n    totalRevenue: 4250,\r\n    projectCount: 5,\r\n    preferences: ['Blinds', 'Shutters', 'Shades'],\r\n    avatar: null\r\n  },\r\n  {\r\n    id: '5',\r\n    name: 'Lisa Anderson',\r\n    email: '<EMAIL>',\r\n    phone: '(*************',\r\n    address: '654 Cedar Lane, Springfield, IL 62705',\r\n    status: 'Inactive',\r\n    lastProject: '2024-08-14',\r\n    totalRevenue: 890,\r\n    projectCount: 1,\r\n    preferences: ['Blinds'],\r\n    avatar: null\r\n  }\r\n]\r\n\r\nconst statsData = [\r\n  {\r\n    title: 'Total Customers',\r\n    value: '89',\r\n    change: '-2%',\r\n    icon: Users,\r\n    trend: 'down'\r\n  },\r\n  {\r\n    title: 'New Customers',\r\n    value: '12',\r\n    change: '+5%',\r\n    icon: ArrowUp,\r\n    trend: 'up'\r\n  },\r\n  {\r\n    title: 'High-Value Clients',\r\n    value: '25',\r\n    change: '',\r\n    icon: Star,\r\n    trend: 'neutral'\r\n  },\r\n  {\r\n    title: 'Avg Revenue/Customer',\r\n    value: '$520',\r\n    change: '+8%',\r\n    icon: DollarSign,\r\n    trend: 'up'\r\n  }\r\n]\r\n\r\nexport default function CustomersPage() {\r\n  const [searchQuery, setSearchQuery] = useState('')\r\n  const [statusFilter, setStatusFilter] = useState('all')\r\n  const [preferenceFilter, setPreferenceFilter] = useState('all')\r\n  const [sortBy, setSortBy] = useState('name')\r\n  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([])\r\n  const [showNewCustomerModal, setShowNewCustomerModal] = useState(false)\r\n  const [currentPage, setCurrentPage] = useState(1)\r\n  const itemsPerPage = 10\r\n\r\n  // Filter and sort customers\r\n  const filteredCustomers = mockCustomers.filter(customer => {\r\n    const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n                         customer.email.toLowerCase().includes(searchQuery.toLowerCase())\r\n    \r\n    const matchesStatus = statusFilter === 'all' || customer.status.toLowerCase() === statusFilter.toLowerCase()\r\n    \r\n    const matchesPreference = preferenceFilter === 'all' || \r\n                             customer.preferences.some(pref => pref.toLowerCase() === preferenceFilter.toLowerCase())\r\n    \r\n    return matchesSearch && matchesStatus && matchesPreference\r\n  }).sort((a, b) => {\r\n    switch (sortBy) {\r\n      case 'name':\r\n        return a.name.localeCompare(b.name)\r\n      case 'date':\r\n        return new Date(b.lastProject || 0).getTime() - new Date(a.lastProject || 0).getTime()\r\n      case 'revenue':\r\n        return b.totalRevenue - a.totalRevenue\r\n      default:\r\n        return 0\r\n    }\r\n  })\r\n\r\n  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage)\r\n  const paginatedCustomers = filteredCustomers.slice(\r\n    (currentPage - 1) * itemsPerPage,\r\n    currentPage * itemsPerPage\r\n  )\r\n\r\n  const handleSelectCustomer = (customerId: string) => {\r\n    setSelectedCustomers(prev => \r\n      prev.includes(customerId)\r\n        ? prev.filter(id => id !== customerId)\r\n        : [...prev, customerId]\r\n    )\r\n  }\r\n\r\n  const handleSelectAll = () => {\r\n    if (selectedCustomers.length === paginatedCustomers.length) {\r\n      setSelectedCustomers([])\r\n    } else {\r\n      setSelectedCustomers(paginatedCustomers.map(customer => customer.id))\r\n    }\r\n  }\r\n\r\n  const getStatusBadgeVariant = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'active':\r\n        return 'default'\r\n      case 'pending':\r\n        return 'secondary'\r\n      case 'inactive':\r\n        return 'outline'\r\n      default:\r\n        return 'outline'\r\n    }\r\n  }\r\n\r\n  const getStatusIcon = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'active':\r\n        return CheckCircle\r\n      case 'pending':\r\n        return Clock\r\n      default:\r\n        return X\r\n    }\r\n  }\r\n\r\n  // Add customer save handler\r\n  const handleSaveCustomer = async (customerData: CustomerFormData) => {\r\n    try {\r\n      // Mock API call - in real app, this would POST to your backend\r\n      console.log('Saving customer:', customerData)\r\n      \r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, 1000))\r\n      \r\n      // Here you would typically:\r\n      // 1. POST to your API endpoint\r\n      // 2. Update the local state\r\n      // 3. Show success notification\r\n      \r\n      // Mock success - in real app, you'd refresh the customer list\r\n      console.log('Customer saved successfully')\r\n    } catch (error) {\r\n      console.error('Error saving customer:', error)\r\n      throw error\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex items-center justify-between\"\r\n      >\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-foreground\">Customers</h1>\r\n          <p className=\"text-muted-foreground\">Manage your client base and preferences</p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-3\">\r\n          <Badge variant=\"secondary\" className=\"text-xs\">\r\n            Saturday, July 12, 2025\r\n          </Badge>\r\n          <Button className=\"bg-primary hover:bg-primary/90\" onClick={() => setShowNewCustomerModal(true)}>\r\n            <Plus className=\"h-4 w-4 mr-2\" />\r\n            New Customer\r\n          </Button>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n        {statsData.map((stat, index) => (\r\n          <motion.div\r\n            key={stat.title}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: index * 0.1 }}\r\n          >\r\n            <Card className=\"hover:border-border/80 transition-colors\">\r\n              <CardHeader className=\"pb-3\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"p-2 rounded-md bg-primary/10\">\r\n                    <stat.icon className=\"h-5 w-5 text-primary\" />\r\n                  </div>\r\n                  {stat.change && (\r\n                    <Badge \r\n                      variant={stat.trend === 'up' ? 'default' : stat.trend === 'down' ? 'destructive' : 'secondary'} \r\n                      className=\"text-xs\"\r\n                    >\r\n                      {stat.change}\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"space-y-1\">\r\n                  <h3 className=\"text-2xl font-bold text-foreground\">{stat.value}</h3>\r\n                  <p className=\"text-sm text-muted-foreground\">{stat.title}</p>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </motion.div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Filters and Search */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.4 }}\r\n      >\r\n        <Card>\r\n          <CardHeader className=\"pb-4\">\r\n            <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\r\n              <div className=\"flex flex-1 items-center space-x-4\">\r\n                <div className=\"relative flex-1 max-w-md\">\r\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\r\n                  <Input\r\n                    placeholder=\"Search customers...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    className=\"pl-10\"\r\n                  />\r\n                </div>\r\n                \r\n                <Select value={statusFilter} onValueChange={setStatusFilter}>\r\n                  <SelectTrigger className=\"w-32\">\r\n                    <SelectValue placeholder=\"Status\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"all\">All Status</SelectItem>\r\n                    <SelectItem value=\"active\">Active</SelectItem>\r\n                    <SelectItem value=\"pending\">Pending</SelectItem>\r\n                    <SelectItem value=\"inactive\">Inactive</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n\r\n                <Select value={preferenceFilter} onValueChange={setPreferenceFilter}>\r\n                  <SelectTrigger className=\"w-36\">\r\n                    <SelectValue placeholder=\"Preference\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"all\">All Products</SelectItem>\r\n                    <SelectItem value=\"blinds\">Blinds</SelectItem>\r\n                    <SelectItem value=\"shutters\">Shutters</SelectItem>\r\n                    <SelectItem value=\"shades\">Shades</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n\r\n                <Select value={sortBy} onValueChange={setSortBy}>\r\n                  <SelectTrigger className=\"w-32\">\r\n                    <SelectValue placeholder=\"Sort by\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"name\">Name</SelectItem>\r\n                    <SelectItem value=\"date\">Date Added</SelectItem>\r\n                    <SelectItem value=\"revenue\">Revenue</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Button variant=\"outline\" size=\"sm\">\r\n                  <Download className=\"h-4 w-4 mr-2\" />\r\n                  Export\r\n                </Button>\r\n                \r\n                {selectedCustomers.length > 0 && (\r\n                  <Badge variant=\"secondary\">\r\n                    {selectedCustomers.length} selected\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </CardHeader>\r\n        </Card>\r\n      </motion.div>\r\n\r\n      {/* Customer Table */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.5 }}\r\n      >\r\n        <Card>\r\n          <CardContent className=\"p-0\">\r\n            <div className=\"overflow-x-auto\">\r\n              <table className=\"w-full\">\r\n                <thead className=\"border-b border-border\">\r\n                  <tr className=\"bg-accent/20\">\r\n                    <th className=\"text-left p-4 w-12\">\r\n                      <Checkbox\r\n                        checked={selectedCustomers.length === paginatedCustomers.length && paginatedCustomers.length > 0}\r\n                        onCheckedChange={handleSelectAll}\r\n                      />\r\n                    </th>\r\n                    <th className=\"text-left p-4 font-medium text-foreground\">Customer</th>\r\n                    <th className=\"text-left p-4 font-medium text-foreground\">Contact</th>\r\n                    <th className=\"text-left p-4 font-medium text-foreground\">Address</th>\r\n                    <th className=\"text-left p-4 font-medium text-foreground\">Last Project</th>\r\n                    <th className=\"text-left p-4 font-medium text-foreground\">Status</th>\r\n                    <th className=\"text-left p-4 font-medium text-foreground\">Revenue</th>\r\n                    <th className=\"text-right p-4 w-16\"></th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {paginatedCustomers.map((customer, index) => {\r\n                    const StatusIcon = getStatusIcon(customer.status)\r\n                    return (\r\n                      <motion.tr\r\n                        key={customer.id}\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: index * 0.05 }}\r\n                        className=\"border-b border-border hover:bg-accent/30 transition-colors\"\r\n                      >\r\n                        <td className=\"p-4\">                            <Checkbox\r\n                              checked={selectedCustomers.includes(customer.id)}\r\n                              onCheckedChange={() => handleSelectCustomer(customer.id)}\r\n                              onClick={(e: React.MouseEvent) => e.stopPropagation()}\r\n                            />\r\n                        </td>\r\n                        <td className=\"p-4\">\r\n                          <div className=\"flex items-center space-x-3\">\r\n                            <Avatar className=\"h-8 w-8\">\r\n                              <AvatarImage src={customer.avatar || undefined} />\r\n                              <AvatarFallback className=\"bg-primary/10 text-primary\">\r\n                                {customer.name.split(' ').map(n => n[0]).join('')}\r\n                              </AvatarFallback>\r\n                            </Avatar>\r\n                            <div>\r\n                              <p className=\"font-medium text-foreground\">{customer.name}</p>\r\n                              <p className=\"text-xs text-muted-foreground\">\r\n                                {customer.projectCount} project{customer.projectCount !== 1 ? 's' : ''}\r\n                              </p>\r\n                            </div>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"p-4\">\r\n                          <div className=\"space-y-1\">\r\n                            <div className=\"flex items-center text-sm text-muted-foreground\">\r\n                              <Mail className=\"h-3 w-3 mr-2\" />\r\n                              {customer.email}\r\n                            </div>\r\n                            <div className=\"flex items-center text-sm text-muted-foreground\">\r\n                              <Phone className=\"h-3 w-3 mr-2\" />\r\n                              {customer.phone}\r\n                            </div>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"p-4\">\r\n                          <div className=\"flex items-center text-sm text-muted-foreground\">\r\n                            <MapPin className=\"h-3 w-3 mr-2 flex-shrink-0\" />\r\n                            <span className=\"truncate max-w-48\">{customer.address}</span>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"p-4\">\r\n                          {customer.lastProject ? (\r\n                            <div className=\"flex items-center text-sm text-muted-foreground\">\r\n                              <Calendar className=\"h-3 w-3 mr-2\" />\r\n                              {new Date(customer.lastProject).toLocaleDateString()}\r\n                            </div>\r\n                          ) : (\r\n                            <span className=\"text-sm text-muted-foreground\">No projects</span>\r\n                          )}\r\n                        </td>\r\n                        <td className=\"p-4\">\r\n                          <Badge variant={getStatusBadgeVariant(customer.status)} className=\"text-xs\">\r\n                            <StatusIcon className=\"h-3 w-3 mr-1\" />\r\n                            {customer.status}\r\n                          </Badge>\r\n                        </td>\r\n                        <td className=\"p-4\">\r\n                          <span className=\"font-medium text-foreground\">\r\n                            ${customer.totalRevenue.toLocaleString()}\r\n                          </span>\r\n                        </td>\r\n                        <td className=\"p-4\">\r\n                          <DropdownMenu>\r\n                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>\r\n                              <Button variant=\"ghost\" size=\"sm\">\r\n                                <MoreHorizontal className=\"h-4 w-4\" />\r\n                              </Button>\r\n                            </DropdownMenuTrigger>\r\n                            <DropdownMenuContent align=\"end\">\r\n                              <DropdownMenuItem>\r\n                                <Eye className=\"h-4 w-4 mr-2\" />\r\n                                View Details\r\n                              </DropdownMenuItem>\r\n                              <DropdownMenuItem>\r\n                                <Edit className=\"h-4 w-4 mr-2\" />\r\n                                Edit Customer\r\n                              </DropdownMenuItem>\r\n                              <DropdownMenuItem>\r\n                                <ExternalLink className=\"h-4 w-4 mr-2\" />\r\n                                View Projects\r\n                              </DropdownMenuItem>\r\n                              <DropdownMenuSeparator />\r\n                              <DropdownMenuItem className=\"text-destructive\">\r\n                                <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                                Delete\r\n                              </DropdownMenuItem>\r\n                            </DropdownMenuContent>\r\n                          </DropdownMenu>\r\n                        </td>\r\n                      </motion.tr>\r\n                    )\r\n                  })}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n\r\n            {/* Pagination */}\r\n            <div className=\"flex items-center justify-between p-4 border-t border-border\">\r\n              <div className=\"text-sm text-muted-foreground\">\r\n                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredCustomers.length)} of {filteredCustomers.length} customers\r\n              </div>\r\n              \r\n              <div className=\"flex items-center space-x-2\">\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\r\n                  disabled={currentPage === 1}\r\n                >\r\n                  Previous\r\n                </Button>\r\n                \r\n                <div className=\"flex items-center space-x-1\">\r\n                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n                    const page = i + 1\r\n                    return (\r\n                      <Button\r\n                        key={page}\r\n                        variant={currentPage === page ? \"default\" : \"outline\"}\r\n                        size=\"sm\"\r\n                        onClick={() => setCurrentPage(page)}\r\n                        className=\"w-8 h-8 p-0\"\r\n                      >\r\n                        {page}\r\n                      </Button>\r\n                    )\r\n                  })}\r\n                </div>\r\n                \r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}\r\n                  disabled={currentPage === totalPages}\r\n                >\r\n                  Next\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </motion.div>\r\n\r\n      {/* Quick Insights */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.6 }}\r\n        className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\"\r\n      >\r\n        <Card className=\"lg:col-span-2\">\r\n          <CardHeader>\r\n            <CardTitle>Top Customers by Revenue</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"space-y-3\">\r\n              {mockCustomers\r\n                .sort((a, b) => b.totalRevenue - a.totalRevenue)\r\n                .slice(0, 5)\r\n                .map((customer, index) => (\r\n                  <div key={customer.id} className=\"flex items-center justify-between p-3 rounded-md bg-accent/20\">\r\n                    <div className=\"flex items-center space-x-3\">\r\n                      <span className=\"text-sm font-medium text-muted-foreground w-4\">\r\n                        #{index + 1}\r\n                      </span>\r\n                      <Avatar className=\"h-6 w-6\">\r\n                        <AvatarFallback className=\"bg-primary/10 text-primary text-xs\">\r\n                          {customer.name.split(' ').map(n => n[0]).join('')}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <span className=\"text-sm font-medium text-foreground\">{customer.name}</span>\r\n                    </div>\r\n                    <span className=\"text-sm font-medium text-foreground\">\r\n                      ${customer.totalRevenue.toLocaleString()}\r\n                    </span>\r\n                  </div>\r\n                ))}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>AI Insights</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"space-y-4\">\r\n              <div className=\"p-3 rounded-md bg-primary/5 border border-primary/20\">\r\n                <p className=\"text-sm text-foreground font-medium mb-1\">Outreach Recommendation</p>\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  5 inactive clients haven&apos;t been contacted in 90+ days. Consider a follow-up campaign.\r\n                </p>\r\n              </div>\r\n              \r\n              <div className=\"p-3 rounded-md bg-accent/20\">\r\n                <p className=\"text-sm text-foreground font-medium mb-1\">Seasonal Opportunity</p>\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  12 customers have shown interest in shutters. Summer promotion recommended.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </motion.div>\r\n\r\n      {/* New Customer Modal */}\r\n      <NewCustomerModal\r\n        isOpen={showNewCustomerModal}\r\n        onClose={() => setShowNewCustomerModal(false)}\r\n        onSave={handleSaveCustomer}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AACA;AACA;AACA;AACA;AACA;AAOA;AAOA;AACA;;;AA9CA;;;;;;;;;;;;;AAqFA,0BAA0B;AAC1B,MAAM,gBAA4B;IAChC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;QACb,cAAc;QACd,cAAc;QACd,aAAa;YAAC;YAAU;SAAW;QACnC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;QACb,cAAc;QACd,cAAc;QACd,aAAa;YAAC;SAAS;QACvB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;QACb,cAAc;QACd,cAAc;QACd,aAAa;YAAC;YAAY;SAAS;QACnC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;QACb,cAAc;QACd,cAAc;QACd,aAAa;YAAC;YAAU;YAAY;SAAS;QAC7C,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;QACb,cAAc;QACd,cAAc;QACd,aAAa;YAAC;SAAS;QACvB,QAAQ;IACV;CACD;AAED,MAAM,YAAY;IAChB;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,+MAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe;IAErB,4BAA4B;IAC5B,MAAM,oBAAoB,cAAc,MAAM,CAAC,CAAA;QAC7C,MAAM,gBAAgB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC7D,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAElF,MAAM,gBAAgB,iBAAiB,SAAS,SAAS,MAAM,CAAC,WAAW,OAAO,aAAa,WAAW;QAE1G,MAAM,oBAAoB,qBAAqB,SACtB,SAAS,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,WAAW,OAAO,iBAAiB,WAAW;QAE9G,OAAO,iBAAiB,iBAAiB;IAC3C,GAAG,IAAI,CAAC,CAAC,GAAG;QACV,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,WAAW,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,IAAI,GAAG,OAAO;YACtF,KAAK;gBACH,OAAO,EAAE,YAAY,GAAG,EAAE,YAAY;YACxC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,KAAK,IAAI,CAAC,kBAAkB,MAAM,GAAG;IACxD,MAAM,qBAAqB,kBAAkB,KAAK,CAChD,CAAC,cAAc,CAAC,IAAI,cACpB,cAAc;IAGhB,MAAM,uBAAuB,CAAC;QAC5B,qBAAqB,CAAA,OACnB,KAAK,QAAQ,CAAC,cACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,cACzB;mBAAI;gBAAM;aAAW;IAE7B;IAEA,MAAM,kBAAkB;QACtB,IAAI,kBAAkB,MAAM,KAAK,mBAAmB,MAAM,EAAE;YAC1D,qBAAqB,EAAE;QACzB,OAAO;YACL,qBAAqB,mBAAmB,GAAG,CAAC,CAAA,WAAY,SAAS,EAAE;QACrE;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO,8NAAA,CAAA,cAAW;YACpB,KAAK;gBACH,OAAO,uMAAA,CAAA,QAAK;YACd;gBACE,OAAO,+LAAA,CAAA,IAAC;QACZ;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,+DAA+D;YAC/D,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,4BAA4B;YAC5B,+BAA+B;YAC/B,4BAA4B;YAC5B,+BAA+B;YAE/B,8DAA8D;YAC9D,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAU;;;;;;0CAG/C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAiC,SAAS,IAAM,wBAAwB;;kDACxF,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;kCAEjC,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;4CAEtB,KAAK,MAAM,kBACV,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAS,KAAK,KAAK,KAAK,OAAO,YAAY,KAAK,KAAK,KAAK,SAAS,gBAAgB;gDACnF,WAAU;0DAET,KAAK,MAAM;;;;;;;;;;;;;;;;;8CAKpB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsC,KAAK,KAAK;;;;;;0DAC9D,6LAAC;gDAAE,WAAU;0DAAiC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;uBAxBzD,KAAK,KAAK;;;;;;;;;;0BAiCrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;;;;;;;sDAId,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAc,eAAe;;8DAC1C,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;;;;;;;;;;;;;sDAIjC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAkB,eAAe;;8DAC9C,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;;;;;;;;;;;;;sDAI/B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAQ,eAAe;;8DACpC,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAKlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAItC,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDACZ,kBAAkB,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4DACP,SAAS,kBAAkB,MAAM,KAAK,mBAAmB,MAAM,IAAI,mBAAmB,MAAM,GAAG;4DAC/F,iBAAiB;;;;;;;;;;;kEAGrB,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,6LAAC;wDAAG,WAAU;;;;;;;;;;;;;;;;;sDAGlB,6LAAC;sDACE,mBAAmB,GAAG,CAAC,CAAC,UAAU;gDACjC,MAAM,aAAa,cAAc,SAAS,MAAM;gDAChD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oDAER,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO,QAAQ;oDAAK;oDAClC,WAAU;;sEAEV,6LAAC;4DAAG,WAAU;;gEAAM;8EAA4B,6LAAC,uIAAA,CAAA,WAAQ;oEACnD,SAAS,kBAAkB,QAAQ,CAAC,SAAS,EAAE;oEAC/C,iBAAiB,IAAM,qBAAqB,SAAS,EAAE;oEACvD,SAAS,CAAC,IAAwB,EAAE,eAAe;;;;;;;;;;;;sEAGzD,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qIAAA,CAAA,SAAM;wEAAC,WAAU;;0FAChB,6LAAC,qIAAA,CAAA,cAAW;gFAAC,KAAK,SAAS,MAAM,IAAI;;;;;;0FACrC,6LAAC,qIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACvB,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;kFAGlD,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAA+B,SAAS,IAAI;;;;;;0FACzD,6LAAC;gFAAE,WAAU;;oFACV,SAAS,YAAY;oFAAC;oFAAS,SAAS,YAAY,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;sEAK5E,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EACf,SAAS,KAAK;;;;;;;kFAEjB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,SAAS,KAAK;;;;;;;;;;;;;;;;;;sEAIrB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6LAAC;wEAAK,WAAU;kFAAqB,SAAS,OAAO;;;;;;;;;;;;;;;;;sEAGzD,6LAAC;4DAAG,WAAU;sEACX,SAAS,WAAW,iBACnB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB;;;;;;qFAGpD,6LAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;sEAGpD,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,sBAAsB,SAAS,MAAM;gEAAG,WAAU;;kFAChE,6LAAC;wEAAW,WAAU;;;;;;oEACrB,SAAS,MAAM;;;;;;;;;;;;sEAGpB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;;oEAA8B;oEAC1C,SAAS,YAAY,CAAC,cAAc;;;;;;;;;;;;sEAG1C,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kFACX,6LAAC,+IAAA,CAAA,sBAAmB;wEAAC,OAAO;wEAAC,SAAS,CAAC,IAAM,EAAE,eAAe;kFAC5D,cAAA,6LAAC,qIAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAQ,MAAK;sFAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAG9B,6LAAC,+IAAA,CAAA,sBAAmB;wEAAC,OAAM;;0FACzB,6LAAC,+IAAA,CAAA,mBAAgB;;kGACf,6LAAC,mMAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGlC,6LAAC,+IAAA,CAAA,mBAAgB;;kGACf,6LAAC,8MAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGnC,6LAAC,+IAAA,CAAA,mBAAgB;;kGACf,6LAAC,yNAAA,CAAA,eAAY;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAG3C,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0FACtB,6LAAC,+IAAA,CAAA,mBAAgB;gFAAC,WAAU;;kGAC1B,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;mDAzFtC,SAAS,EAAE;;;;;4CAiGtB;;;;;;;;;;;;;;;;;0CAMN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAgC;4CACnC,CAAC,cAAc,CAAC,IAAI,eAAgB;4CAAE;4CAAK,KAAK,GAAG,CAAC,cAAc,cAAc,kBAAkB,MAAM;4CAAE;4CAAK,kBAAkB,MAAM;4CAAC;;;;;;;kDAGpJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;gDACzD,UAAU,gBAAgB;0DAC3B;;;;;;0DAID,6LAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,CAAC;oDAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;gDAAY,GAAG,CAAC,GAAG;oDACnD,MAAM,OAAO,IAAI;oDACjB,qBACE,6LAAC,qIAAA,CAAA,SAAM;wDAEL,SAAS,gBAAgB,OAAO,YAAY;wDAC5C,MAAK;wDACL,SAAS,IAAM,eAAe;wDAC9B,WAAU;kEAET;uDANI;;;;;gDASX;;;;;;0DAGF,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,YAAY,OAAO;gDAClE,UAAU,gBAAgB;0DAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,cACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY,EAC9C,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,UAAU,sBACd,6LAAC;4CAAsB,WAAU;;8DAC/B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAgD;gEAC5D,QAAQ;;;;;;;sEAEZ,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;sEAGlD,6LAAC;4DAAK,WAAU;sEAAuC,SAAS,IAAI;;;;;;;;;;;;8DAEtE,6LAAC;oDAAK,WAAU;;wDAAsC;wDAClD,SAAS,YAAY,CAAC,cAAc;;;;;;;;2CAbhC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;kCAqB/B,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAK/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvD,6LAAC,8JAAA,CAAA,mBAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,wBAAwB;gBACvC,QAAQ;;;;;;;;;;;;AAIhB;GArewB;KAAA", "debugId": null}}]}