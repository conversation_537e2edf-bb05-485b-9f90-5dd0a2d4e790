"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[0],{704:(e,t,r)=>{r.d(t,{B8:()=>I,UC:()=>D,bL:()=>N,l9:()=>M});var n=r(2115),l=r(5185),a=r(6081),o=r(9196),i=r(8905),s=r(3655),d=r(4315),u=r(5845),c=r(1285),p=r(5155),v="Tabs",[f,h]=(0,a.A)(v,[o.RG]),m=(0,o.RG)(),[w,g]=f(v),y=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:l,defaultValue:a,orientation:o="horizontal",dir:i,activationMode:f="automatic",...h}=e,m=(0,d.jH)(i),[g,y]=(0,u.i)({prop:n,onChange:l,defaultProp:null!=a?a:"",caller:v});return(0,p.jsx)(w,{scope:r,baseId:(0,c.B)(),value:g,onValueChange:y,orientation:o,dir:m,activationMode:f,children:(0,p.jsx)(s.sG.div,{dir:m,"data-orientation":o,...h,ref:t})})});y.displayName=v;var x="TabsList",b=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...l}=e,a=g(x,r),i=m(r);return(0,p.jsx)(o.bL,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:n,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...l,ref:t})})});b.displayName=x;var S="TabsTrigger",C=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...i}=e,d=g(S,r),u=m(r),c=T(d.baseId,n),v=R(d.baseId,n),f=n===d.value;return(0,p.jsx)(o.q7,{asChild:!0,...u,focusable:!a,active:f,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":v,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...i,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(n)}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(n)}),onFocus:(0,l.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;f||a||!e||d.onValueChange(n)})})})});C.displayName=S;var j="TabsContent",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:l,forceMount:a,children:o,...d}=e,u=g(j,r),c=T(u.baseId,l),v=R(u.baseId,l),f=l===u.value,h=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:a||f,children:r=>{let{present:n}=r;return(0,p.jsx)(s.sG.div,{"data-state":f?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:v,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:n&&o})}})});function T(e,t){return"".concat(e,"-trigger-").concat(t)}function R(e,t){return"".concat(e,"-content-").concat(t)}k.displayName=j;var N=y,I=b,M=C,D=k},968:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(2115),l=r(3655),a=r(5155),o=n.forwardRef((e,t)=>(0,a.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},1007:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1788:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},4229:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5503:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(2115);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},6474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8715:(e,t,r)=>{r.d(t,{UC:()=>eL,YJ:()=>eV,In:()=>eE,q7:()=>eG,VF:()=>eF,p4:()=>e_,JU:()=>eB,ZL:()=>eA,bL:()=>eM,wn:()=>eO,PP:()=>eK,wv:()=>eU,l9:()=>eD,WT:()=>eP,LM:()=>eH});var n=r(2115),l=r(7650);function a(e,[t,r]){return Math.min(r,Math.max(t,e))}var o=r(5185),i=r(7328),s=r(6101),d=r(6081),u=r(4315),c=r(9178),p=r(2293),v=r(7900),f=r(1285),h=r(5152),m=r(4378),w=r(3655),g=r(9708),y=r(9033),x=r(5845),b=r(2712),S=r(5503),C=r(5155),j=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(w.sG.span,{...e,ref:t,style:{...j,...e.style}})).displayName="VisuallyHidden";var k=r(8168),T=r(3795),R=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],I="Select",[M,D,P]=(0,i.N)(I),[E,A]=(0,d.A)(I,[P,h.Bk]),L=(0,h.Bk)(),[H,V]=E(I),[B,G]=E(I),_=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:a,onOpenChange:o,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:v,disabled:m,required:w,form:g}=e,y=L(t),[b,S]=n.useState(null),[j,k]=n.useState(null),[T,R]=n.useState(!1),N=(0,u.jH)(c),[D,P]=(0,x.i)({prop:l,defaultProp:null!=a&&a,onChange:o,caller:I}),[E,A]=(0,x.i)({prop:i,defaultProp:s,onChange:d,caller:I}),V=n.useRef(null),G=!b||g||!!b.closest("form"),[_,F]=n.useState(new Set),K=Array.from(_).map(e=>e.props.value).join(";");return(0,C.jsx)(h.bL,{...y,children:(0,C.jsxs)(H,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:j,onValueNodeChange:k,valueNodeHasChildren:T,onValueNodeHasChildrenChange:R,contentId:(0,f.B)(),value:E,onValueChange:A,open:D,onOpenChange:P,dir:N,triggerPointerDownPosRef:V,disabled:m,children:[(0,C.jsx)(M.Provider,{scope:t,children:(0,C.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{F(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),G?(0,C.jsxs)(eT,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:v,value:E,onChange:e=>A(e.target.value),disabled:m,form:g,children:[void 0===E?(0,C.jsx)("option",{value:""}):null,Array.from(_)]},K):null]})})};_.displayName=I;var F="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...a}=e,i=L(r),d=V(F,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=D(r),v=n.useRef("touch"),[f,m,g]=eN(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eI(t,e,r);void 0!==n&&d.onValueChange(n.value)}),y=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(h.Mz,{asChild:!0,...i,children:(0,C.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eR(d.value)?"":void 0,...a,ref:c,onClick:(0,o.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==v.current&&y(e)}),onPointerDown:(0,o.m)(a.onPointerDown,e=>{v.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,o.m)(a.onKeyDown,e=>{let t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&R.includes(e.key)&&(y(),e.preventDefault())})})})});K.displayName=F;var O="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:a,placeholder:o="",...i}=e,d=V(O,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==a,p=(0,s.s)(t,d.onValueNodeChange);return(0,b.N)(()=>{u(c)},[u,c]),(0,C.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eR(d.value)?(0,C.jsx)(C.Fragment,{children:o}):a})});U.displayName=O;var W=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});W.displayName="SelectIcon";var z=e=>(0,C.jsx)(m.Z,{asChild:!0,...e});z.displayName="SelectPortal";var q="SelectContent",Z=n.forwardRef((e,t)=>{let r=V(q,e.__scopeSelect),[a,o]=n.useState();return((0,b.N)(()=>{o(new DocumentFragment)},[]),r.open)?(0,C.jsx)(Q,{...e,ref:t}):a?l.createPortal((0,C.jsx)(Y,{scope:e.__scopeSelect,children:(0,C.jsx)(M.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),a):null});Z.displayName=q;var[Y,J]=E(q),X=(0,g.TL)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:f,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S,...j}=e,R=V(q,r),[N,I]=n.useState(null),[M,P]=n.useState(null),E=(0,s.s)(t,e=>I(e)),[A,L]=n.useState(null),[H,B]=n.useState(null),G=D(r),[_,F]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(N)return(0,k.Eq)(N)},[N]),(0,p.Oh)();let O=n.useCallback(e=>{let[t,...r]=G().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&M&&(M.scrollTop=0),r===n&&M&&(M.scrollTop=M.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[G,M]),U=n.useCallback(()=>O([A,N]),[O,A,N]);n.useEffect(()=>{_&&U()},[_,U]);let{onOpenChange:W,triggerPointerDownPosRef:z}=R;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{var r,n,l,a;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=z.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(a=null==(n=z.current)?void 0:n.y)?a:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||W(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,W,z]),n.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[Z,J]=eN(e=>{let t=G().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eI(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==R.value&&R.value===t||n)&&(L(e),n&&(K.current=!0))},[R.value]),et=n.useCallback(()=>null==N?void 0:N.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==R.value&&R.value===t||n)&&B(e)},[R.value]),en="popper"===l?ee:$,el=en===ee?{side:u,sideOffset:f,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(Y,{scope:r,content:N,viewport:M,onViewportChange:P,itemRefCallback:Q,selectedItem:A,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:U,selectedItemText:H,position:l,isPositioned:_,searchRef:Z,children:(0,C.jsx)(T.A,{as:X,allowPinchZoom:!0,children:(0,C.jsx)(v.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,o.m)(a,e=>{var t;null==(t=R.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>F(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,o.m)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||J(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=G().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>O(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...o}=e,i=V(q,r),d=J(q,r),[u,c]=n.useState(null),[p,v]=n.useState(null),f=(0,s.s)(t,e=>v(e)),h=D(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:S,focusSelectedItem:j}=d,k=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&x&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,o=r.left-l,i=e.left-o,s=e.width+i,d=Math.max(s,t.width),c=a(o,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,o=window.innerWidth-r.right-l,i=window.innerWidth-e.right-o,s=e.width+i,d=Math.max(s,t.width),c=a(o,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let o=h(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),v=parseInt(c.borderTopWidth,10),f=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=v+f+d+parseInt(c.paddingBottom,10)+w,b=Math.min(5*x.offsetHeight,g),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,R=x.offsetHeight/2,N=v+f+(x.offsetTop+R);if(N<=T){let e=o.length>0&&x===o[o.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-T,R+(e?k:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+w);u.style.height=N+t+"px"}else{let e=o.length>0&&x===o[0].ref.current;u.style.top="0px";let t=Math.max(T,v+y.offsetTop+(e?j:0)+R);u.style.height=t+(g-N)+"px",y.scrollTop=N-T+y.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=b+"px",u.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,u,p,y,x,S,i.dir,l]);(0,b.N)(()=>k(),[k]);let[T,R]=n.useState();(0,b.N)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===g.current&&(k(),null==j||j(),g.current=!1)},[k,j]);return(0,C.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,C.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,C.jsx)(w.sG.div,{...o,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});$.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...a}=e,o=L(r);return(0,C.jsx)(h.UC,{...o,...a,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=E(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...a}=e,i=J(en,r),d=er(en,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,C.jsx)(M.Slot,{scope:r,children:(0,C.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,o.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let a=l+e,o=Math.min(n,a),i=a-o;r.style.height=o+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var ea="SelectGroup",[eo,ei]=E(ea),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,f.B)();return(0,C.jsx)(eo,{scope:r,id:l,children:(0,C.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});es.displayName=ea;var ed="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(ed,r);return(0,C.jsx)(w.sG.div,{id:l.id,...n,ref:t})});eu.displayName=ed;var ec="SelectItem",[ep,ev]=E(ec),ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:a=!1,textValue:i,...d}=e,u=V(ec,r),c=J(ec,r),p=u.value===l,[v,h]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),y=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,a)}),x=(0,f.B)(),b=n.useRef("touch"),S=()=>{a||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ep,{scope:r,value:l,disabled:a,textId:x,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,C.jsx)(M.ItemSlot,{scope:r,value:l,disabled:a,textValue:v,children:(0,C.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...d,ref:y,onFocus:(0,o.m)(d.onFocus,()=>g(!0)),onBlur:(0,o.m)(d.onBlur,()=>g(!1)),onClick:(0,o.m)(d.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,o.m)(d.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,o.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,o.m)(d.onPointerMove,e=>{if(b.current=e.pointerType,a){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,o.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,o.m)(d.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(N.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ef.displayName=ec;var eh="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:a,style:o,...i}=e,d=V(eh,r),u=J(eh,r),c=ev(eh,r),p=G(eh,r),[v,f]=n.useState(null),h=(0,s.s)(t,e=>f(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),m=null==v?void 0:v.textContent,g=n.useMemo(()=>(0,C.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=p;return(0,b.N)(()=>(y(g),()=>x(g)),[y,x,g]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(w.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});em.displayName=eh;var ew="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ev(ew,r).isSelected?(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=ew;var ey="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=J(ey,e.__scopeSelect),l=er(ey,e.__scopeSelect),[a,o]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){o(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=ey;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=J(eb,e.__scopeSelect),l=er(eb,e.__scopeSelect),[a,o]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;o(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...a}=e,i=J("SelectScrollButton",r),s=n.useRef(null),d=D(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,b.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,o.m)(a.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,o.m)(a.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,o.m)(a.onPointerLeave,()=>{u()})})}),ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})});ej.displayName="SelectSeparator";var ek="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=L(r),a=V(ek,r),o=J(ek,r);return a.open&&"popper"===o.position?(0,C.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=ek;var eT=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...a}=e,o=n.useRef(null),i=(0,s.s)(t,o),d=(0,S.Z)(l);return n.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(d!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[d,l]),(0,C.jsx)(w.sG.select,{...a,style:{...j,...a.style},ref:i,defaultValue:l})});function eR(e){return""===e||void 0===e}function eN(e){let t=(0,y.c)(e),r=n.useRef(""),l=n.useRef(0),a=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),o=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,a,o]}function eI(e,t,r){var n,l;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=r?e.indexOf(r):-1,i=(n=e,l=Math.max(o,0),n.map((e,t)=>n[(l+t)%n.length]));1===a.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return s!==r?s:void 0}eT.displayName="SelectBubbleInput";var eM=_,eD=K,eP=U,eE=W,eA=z,eL=Z,eH=el,eV=es,eB=eu,eG=ef,e_=em,eF=eg,eK=ex,eO=eS,eU=ej}}]);