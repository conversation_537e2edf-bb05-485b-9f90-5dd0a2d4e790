(()=>{var e={};e.id=826,e.ids=[826],e.modules={13:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(687);s(3210);var r=s(8148),i=s(4780);function n({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},22:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},502:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(7413),r=s(8054),i=s(4213);function n(){return(0,a.jsx)(r.DashboardLayout,{children:(0,a.jsx)(i.default,{})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1860:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},1904:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=s(5239),r=s(8088),i=s(8170),n=s.n(i),l=s(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["schedule",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,502)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\schedule\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\schedule\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/schedule/page",pathname:"/schedule",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3045:(e,t,s)=>{"use strict";s.d(t,{default:()=>V});var a=s(687),r=s(3210),i=s(6001),n=s(2688);let l=(0,n.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var d=s(1158),o=s(6474),c=s(228);let m=(0,n.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),x=(0,n.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var u=s(8469),h=s(6349),p=s(9270),g=s(7992),f=s(9523),j=s(9667),b=s(4493),v=s(6834),N=s(2584);let y=(0,n.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),w=(0,n.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function k({selectedDate:e,events:t,onDateSelect:s,onEventClick:r,onNavigate:n,onNewAppointment:l}){let d=new Date,c=e.getMonth(),m=e.getFullYear(),x=new Date(m,c,1),u=new Date(m,c+1,0),h=x.getDay(),p=u.getDate(),g=[],j=new Date(m,c-1,0);for(let e=h-1;e>=0;e--)g.push({date:j.getDate()-e,isCurrentMonth:!1,isToday:!1,fullDate:new Date(m,c-1,j.getDate()-e)});for(let e=1;e<=p;e++){let t=new Date(m,c,e);g.push({date:e,isCurrentMonth:!0,isToday:t.toDateString()===d.toDateString(),fullDate:t})}let b=42-g.length;for(let e=1;e<=b;e++)g.push({date:e,isCurrentMonth:!1,isToday:!1,fullDate:new Date(m,c+1,e)});let v=e=>{let s=e.toISOString().split("T")[0];return t.filter(e=>e.date===s)},N=e=>{switch(e){case"pending":return"bg-amber-500";case"assigned":return"bg-blue-500";case"in-progress":return"bg-orange-500";case"completed":return"bg-green-500";case"conflict":return"bg-red-500";default:return"bg-gray-400"}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-slate-700",children:[["January","February","March","April","May","June","July","August","September","October","November","December"][c]," ",m]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>n("prev"),className:"h-8 w-8 p-0",children:(0,a.jsx)(y,{className:"h-4 w-4"})}),(0,a.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>n("next"),className:"h-8 w-8 p-0",children:(0,a.jsx)(w,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("div",{className:"grid grid-cols-7 mb-2",children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>(0,a.jsx)("div",{className:"text-center text-sm font-medium text-slate-600 py-2",children:e},e))}),(0,a.jsx)("div",{className:"grid grid-cols-7 gap-1",children:g.map((t,n)=>{let d=v(t.fullDate),c=e.toDateString()===t.fullDate.toDateString();return(0,a.jsxs)(i.P.div,{className:`
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  ${!t.isCurrentMonth?"opacity-40":""}
                  ${t.isToday?"ring-2 ring-amber-400 bg-amber-50":""}
                  ${c?"ring-2 ring-blue-400 bg-blue-50":""}
                `,whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>s(t.fullDate),children:[(0,a.jsx)("div",{className:`
                  text-sm font-medium mb-1
                  ${t.isToday?"text-amber-700":t.isCurrentMonth?"text-slate-700":"text-slate-400"}
                `,children:t.date}),(0,a.jsxs)("div",{className:"space-y-1",children:[d.slice(0,3).map(e=>(0,a.jsxs)(i.P.div,{className:`
                        text-xs p-1 rounded text-white truncate cursor-pointer
                        ${N(e.status)}
                      `,whileHover:{scale:1.05},onClick:t=>{t.stopPropagation(),r(e)},title:`${e.time} - ${e.title}`,children:[e.time," ",e.title]},e.id)),d.length>3&&(0,a.jsxs)("div",{className:"text-xs text-slate-500 font-medium",children:["+",d.length-3," more"]})]}),(0,a.jsx)(i.P.button,{className:"absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200",initial:!1,animate:{opacity:0},whileHover:{opacity:1},onClick:e=>{e.stopPropagation(),l(t.fullDate)},title:"Add appointment",children:(0,a.jsx)(o.A,{className:"h-3 w-3"})})]},n)})})]}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-4 p-4 border-t border-gray-200 bg-gray-50",children:[{status:"pending",label:"Pending"},{status:"assigned",label:"Assigned"},{status:"in-progress",label:"In Progress"},{status:"completed",label:"Completed"},{status:"conflict",label:"Conflict"}].map(({status:e,label:t})=>(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:`w-3 h-3 rounded ${N(e)}`}),(0,a.jsx)("span",{className:"text-xs text-slate-600",children:t})]},e))})]})}var C=s(8869);function D({date:e,events:t,onEventClick:s,onTimeSlotClick:r}){let n=[];for(let e=8;e<=18;e++)n.push({time:`${e.toString().padStart(2,"0")}:00`,hour:e});let l=e=>{let[t,s]=e.startTime.split(":").map(Number),a=((t-8)*60+s)/600*100,r=e.duration/600*100;return{top:`${Math.max(0,a)}%`,height:`${Math.min(r,100-a)}%`}},d=e=>{switch(e){case"pending":return"bg-amber-500 border-amber-600";case"assigned":return"bg-blue-500 border-blue-600";case"in-progress":return"bg-orange-500 border-orange-600";case"completed":return"bg-green-500 border-green-600";case"conflict":return"bg-red-500 border-red-600 animate-pulse";default:return"bg-gray-400 border-gray-500"}},c=e=>{let[t,s]=e.split(":").map(Number);return`${t%12||12}:${s.toString().padStart(2,"0")} ${t>=12?"PM":"AM"}`},m=(()=>{let t=new Date;if(t.toDateString()!==e.toDateString())return null;let s=t.getHours(),a=t.getMinutes();return s<8||s>18?null:Math.min(Math.max(((s-8)*60+a)/10/60*100,0),100)})();return(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-slate-700",children:e.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})}),(0,a.jsxs)("p",{className:"text-sm text-slate-500",children:[t.length," ",1===t.length?"appointment":"appointments"," scheduled"]})]}),(0,a.jsxs)(f.$,{onClick:()=>r("09:00"),className:"bg-amber-500 hover:bg-amber-600 text-white",size:"sm",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Add Appointment"]})]}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-20 flex-shrink-0",children:n.map(({time:e})=>(0,a.jsx)("div",{className:"h-16 flex items-start pt-2",children:(0,a.jsx)("span",{className:"text-xs text-slate-500 font-medium",children:c(e)})},e))}),(0,a.jsxs)("div",{className:"flex-1 relative border-l border-gray-200",children:[n.map(({time:e})=>(0,a.jsx)(i.P.div,{className:"h-16 border-b border-gray-100 hover:bg-gray-50 cursor-pointer group relative",whileHover:{backgroundColor:"#f9fafb"},onClick:()=>r(e),children:(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsxs)(f.$,{variant:"outline",size:"sm",className:"text-xs h-6 px-2 border-amber-200 text-amber-600 hover:bg-amber-50",children:[(0,a.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"Add"]})})},e)),t.map(e=>{let t=l(e);return(0,a.jsx)(i.P.div,{className:`
                    absolute left-2 right-2 rounded-md border-2 cursor-pointer
                    ${d(e.status)} text-white p-2 shadow-sm
                    overflow-hidden z-10
                  `,style:t,whileHover:{scale:1.02,zIndex:20},whileTap:{scale:.98},onClick:()=>s(e),initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.3},children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"font-medium text-sm truncate",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs opacity-90",children:[(0,a.jsx)(h.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[c(e.startTime)," (",e.duration,"m)"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs opacity-90",children:[(0,a.jsx)(C.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"truncate",children:e.customer})]}),e.installer&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsxs)(N.eu,{className:"h-5 w-5",children:[(0,a.jsx)(N.BK,{src:`/avatars/${e.installer.toLowerCase().replace(" ","-")}.jpg`}),(0,a.jsx)(N.q5,{className:"bg-white text-slate-700 text-xs",children:e.installer.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsx)("span",{className:"text-xs truncate",children:e.installer})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs opacity-90",children:[(0,a.jsx)(g.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"truncate",children:e.address})]})]})},e.id)}),null!==m&&(0,a.jsxs)(i.P.div,{className:"absolute left-0 right-0 z-30 flex items-center",style:{top:`${m}%`},initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full border-2 border-white shadow-md"}),(0,a.jsx)("div",{className:"flex-1 h-0.5 bg-red-500"}),(0,a.jsx)("div",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-md ml-2 shadow-md",children:"Now"})]})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-slate-600",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("span",{className:"font-medium",children:t.length})," appointments"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("span",{className:"font-medium",children:t.reduce((e,t)=>e+t.duration,0)})," minutes total"]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:[{status:"pending",count:t.filter(e=>"pending"===e.status).length},{status:"assigned",count:t.filter(e=>"assigned"===e.status).length},{status:"in-progress",count:t.filter(e=>"in-progress"===e.status).length},{status:"completed",count:t.filter(e=>"completed"===e.status).length},{status:"conflict",count:t.filter(e=>"conflict"===e.status).length}].filter(({count:e})=>e>0).map(({status:e,count:t})=>(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${d(e).split(" ")[0]}`}),(0,a.jsx)("span",{className:"text-xs",children:t})]},e))})]})]})}function S({date:e,events:t,onEventClick:s,onTimeSlotClick:r,onNavigate:n,onNewAppointment:l}){let d=(e,t)=>{let[s,a]=e.split(":").map(Number);return{top:`${((s-8)*60+a)/600*100}%`,height:`${Math.max(t/600*100,8)}%`}},c=e=>{switch(e){case"assigned":return"bg-blue-500";case"pending":return"bg-amber-500";case"in-progress":return"bg-orange-500";case"completed":return"bg-green-500";default:return"bg-gray-500"}},m=e=>e.toISOString().split("T")[0]===new Date().toISOString().split("T")[0]?t:[],x=(e=>{let t=new Date(e),s=t.getDay(),a=t.getDate()-s;return new Date(t.setDate(a))})(e),u=(e=>{let t=[];for(let s=0;s<7;s++){let a=new Date(e);a.setDate(e.getDate()+s),t.push(a)}return t})(x),h=(()=>{let e=[];for(let t=8;t<=18;t++)e.push(`${t.toString().padStart(2,"0")}:00`),t<18&&e.push(`${t.toString().padStart(2,"0")}:30`);return e})(),p=new Date;return(0,a.jsx)(b.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsxs)(b.Wu,{className:"p-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>n("prev"),children:(0,a.jsx)(y,{className:"h-4 w-4"})}),(0,a.jsxs)("h3",{className:"font-semibold text-slate-700",children:[x.toLocaleDateString("en-US",{month:"long",day:"numeric"})," - "," ",u[6].toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]}),(0,a.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>n("next"),children:(0,a.jsx)(w,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-8 min-h-[600px]",children:[(0,a.jsxs)("div",{className:"border-r border-gray-200 bg-gray-50",children:[(0,a.jsx)("div",{className:"h-16 border-b border-gray-200"})," ",h.map(e=>(0,a.jsx)("div",{className:"h-12 px-2 py-1 text-xs text-slate-500 border-b border-gray-100 flex items-center justify-end",children:e},e))]}),u.map(e=>{let t=m(e),n=e.toDateString()===p.toDateString(),x=0===e.getDay()||6===e.getDay();return(0,a.jsxs)("div",{className:`border-r border-gray-200 relative ${x?"bg-gray-50":"bg-white"}`,children:[(0,a.jsxs)("div",{className:`h-16 p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors ${n?"bg-amber-50":""}`,onClick:()=>l(e),children:[(0,a.jsx)("div",{className:"text-xs text-slate-500 uppercase font-medium",children:e.toLocaleDateString("en-US",{weekday:"short"})}),(0,a.jsx)("div",{className:`text-lg font-semibold mt-1 ${n?"text-amber-600":"text-slate-700"}`,children:e.getDate()}),t.length>0&&(0,a.jsxs)("div",{className:"text-xs text-slate-500 mt-1",children:[t.length," appointment",t.length>1?"s":""]})]}),(0,a.jsxs)("div",{className:"relative",children:[h.map(t=>(0,a.jsx)("div",{className:"h-12 border-b border-gray-100 hover:bg-amber-50 cursor-pointer transition-colors group",onClick:()=>r(e,t),children:(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity p-1",children:(0,a.jsx)(o.A,{className:"h-3 w-3 text-amber-500"})})},t)),t.map(e=>{let t=d(e.startTime,e.duration);return(0,a.jsxs)(i.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},className:`absolute left-1 right-1 rounded-lg p-2 cursor-pointer hover:shadow-md transition-shadow z-10 ${c(e.status)} text-white text-xs`,style:t,onClick:()=>s(e),children:[(0,a.jsx)("div",{className:"font-medium truncate",children:e.title}),(0,a.jsx)("div",{className:"opacity-90 truncate",children:e.customer}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(N.eu,{className:"h-4 w-4 mr-1",children:(0,a.jsx)(N.q5,{className:"text-[10px] bg-white/20",children:e.installer.split(" ").map(e=>e[0]).join("")})}),(0,a.jsx)("span",{className:"text-[10px] opacity-90 truncate",children:e.installer})]})]},e.id)})]})]},e.toISOString())})]})]})})}var A=s(1312),M=s(7051);let T=(0,n.A)("paperclip",[["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551",key:"1miecu"}]]),R=(0,n.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),I=(0,n.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var $=s(22),z=s(8819),P=s(13),q=s(4729),E=s(4987),O=s(5763),_=s(5079),J=s(3503);function L({isOpen:e,onClose:t,onSave:s,selectedDate:n,selectedTime:l,projects:d=[],installers:o=[],customers:m=[]}){let[x,u]=(0,r.useState)("details"),[h,p]=(0,r.useState)(!1),[y,w]=(0,r.useState)({projectId:"",customerId:"",date:n?.toISOString().split("T")[0]||"",startTime:l||"09:00",endTime:"12:00",duration:180,installerId:"",location:"",productTypes:[],notes:"",estimatedDuration:180,sendNotification:!0,autoReminder:!0,smsReminder:!1,emailReminder:!0}),[k,D]=(0,r.useState)({}),S=()=>{let e={};return y.projectId||(e.projectId="Project is required"),y.date||(e.date="Date is required"),y.startTime||(e.startTime="Start time is required"),y.endTime||(e.endTime="End time is required"),y.installerId||(e.installerId="Installer is required"),D(e),0===Object.keys(e).length},L=async()=>{p(!0),await new Promise(e=>setTimeout(e,2e3)),w(e=>({...e,startTime:"14:00",endTime:"16:30",installerId:o.find(e=>"available"===e.availability)?.id||e.installerId})),p(!1)},W=d.find(e=>e.id===y.projectId),G=o.find(e=>e.id===y.installerId),Z=e=>{switch(e){case"available":return"bg-green-500";case"busy":return"bg-amber-500";case"unavailable":return"bg-red-500";default:return"bg-gray-500"}};return(0,a.jsx)(J.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)(J.Cf,{className:"max-w-4xl max-h-[90vh] overflow-hidden p-0",children:[(0,a.jsx)(J.c7,{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)(J.L3,{className:"text-xl font-bold text-slate-700",children:"Schedule New Appointment"})}),(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsxs)(O.tU,{value:x,onValueChange:u,className:"w-full",children:[(0,a.jsxs)(O.j7,{className:"grid grid-cols-4 w-full px-6 py-2",children:[(0,a.jsxs)(O.Xi,{value:"details",className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Details"})]}),(0,a.jsxs)(O.Xi,{value:"workflow",className:"flex items-center space-x-2",children:[(0,a.jsx)(A.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Team"})]}),(0,a.jsxs)(O.Xi,{value:"notifications",className:"flex items-center space-x-2",children:[(0,a.jsx)(M.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Notifications"})]}),(0,a.jsxs)(O.Xi,{value:"attachments",className:"flex items-center space-x-2",children:[(0,a.jsx)(T,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Files"})]})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)(O.av,{value:"details",className:"space-y-6 mt-0",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Project *"}),(0,a.jsxs)(_.l6,{value:y.projectId,onValueChange:e=>{let t=d.find(t=>t.id===e);w(s=>({...s,projectId:e,location:t?.address||""}))},children:[(0,a.jsx)(_.bq,{className:k.projectId?"border-red-500":"",children:(0,a.jsx)(_.yv,{placeholder:"Select project..."})}),(0,a.jsx)(_.gC,{children:d.map(e=>(0,a.jsx)(_.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.customer," • ",e.address]})]})},e.id))})]}),k.projectId&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:k.projectId}),W&&(0,a.jsx)(b.Zp,{className:"mt-3",children:(0,a.jsx)(b.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:W.customer})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:W.address})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(R,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:W.productTypes.map(e=>(0,a.jsx)(v.E,{variant:"secondary",className:"text-xs",children:e},e))})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Date *"}),(0,a.jsx)(j.p,{type:"date",value:y.date,onChange:e=>w(t=>({...t,date:e.target.value})),className:k.date?"border-red-500":""}),k.date&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:k.date})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Time Range *"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)(j.p,{type:"time",value:y.startTime,onChange:e=>w(t=>({...t,startTime:e.target.value})),className:k.startTime?"border-red-500":""}),(0,a.jsx)(j.p,{type:"time",value:y.endTime,onChange:e=>w(t=>({...t,endTime:e.target.value})),className:k.endTime?"border-red-500":""})]}),(k.startTime||k.endTime)&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:"Start and end times are required"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(P.J,{className:"text-sm font-medium",children:["Estimated Duration: ",y.estimatedDuration," minutes"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(f.$,{type:"button",variant:"outline",size:"sm",onClick:()=>w(e=>({...e,estimatedDuration:Math.max(30,(e.estimatedDuration||180)-15)})),children:"-15m"}),(0,a.jsx)(j.p,{type:"number",min:"30",max:"480",step:"15",value:y.estimatedDuration,onChange:e=>w(t=>({...t,estimatedDuration:parseInt(e.target.value)||180})),className:"text-center"}),(0,a.jsx)(f.$,{type:"button",variant:"outline",size:"sm",onClick:()=>w(e=>({...e,estimatedDuration:Math.min(480,(e.estimatedDuration||180)+15)})),children:"+15m"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,a.jsx)("span",{children:"30 min minimum"}),(0,a.jsx)("span",{children:"8 hours maximum"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Product Types"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:["Windows","Blinds","Shutters","Curtains","Shades","Awnings","Security Screens","Repair","Maintenance"].map(e=>(0,a.jsx)(f.$,{variant:y.productTypes?.includes(e)?"default":"outline",size:"sm",onClick:()=>{let t=y.productTypes||[],s=t.includes(e)?t.filter(t=>t!==e):[...t,e];w(e=>({...e,productTypes:s}))},children:e},e))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Location"}),(0,a.jsx)(j.p,{placeholder:"Enter appointment location...",value:y.location,onChange:e=>w(t=>({...t,location:e.target.value}))})]}),(0,a.jsx)(b.Zp,{className:"bg-amber-50 border-amber-200",children:(0,a.jsx)(b.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-amber-100 rounded-full",children:(0,a.jsx)(I,{className:"h-4 w-4 text-amber-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-700",children:"AI Schedule Optimization"}),(0,a.jsx)("p",{className:"text-xs text-slate-600",children:"Find the optimal time slot based on installer availability and location"})]})]}),(0,a.jsx)(f.$,{variant:"outline",size:"sm",onClick:L,disabled:h,className:"bg-amber-500 text-white hover:bg-amber-600 border-amber-500",children:h?(0,a.jsx)(i.P.div,{className:"h-3 w-3 border-2 border-white border-t-transparent rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(I,{className:"h-3 w-3 mr-1"}),"Optimize"]})})]})})})]}),(0,a.jsxs)(O.av,{value:"workflow",className:"space-y-6 mt-0",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Assign Installer *"}),(0,a.jsxs)(_.l6,{value:y.installerId,onValueChange:e=>w(t=>({...t,installerId:e})),children:[(0,a.jsx)(_.bq,{className:k.installerId?"border-red-500":"",children:(0,a.jsx)(_.yv,{placeholder:"Select an installer..."})}),(0,a.jsx)(_.gC,{children:o.map(e=>(0,a.jsx)(_.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(N.eu,{className:"h-6 w-6",children:[(0,a.jsx)(N.BK,{src:e.avatar||void 0}),(0,a.jsx)(N.q5,{className:"text-xs",children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${Z(e.availability)}`}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.utilization,"%"]})]})]})},e.id))})]}),k.installerId&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:k.installerId}),G&&(0,a.jsx)(b.Zp,{className:"mt-3",children:(0,a.jsx)(b.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(N.eu,{className:"h-8 w-8",children:[(0,a.jsx)(N.BK,{src:G.avatar||void 0}),(0,a.jsx)(N.q5,{children:G.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:G.name}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground capitalize",children:[G.availability," • ",G.utilization,"% utilized"]})]})]}),(0,a.jsx)("div",{className:`w-3 h-3 rounded-full ${Z(G.availability)}`})]})})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Customer"}),(0,a.jsxs)(_.l6,{value:y.customerId,onValueChange:e=>w(t=>({...t,customerId:e})),children:[(0,a.jsx)(_.bq,{children:(0,a.jsx)(_.yv,{placeholder:"Select customer..."})}),(0,a.jsx)(_.gC,{children:m.map(e=>(0,a.jsx)(_.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.email," • ",e.phone]})]})},e.id))})]})]})]}),(0,a.jsx)(O.av,{value:"notifications",className:"space-y-6 mt-0",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Send Initial Notification"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Notify customer and installer about this appointment"})]}),(0,a.jsx)(E.d,{checked:y.sendNotification,onCheckedChange:e=>w(t=>({...t,sendNotification:e}))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Auto Reminder"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Send automatic reminder 24 hours before appointment"})]}),(0,a.jsx)(E.d,{checked:y.autoReminder,onCheckedChange:e=>w(t=>({...t,autoReminder:e}))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"SMS Notifications"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Send SMS updates to customer and installer"})]}),(0,a.jsx)(E.d,{checked:y.smsReminder,onCheckedChange:e=>w(t=>({...t,smsReminder:e}))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Send email confirmations and updates"})]}),(0,a.jsx)(E.d,{checked:y.emailReminder,onCheckedChange:e=>w(t=>({...t,emailReminder:e}))})]})]})}),(0,a.jsx)(O.av,{value:"attachments",className:"space-y-6 mt-0",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"Appointment Notes"}),(0,a.jsx)(q.T,{placeholder:"Add any special instructions, notes, or requirements for this appointment...",value:y.notes,onChange:e=>w(t=>({...t,notes:e.target.value})),rows:4})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-sm font-medium",children:"File Attachments"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-amber-400 transition-colors",children:[(0,a.jsx)($.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Drag and drop files here, or click to browse"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Support for images, PDFs, and documents up to 10MB"}),(0,a.jsx)(f.$,{variant:"outline",className:"mt-4",size:"sm",children:"Choose Files"})]})]})]})})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-gray-50",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"* Required fields"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(f.$,{variant:"outline",onClick:t,children:"Cancel"}),(0,a.jsxs)(f.$,{onClick:()=>{if(!S())return void u("details");s({...y,id:`appt_${Date.now()}`}),t()},className:"bg-amber-500 hover:bg-amber-600 text-white",children:[(0,a.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Schedule Appointment"]})]})]})]})})}var W=s(2587);let G=[{id:"project-1",name:"Kitchen Window Installation",customer:"Sarah Johnson",address:"123 Oak Street, Portland, OR",productTypes:["Windows","Blinds"],status:"active"},{id:"project-2",name:"Bathroom Renovation",customer:"Mike Chen",address:"456 Pine Avenue, Seattle, WA",productTypes:["Shutters","Blinds"],status:"active"},{id:"project-3",name:"Living Room Makeover",customer:"Emily Davis",address:"789 Maple Drive, Vancouver, WA",productTypes:["Curtains","Shades"],status:"active"}],Z=[{id:"installer-1",name:"Alex Thompson",avatar:null,availability:"available",utilization:65},{id:"installer-2",name:"Jordan Martinez",avatar:null,availability:"busy",utilization:85},{id:"installer-3",name:"Sam Wilson",avatar:null,availability:"available",utilization:45}],F=[{id:"customer-1",name:"Sarah Johnson",email:"<EMAIL>",phone:"(*************",address:"123 Oak Street, Portland, OR"},{id:"customer-2",name:"Mike Chen",email:"<EMAIL>",phone:"(*************",address:"456 Pine Avenue, Seattle, WA"},{id:"customer-3",name:"Emily Davis",email:"<EMAIL>",phone:"(*************",address:"789 Maple Drive, Vancouver, WA"}],B=new Date("2025-07-12"),U=[{id:"1",title:"Kitchen Window Installation",date:"2025-07-12",time:"09:00",status:"assigned",customer:"Sarah Johnson",installer:"Alex Thompson"},{id:"2",title:"Bathroom Consultation",date:"2025-07-12",time:"14:00",status:"pending",customer:"Mike Chen",installer:"Jordan Martinez"},{id:"3",title:"Living Room Curtains",date:"2025-07-13",time:"10:00",status:"in-progress",customer:"Emily Davis",installer:"Sam Wilson"},{id:"4",title:"Window Maintenance",date:"2025-07-14",time:"15:00",status:"completed",customer:"Sarah Johnson",installer:"Alex Thompson"}],H=[{id:"1",title:"Kitchen Window Installation",startTime:"09:00",duration:180,customer:"Sarah Johnson",address:"123 Oak Street",installer:"Alex Thompson",status:"assigned"},{id:"2",title:"Bathroom Consultation",startTime:"14:00",duration:120,customer:"Mike Chen",address:"456 Pine Avenue",installer:"Jordan Martinez",status:"pending"}];function V(){let{accentColor:e}=(0,W.D)(),[t,s]=(0,r.useState)("month"),[n,y]=(0,r.useState)(B),[w,C]=(0,r.useState)(!1),[A,M]=(0,r.useState)(""),T=e=>{e&&y(e),C(!0)},R=e=>{let s=new Date(n);"month"===t?s.setMonth(s.getMonth()+("next"===e?1:-1)):"week"===t?s.setDate(s.getDate()+("next"===e?7:-7)):s.setDate(s.getDate()+("next"===e?1:-1)),y(s)},I=U.filter(e=>e.date===B.toISOString().split("T")[0]),$=U.filter(e=>new Date(e.date)>B).slice(0,5);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-slate-700",children:"Schedule"}),(0,a.jsx)("p",{className:"text-slate-600 mt-1",children:"Manage installer appointments and project timelines"}),(0,a.jsx)("p",{className:"text-sm font-medium mt-1",style:{color:e},children:B.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(f.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(l,{className:"h-4 w-4 mr-2"}),"Filter"]}),(0,a.jsxs)(f.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,a.jsxs)(f.$,{onClick:()=>T(),className:"text-white",style:{backgroundColor:e,borderColor:e},children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"New Appointment"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)(b.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsxs)(b.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(b.ZB,{className:"text-sm font-medium text-slate-600",children:"Upcoming Appointments"}),(0,a.jsx)(c.A,{className:"h-4 w-4",style:{color:e}})]}),(0,a.jsxs)(b.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-700",children:"15"}),(0,a.jsx)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:"+10% from last week"})]})]}),(0,a.jsxs)(b.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsxs)(b.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(b.ZB,{className:"text-sm font-medium text-slate-600",children:"Installer Utilization"}),(0,a.jsx)(m,{className:"h-4 w-4",style:{color:e}})]}),(0,a.jsxs)(b.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-700",children:"75%"}),(0,a.jsx)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:"+5% from last month"})]})]}),(0,a.jsxs)(b.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsxs)(b.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(b.ZB,{className:"text-sm font-medium text-slate-600",children:"Conflicts"}),(0,a.jsx)(x,{className:"h-4 w-4 text-red-500"})]}),(0,a.jsxs)(b.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-700",children:"2"}),(0,a.jsx)("p",{className:"text-xs text-red-600 flex items-center mt-1",children:"Requires attention"})]})]}),(0,a.jsxs)(b.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsxs)(b.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(b.ZB,{className:"text-sm font-medium text-slate-600",children:"Avg Job Duration"}),(0,a.jsx)(u.A,{className:"h-4 w-4",style:{color:e}})]}),(0,a.jsxs)(b.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-700",children:"4h"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 flex items-center mt-1",children:"Per appointment"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"xl:col-span-3 space-y-6",children:[(0,a.jsx)(b.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsx)(b.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(f.$,{variant:"month"===t?"default":"outline",size:"sm",onClick:()=>s("month"),className:"month"===t?"text-white":"",style:"month"===t?{backgroundColor:e,borderColor:e}:{},children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Month"]}),(0,a.jsxs)(f.$,{variant:"week"===t?"default":"outline",size:"sm",onClick:()=>s("week"),className:"week"===t?"text-white":"",style:"week"===t?{backgroundColor:e,borderColor:e}:{},children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Week"]}),(0,a.jsxs)(f.$,{variant:"day"===t?"default":"outline",size:"sm",onClick:()=>s("day"),className:"day"===t?"text-white":"",style:"day"===t?{backgroundColor:e,borderColor:e}:{},children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Day"]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("h2",{className:"font-semibold text-slate-700",children:"month"===t?n.toLocaleDateString("en-US",{month:"long",year:"numeric"}):"week"===t?(()=>{let e=new Date(n),t=e.getDay(),s=e.getDate()-t;e.setDate(s);let a=new Date(e);return a.setDate(e.getDate()+6),`${e.toLocaleDateString("en-US",{month:"short",day:"numeric"})} - ${a.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}`})():n.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})})}),(0,a.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>y(B),children:"Today"})]})})}),(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:"month"===t?(0,a.jsx)(k,{selectedDate:n,events:U,onDateSelect:e=>{y(e),s("day")},onEventClick:e=>console.log("Event clicked:",e),onNavigate:R,onNewAppointment:T}):"week"===t?(0,a.jsx)(S,{date:n,events:H,onEventClick:e=>console.log("Event clicked:",e),onTimeSlotClick:e=>T(e),onNavigate:R,onNewAppointment:T}):(0,a.jsx)(D,{date:n,events:n.toDateString()===B.toDateString()?H:[],onEventClick:e=>console.log("Event clicked:",e),onTimeSlotClick:()=>T(n)})},t)]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(b.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(b.aR,{className:"pb-3",children:(0,a.jsx)(b.ZB,{className:"text-lg text-slate-700",children:"Quick Search"})}),(0,a.jsx)(b.Wu,{className:"space-y-3",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)(j.p,{placeholder:"Search appointments...",value:A,onChange:e=>M(e.target.value),className:"pl-10 border-gray-200"})]})})]}),(0,a.jsxs)(b.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(b.aR,{className:"pb-3",children:(0,a.jsx)(b.ZB,{className:"text-lg text-slate-700",children:"Today's Schedule"})}),(0,a.jsx)(b.Wu,{className:"space-y-3",children:I.length>0?I.map(t=>(0,a.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:t.title}),(0,a.jsx)(v.E,{variant:"completed"===t.status?"default":"secondary",className:"assigned"===t.status?"bg-blue-100 text-blue-700":"pending"===t.status?"bg-opacity-20 text-slate-700":"in-progress"===t.status?"bg-orange-100 text-orange-700":"completed"===t.status?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700",style:"pending"===t.status?{backgroundColor:`${e}33`,color:e}:{},children:t.status})]}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-slate-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-3 w-3 mr-1"}),t.time]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-3 w-3 mr-1"}),t.customer]})]}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(N.eu,{className:"h-5 w-5 mr-2",children:(0,a.jsx)(N.q5,{className:"text-xs bg-opacity-20 text-slate-700",style:{backgroundColor:`${e}33`,color:e},children:t.installer.split(" ").map(e=>e[0]).join("")})}),(0,a.jsx)("span",{className:"text-xs text-slate-600",children:t.installer})]})]},t.id)):(0,a.jsx)("p",{className:"text-sm text-slate-500",children:"No appointments today"})})]}),(0,a.jsxs)(b.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(b.aR,{className:"pb-3",children:(0,a.jsx)(b.ZB,{className:"text-lg text-slate-700",children:"Upcoming"})}),(0,a.jsx)(b.Wu,{className:"space-y-3",children:$.map(e=>(0,a.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:e.title}),(0,a.jsx)(v.E,{variant:"secondary",className:"bg-gray-100 text-gray-700",children:e.status})]}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-slate-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-3 w-3 mr-1"}),new Date(e.date).toLocaleDateString()]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-3 w-3 mr-1"}),e.time]})]})]},e.id))})]})]})]}),(0,a.jsx)(L,{isOpen:w,onClose:()=>C(!1),onSave:e=>{console.log("Saving appointment:",e),C(!1)},selectedDate:n,projects:G,installers:Z,customers:F})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3503:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>c,L3:()=>x,c7:()=>m,lG:()=>l});var a=s(687);s(3210);var r=s(6134),i=s(1860),n=s(4780);function l({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e})}function d({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function o({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function c({className:e,children:t,showCloseButton:s=!0,...l}){return(0,a.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,a.jsx)(o,{}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...l,children:[t,s&&(0,a.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(i.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function x({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...t})}},3873:e=>{"use strict";e.exports=require("path")},4213:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Source Code\\\\GoogleGemini\\\\windowworks-crm\\\\src\\\\components\\\\schedule\\\\schedule-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\schedule-page.tsx","default")},4729:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var a=s(687),r=s(3210),i=s(4780);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));n.displayName="Textarea"},4983:(e,t,s)=>{Promise.resolve().then(s.bind(s,8054)),Promise.resolve().then(s.bind(s,4213))},4987:(e,t,s)=>{"use strict";s.d(t,{d:()=>n});var a=s(687),r=s(3210),i=s(4780);let n=r.forwardRef(({className:e,checked:t=!1,onCheckedChange:s,disabled:r=!1,id:n,...l},d)=>(0,a.jsx)("button",{ref:d,type:"button",role:"switch","aria-checked":t,disabled:r,onClick:()=>{!r&&s&&s(!t)},id:n,className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-sm border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",t?"bg-primary":"bg-input",e),...l,children:(0,a.jsx)("span",{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded bg-background shadow-lg ring-0 transition-transform",t?"translate-x-5":"translate-x-0")})}));n.displayName="Switch"},5079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>x,eb:()=>g,gC:()=>p,l6:()=>c,yv:()=>m});var a=s(687),r=s(3210),i=s(7822),n=s(5891),l=s(3589),d=s(3964),o=s(4780);let c=i.bL;i.YJ;let m=i.WT,x=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,a.jsx)(i.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=i.l9.displayName;let u=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})}));u.displayName=i.PP.displayName;let h=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=i.wn.displayName;let p=r.forwardRef(({className:e,children:t,position:s="popper",...r},n)=>(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{ref:n,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,a.jsx)(u,{}),(0,a.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(h,{})]})}));p.displayName=i.UC.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let g=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(i.p4,{children:t})]}));g.displayName=i.q7.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},5247:(e,t,s)=>{Promise.resolve().then(s.bind(s,7634)),Promise.resolve().then(s.bind(s,3045))},5763:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>o,j7:()=>l,tU:()=>n});var a=s(687);s(3210);var r=s(5146),i=s(4780);function n({className:e,...t}){return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",e),...t})}function l({className:e,...t}){return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function d({className:e,...t}){return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function o({className:e,...t}){return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",e),...t})}},6134:(e,t,s)=>{"use strict";s.d(t,{UC:()=>Q,ZL:()=>X,bL:()=>K,bm:()=>et,hE:()=>ee,hJ:()=>Y});var a=s(3210),r=s(569),i=s(8599),n=s(1273),l=s(6963),d=s(5551),o=s(1355),c=s(2547),m=s(5028),x=s(6059),u=s(4163),h=s(1359),p=s(2247),g=s(3376),f=s(8730),j=s(687),b="Dialog",[v,N]=(0,n.A)(b),[y,w]=v(b),k=e=>{let{__scopeDialog:t,children:s,open:r,defaultOpen:i,onOpenChange:n,modal:o=!0}=e,c=a.useRef(null),m=a.useRef(null),[x,u]=(0,d.i)({prop:r,defaultProp:i??!1,onChange:n,caller:b});return(0,j.jsx)(y,{scope:t,triggerRef:c,contentRef:m,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:x,onOpenChange:u,onOpenToggle:a.useCallback(()=>u(e=>!e),[u]),modal:o,children:s})};k.displayName=b;var C="DialogTrigger";a.forwardRef((e,t)=>{let{__scopeDialog:s,...a}=e,n=w(C,s),l=(0,i.s)(t,n.triggerRef);return(0,j.jsx)(u.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":Z(n.open),...a,ref:l,onClick:(0,r.m)(e.onClick,n.onOpenToggle)})}).displayName=C;var D="DialogPortal",[S,A]=v(D,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:s,children:r,container:i}=e,n=w(D,t);return(0,j.jsx)(S,{scope:t,forceMount:s,children:a.Children.map(r,e=>(0,j.jsx)(x.C,{present:s||n.open,children:(0,j.jsx)(m.Z,{asChild:!0,container:i,children:e})}))})};M.displayName=D;var T="DialogOverlay",R=a.forwardRef((e,t)=>{let s=A(T,e.__scopeDialog),{forceMount:a=s.forceMount,...r}=e,i=w(T,e.__scopeDialog);return i.modal?(0,j.jsx)(x.C,{present:a||i.open,children:(0,j.jsx)($,{...r,ref:t})}):null});R.displayName=T;var I=(0,f.TL)("DialogOverlay.RemoveScroll"),$=a.forwardRef((e,t)=>{let{__scopeDialog:s,...a}=e,r=w(T,s);return(0,j.jsx)(p.A,{as:I,allowPinchZoom:!0,shards:[r.contentRef],children:(0,j.jsx)(u.sG.div,{"data-state":Z(r.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),z="DialogContent",P=a.forwardRef((e,t)=>{let s=A(z,e.__scopeDialog),{forceMount:a=s.forceMount,...r}=e,i=w(z,e.__scopeDialog);return(0,j.jsx)(x.C,{present:a||i.open,children:i.modal?(0,j.jsx)(q,{...r,ref:t}):(0,j.jsx)(E,{...r,ref:t})})});P.displayName=z;var q=a.forwardRef((e,t)=>{let s=w(z,e.__scopeDialog),n=a.useRef(null),l=(0,i.s)(t,s.contentRef,n);return a.useEffect(()=>{let e=n.current;if(e)return(0,g.Eq)(e)},[]),(0,j.jsx)(O,{...e,ref:l,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),s.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,s=0===t.button&&!0===t.ctrlKey;(2===t.button||s)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),E=a.forwardRef((e,t)=>{let s=w(z,e.__scopeDialog),r=a.useRef(!1),i=a.useRef(!1);return(0,j.jsx)(O,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||s.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;s.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),O=a.forwardRef((e,t)=>{let{__scopeDialog:s,trapFocus:r,onOpenAutoFocus:n,onCloseAutoFocus:l,...d}=e,m=w(z,s),x=a.useRef(null),u=(0,i.s)(t,x);return(0,h.Oh)(),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:n,onUnmountAutoFocus:l,children:(0,j.jsx)(o.qW,{role:"dialog",id:m.contentId,"aria-describedby":m.descriptionId,"aria-labelledby":m.titleId,"data-state":Z(m.open),...d,ref:u,onDismiss:()=>m.onOpenChange(!1)})}),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(H,{titleId:m.titleId}),(0,j.jsx)(V,{contentRef:x,descriptionId:m.descriptionId})]})]})}),_="DialogTitle",J=a.forwardRef((e,t)=>{let{__scopeDialog:s,...a}=e,r=w(_,s);return(0,j.jsx)(u.sG.h2,{id:r.titleId,...a,ref:t})});J.displayName=_;var L="DialogDescription";a.forwardRef((e,t)=>{let{__scopeDialog:s,...a}=e,r=w(L,s);return(0,j.jsx)(u.sG.p,{id:r.descriptionId,...a,ref:t})}).displayName=L;var W="DialogClose",G=a.forwardRef((e,t)=>{let{__scopeDialog:s,...a}=e,i=w(W,s);return(0,j.jsx)(u.sG.button,{type:"button",...a,ref:t,onClick:(0,r.m)(e.onClick,()=>i.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}G.displayName=W;var F="DialogTitleWarning",[B,U]=(0,n.q)(F,{contentName:z,titleName:_,docsSlug:"dialog"}),H=({titleId:e})=>{let t=U(F),s=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return a.useEffect(()=>{e&&(document.getElementById(e)||console.error(s))},[s,e]),null},V=({contentRef:e,descriptionId:t})=>{let s=U("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${s.contentName}}.`;return a.useEffect(()=>{let s=e.current?.getAttribute("aria-describedby");t&&s&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},K=k,X=M,Y=R,Q=P,ee=J,et=G},6474:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},7992:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8054:(e,t,s)=>{"use strict";s.d(t,{DashboardLayout:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\dashboard\\dashboard-layout.tsx","DashboardLayout")},8469:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,145,861,979,171],()=>s(1904));module.exports=a})();