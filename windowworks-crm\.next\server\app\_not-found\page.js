(()=>{var e={};e.id=492,e.ids=[492],e.modules={165:(e,t,r)=>{Promise.resolve().then(r.bind(r,6457))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2021:(e,t,r)=>{Promise.resolve().then(r.bind(r,2587))},2587:(e,t,r)=>{"use strict";r.d(t,{D:()=>d,ThemeProvider:()=>i});var o=r(687),n=r(3210);let s=(0,n.createContext)(void 0);function i({children:e}){let[t,r]=(0,n.useState)("light"),[i,d]=(0,n.useState)("#D97706"),[a,l]=(0,n.useState)(!1),c=(0,n.useCallback)(e=>{r(e);try{localStorage.setItem("windowworks-theme",e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}catch(e){console.error("Failed to set theme in localStorage",e)}},[]),m=(0,n.useCallback)(e=>{d(e);try{localStorage.setItem("windowworks-accent-color",e);let t=document.documentElement,r=`oklch(from ${e} l c h)`;t.style.setProperty("--primary",r),t.style.setProperty("--color-brand-primary",e)}catch(e){console.error("Failed to set accent color in localStorage",e)}},[]);return(0,o.jsx)(s.Provider,{value:{theme:t,setTheme:c,accentColor:i,setAccentColor:m,mounted:a},children:e})}function d(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var o=r(7413),n=r(2376),s=r.n(n),i=r(8726),d=r.n(i);r(1135);var a=r(6457);let l={title:"WindowWorks CRM",description:"Customer and project management with installer workflows"};function c({children:e}){return(0,o.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,o.jsx)("head",{children:(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                try {
                  const theme = localStorage.getItem('windowworks-theme');
                  if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                  } else {
                    document.documentElement.classList.remove('dark');
                  }
                } catch (_) {}
              })();
            `}})}),(0,o.jsx)("body",{className:`${s().variable} ${d().variable} antialiased`,children:(0,o.jsx)(a.ThemeProvider,{children:e})})]})}},4685:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},6125:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6369:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var o=r(5239),n=r(8088),s=r(8170),i=r.n(s),d=r(893),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);r.d(t,a);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],m={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},6457:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var o=r(2907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\contexts\\theme-context.tsx","ThemeProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\contexts\\theme-context.tsx","useTheme")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,145],()=>r(6369));module.exports=o})();