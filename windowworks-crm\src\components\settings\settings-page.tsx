'use client'

import { useState, useRef, useId, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  User, 
  Mail, 
  Lock, 
  Eye, 
  EyeOff, 
  Upload, 
  Moon, 
  Sun, 
  Globe,
  Calendar,
  MapPin,
  Shield,
  Download,
  Trash2,
  Edit,
  UserPlus,
  RotateCcw,
  Smartphone,
  Monitor,
  LogOut,
  Settings as SettingsIcon,
  Clock,
  Save,
  Camera,
  HelpCircle
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Switch } from '@/components/ui/switch'
import { <PERSON><PERSON>, TabsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useTheme } from '@/contexts/theme-context'

// Types
interface UserProfile {
  id: string
  name: string
  email: string
  phone: string
  bio: string
  avatar: string | null
  role: 'Admin' | 'Manager' | 'Installer' | 'User'
}

interface UserPreferences {
  darkMode: boolean
  emailNotifications: boolean
  smsAlerts: boolean
  language: 'en' | 'es'
  timezone: string
  customAccentColor: string
}

interface Integration {
  id: string
  name: string
  description: string
  status: 'connected' | 'disconnected' | 'error'
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>
  connectionType: 'oauth' | 'api-key' | 'toggle'
  apiKey?: string
}

interface TeamMember {
  id: string
  name: string
  email: string
  role: 'Admin' | 'Manager' | 'Installer' | 'User'
  status: 'Active' | 'Inactive' | 'Pending'
  lastActive: string
}

interface AuditLog {
  id: string
  action: string
  user: string
  timestamp: string
  details: string
}

interface SecuritySession {
  id: string
  device: string
  location: string
  lastActive: string
  current: boolean
}

// Mock data
const mockProfile: UserProfile = {
  id: 'user_001',
  name: 'John Admin',
  email: '<EMAIL>',
  phone: '(*************',
  bio: 'Operations manager with 5+ years in window treatment industry.',
  avatar: null,
  role: 'Admin'
}

const mockPreferences: UserPreferences = {
  darkMode: false,
  emailNotifications: true,
  smsAlerts: false,
  language: 'en',
  timezone: 'America/Chicago',
  customAccentColor: '#D97706'
}

const mockIntegrations: Integration[] = [
  {
    id: 'google-maps',
    name: 'Google Maps',
    description: 'Get directions and location data for customer addresses',
    status: 'connected',
    icon: MapPin,
    connectionType: 'oauth'
  },
  {
    id: 'email-provider',
    name: 'Email Provider',
    description: 'Send automated emails and notifications',
    status: 'disconnected',
    icon: Mail,
    connectionType: 'api-key',
    apiKey: ''
  },
  {
    id: 'calendar-sync',
    name: 'Calendar Sync',
    description: 'Sync appointments with external calendars',
    status: 'connected',
    icon: Calendar,
    connectionType: 'toggle'
  }
]

const mockTeamMembers: TeamMember[] = [
  {
    id: 'team_001',
    name: 'John Admin',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'Active',
    lastActive: '2025-07-12T14:30:00Z'
  },
  {
    id: 'team_002',
    name: 'Sarah Manager',
    email: '<EMAIL>',
    role: 'Manager',
    status: 'Active',
    lastActive: '2025-07-12T12:15:00Z'
  },
  {
    id: 'team_003',
    name: 'Mike Installer',
    email: '<EMAIL>',
    role: 'Installer',
    status: 'Active',
    lastActive: '2025-07-12T09:45:00Z'
  },
  {
    id: 'team_004',
    name: 'Lisa Anderson',
    email: '<EMAIL>',
    role: 'Installer',
    status: 'Inactive',
    lastActive: '2025-07-10T16:20:00Z'
  }
]

const mockAuditLogs: AuditLog[] = [
  {
    id: 'audit_001',
    action: 'Project Created',
    user: 'John Admin',
    timestamp: '2025-07-12T14:30:00Z',
    details: 'Created project "Living Room Shutters" for Sarah Johnson'
  },
  {
    id: 'audit_002',
    action: 'User Invited',
    user: 'John Admin',
    timestamp: '2025-07-12T13:15:00Z',
    details: 'Invited new installer: <EMAIL>'
  },
  {
    id: 'audit_003',
    action: 'Settings Updated',
    user: 'Sarah Manager',
    timestamp: '2025-07-12T11:20:00Z',
    details: 'Updated notification preferences'
  }
]

const mockSessions: SecuritySession[] = [
  {
    id: 'session_001',
    device: 'Chrome on Windows',
    location: 'Chicago, IL',
    lastActive: '2025-07-12T14:30:00Z',
    current: true
  },
  {
    id: 'session_002',
    device: 'Safari on iPhone',
    location: 'Chicago, IL',
    lastActive: '2025-07-12T09:15:00Z',
    current: false
  }
]

export default function SettingsPage() {
  const { theme, setTheme, accentColor, setAccentColor } = useTheme()
  const [activeTab, setActiveTab] = useState('profile')
  const [profile, setProfile] = useState(mockProfile)
  const [preferences, setPreferences] = useState({
    ...mockPreferences,
    darkMode: false, // Will be updated by useEffect
    customAccentColor: '#D97706' // Will be updated by useEffect
  })
  const [integrations, setIntegrations] = useState(mockIntegrations)
  const [hasChanges, setHasChanges] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Sync preferences with theme context
  useEffect(() => {
    setPreferences(prev => ({
      ...prev,
      darkMode: theme === 'dark',
      customAccentColor: accentColor
    }))
  }, [theme, accentColor])
  
  // Generate unique IDs for switches
  const darkModeId = useId()
  const emailNotificationsId = useId()
  const smsAlertsId = useId()
  const twoFactorId = useId()

  const handleProfileUpdate = (field: keyof UserProfile, value: string) => {
    setProfile(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)
  }

  const handlePreferenceUpdate = (field: keyof UserPreferences, value: string | boolean) => {
    if (field === 'darkMode') {
      setTheme(value ? 'dark' : 'light')
    } else if (field === 'customAccentColor') {
      setAccentColor(value as string)
    }
    setPreferences(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)
  }

  const handleSaveChanges = () => {
    // Save logic here
    console.log('Saving changes...', { profile, preferences })
    setHasChanges(false)
  }

  const handleAvatarUpload = (files: FileList | null) => {
    if (files && files[0]) {
      const file = files[0]
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        handleProfileUpdate('avatar', result)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleAvatarUpload(e.dataTransfer.files)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'connected':
        return <Badge className="bg-green-500/10 text-green-600 border-green-500/20">Active</Badge>
      case 'inactive':
      case 'disconnected':
        return <Badge variant="secondary">Inactive</Badge>
      case 'pending':
        return <Badge className="bg-yellow-500/10 text-yellow-600 border-yellow-500/20">Pending</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'bg-primary/10 text-primary border-primary/20'
      case 'manager':
        return 'bg-blue-500/10 text-blue-600 border-blue-500/20'
      case 'installer':
        return 'bg-green-500/10 text-green-600 border-green-500/20'
      default:
        return 'bg-gray-500/10 text-gray-600 border-gray-500/20'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">Settings</h1>
          <p className="text-muted-foreground">
            Customize your profile, preferences, and platform integrations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="secondary" className="text-xs">
            Saturday, July 12, 2025
          </Badge>
          <Button 
            className="bg-primary hover:bg-primary/90"
            disabled={!hasChanges}
            onClick={handleSaveChanges}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-5 w-full max-w-2xl">
            <TabsTrigger value="profile" className="flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span className="hidden sm:inline">Profile</span>
            </TabsTrigger>
            <TabsTrigger value="preferences" className="flex items-center space-x-2">
              <SettingsIcon className="h-4 w-4" />
              <span className="hidden sm:inline">Preferences</span>
            </TabsTrigger>
            <TabsTrigger value="integrations" className="flex items-center space-x-2">
              <Globe className="h-4 w-4" />
              <span className="hidden sm:inline">Integrations</span>
            </TabsTrigger>
            <TabsTrigger value="admin" className="flex items-center space-x-2">
              <Shield className="h-4 w-4" />
              <span className="hidden sm:inline">Admin</span>
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center space-x-2">
              <Lock className="h-4 w-4" />
              <span className="hidden sm:inline">Security</span>
            </TabsTrigger>
          </TabsList>

          <div className="mt-6 grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Main Content Area */}
            <div className="lg:col-span-3">
              <AnimatePresence mode="wait">
                {/* Profile Tab */}
                <TabsContent key="profile" value="profile" className="mt-0">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    <Card>
                      <CardHeader>
                        <CardTitle>Profile Information</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        {/* Avatar Upload */}
                        <div className="flex items-center space-x-6">
                          <div className="relative">
                            <Avatar className="h-24 w-24">
                              <AvatarImage src={profile.avatar || undefined} />
                              <AvatarFallback className="bg-primary/10 text-primary text-xl">
                                {profile.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <button
                              onClick={() => fileInputRef.current?.click()}
                              className="absolute -bottom-2 -right-2 bg-primary text-white rounded-full p-2 hover:bg-primary/90 transition-colors"
                            >
                              <Camera className="h-4 w-4" />
                            </button>
                          </div>
                          
                          <div className="flex-1">
                            <div
                              className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
                                dragActive ? 'border-primary bg-primary/5' : 'border-border'
                              }`}
                              onDragEnter={handleDrag}
                              onDragLeave={handleDrag}
                              onDragOver={handleDrag}
                              onDrop={handleDrop}
                            >
                              <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                              <p className="text-sm text-muted-foreground">
                                Drag and drop an image, or{' '}
                                <button
                                  onClick={() => fileInputRef.current?.click()}
                                  className="text-primary hover:underline"
                                >
                                  browse
                                </button>
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                JPG, PNG up to 2MB
                              </p>
                            </div>
                            <input
                              ref={fileInputRef}
                              type="file"
                              accept="image/*"
                              onChange={(e) => handleAvatarUpload(e.target.files)}
                              className="hidden"
                            />
                          </div>
                        </div>

                        {/* Form Fields */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="name">Full Name</Label>
                            <Input
                              id="name"
                              value={profile.name}
                              onChange={(e) => handleProfileUpdate('name', e.target.value)}
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="email">Email Address</Label>
                            <Input
                              id="email"
                              type="email"
                              value={profile.email}
                              onChange={(e) => handleProfileUpdate('email', e.target.value)}
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="phone">Phone Number</Label>
                            <Input
                              id="phone"
                              value={profile.phone}
                              onChange={(e) => handleProfileUpdate('phone', e.target.value)}
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="role">Role</Label>
                            <div className="pt-2">
                              <Badge className={getRoleColor(profile.role)}>
                                {profile.role}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="bio">Bio</Label>
                          <Textarea
                            id="bio"
                            value={profile.bio}
                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleProfileUpdate('bio', e.target.value)}
                            rows={3}
                            placeholder="Tell us about yourself..."
                          />
                        </div>
                      </CardContent>
                    </Card>

                    {/* Change Password */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Change Password</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="current-password">Current Password</Label>
                          <div className="relative">
                            <Input
                              id="current-password"
                              type={showPassword ? 'text' : 'password'}
                              placeholder="Enter current password"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="new-password">New Password</Label>
                            <Input
                              id="new-password"
                              type="password"
                              placeholder="Enter new password"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="confirm-password">Confirm Password</Label>
                            <Input
                              id="confirm-password"
                              type="password"
                              placeholder="Confirm new password"
                            />
                          </div>
                        </div>
                        
                        <Button variant="outline">
                          Update Password
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                </TabsContent>

                {/* Preferences Tab */}
                <TabsContent key="preferences" value="preferences" className="mt-0">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    <Card>
                      <CardHeader>
                        <CardTitle>Appearance</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor={darkModeId} className="text-base">Dark Mode</Label>
                            <p className="text-sm text-muted-foreground">
                              Toggle between light and dark themes
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Sun className="h-4 w-4 text-muted-foreground" />
                            <Switch
                              id={darkModeId}
                              checked={preferences.darkMode}
                              onCheckedChange={(value: boolean) => handlePreferenceUpdate('darkMode', value)}
                              className="rounded-sm [&_span]:rounded"
                            />
                            <Moon className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Custom Accent Color</Label>
                          <div className="flex items-center justify-between">
                            <input
                              type="color"
                              value={preferences.customAccentColor}
                              onChange={(e) => handlePreferenceUpdate('customAccentColor', e.target.value)}
                              className="w-12 h-12 rounded border border-border cursor-pointer"
                            />
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium">Preview</span>
                              <div 
                                className="w-24 h-8 rounded border border-border"
                                style={{ backgroundColor: preferences.customAccentColor }}
                              />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Notifications</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor={emailNotificationsId} className="text-base">Email Notifications</Label>
                            <p className="text-sm text-muted-foreground">
                              Receive updates about projects and appointments
                            </p>
                          </div>
                          <Switch
                            id={emailNotificationsId}
                            checked={preferences.emailNotifications}
                            onCheckedChange={(value: boolean) => handlePreferenceUpdate('emailNotifications', value)}
                            className="rounded-sm [&_span]:rounded"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor={smsAlertsId} className="text-base">SMS Alerts</Label>
                            <p className="text-sm text-muted-foreground">
                              Get text messages for urgent updates
                            </p>
                          </div>
                          <Switch
                            id={smsAlertsId}
                            checked={preferences.smsAlerts}
                            onCheckedChange={(value: boolean) => handlePreferenceUpdate('smsAlerts', value)}
                            className="rounded-sm [&_span]:rounded"
                          />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Localization</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Language</Label>
                            <Select 
                              value={preferences.language} 
                              onValueChange={(value) => handlePreferenceUpdate('language', value)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="en">English</SelectItem>
                                <SelectItem value="es">Español</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Timezone</Label>
                            <Select 
                              value={preferences.timezone} 
                              onValueChange={(value) => handlePreferenceUpdate('timezone', value)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="America/Chicago">Central Time</SelectItem>
                                <SelectItem value="America/New_York">Eastern Time</SelectItem>
                                <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                                <SelectItem value="America/Denver">Mountain Time</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                </TabsContent>

                {/* Integrations Tab */}
                <TabsContent key="integrations" value="integrations" className="mt-0">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    {integrations.map((integration) => {
                      const IconComponent = integration.icon
                      return (
                        <Card key={integration.id}>
                          <CardHeader>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="p-2 rounded-md bg-primary/10">
                                  <IconComponent className="h-6 w-6 text-primary" />
                                </div>
                                <div>
                                  <CardTitle className="text-lg">{integration.name}</CardTitle>
                                  <p className="text-sm text-muted-foreground">
                                    {integration.description}
                                  </p>
                                </div>
                              </div>
                              {getStatusBadge(integration.status)}
                            </div>
                          </CardHeader>
                          <CardContent>
                            {integration.connectionType === 'oauth' && (
                              <div className="flex items-center space-x-3">
                                <Button 
                                  variant={integration.status === 'connected' ? 'outline' : 'default'}
                                  className={integration.status === 'connected' ? '' : 'bg-primary hover:bg-primary/90'}
                                >
                                  {integration.status === 'connected' ? 'Disconnect' : 'Connect with OAuth'}
                                </Button>
                                {integration.status === 'connected' && (
                                  <Button variant="outline" size="sm">
                                    Test Connection
                                  </Button>
                                )}
                              </div>
                            )}

                            {integration.connectionType === 'api-key' && (
                              <div className="space-y-3">
                                <div className="flex items-center space-x-3">
                                  <Input
                                    placeholder="Enter API key"
                                    type="password"
                                    value={integration.apiKey || ''}
                                    onChange={(e) => {
                                      const updated = integrations.map(i => 
                                        i.id === integration.id 
                                          ? { ...i, apiKey: e.target.value }
                                          : i
                                      )
                                      setIntegrations(updated)
                                      setHasChanges(true)
                                    }}
                                  />
                                  <Button variant="outline">
                                    Test
                                  </Button>
                                </div>
                              </div>
                            )}

                            {integration.connectionType === 'toggle' && (
                              <div className="flex items-center justify-between">
                                <Label htmlFor={`${integration.name.toLowerCase().replace(' ', '-')}-sync`} className="text-sm text-muted-foreground">
                                  Enable {integration.name} synchronization
                                </Label>
                                <Switch 
                                  id={`${integration.name.toLowerCase().replace(' ', '-')}-sync`}
                                  checked={integration.status === 'connected'} 
                                  className="rounded-sm [&_span]:rounded"
                                />
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </motion.div>
                </TabsContent>

                {/* Admin Controls Tab */}
                <TabsContent key="admin" value="admin" className="mt-0">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between">
                        <CardTitle>Team Management</CardTitle>
                        <Button className="bg-primary hover:bg-primary/90">
                          <UserPlus className="h-4 w-4 mr-2" />
                          Invite User
                        </Button>
                      </CardHeader>
                      <CardContent>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>User</TableHead>
                              <TableHead>Role</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Last Active</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {mockTeamMembers.map((member) => (
                              <TableRow key={member.id}>
                                <TableCell>
                                  <div className="flex items-center space-x-3">
                                    <Avatar className="h-8 w-8">
                                      <AvatarFallback className="bg-primary/10 text-primary text-xs">
                                        {member.name.split(' ').map(n => n[0]).join('')}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div>
                                      <p className="font-medium">{member.name}</p>
                                      <p className="text-sm text-muted-foreground">{member.email}</p>
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge className={getRoleColor(member.role)}>
                                    {member.role}
                                  </Badge>
                                </TableCell>
                                <TableCell>{getStatusBadge(member.status)}</TableCell>
                                <TableCell className="text-sm text-muted-foreground">
                                  {formatTimestamp(member.lastActive)}
                                </TableCell>
                                <TableCell>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="sm">
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                      <DropdownMenuItem>Edit User</DropdownMenuItem>
                                      <DropdownMenuItem>Change Role</DropdownMenuItem>
                                      <DropdownMenuItem className="text-destructive">
                                        Remove User
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between">
                        <CardTitle>Audit Logs</CardTitle>
                        <Button variant="outline">
                          <Download className="h-4 w-4 mr-2" />
                          Export Logs
                        </Button>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {mockAuditLogs.map((log) => (
                            <div key={log.id} className="flex items-start space-x-3 p-3 rounded-lg border border-border">
                              <div className="p-1 rounded-full bg-primary/10 mt-1">
                                <Clock className="h-3 w-3 text-primary" />
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <h4 className="font-medium">{log.action}</h4>
                                  <span className="text-xs text-muted-foreground">
                                    {formatTimestamp(log.timestamp)}
                                  </span>
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">{log.details}</p>
                                <p className="text-xs text-muted-foreground mt-1">by {log.user}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Data Export</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center space-x-3">
                          <Button variant="outline">
                            <Download className="h-4 w-4 mr-2" />
                            Export as CSV
                          </Button>
                          <Button variant="outline">
                            <Download className="h-4 w-4 mr-2" />
                            Export as JSON
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                </TabsContent>

                {/* Security Tab */}
                <TabsContent key="security" value="security" className="mt-0">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    <Card>
                      <CardHeader>
                        <CardTitle>Two-Factor Authentication</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor={twoFactorId} className="text-base">Enable 2FA</Label>
                            <p className="text-sm text-muted-foreground">
                              Add an extra layer of security to your account
                            </p>
                          </div>
                          <Switch
                            id={twoFactorId}
                            checked={twoFactorEnabled}
                            onCheckedChange={setTwoFactorEnabled}
                            className="rounded-sm [&_span]:rounded"
                          />
                        </div>

                        {twoFactorEnabled && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            className="border border-border rounded-lg p-4 space-y-3"
                          >
                            <p className="text-sm font-medium">Scan QR Code</p>
                            <div className="w-32 h-32 bg-muted rounded-lg flex items-center justify-center">
                              <p className="text-xs text-muted-foreground text-center">
                                QR Code<br />Placeholder
                              </p>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              Scan this QR code with your authenticator app
                            </p>
                          </motion.div>
                        )}
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between">
                        <CardTitle>Active Sessions</CardTitle>
                        <Button variant="outline" size="sm">
                          <LogOut className="h-4 w-4 mr-2" />
                          Logout All
                        </Button>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {mockSessions.map((session) => (
                            <div 
                              key={session.id} 
                              className="flex items-center justify-between p-3 rounded-lg border border-border"
                            >
                              <div className="flex items-center space-x-3">
                                <div className="p-2 rounded-md bg-primary/10">
                                  {session.device.includes('iPhone') ? (
                                    <Smartphone className="h-4 w-4 text-primary" />
                                  ) : (
                                    <Monitor className="h-4 w-4 text-primary" />
                                  )}
                                </div>
                                <div>
                                  <p className="font-medium">{session.device}</p>
                                  <p className="text-sm text-muted-foreground">
                                    {session.location} • {formatTimestamp(session.lastActive)}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                {session.current && (
                                  <Badge className="bg-green-500/10 text-green-600 border-green-500/20">
                                    Current
                                  </Badge>
                                )}
                                {!session.current && (
                                  <Button variant="ghost" size="sm">
                                    <LogOut className="h-4 w-4" />
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Privacy Settings</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label>Data Retention Period</Label>
                          <Select defaultValue="12">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="6">6 months</SelectItem>
                              <SelectItem value="12">12 months</SelectItem>
                              <SelectItem value="24">24 months</SelectItem>
                              <SelectItem value="60">5 years</SelectItem>
                            </SelectContent>
                          </Select>
                          <p className="text-xs text-muted-foreground">
                            How long to keep deleted data before permanent removal
                          </p>
                        </div>
                        
                        <Button variant="outline" className="text-destructive border-destructive hover:bg-destructive/10">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete All My Data
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                </TabsContent>
              </AnimatePresence>
            </div>

            {/* Right Sidebar - Tips & AI Helper */}
            <div className="lg:col-span-1">
              <Card className="sticky top-6">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <HelpCircle className="h-5 w-5 text-primary" />
                    <span>Quick Tips</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {activeTab === 'profile' && (
                    <div key="profile-tips" className="space-y-3">
                      <div className="p-3 rounded-lg bg-primary/5 border border-primary/20">
                        <p className="text-sm text-foreground font-medium mb-1">Profile Photo</p>
                        <p className="text-xs text-muted-foreground">
                          A professional photo helps build trust with customers
                        </p>
                      </div>
                      <div className="p-3 rounded-lg bg-blue-500/5 border border-blue-500/20">
                        <p className="text-sm text-foreground font-medium mb-1">Strong Password</p>
                        <p className="text-xs text-muted-foreground">
                          Use at least 12 characters with mixed case and symbols
                        </p>
                      </div>
                    </div>
                  )}

                  {activeTab === 'preferences' && (
                    <div key="preferences-tips" className="space-y-3">
                      <div className="p-3 rounded-lg bg-primary/5 border border-primary/20">
                        <p className="text-sm text-foreground font-medium mb-1">Notifications</p>
                        <p className="text-xs text-muted-foreground">
                          Enable SMS for urgent project updates
                        </p>
                      </div>
                    </div>
                  )}

                  {activeTab === 'integrations' && (
                    <div key="integrations-tips" className="space-y-3">
                      <div className="p-3 rounded-lg bg-primary/5 border border-primary/20">
                        <p className="text-sm text-foreground font-medium mb-1">Google Maps</p>
                        <p className="text-xs text-muted-foreground">
                          Connect to get automatic driving directions to appointments
                        </p>
                      </div>
                    </div>
                  )}

                  {activeTab === 'security' && (
                    <div key="security-tips" className="space-y-3">
                      <div className="p-3 rounded-lg bg-primary/5 border border-primary/20">
                        <p className="text-sm text-foreground font-medium mb-1">2FA Required</p>
                        <p className="text-xs text-muted-foreground">
                          Admin accounts must have two-factor authentication enabled
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="pt-4 border-t border-border">
                    <div className="space-y-3">
                      <p className="text-sm font-medium">Need help with integrations?</p>
                      <div className="flex items-center space-x-2">
                        <Input placeholder="Ask here..." className="text-sm" />
                        <Button size="sm" variant="outline">
                          Ask
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </Tabs>
      </motion.div>

      {/* Footer */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="flex items-center justify-between pt-6 border-t border-border"
      >
        <div className="text-sm text-muted-foreground">
          WindowWorks CRM v2.1.0
        </div>
        <Button variant="outline" size="sm">
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset to Defaults
        </Button>
      </motion.div>
    </div>
  )
}
