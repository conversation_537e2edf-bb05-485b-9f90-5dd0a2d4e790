{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/stores/auth-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, persist } from 'zustand/middleware'\r\nimport type { User } from '@/types'\r\n\r\ninterface AuthState {\r\n  user: User | null\r\n  isLoading: boolean\r\n  isAuthenticated: boolean\r\n}\r\n\r\ninterface AuthActions {\r\n  setUser: (user: User | null) => void\r\n  setLoading: (loading: boolean) => void\r\n  clearAuth: () => void\r\n}\r\n\r\nexport const useAuthStore = create<AuthState & AuthActions>()(\r\n  devtools(\r\n    persist(\r\n      (set) => ({\r\n        user: null,\r\n        isLoading: true,\r\n        isAuthenticated: false,\r\n        setUser: (user) =>\r\n          set(\r\n            {\r\n              user,\r\n              isAuthenticated: !!user,\r\n              isLoading: false,\r\n            },\r\n            false,\r\n            'setUser'\r\n          ),\r\n        setLoading: (isLoading) =>\r\n          set({ isLoading }, false, 'setLoading'),\r\n        clearAuth: () =>\r\n          set(\r\n            {\r\n              user: null,\r\n              isAuthenticated: false,\r\n              isLoading: false,\r\n            },\r\n            false,\r\n            'clearAuth'\r\n          ),\r\n      }),\r\n      {\r\n        name: 'auth-storage',\r\n        partialize: (state) => ({\r\n          user: state.user,\r\n          isAuthenticated: state.isAuthenticated,\r\n        }),\r\n      }\r\n    ),\r\n    {\r\n      name: 'auth-store',\r\n    }\r\n  )\r\n)\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAeO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,WAAW;QACX,iBAAiB;QACjB,SAAS,CAAC,OACR,IACE;gBACE;gBACA,iBAAiB,CAAC,CAAC;gBACnB,WAAW;YACb,GACA,OACA;QAEJ,YAAY,CAAC,YACX,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B,WAAW,IACT,IACE;gBACE,MAAM;gBACN,iBAAiB;gBACjB,WAAW;YACb,GACA,OACA;IAEN,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH,IAEF;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/stores/notification-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools } from 'zustand/middleware'\r\nimport type { Notification } from '@/types'\r\n\r\ninterface NotificationState {\r\n  notifications: Notification[]\r\n  unreadCount: number\r\n}\r\n\r\ninterface NotificationActions {\r\n  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void\r\n  markAsRead: (id: string) => void\r\n  markAllAsRead: () => void\r\n  removeNotification: (id: string) => void\r\n  setNotifications: (notifications: Notification[]) => void\r\n}\r\n\r\nexport const useNotificationStore = create<NotificationState & NotificationActions>()(\r\n  devtools(\r\n    (set) => ({\r\n      notifications: [],\r\n      unreadCount: 0,\r\n      addNotification: (notification) => {\r\n        const newNotification: Notification = {\r\n          ...notification,\r\n          id: crypto.randomUUID(),\r\n          createdAt: new Date().toISOString(),\r\n        }\r\n        set(\r\n          (state) => ({\r\n            notifications: [newNotification, ...state.notifications],\r\n            unreadCount: state.unreadCount + 1,\r\n          }),\r\n          false,\r\n          'addNotification'\r\n        )\r\n      },\r\n      markAsRead: (id) =>\r\n        set(\r\n          (state) => ({\r\n            notifications: state.notifications.map((notification) =>\r\n              notification.id === id\r\n                ? { ...notification, read: true }\r\n                : notification\r\n            ),\r\n            unreadCount: Math.max(0, state.unreadCount - 1),\r\n          }),\r\n          false,\r\n          'markAsRead'\r\n        ),\r\n      markAllAsRead: () =>\r\n        set(\r\n          (state) => ({\r\n            notifications: state.notifications.map((notification) => ({\r\n              ...notification,\r\n              read: true,\r\n            })),\r\n            unreadCount: 0,\r\n          }),\r\n          false,\r\n          'markAllAsRead'\r\n        ),\r\n      removeNotification: (id) =>\r\n        set(\r\n          (state) => {\r\n            const notification = state.notifications.find((n) => n.id === id)\r\n            const wasUnread = notification && !notification.read\r\n            return {\r\n              notifications: state.notifications.filter((n) => n.id !== id),\r\n              unreadCount: wasUnread\r\n                ? Math.max(0, state.unreadCount - 1)\r\n                : state.unreadCount,\r\n            }\r\n          },\r\n          false,\r\n          'removeNotification'\r\n        ),\r\n      setNotifications: (notifications) =>\r\n        set(\r\n          {\r\n            notifications,\r\n            unreadCount: notifications.filter((n) => !n.read).length,\r\n          },\r\n          false,\r\n          'setNotifications'\r\n        ),\r\n    }),\r\n    {\r\n      name: 'notification-store',\r\n    }\r\n  )\r\n)\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBO,MAAM,uBAAuB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACvC,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAC,MAAQ,CAAC;QACR,eAAe,EAAE;QACjB,aAAa;QACb,iBAAiB,CAAC;YAChB,MAAM,kBAAgC;gBACpC,GAAG,YAAY;gBACf,IAAI,OAAO,UAAU;gBACrB,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,IACE,CAAC,QAAU,CAAC;oBACV,eAAe;wBAAC;2BAAoB,MAAM,aAAa;qBAAC;oBACxD,aAAa,MAAM,WAAW,GAAG;gBACnC,CAAC,GACD,OACA;QAEJ;QACA,YAAY,CAAC,KACX,IACE,CAAC,QAAU,CAAC;oBACV,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eACtC,aAAa,EAAE,KAAK,KAChB;4BAAE,GAAG,YAAY;4BAAE,MAAM;wBAAK,IAC9B;oBAEN,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;gBAC/C,CAAC,GACD,OACA;QAEJ,eAAe,IACb,IACE,CAAC,QAAU,CAAC;oBACV,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eAAiB,CAAC;4BACxD,GAAG,YAAY;4BACf,MAAM;wBACR,CAAC;oBACD,aAAa;gBACf,CAAC,GACD,OACA;QAEJ,oBAAoB,CAAC,KACnB,IACE,CAAC;gBACC,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAC9D,MAAM,YAAY,gBAAgB,CAAC,aAAa,IAAI;gBACpD,OAAO;oBACL,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAC1D,aAAa,YACT,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG,KAChC,MAAM,WAAW;gBACvB;YACF,GACA,OACA;QAEJ,kBAAkB,CAAC,gBACjB,IACE;gBACE;gBACA,aAAa,cAAc,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,IAAI,EAAE,MAAM;YAC1D,GACA,OACA;IAEN,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/dashboard/dashboard-layout.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ReactNode } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { \r\n  Bell, \r\n  Home, \r\n  Users, \r\n  FolderOpen, \r\n  Calendar,\r\n  Settings,\r\n  LogOut,\r\n  Menu,\r\n  Search\r\n} from 'lucide-react'\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\nimport { useAuthStore } from '@/lib/stores/auth-store'\r\nimport { useNotificationStore } from '@/lib/stores/notification-store'\r\n\r\ninterface DashboardLayoutProps {\r\n  children: ReactNode\r\n}\r\n\r\nconst navigationItems = [\r\n  { icon: Home, label: 'Dashboard', href: '/', badge: null },\r\n  { icon: Users, label: 'Customers', href: '/customers', badge: null },\r\n  { icon: FolderOpen, label: 'Projects', href: '/projects', badge: null },\r\n  { icon: Calendar, label: 'Schedule', href: '/schedule', badge: null },\r\n  { icon: Settings, label: 'Settings', href: '/settings', badge: null },\r\n]\r\n\r\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\r\n  const { user, clearAuth } = useAuthStore()\r\n  const { unreadCount } = useNotificationStore()\r\n  const pathname = usePathname()\r\n\r\n  const handleLogout = () => {\r\n    clearAuth()\r\n    // In a real app, you'd also call Supabase signOut here\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-screen bg-background flex overflow-hidden\">\r\n      {/* Sidebar */}\r\n      <motion.aside\r\n        initial={{ x: -300 }}\r\n        animate={{ x: 0 }}\r\n        className=\"w-64 bg-card border-r border-border flex flex-col fixed left-0 top-0 h-full z-30\"\r\n      >\r\n        {/* Logo */}\r\n        <div className=\"p-6 border-b border-border flex-shrink-0\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.2 }}\r\n            className=\"flex items-center space-x-3\"\r\n          >\r\n            <div className=\"w-8 h-8 bg-[var(--color-brand-primary)] rounded-md flex items-center justify-center\">\r\n              <span className=\"text-white font-bold text-sm\">W</span>\r\n            </div>\r\n            <div>\r\n              <h1 className=\"text-xl font-bold text-foreground\">WindowWorks</h1>\r\n              <p className=\"text-xs text-muted-foreground\">CRM Platform</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <nav className=\"flex-1 p-4 space-y-2 overflow-y-auto\">\r\n          {navigationItems.map((item, index) => {\r\n            const isActive = pathname === item.href\r\n            return (\r\n              <motion.div\r\n                key={item.href}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                transition={{ delay: 0.1 * (index + 1) }}\r\n              >\r\n                <Link href={item.href}>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className={`w-full justify-start rounded-md ${\r\n                      isActive \r\n                        ? 'bg-primary/10 text-primary border-primary/20' \r\n                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'\r\n                    }`}\r\n                  >\r\n                    <item.icon className=\"mr-3 h-5 w-5\" />\r\n                    {item.label}\r\n                    {item.badge && (\r\n                      <Badge variant=\"secondary\" className=\"ml-auto\">\r\n                        {item.badge}\r\n                      </Badge>\r\n                    )}\r\n                  </Button>\r\n                </Link>\r\n              </motion.div>\r\n            )\r\n          })}\r\n        </nav>\r\n\r\n        {/* User Profile - Sticky at bottom */}\r\n        <div className=\"p-4 border-t border-border flex-shrink-0 sticky bottom-0 bg-card\">\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" className=\"w-full justify-start p-3 hover:bg-accent\">\r\n                <Avatar className=\"h-8 w-8 mr-3\">\r\n                  <AvatarImage src={user?.profile?.avatar} />\r\n                  <AvatarFallback className=\"bg-primary/10 text-primary\">\r\n                    {user?.profile?.firstName?.[0]}{user?.profile?.lastName?.[0]}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"text-left\">\r\n                  <p className=\"text-sm font-medium text-foreground\">\r\n                    {user?.profile?.firstName} {user?.profile?.lastName}\r\n                  </p>\r\n                  <p className=\"text-xs text-muted-foreground capitalize\">{user?.role}</p>\r\n                </div>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"start\" className=\"w-56\">\r\n              <DropdownMenuItem>\r\n                <Settings className=\"mr-2 h-4 w-4\" />\r\n                Account Settings\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                Sign Out\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </motion.aside>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 flex flex-col bg-background ml-64\">\r\n        {/* Top Header - Sticky */}\r\n        <motion.header\r\n          initial={{ y: -50, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"bg-card border-b border-border px-6 py-4 sticky top-0 z-20 flex-shrink-0\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\r\n                <Menu className=\"h-5 w-5\" />\r\n              </Button>\r\n              \r\n              {/* Search */}\r\n              <div className=\"relative w-96 hidden md:block\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\r\n                <Input\r\n                  placeholder=\"Search customers, projects...\"\r\n                  className=\"pl-10 bg-accent/50 border-border focus:bg-background\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Notifications */}\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\r\n                <Bell className=\"h-5 w-5\" />\r\n                {unreadCount > 0 && (\r\n                  <motion.div\r\n                    initial={{ scale: 0 }}\r\n                    animate={{ scale: 1 }}\r\n                    className=\"absolute -top-1 -right-1 h-5 w-5 bg-destructive rounded-full flex items-center justify-center\"\r\n                  >\r\n                    <span className=\"text-xs text-destructive-foreground font-medium\">\r\n                      {unreadCount > 9 ? '9+' : unreadCount}\r\n                    </span>\r\n                  </motion.div>\r\n                )}\r\n              </Button>\r\n\r\n\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content - Scrollable */}\r\n        <main className=\"flex-1 overflow-y-auto bg-background\">\r\n          <div className=\"p-6\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.3 }}\r\n            >\r\n              {children}\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AAMA;AACA;AA5BA;;;;;;;;;;;;;AAkCA,MAAM,kBAAkB;IACtB;QAAE,MAAM,mMAAA,CAAA,OAAI;QAAE,OAAO;QAAa,MAAM;QAAK,OAAO;IAAK;IACzD;QAAE,MAAM,oMAAA,CAAA,QAAK;QAAE,OAAO;QAAa,MAAM;QAAc,OAAO;IAAK;IACnE;QAAE,MAAM,kNAAA,CAAA,aAAU;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;IACtE;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;IACpE;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;CACrE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,uBAAoB,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB;IACA,uDAAuD;IACzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;kCAMnD,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM;4BAC1B,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,CAAC,QAAQ,CAAC;gCAAE;0CAEvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,IAAI;8CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAW,CAAC,gCAAgC,EAC1C,WACI,iDACA,+DACJ;;0DAEF,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,KAAK;4CACV,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,KAAK,KAAK;;;;;;;;;;;;;;;;;+BAlBd,KAAK,IAAI;;;;;wBAyBpB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;;0DAChC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDAAC,KAAK,MAAM,SAAS;;;;;;kEACjC,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;;4DACvB,MAAM,SAAS,WAAW,CAAC,EAAE;4DAAE,MAAM,SAAS,UAAU,CAAC,EAAE;;;;;;;;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DACV,MAAM,SAAS;4DAAU;4DAAE,MAAM,SAAS;;;;;;;kEAE7C,8OAAC;wDAAE,WAAU;kEAA4C,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAIrE,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAQ,WAAU;;sDAC3C,8OAAC,4IAAA,CAAA,mBAAgB;;8DACf,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BAAE,GAAG,CAAC;4BAAI,SAAS;wBAAE;wBAC9B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;;0DAC1C,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,cAAc,mBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO;gDAAE;gDACpB,WAAU;0DAEV,cAAA,8OAAC;oDAAK,WAAU;8DACb,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYxC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAExB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/textarea.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nTextarea.displayName = 'Textarea'\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm transition-colors\",\n        \"border-border dark:border-muted-foreground/50 dark:hover:border-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,oFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/switch.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface SwitchProps {\r\n  checked?: boolean\r\n  onCheckedChange?: (checked: boolean) => void\r\n  disabled?: boolean\r\n  className?: string\r\n  id?: string\r\n}\r\n\r\nconst Switch = React.forwardRef<HTMLButtonElement, SwitchProps>(\r\n  ({ className, checked = false, onCheckedChange, disabled = false, id, ...props }, ref) => {\r\n    const handleClick = () => {\r\n      if (!disabled && onCheckedChange) {\r\n        onCheckedChange(!checked)\r\n      }\r\n    }\r\n\r\n    return (\r\n      <button\r\n        ref={ref}\r\n        type=\"button\"\r\n        role=\"switch\"\r\n        aria-checked={checked}\r\n        disabled={disabled}\r\n        onClick={handleClick}\r\n        id={id}\r\n        className={cn(\r\n          'peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-sm border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50',\r\n          checked ? 'bg-primary' : 'bg-input',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <span\r\n          className={cn(\r\n            'pointer-events-none block h-5 w-5 rounded bg-background shadow-lg ring-0 transition-transform',\r\n            checked ? 'translate-x-5' : 'translate-x-0'\r\n          )}\r\n        />\r\n      </button>\r\n    )\r\n  }\r\n)\r\nSwitch.displayName = 'Switch'\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAaA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,eAAe,EAAE,WAAW,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAChF,MAAM,cAAc;QAClB,IAAI,CAAC,YAAY,iBAAiB;YAChC,gBAAgB,CAAC;QACnB;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,MAAK;QACL,MAAK;QACL,gBAAc;QACd,UAAU;QACV,SAAS;QACT,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oTACA,UAAU,eAAe,YACzB;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,UAAU,kBAAkB;;;;;;;;;;;AAKtC;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1378, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1570, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/table.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport { cn } from '@/lib/utils'\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn('w-full caption-bottom text-sm', className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n))\r\nTable.displayName = 'Table'\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn('[&_tr]:border-b', className)} {...props} />\r\n))\r\nTableHeader.displayName = 'TableHeader'\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn('[&_tr:last-child]:border-0', className)}\r\n    {...props}\r\n  />\r\n))\r\nTableBody.displayName = 'TableBody'\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      'border-t bg-muted/50 font-medium [&>tr]:last:border-b-0',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableFooter.displayName = 'TableFooter'\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableRow.displayName = 'TableRow'\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      'h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableHead.displayName = 'TableHead'\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn('p-4 align-middle [&:has([role=checkbox])]:pr-0', className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCell.displayName = 'TableCell'\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn('mt-4 text-sm text-muted-foreground', className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCaption.displayName = 'TableCaption'\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1681, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/settings/settings-page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useRef, useId, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { \r\n  User, \r\n  Mail, \r\n  Lock, \r\n  Eye, \r\n  EyeOff, \r\n  Upload, \r\n  Moon, \r\n  Sun, \r\n  Globe,\r\n  Calendar,\r\n  MapPin,\r\n  Shield,\r\n  Download,\r\n  Trash2,\r\n  Edit,\r\n  UserPlus,\r\n  RotateCcw,\r\n  Smartphone,\r\n  Monitor,\r\n  LogOut,\r\n  Settings as SettingsIcon,\r\n  Clock,\r\n  Save,\r\n  Camera,\r\n  HelpCircle\r\n} from 'lucide-react'\r\n\r\nimport { Button } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Textarea } from '@/components/ui/textarea'\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport { Switch } from '@/components/ui/switch'\r\nimport { <PERSON><PERSON>, TabsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\nimport { useTheme } from '@/contexts/theme-context'\r\n\r\n// Types\r\ninterface UserProfile {\r\n  id: string\r\n  name: string\r\n  email: string\r\n  phone: string\r\n  bio: string\r\n  avatar: string | null\r\n  role: 'Admin' | 'Manager' | 'Installer' | 'User'\r\n}\r\n\r\ninterface UserPreferences {\r\n  darkMode: boolean\r\n  emailNotifications: boolean\r\n  smsAlerts: boolean\r\n  language: 'en' | 'es'\r\n  timezone: string\r\n  customAccentColor: string\r\n}\r\n\r\ninterface Integration {\r\n  id: string\r\n  name: string\r\n  description: string\r\n  status: 'connected' | 'disconnected' | 'error'\r\n  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>\r\n  connectionType: 'oauth' | 'api-key' | 'toggle'\r\n  apiKey?: string\r\n}\r\n\r\ninterface TeamMember {\r\n  id: string\r\n  name: string\r\n  email: string\r\n  role: 'Admin' | 'Manager' | 'Installer' | 'User'\r\n  status: 'Active' | 'Inactive' | 'Pending'\r\n  lastActive: string\r\n}\r\n\r\ninterface AuditLog {\r\n  id: string\r\n  action: string\r\n  user: string\r\n  timestamp: string\r\n  details: string\r\n}\r\n\r\ninterface SecuritySession {\r\n  id: string\r\n  device: string\r\n  location: string\r\n  lastActive: string\r\n  current: boolean\r\n}\r\n\r\n// Mock data\r\nconst mockProfile: UserProfile = {\r\n  id: 'user_001',\r\n  name: 'John Admin',\r\n  email: '<EMAIL>',\r\n  phone: '(*************',\r\n  bio: 'Operations manager with 5+ years in window treatment industry.',\r\n  avatar: null,\r\n  role: 'Admin'\r\n}\r\n\r\nconst mockPreferences: UserPreferences = {\r\n  darkMode: false,\r\n  emailNotifications: true,\r\n  smsAlerts: false,\r\n  language: 'en',\r\n  timezone: 'America/Chicago',\r\n  customAccentColor: '#D97706'\r\n}\r\n\r\nconst mockIntegrations: Integration[] = [\r\n  {\r\n    id: 'google-maps',\r\n    name: 'Google Maps',\r\n    description: 'Get directions and location data for customer addresses',\r\n    status: 'connected',\r\n    icon: MapPin,\r\n    connectionType: 'oauth'\r\n  },\r\n  {\r\n    id: 'email-provider',\r\n    name: 'Email Provider',\r\n    description: 'Send automated emails and notifications',\r\n    status: 'disconnected',\r\n    icon: Mail,\r\n    connectionType: 'api-key',\r\n    apiKey: ''\r\n  },\r\n  {\r\n    id: 'calendar-sync',\r\n    name: 'Calendar Sync',\r\n    description: 'Sync appointments with external calendars',\r\n    status: 'connected',\r\n    icon: Calendar,\r\n    connectionType: 'toggle'\r\n  }\r\n]\r\n\r\nconst mockTeamMembers: TeamMember[] = [\r\n  {\r\n    id: 'team_001',\r\n    name: 'John Admin',\r\n    email: '<EMAIL>',\r\n    role: 'Admin',\r\n    status: 'Active',\r\n    lastActive: '2025-07-12T14:30:00Z'\r\n  },\r\n  {\r\n    id: 'team_002',\r\n    name: 'Sarah Manager',\r\n    email: '<EMAIL>',\r\n    role: 'Manager',\r\n    status: 'Active',\r\n    lastActive: '2025-07-12T12:15:00Z'\r\n  },\r\n  {\r\n    id: 'team_003',\r\n    name: 'Mike Installer',\r\n    email: '<EMAIL>',\r\n    role: 'Installer',\r\n    status: 'Active',\r\n    lastActive: '2025-07-12T09:45:00Z'\r\n  },\r\n  {\r\n    id: 'team_004',\r\n    name: 'Lisa Anderson',\r\n    email: '<EMAIL>',\r\n    role: 'Installer',\r\n    status: 'Inactive',\r\n    lastActive: '2025-07-10T16:20:00Z'\r\n  }\r\n]\r\n\r\nconst mockAuditLogs: AuditLog[] = [\r\n  {\r\n    id: 'audit_001',\r\n    action: 'Project Created',\r\n    user: 'John Admin',\r\n    timestamp: '2025-07-12T14:30:00Z',\r\n    details: 'Created project \"Living Room Shutters\" for Sarah Johnson'\r\n  },\r\n  {\r\n    id: 'audit_002',\r\n    action: 'User Invited',\r\n    user: 'John Admin',\r\n    timestamp: '2025-07-12T13:15:00Z',\r\n    details: 'Invited new installer: <EMAIL>'\r\n  },\r\n  {\r\n    id: 'audit_003',\r\n    action: 'Settings Updated',\r\n    user: 'Sarah Manager',\r\n    timestamp: '2025-07-12T11:20:00Z',\r\n    details: 'Updated notification preferences'\r\n  }\r\n]\r\n\r\nconst mockSessions: SecuritySession[] = [\r\n  {\r\n    id: 'session_001',\r\n    device: 'Chrome on Windows',\r\n    location: 'Chicago, IL',\r\n    lastActive: '2025-07-12T14:30:00Z',\r\n    current: true\r\n  },\r\n  {\r\n    id: 'session_002',\r\n    device: 'Safari on iPhone',\r\n    location: 'Chicago, IL',\r\n    lastActive: '2025-07-12T09:15:00Z',\r\n    current: false\r\n  }\r\n]\r\n\r\nexport default function SettingsPage() {\r\n  const { theme, setTheme, accentColor, setAccentColor } = useTheme()\r\n  const [activeTab, setActiveTab] = useState('profile')\r\n  const [profile, setProfile] = useState(mockProfile)\r\n  const [preferences, setPreferences] = useState({\r\n    ...mockPreferences,\r\n    darkMode: false, // Will be updated by useEffect\r\n    customAccentColor: '#D97706' // Will be updated by useEffect\r\n  })\r\n  const [integrations, setIntegrations] = useState(mockIntegrations)\r\n  const [hasChanges, setHasChanges] = useState(false)\r\n  const [showPassword, setShowPassword] = useState(false)\r\n  const [dragActive, setDragActive] = useState(false)\r\n  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)\r\n  const fileInputRef = useRef<HTMLInputElement>(null)\r\n\r\n  // Sync preferences with theme context\r\n  useEffect(() => {\r\n    setPreferences(prev => ({\r\n      ...prev,\r\n      darkMode: theme === 'dark',\r\n      customAccentColor: accentColor\r\n    }))\r\n  }, [theme, accentColor])\r\n  \r\n  // Generate unique IDs for switches\r\n  const darkModeId = useId()\r\n  const emailNotificationsId = useId()\r\n  const smsAlertsId = useId()\r\n  const twoFactorId = useId()\r\n\r\n  const handleProfileUpdate = (field: keyof UserProfile, value: string) => {\r\n    setProfile(prev => ({ ...prev, [field]: value }))\r\n    setHasChanges(true)\r\n  }\r\n\r\n  const handlePreferenceUpdate = (field: keyof UserPreferences, value: string | boolean) => {\r\n    if (field === 'darkMode') {\r\n      setTheme(value ? 'dark' : 'light')\r\n    } else if (field === 'customAccentColor') {\r\n      setAccentColor(value as string)\r\n    }\r\n    setPreferences(prev => ({ ...prev, [field]: value }))\r\n    setHasChanges(true)\r\n  }\r\n\r\n  const handleSaveChanges = () => {\r\n    // Save logic here\r\n    console.log('Saving changes...', { profile, preferences })\r\n    setHasChanges(false)\r\n  }\r\n\r\n  const handleAvatarUpload = (files: FileList | null) => {\r\n    if (files && files[0]) {\r\n      const file = files[0]\r\n      const reader = new FileReader()\r\n      reader.onload = (e) => {\r\n        const result = e.target?.result as string\r\n        handleProfileUpdate('avatar', result)\r\n      }\r\n      reader.readAsDataURL(file)\r\n    }\r\n  }\r\n\r\n  const handleDrag = (e: React.DragEvent) => {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    if (e.type === 'dragenter' || e.type === 'dragover') {\r\n      setDragActive(true)\r\n    } else if (e.type === 'dragleave') {\r\n      setDragActive(false)\r\n    }\r\n  }\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    setDragActive(false)\r\n    \r\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\r\n      handleAvatarUpload(e.dataTransfer.files)\r\n    }\r\n  }\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'active':\r\n      case 'connected':\r\n        return <Badge className=\"bg-green-500/10 text-green-600 border-green-500/20\">Active</Badge>\r\n      case 'inactive':\r\n      case 'disconnected':\r\n        return <Badge variant=\"secondary\">Inactive</Badge>\r\n      case 'pending':\r\n        return <Badge className=\"bg-yellow-500/10 text-yellow-600 border-yellow-500/20\">Pending</Badge>\r\n      case 'error':\r\n        return <Badge variant=\"destructive\">Error</Badge>\r\n      default:\r\n        return <Badge variant=\"outline\">{status}</Badge>\r\n    }\r\n  }\r\n\r\n  const getRoleColor = (role: string) => {\r\n    switch (role.toLowerCase()) {\r\n      case 'admin':\r\n        return 'bg-primary/10 text-primary border-primary/20'\r\n      case 'manager':\r\n        return 'bg-blue-500/10 text-blue-600 border-blue-500/20'\r\n      case 'installer':\r\n        return 'bg-green-500/10 text-green-600 border-green-500/20'\r\n      default:\r\n        return 'bg-gray-500/10 text-gray-600 border-gray-500/20'\r\n    }\r\n  }\r\n\r\n  const formatTimestamp = (timestamp: string) => {\r\n    return new Date(timestamp).toLocaleString()\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex items-center justify-between\"\r\n      >\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-foreground\">Settings</h1>\r\n          <p className=\"text-muted-foreground\">\r\n            Customize your profile, preferences, and platform integrations\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-3\">\r\n          <Badge variant=\"secondary\" className=\"text-xs\">\r\n            Saturday, July 12, 2025\r\n          </Badge>\r\n          <Button \r\n            className=\"bg-primary hover:bg-primary/90\"\r\n            disabled={!hasChanges}\r\n            onClick={handleSaveChanges}\r\n          >\r\n            <Save className=\"h-4 w-4 mr-2\" />\r\n            Save Changes\r\n          </Button>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Main Content */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.1 }}\r\n      >\r\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n          <TabsList className=\"grid grid-cols-5 w-full max-w-2xl\">\r\n            <TabsTrigger value=\"profile\" className=\"flex items-center space-x-2\">\r\n              <User className=\"h-4 w-4\" />\r\n              <span className=\"hidden sm:inline\">Profile</span>\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"preferences\" className=\"flex items-center space-x-2\">\r\n              <SettingsIcon className=\"h-4 w-4\" />\r\n              <span className=\"hidden sm:inline\">Preferences</span>\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"integrations\" className=\"flex items-center space-x-2\">\r\n              <Globe className=\"h-4 w-4\" />\r\n              <span className=\"hidden sm:inline\">Integrations</span>\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"admin\" className=\"flex items-center space-x-2\">\r\n              <Shield className=\"h-4 w-4\" />\r\n              <span className=\"hidden sm:inline\">Admin</span>\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"security\" className=\"flex items-center space-x-2\">\r\n              <Lock className=\"h-4 w-4\" />\r\n              <span className=\"hidden sm:inline\">Security</span>\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          <div className=\"mt-6 grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n            {/* Main Content Area */}\r\n            <div className=\"lg:col-span-3\">\r\n              <AnimatePresence mode=\"wait\">\r\n                {/* Profile Tab */}\r\n                <TabsContent key=\"profile\" value=\"profile\" className=\"mt-0\">\r\n                  <motion.div\r\n                    initial={{ opacity: 0, x: 20 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: -20 }}\r\n                    className=\"space-y-6\"\r\n                  >\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Profile Information</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent className=\"space-y-6\">\r\n                        {/* Avatar Upload */}\r\n                        <div className=\"flex items-center space-x-6\">\r\n                          <div className=\"relative\">\r\n                            <Avatar className=\"h-24 w-24\">\r\n                              <AvatarImage src={profile.avatar || undefined} />\r\n                              <AvatarFallback className=\"bg-primary/10 text-primary text-xl\">\r\n                                {profile.name.split(' ').map(n => n[0]).join('')}\r\n                              </AvatarFallback>\r\n                            </Avatar>\r\n                            <button\r\n                              onClick={() => fileInputRef.current?.click()}\r\n                              className=\"absolute -bottom-2 -right-2 bg-primary text-white rounded-full p-2 hover:bg-primary/90 transition-colors\"\r\n                            >\r\n                              <Camera className=\"h-4 w-4\" />\r\n                            </button>\r\n                          </div>\r\n                          \r\n                          <div className=\"flex-1\">\r\n                            <div\r\n                              className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${\r\n                                dragActive ? 'border-primary bg-primary/5' : 'border-border'\r\n                              }`}\r\n                              onDragEnter={handleDrag}\r\n                              onDragLeave={handleDrag}\r\n                              onDragOver={handleDrag}\r\n                              onDrop={handleDrop}\r\n                            >\r\n                              <Upload className=\"h-8 w-8 text-muted-foreground mx-auto mb-2\" />\r\n                              <p className=\"text-sm text-muted-foreground\">\r\n                                Drag and drop an image, or{' '}\r\n                                <button\r\n                                  onClick={() => fileInputRef.current?.click()}\r\n                                  className=\"text-primary hover:underline\"\r\n                                >\r\n                                  browse\r\n                                </button>\r\n                              </p>\r\n                              <p className=\"text-xs text-muted-foreground mt-1\">\r\n                                JPG, PNG up to 2MB\r\n                              </p>\r\n                            </div>\r\n                            <input\r\n                              ref={fileInputRef}\r\n                              type=\"file\"\r\n                              accept=\"image/*\"\r\n                              onChange={(e) => handleAvatarUpload(e.target.files)}\r\n                              className=\"hidden\"\r\n                            />\r\n                          </div>\r\n                        </div>\r\n\r\n                        {/* Form Fields */}\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                          <div className=\"space-y-2\">\r\n                            <Label htmlFor=\"name\">Full Name</Label>\r\n                            <Input\r\n                              id=\"name\"\r\n                              value={profile.name}\r\n                              onChange={(e) => handleProfileUpdate('name', e.target.value)}\r\n                            />\r\n                          </div>\r\n                          \r\n                          <div className=\"space-y-2\">\r\n                            <Label htmlFor=\"email\">Email Address</Label>\r\n                            <Input\r\n                              id=\"email\"\r\n                              type=\"email\"\r\n                              value={profile.email}\r\n                              onChange={(e) => handleProfileUpdate('email', e.target.value)}\r\n                            />\r\n                          </div>\r\n                          \r\n                          <div className=\"space-y-2\">\r\n                            <Label htmlFor=\"phone\">Phone Number</Label>\r\n                            <Input\r\n                              id=\"phone\"\r\n                              value={profile.phone}\r\n                              onChange={(e) => handleProfileUpdate('phone', e.target.value)}\r\n                            />\r\n                          </div>\r\n                          \r\n                          <div className=\"space-y-2\">\r\n                            <Label htmlFor=\"role\">Role</Label>\r\n                            <div className=\"pt-2\">\r\n                              <Badge className={getRoleColor(profile.role)}>\r\n                                {profile.role}\r\n                              </Badge>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"bio\">Bio</Label>\r\n                          <Textarea\r\n                            id=\"bio\"\r\n                            value={profile.bio}\r\n                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleProfileUpdate('bio', e.target.value)}\r\n                            rows={3}\r\n                            placeholder=\"Tell us about yourself...\"\r\n                          />\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    {/* Change Password */}\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Change Password</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent className=\"space-y-4\">\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"current-password\">Current Password</Label>\r\n                          <div className=\"relative\">\r\n                            <Input\r\n                              id=\"current-password\"\r\n                              type={showPassword ? 'text' : 'password'}\r\n                              placeholder=\"Enter current password\"\r\n                            />\r\n                            <button\r\n                              type=\"button\"\r\n                              onClick={() => setShowPassword(!showPassword)}\r\n                              className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground\"\r\n                            >\r\n                              {showPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                        \r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                          <div className=\"space-y-2\">\r\n                            <Label htmlFor=\"new-password\">New Password</Label>\r\n                            <Input\r\n                              id=\"new-password\"\r\n                              type=\"password\"\r\n                              placeholder=\"Enter new password\"\r\n                            />\r\n                          </div>\r\n                          \r\n                          <div className=\"space-y-2\">\r\n                            <Label htmlFor=\"confirm-password\">Confirm Password</Label>\r\n                            <Input\r\n                              id=\"confirm-password\"\r\n                              type=\"password\"\r\n                              placeholder=\"Confirm new password\"\r\n                            />\r\n                          </div>\r\n                        </div>\r\n                        \r\n                        <Button variant=\"outline\">\r\n                          Update Password\r\n                        </Button>\r\n                      </CardContent>\r\n                    </Card>\r\n                  </motion.div>\r\n                </TabsContent>\r\n\r\n                {/* Preferences Tab */}\r\n                <TabsContent key=\"preferences\" value=\"preferences\" className=\"mt-0\">\r\n                  <motion.div\r\n                    initial={{ opacity: 0, x: 20 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: -20 }}\r\n                    className=\"space-y-6\"\r\n                  >\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Appearance</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent className=\"space-y-6\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"space-y-0.5\">\r\n                            <Label htmlFor={darkModeId} className=\"text-base\">Dark Mode</Label>\r\n                            <p className=\"text-sm text-muted-foreground\">\r\n                              Toggle between light and dark themes\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <Sun className=\"h-4 w-4 text-muted-foreground\" />\r\n                            <Switch\r\n                              id={darkModeId}\r\n                              checked={preferences.darkMode}\r\n                              onCheckedChange={(value: boolean) => handlePreferenceUpdate('darkMode', value)}\r\n                              className=\"rounded-sm [&_span]:rounded\"\r\n                            />\r\n                            <Moon className=\"h-4 w-4 text-muted-foreground\" />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"space-y-2\">\r\n                          <Label>Custom Accent Color</Label>\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <input\r\n                              type=\"color\"\r\n                              value={preferences.customAccentColor}\r\n                              onChange={(e) => handlePreferenceUpdate('customAccentColor', e.target.value)}\r\n                              className=\"w-12 h-12 rounded border border-border cursor-pointer\"\r\n                            />\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <span className=\"text-sm font-medium\">Preview</span>\r\n                              <div \r\n                                className=\"w-24 h-8 rounded border border-border\"\r\n                                style={{ backgroundColor: preferences.customAccentColor }}\r\n                              />\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Notifications</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent className=\"space-y-6\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"space-y-0.5\">\r\n                            <Label htmlFor={emailNotificationsId} className=\"text-base\">Email Notifications</Label>\r\n                            <p className=\"text-sm text-muted-foreground\">\r\n                              Receive updates about projects and appointments\r\n                            </p>\r\n                          </div>\r\n                          <Switch\r\n                            id={emailNotificationsId}\r\n                            checked={preferences.emailNotifications}\r\n                            onCheckedChange={(value: boolean) => handlePreferenceUpdate('emailNotifications', value)}\r\n                            className=\"rounded-sm [&_span]:rounded\"\r\n                          />\r\n                        </div>\r\n\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"space-y-0.5\">\r\n                            <Label htmlFor={smsAlertsId} className=\"text-base\">SMS Alerts</Label>\r\n                            <p className=\"text-sm text-muted-foreground\">\r\n                              Get text messages for urgent updates\r\n                            </p>\r\n                          </div>\r\n                          <Switch\r\n                            id={smsAlertsId}\r\n                            checked={preferences.smsAlerts}\r\n                            onCheckedChange={(value: boolean) => handlePreferenceUpdate('smsAlerts', value)}\r\n                            className=\"rounded-sm [&_span]:rounded\"\r\n                          />\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Localization</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent className=\"space-y-4\">\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                          <div className=\"space-y-2\">\r\n                            <Label>Language</Label>\r\n                            <Select \r\n                              value={preferences.language} \r\n                              onValueChange={(value) => handlePreferenceUpdate('language', value)}\r\n                            >\r\n                              <SelectTrigger>\r\n                                <SelectValue />\r\n                              </SelectTrigger>\r\n                              <SelectContent>\r\n                                <SelectItem value=\"en\">English</SelectItem>\r\n                                <SelectItem value=\"es\">Español</SelectItem>\r\n                              </SelectContent>\r\n                            </Select>\r\n                          </div>\r\n\r\n                          <div className=\"space-y-2\">\r\n                            <Label>Timezone</Label>\r\n                            <Select \r\n                              value={preferences.timezone} \r\n                              onValueChange={(value) => handlePreferenceUpdate('timezone', value)}\r\n                            >\r\n                              <SelectTrigger>\r\n                                <SelectValue />\r\n                              </SelectTrigger>\r\n                              <SelectContent>\r\n                                <SelectItem value=\"America/Chicago\">Central Time</SelectItem>\r\n                                <SelectItem value=\"America/New_York\">Eastern Time</SelectItem>\r\n                                <SelectItem value=\"America/Los_Angeles\">Pacific Time</SelectItem>\r\n                                <SelectItem value=\"America/Denver\">Mountain Time</SelectItem>\r\n                              </SelectContent>\r\n                            </Select>\r\n                          </div>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  </motion.div>\r\n                </TabsContent>\r\n\r\n                {/* Integrations Tab */}\r\n                <TabsContent key=\"integrations\" value=\"integrations\" className=\"mt-0\">\r\n                  <motion.div\r\n                    initial={{ opacity: 0, x: 20 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: -20 }}\r\n                    className=\"space-y-6\"\r\n                  >\r\n                    {integrations.map((integration) => {\r\n                      const IconComponent = integration.icon\r\n                      return (\r\n                        <Card key={integration.id}>\r\n                          <CardHeader>\r\n                            <div className=\"flex items-center justify-between\">\r\n                              <div className=\"flex items-center space-x-3\">\r\n                                <div className=\"p-2 rounded-md bg-primary/10\">\r\n                                  <IconComponent className=\"h-6 w-6 text-primary\" />\r\n                                </div>\r\n                                <div>\r\n                                  <CardTitle className=\"text-lg\">{integration.name}</CardTitle>\r\n                                  <p className=\"text-sm text-muted-foreground\">\r\n                                    {integration.description}\r\n                                  </p>\r\n                                </div>\r\n                              </div>\r\n                              {getStatusBadge(integration.status)}\r\n                            </div>\r\n                          </CardHeader>\r\n                          <CardContent>\r\n                            {integration.connectionType === 'oauth' && (\r\n                              <div className=\"flex items-center space-x-3\">\r\n                                <Button \r\n                                  variant={integration.status === 'connected' ? 'outline' : 'default'}\r\n                                  className={integration.status === 'connected' ? '' : 'bg-primary hover:bg-primary/90'}\r\n                                >\r\n                                  {integration.status === 'connected' ? 'Disconnect' : 'Connect with OAuth'}\r\n                                </Button>\r\n                                {integration.status === 'connected' && (\r\n                                  <Button variant=\"outline\" size=\"sm\">\r\n                                    Test Connection\r\n                                  </Button>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n\r\n                            {integration.connectionType === 'api-key' && (\r\n                              <div className=\"space-y-3\">\r\n                                <div className=\"flex items-center space-x-3\">\r\n                                  <Input\r\n                                    placeholder=\"Enter API key\"\r\n                                    type=\"password\"\r\n                                    value={integration.apiKey || ''}\r\n                                    onChange={(e) => {\r\n                                      const updated = integrations.map(i => \r\n                                        i.id === integration.id \r\n                                          ? { ...i, apiKey: e.target.value }\r\n                                          : i\r\n                                      )\r\n                                      setIntegrations(updated)\r\n                                      setHasChanges(true)\r\n                                    }}\r\n                                  />\r\n                                  <Button variant=\"outline\">\r\n                                    Test\r\n                                  </Button>\r\n                                </div>\r\n                              </div>\r\n                            )}\r\n\r\n                            {integration.connectionType === 'toggle' && (\r\n                              <div className=\"flex items-center justify-between\">\r\n                                <Label htmlFor={`${integration.name.toLowerCase().replace(' ', '-')}-sync`} className=\"text-sm text-muted-foreground\">\r\n                                  Enable {integration.name} synchronization\r\n                                </Label>\r\n                                <Switch \r\n                                  id={`${integration.name.toLowerCase().replace(' ', '-')}-sync`}\r\n                                  checked={integration.status === 'connected'} \r\n                                  className=\"rounded-sm [&_span]:rounded\"\r\n                                />\r\n                              </div>\r\n                            )}\r\n                          </CardContent>\r\n                        </Card>\r\n                      )\r\n                    })}\r\n                  </motion.div>\r\n                </TabsContent>\r\n\r\n                {/* Admin Controls Tab */}\r\n                <TabsContent key=\"admin\" value=\"admin\" className=\"mt-0\">\r\n                  <motion.div\r\n                    initial={{ opacity: 0, x: 20 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: -20 }}\r\n                    className=\"space-y-6\"\r\n                  >\r\n                    <Card>\r\n                      <CardHeader className=\"flex flex-row items-center justify-between\">\r\n                        <CardTitle>Team Management</CardTitle>\r\n                        <Button className=\"bg-primary hover:bg-primary/90\">\r\n                          <UserPlus className=\"h-4 w-4 mr-2\" />\r\n                          Invite User\r\n                        </Button>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <Table>\r\n                          <TableHeader>\r\n                            <TableRow>\r\n                              <TableHead>User</TableHead>\r\n                              <TableHead>Role</TableHead>\r\n                              <TableHead>Status</TableHead>\r\n                              <TableHead>Last Active</TableHead>\r\n                              <TableHead>Actions</TableHead>\r\n                            </TableRow>\r\n                          </TableHeader>\r\n                          <TableBody>\r\n                            {mockTeamMembers.map((member) => (\r\n                              <TableRow key={member.id}>\r\n                                <TableCell>\r\n                                  <div className=\"flex items-center space-x-3\">\r\n                                    <Avatar className=\"h-8 w-8\">\r\n                                      <AvatarFallback className=\"bg-primary/10 text-primary text-xs\">\r\n                                        {member.name.split(' ').map(n => n[0]).join('')}\r\n                                      </AvatarFallback>\r\n                                    </Avatar>\r\n                                    <div>\r\n                                      <p className=\"font-medium\">{member.name}</p>\r\n                                      <p className=\"text-sm text-muted-foreground\">{member.email}</p>\r\n                                    </div>\r\n                                  </div>\r\n                                </TableCell>\r\n                                <TableCell>\r\n                                  <Badge className={getRoleColor(member.role)}>\r\n                                    {member.role}\r\n                                  </Badge>\r\n                                </TableCell>\r\n                                <TableCell>{getStatusBadge(member.status)}</TableCell>\r\n                                <TableCell className=\"text-sm text-muted-foreground\">\r\n                                  {formatTimestamp(member.lastActive)}\r\n                                </TableCell>\r\n                                <TableCell>\r\n                                  <DropdownMenu>\r\n                                    <DropdownMenuTrigger asChild>\r\n                                      <Button variant=\"ghost\" size=\"sm\">\r\n                                        <Edit className=\"h-4 w-4\" />\r\n                                      </Button>\r\n                                    </DropdownMenuTrigger>\r\n                                    <DropdownMenuContent>\r\n                                      <DropdownMenuItem>Edit User</DropdownMenuItem>\r\n                                      <DropdownMenuItem>Change Role</DropdownMenuItem>\r\n                                      <DropdownMenuItem className=\"text-destructive\">\r\n                                        Remove User\r\n                                      </DropdownMenuItem>\r\n                                    </DropdownMenuContent>\r\n                                  </DropdownMenu>\r\n                                </TableCell>\r\n                              </TableRow>\r\n                            ))}\r\n                          </TableBody>\r\n                        </Table>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                      <CardHeader className=\"flex flex-row items-center justify-between\">\r\n                        <CardTitle>Audit Logs</CardTitle>\r\n                        <Button variant=\"outline\">\r\n                          <Download className=\"h-4 w-4 mr-2\" />\r\n                          Export Logs\r\n                        </Button>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <div className=\"space-y-4\">\r\n                          {mockAuditLogs.map((log) => (\r\n                            <div key={log.id} className=\"flex items-start space-x-3 p-3 rounded-lg border border-border\">\r\n                              <div className=\"p-1 rounded-full bg-primary/10 mt-1\">\r\n                                <Clock className=\"h-3 w-3 text-primary\" />\r\n                              </div>\r\n                              <div className=\"flex-1\">\r\n                                <div className=\"flex items-center justify-between\">\r\n                                  <h4 className=\"font-medium\">{log.action}</h4>\r\n                                  <span className=\"text-xs text-muted-foreground\">\r\n                                    {formatTimestamp(log.timestamp)}\r\n                                  </span>\r\n                                </div>\r\n                                <p className=\"text-sm text-muted-foreground mt-1\">{log.details}</p>\r\n                                <p className=\"text-xs text-muted-foreground mt-1\">by {log.user}</p>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Data Export</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <div className=\"flex items-center space-x-3\">\r\n                          <Button variant=\"outline\">\r\n                            <Download className=\"h-4 w-4 mr-2\" />\r\n                            Export as CSV\r\n                          </Button>\r\n                          <Button variant=\"outline\">\r\n                            <Download className=\"h-4 w-4 mr-2\" />\r\n                            Export as JSON\r\n                          </Button>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  </motion.div>\r\n                </TabsContent>\r\n\r\n                {/* Security Tab */}\r\n                <TabsContent key=\"security\" value=\"security\" className=\"mt-0\">\r\n                  <motion.div\r\n                    initial={{ opacity: 0, x: 20 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: -20 }}\r\n                    className=\"space-y-6\"\r\n                  >\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Two-Factor Authentication</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent className=\"space-y-4\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"space-y-0.5\">\r\n                            <Label htmlFor={twoFactorId} className=\"text-base\">Enable 2FA</Label>\r\n                            <p className=\"text-sm text-muted-foreground\">\r\n                              Add an extra layer of security to your account\r\n                            </p>\r\n                          </div>\r\n                          <Switch\r\n                            id={twoFactorId}\r\n                            checked={twoFactorEnabled}\r\n                            onCheckedChange={setTwoFactorEnabled}\r\n                            className=\"rounded-sm [&_span]:rounded\"\r\n                          />\r\n                        </div>\r\n\r\n                        {twoFactorEnabled && (\r\n                          <motion.div\r\n                            initial={{ opacity: 0, height: 0 }}\r\n                            animate={{ opacity: 1, height: 'auto' }}\r\n                            className=\"border border-border rounded-lg p-4 space-y-3\"\r\n                          >\r\n                            <p className=\"text-sm font-medium\">Scan QR Code</p>\r\n                            <div className=\"w-32 h-32 bg-muted rounded-lg flex items-center justify-center\">\r\n                              <p className=\"text-xs text-muted-foreground text-center\">\r\n                                QR Code<br />Placeholder\r\n                              </p>\r\n                            </div>\r\n                            <p className=\"text-xs text-muted-foreground\">\r\n                              Scan this QR code with your authenticator app\r\n                            </p>\r\n                          </motion.div>\r\n                        )}\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                      <CardHeader className=\"flex flex-row items-center justify-between\">\r\n                        <CardTitle>Active Sessions</CardTitle>\r\n                        <Button variant=\"outline\" size=\"sm\">\r\n                          <LogOut className=\"h-4 w-4 mr-2\" />\r\n                          Logout All\r\n                        </Button>\r\n                      </CardHeader>\r\n                      <CardContent>\r\n                        <div className=\"space-y-3\">\r\n                          {mockSessions.map((session) => (\r\n                            <div \r\n                              key={session.id} \r\n                              className=\"flex items-center justify-between p-3 rounded-lg border border-border\"\r\n                            >\r\n                              <div className=\"flex items-center space-x-3\">\r\n                                <div className=\"p-2 rounded-md bg-primary/10\">\r\n                                  {session.device.includes('iPhone') ? (\r\n                                    <Smartphone className=\"h-4 w-4 text-primary\" />\r\n                                  ) : (\r\n                                    <Monitor className=\"h-4 w-4 text-primary\" />\r\n                                  )}\r\n                                </div>\r\n                                <div>\r\n                                  <p className=\"font-medium\">{session.device}</p>\r\n                                  <p className=\"text-sm text-muted-foreground\">\r\n                                    {session.location} • {formatTimestamp(session.lastActive)}\r\n                                  </p>\r\n                                </div>\r\n                              </div>\r\n                              <div className=\"flex items-center space-x-2\">\r\n                                {session.current && (\r\n                                  <Badge className=\"bg-green-500/10 text-green-600 border-green-500/20\">\r\n                                    Current\r\n                                  </Badge>\r\n                                )}\r\n                                {!session.current && (\r\n                                  <Button variant=\"ghost\" size=\"sm\">\r\n                                    <LogOut className=\"h-4 w-4\" />\r\n                                  </Button>\r\n                                )}\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                      <CardHeader>\r\n                        <CardTitle>Privacy Settings</CardTitle>\r\n                      </CardHeader>\r\n                      <CardContent className=\"space-y-4\">\r\n                        <div className=\"space-y-2\">\r\n                          <Label>Data Retention Period</Label>\r\n                          <Select defaultValue=\"12\">\r\n                            <SelectTrigger>\r\n                              <SelectValue />\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                              <SelectItem value=\"6\">6 months</SelectItem>\r\n                              <SelectItem value=\"12\">12 months</SelectItem>\r\n                              <SelectItem value=\"24\">24 months</SelectItem>\r\n                              <SelectItem value=\"60\">5 years</SelectItem>\r\n                            </SelectContent>\r\n                          </Select>\r\n                          <p className=\"text-xs text-muted-foreground\">\r\n                            How long to keep deleted data before permanent removal\r\n                          </p>\r\n                        </div>\r\n                        \r\n                        <Button variant=\"outline\" className=\"text-destructive border-destructive hover:bg-destructive/10\">\r\n                          <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                          Delete All My Data\r\n                        </Button>\r\n                      </CardContent>\r\n                    </Card>\r\n                  </motion.div>\r\n                </TabsContent>\r\n              </AnimatePresence>\r\n            </div>\r\n\r\n            {/* Right Sidebar - Tips & AI Helper */}\r\n            <div className=\"lg:col-span-1\">\r\n              <Card className=\"sticky top-6\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center space-x-2\">\r\n                    <HelpCircle className=\"h-5 w-5 text-primary\" />\r\n                    <span>Quick Tips</span>\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  {activeTab === 'profile' && (\r\n                    <div key=\"profile-tips\" className=\"space-y-3\">\r\n                      <div className=\"p-3 rounded-lg bg-primary/5 border border-primary/20\">\r\n                        <p className=\"text-sm text-foreground font-medium mb-1\">Profile Photo</p>\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          A professional photo helps build trust with customers\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"p-3 rounded-lg bg-blue-500/5 border border-blue-500/20\">\r\n                        <p className=\"text-sm text-foreground font-medium mb-1\">Strong Password</p>\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          Use at least 12 characters with mixed case and symbols\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {activeTab === 'preferences' && (\r\n                    <div key=\"preferences-tips\" className=\"space-y-3\">\r\n                      <div className=\"p-3 rounded-lg bg-primary/5 border border-primary/20\">\r\n                        <p className=\"text-sm text-foreground font-medium mb-1\">Notifications</p>\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          Enable SMS for urgent project updates\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {activeTab === 'integrations' && (\r\n                    <div key=\"integrations-tips\" className=\"space-y-3\">\r\n                      <div className=\"p-3 rounded-lg bg-primary/5 border border-primary/20\">\r\n                        <p className=\"text-sm text-foreground font-medium mb-1\">Google Maps</p>\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          Connect to get automatic driving directions to appointments\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {activeTab === 'security' && (\r\n                    <div key=\"security-tips\" className=\"space-y-3\">\r\n                      <div className=\"p-3 rounded-lg bg-primary/5 border border-primary/20\">\r\n                        <p className=\"text-sm text-foreground font-medium mb-1\">2FA Required</p>\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          Admin accounts must have two-factor authentication enabled\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"pt-4 border-t border-border\">\r\n                    <div className=\"space-y-3\">\r\n                      <p className=\"text-sm font-medium\">Need help with integrations?</p>\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <Input placeholder=\"Ask here...\" className=\"text-sm\" />\r\n                        <Button size=\"sm\" variant=\"outline\">\r\n                          Ask\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </div>\r\n        </Tabs>\r\n      </motion.div>\r\n\r\n      {/* Footer */}\r\n      <motion.div\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        transition={{ delay: 0.5 }}\r\n        className=\"flex items-center justify-between pt-6 border-t border-border\"\r\n      >\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          WindowWorks CRM v2.1.0\r\n        </div>\r\n        <Button variant=\"outline\" size=\"sm\">\r\n          <RotateCcw className=\"h-4 w-4 mr-2\" />\r\n          Reset to Defaults\r\n        </Button>\r\n      </motion.div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAQA;AAMA;AA9DA;;;;;;;;;;;;;;;;;;AAuHA,YAAY;AACZ,MAAM,cAA2B;IAC/B,IAAI;IACJ,MAAM;IACN,OAAO;IACP,OAAO;IACP,KAAK;IACL,QAAQ;IACR,MAAM;AACR;AAEA,MAAM,kBAAmC;IACvC,UAAU;IACV,oBAAoB;IACpB,WAAW;IACX,UAAU;IACV,UAAU;IACV,mBAAmB;AACrB;AAEA,MAAM,mBAAkC;IACtC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM,0MAAA,CAAA,SAAM;QACZ,gBAAgB;IAClB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM,kMAAA,CAAA,OAAI;QACV,gBAAgB;QAChB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM,0MAAA,CAAA,WAAQ;QACd,gBAAgB;IAClB;CACD;AAED,MAAM,kBAAgC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;IACd;CACD;AAED,MAAM,gBAA4B;IAChC;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,WAAW;QACX,SAAS;IACX;CACD;AAED,MAAM,eAAkC;IACtC;QACE,IAAI;QACJ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,SAAS;IACX;IACA;QACE,IAAI;QACJ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,SAAS;IACX;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,GAAG,eAAe;QAClB,UAAU;QACV,mBAAmB,UAAU,+BAA+B;IAC9D;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,UAAU,UAAU;gBACpB,mBAAmB;YACrB,CAAC;IACH,GAAG;QAAC;QAAO;KAAY;IAEvB,mCAAmC;IACnC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACvB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACjC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IAExB,MAAM,sBAAsB,CAAC,OAA0B;QACrD,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAC/C,cAAc;IAChB;IAEA,MAAM,yBAAyB,CAAC,OAA8B;QAC5D,IAAI,UAAU,YAAY;YACxB,SAAS,QAAQ,SAAS;QAC5B,OAAO,IAAI,UAAU,qBAAqB;YACxC,eAAe;QACjB;QACA,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACnD,cAAc;IAChB;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,QAAQ,GAAG,CAAC,qBAAqB;YAAE;YAAS;QAAY;QACxD,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;YACrB,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,oBAAoB,UAAU;YAChC;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,mBAAmB,EAAE,YAAY,CAAC,KAAK;QACzC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAqD;;;;;;YAC/E,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAwD;;;;;;YAClF,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAW;;;;;;QACrC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,KAAK,WAAW,cAAc;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAU;;;;;;0CAG/C,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,UAAU,CAAC;gCACX,SAAS;;kDAET,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;8CAErC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAc,WAAU;;sDACzC,8OAAC,0MAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;sDACxB,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;8CAErC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAe,WAAU;;sDAC1C,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;8CAErC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;;sDACnC,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;8CAErC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;wCAAC,MAAK;;0DAEpB,8OAAC,gIAAA,CAAA,cAAW;gDAAe,OAAM;gDAAU,WAAU;0DACnD,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,MAAM;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC3B,WAAU;;sEAEV,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;8EACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;8EAEb,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFAErB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,kIAAA,CAAA,SAAM;4FAAC,WAAU;;8GAChB,8OAAC,kIAAA,CAAA,cAAW;oGAAC,KAAK,QAAQ,MAAM,IAAI;;;;;;8GACpC,8OAAC,kIAAA,CAAA,iBAAc;oGAAC,WAAU;8GACvB,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;sGAGjD,8OAAC;4FACC,SAAS,IAAM,aAAa,OAAO,EAAE;4FACrC,WAAU;sGAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;;;;;;;;;;;;8FAItB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FACC,WAAW,CAAC,oEAAoE,EAC9E,aAAa,gCAAgC,iBAC7C;4FACF,aAAa;4FACb,aAAa;4FACb,YAAY;4FACZ,QAAQ;;8GAER,8OAAC,sMAAA,CAAA,SAAM;oGAAC,WAAU;;;;;;8GAClB,8OAAC;oGAAE,WAAU;;wGAAgC;wGAChB;sHAC3B,8OAAC;4GACC,SAAS,IAAM,aAAa,OAAO,EAAE;4GACrC,WAAU;sHACX;;;;;;;;;;;;8GAIH,8OAAC;oGAAE,WAAU;8GAAqC;;;;;;;;;;;;sGAIpD,8OAAC;4FACC,KAAK;4FACL,MAAK;4FACL,QAAO;4FACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4FAClD,WAAU;;;;;;;;;;;;;;;;;;sFAMhB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAQ;sGAAO;;;;;;sGACtB,8OAAC,iIAAA,CAAA,QAAK;4FACJ,IAAG;4FACH,OAAO,QAAQ,IAAI;4FACnB,UAAU,CAAC,IAAM,oBAAoB,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8FAI/D,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAQ;sGAAQ;;;;;;sGACvB,8OAAC,iIAAA,CAAA,QAAK;4FACJ,IAAG;4FACH,MAAK;4FACL,OAAO,QAAQ,KAAK;4FACpB,UAAU,CAAC,IAAM,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8FAIhE,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAQ;sGAAQ;;;;;;sGACvB,8OAAC,iIAAA,CAAA,QAAK;4FACJ,IAAG;4FACH,OAAO,QAAQ,KAAK;4FACpB,UAAU,CAAC,IAAM,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8FAIhE,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAQ;sGAAO;;;;;;sGACtB,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,WAAW,aAAa,QAAQ,IAAI;0GACxC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;sFAMrB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;8FAAM;;;;;;8FACrB,8OAAC,oIAAA,CAAA,WAAQ;oFACP,IAAG;oFACH,OAAO,QAAQ,GAAG;oFAClB,UAAU,CAAC,IAA8C,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK;oFAClG,MAAM;oFACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sEAOpB,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;8EACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;8EAEb,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;8FAAmB;;;;;;8FAClC,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FACJ,IAAG;4FACH,MAAM,eAAe,SAAS;4FAC9B,aAAY;;;;;;sGAEd,8OAAC;4FACC,MAAK;4FACL,SAAS,IAAM,gBAAgB,CAAC;4FAChC,WAAU;sGAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;gGAAC,WAAU;;;;;qHAAe,8OAAC,gMAAA,CAAA,MAAG;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sFAKtE,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAQ;sGAAe;;;;;;sGAC9B,8OAAC,iIAAA,CAAA,QAAK;4FACJ,IAAG;4FACH,MAAK;4FACL,aAAY;;;;;;;;;;;;8FAIhB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAQ;sGAAmB;;;;;;sGAClC,8OAAC,iIAAA,CAAA,QAAK;4FACJ,IAAG;4FACH,MAAK;4FACL,aAAY;;;;;;;;;;;;;;;;;;sFAKlB,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAQ;sFAAU;;;;;;;;;;;;;;;;;;;;;;;;+CAhKjB;;;;;0DAyKjB,8OAAC,gIAAA,CAAA,cAAW;gDAAmB,OAAM;gDAAc,WAAU;0DAC3D,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,MAAM;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC3B,WAAU;;sEAEV,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;8EACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;8EAEb,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAS;4FAAY,WAAU;sGAAY;;;;;;sGAClD,8OAAC;4FAAE,WAAU;sGAAgC;;;;;;;;;;;;8FAI/C,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,gMAAA,CAAA,MAAG;4FAAC,WAAU;;;;;;sGACf,8OAAC,kIAAA,CAAA,SAAM;4FACL,IAAI;4FACJ,SAAS,YAAY,QAAQ;4FAC7B,iBAAiB,CAAC,QAAmB,uBAAuB,YAAY;4FACxE,WAAU;;;;;;sGAEZ,8OAAC,kMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;;;;;;;;;;;;;sFAIpB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;8FAAC;;;;;;8FACP,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FACC,MAAK;4FACL,OAAO,YAAY,iBAAiB;4FACpC,UAAU,CAAC,IAAM,uBAAuB,qBAAqB,EAAE,MAAM,CAAC,KAAK;4FAC3E,WAAU;;;;;;sGAEZ,8OAAC;4FAAI,WAAU;;8GACb,8OAAC;oGAAK,WAAU;8GAAsB;;;;;;8GACtC,8OAAC;oGACC,WAAU;oGACV,OAAO;wGAAE,iBAAiB,YAAY,iBAAiB;oGAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAQpE,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;8EACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;8EAEb,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAS;4FAAsB,WAAU;sGAAY;;;;;;sGAC5D,8OAAC;4FAAE,WAAU;sGAAgC;;;;;;;;;;;;8FAI/C,8OAAC,kIAAA,CAAA,SAAM;oFACL,IAAI;oFACJ,SAAS,YAAY,kBAAkB;oFACvC,iBAAiB,CAAC,QAAmB,uBAAuB,sBAAsB;oFAClF,WAAU;;;;;;;;;;;;sFAId,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAS;4FAAa,WAAU;sGAAY;;;;;;sGACnD,8OAAC;4FAAE,WAAU;sGAAgC;;;;;;;;;;;;8FAI/C,8OAAC,kIAAA,CAAA,SAAM;oFACL,IAAI;oFACJ,SAAS,YAAY,SAAS;oFAC9B,iBAAiB,CAAC,QAAmB,uBAAuB,aAAa;oFACzE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sEAMlB,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;8EACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;8EAEb,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;8EACrB,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,iIAAA,CAAA,QAAK;kGAAC;;;;;;kGACP,8OAAC,kIAAA,CAAA,SAAM;wFACL,OAAO,YAAY,QAAQ;wFAC3B,eAAe,CAAC,QAAU,uBAAuB,YAAY;;0GAE7D,8OAAC,kIAAA,CAAA,gBAAa;0GACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0GAEd,8OAAC,kIAAA,CAAA,gBAAa;;kHACZ,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAK;;;;;;kHACvB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAK;;;;;;;;;;;;;;;;;;;;;;;;0FAK7B,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,iIAAA,CAAA,QAAK;kGAAC;;;;;;kGACP,8OAAC,kIAAA,CAAA,SAAM;wFACL,OAAO,YAAY,QAAQ;wFAC3B,eAAe,CAAC,QAAU,uBAAuB,YAAY;;0GAE7D,8OAAC,kIAAA,CAAA,gBAAa;0GACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0GAEd,8OAAC,kIAAA,CAAA,gBAAa;;kHACZ,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAkB;;;;;;kHACpC,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAmB;;;;;;kHACrC,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAsB;;;;;;kHACxC,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CA5HlC;;;;;0DAuIjB,8OAAC,gIAAA,CAAA,cAAW;gDAAoB,OAAM;gDAAe,WAAU;0DAC7D,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,MAAM;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC3B,WAAU;8DAET,aAAa,GAAG,CAAC,CAAC;wDACjB,MAAM,gBAAgB,YAAY,IAAI;wDACtC,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;8EACT,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGACb,cAAA,8OAAC;4FAAc,WAAU;;;;;;;;;;;kGAE3B,8OAAC;;0GACC,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAW,YAAY,IAAI;;;;;;0GAChD,8OAAC;gGAAE,WAAU;0GACV,YAAY,WAAW;;;;;;;;;;;;;;;;;;4EAI7B,eAAe,YAAY,MAAM;;;;;;;;;;;;8EAGtC,8OAAC,gIAAA,CAAA,cAAW;;wEACT,YAAY,cAAc,KAAK,yBAC9B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kIAAA,CAAA,SAAM;oFACL,SAAS,YAAY,MAAM,KAAK,cAAc,YAAY;oFAC1D,WAAW,YAAY,MAAM,KAAK,cAAc,KAAK;8FAEpD,YAAY,MAAM,KAAK,cAAc,eAAe;;;;;;gFAEtD,YAAY,MAAM,KAAK,6BACtB,8OAAC,kIAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAU,MAAK;8FAAK;;;;;;;;;;;;wEAOzC,YAAY,cAAc,KAAK,2BAC9B,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,iIAAA,CAAA,QAAK;wFACJ,aAAY;wFACZ,MAAK;wFACL,OAAO,YAAY,MAAM,IAAI;wFAC7B,UAAU,CAAC;4FACT,MAAM,UAAU,aAAa,GAAG,CAAC,CAAA,IAC/B,EAAE,EAAE,KAAK,YAAY,EAAE,GACnB;oGAAE,GAAG,CAAC;oGAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gGAAC,IAC/B;4FAEN,gBAAgB;4FAChB,cAAc;wFAChB;;;;;;kGAEF,8OAAC,kIAAA,CAAA,SAAM;wFAAC,SAAQ;kGAAU;;;;;;;;;;;;;;;;;wEAO/B,YAAY,cAAc,KAAK,0BAC9B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAS,GAAG,YAAY,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;oFAAE,WAAU;;wFAAgC;wFAC5G,YAAY,IAAI;wFAAC;;;;;;;8FAE3B,8OAAC,kIAAA,CAAA,SAAM;oFACL,IAAI,GAAG,YAAY,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;oFAC9D,SAAS,YAAY,MAAM,KAAK;oFAChC,WAAU;;;;;;;;;;;;;;;;;;;2DAlET,YAAY,EAAE;;;;;oDAyE7B;;;;;;+CAnFa;;;;;0DAwFjB,8OAAC,gIAAA,CAAA,cAAW;gDAAa,OAAM;gDAAQ,WAAU;0DAC/C,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,MAAM;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC3B,WAAU;;sEAEV,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;oEAAC,WAAU;;sFACpB,8OAAC,gIAAA,CAAA,YAAS;sFAAC;;;;;;sFACX,8OAAC,kIAAA,CAAA,SAAM;4EAAC,WAAU;;8FAChB,8OAAC,8MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;8EAIzC,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0FACJ,8OAAC,iIAAA,CAAA,cAAW;0FACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;sGACP,8OAAC,iIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,iIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,iIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,iIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,iIAAA,CAAA,YAAS;sGAAC;;;;;;;;;;;;;;;;;0FAGf,8OAAC,iIAAA,CAAA,YAAS;0FACP,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,iIAAA,CAAA,WAAQ;;0GACP,8OAAC,iIAAA,CAAA,YAAS;0GACR,cAAA,8OAAC;oGAAI,WAAU;;sHACb,8OAAC,kIAAA,CAAA,SAAM;4GAAC,WAAU;sHAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;gHAAC,WAAU;0HACvB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;sHAGhD,8OAAC;;8HACC,8OAAC;oHAAE,WAAU;8HAAe,OAAO,IAAI;;;;;;8HACvC,8OAAC;oHAAE,WAAU;8HAAiC,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;0GAIhE,8OAAC,iIAAA,CAAA,YAAS;0GACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oGAAC,WAAW,aAAa,OAAO,IAAI;8GACvC,OAAO,IAAI;;;;;;;;;;;0GAGhB,8OAAC,iIAAA,CAAA,YAAS;0GAAE,eAAe,OAAO,MAAM;;;;;;0GACxC,8OAAC,iIAAA,CAAA,YAAS;gGAAC,WAAU;0GAClB,gBAAgB,OAAO,UAAU;;;;;;0GAEpC,8OAAC,iIAAA,CAAA,YAAS;0GACR,cAAA,8OAAC,4IAAA,CAAA,eAAY;;sHACX,8OAAC,4IAAA,CAAA,sBAAmB;4GAAC,OAAO;sHAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gHAAC,SAAQ;gHAAQ,MAAK;0HAC3B,cAAA,8OAAC,2MAAA,CAAA,OAAI;oHAAC,WAAU;;;;;;;;;;;;;;;;sHAGpB,8OAAC,4IAAA,CAAA,sBAAmB;;8HAClB,8OAAC,4IAAA,CAAA,mBAAgB;8HAAC;;;;;;8HAClB,8OAAC,4IAAA,CAAA,mBAAgB;8HAAC;;;;;;8HAClB,8OAAC,4IAAA,CAAA,mBAAgB;oHAAC,WAAU;8HAAmB;;;;;;;;;;;;;;;;;;;;;;;;uFAjCxC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sEA8ClC,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;oEAAC,WAAU;;sFACpB,8OAAC,gIAAA,CAAA,YAAS;sFAAC;;;;;;sFACX,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAQ;;8FACd,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;8EAIzC,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC;wEAAI,WAAU;kFACZ,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC;gFAAiB,WAAU;;kGAC1B,8OAAC;wFAAI,WAAU;kGACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;;;;;;kGAEnB,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAI,WAAU;;kHACb,8OAAC;wGAAG,WAAU;kHAAe,IAAI,MAAM;;;;;;kHACvC,8OAAC;wGAAK,WAAU;kHACb,gBAAgB,IAAI,SAAS;;;;;;;;;;;;0GAGlC,8OAAC;gGAAE,WAAU;0GAAsC,IAAI,OAAO;;;;;;0GAC9D,8OAAC;gGAAE,WAAU;;oGAAqC;oGAAI,IAAI,IAAI;;;;;;;;;;;;;;+EAZxD,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;sEAoBxB,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;8EACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;8EAEb,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kIAAA,CAAA,SAAM;gFAAC,SAAQ;;kGACd,8OAAC,0MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGvC,8OAAC,kIAAA,CAAA,SAAM;gFAAC,SAAQ;;kGACd,8OAAC,0MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CApHhC;;;;;0DA8HjB,8OAAC,gIAAA,CAAA,cAAW;gDAAgB,OAAM;gDAAW,WAAU;0DACrD,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,MAAM;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC3B,WAAU;;sEAEV,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;8EACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;8EAEb,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iIAAA,CAAA,QAAK;4FAAC,SAAS;4FAAa,WAAU;sGAAY;;;;;;sGACnD,8OAAC;4FAAE,WAAU;sGAAgC;;;;;;;;;;;;8FAI/C,8OAAC,kIAAA,CAAA,SAAM;oFACL,IAAI;oFACJ,SAAS;oFACT,iBAAiB;oFACjB,WAAU;;;;;;;;;;;;wEAIb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4EACT,SAAS;gFAAE,SAAS;gFAAG,QAAQ;4EAAE;4EACjC,SAAS;gFAAE,SAAS;gFAAG,QAAQ;4EAAO;4EACtC,WAAU;;8FAEV,8OAAC;oFAAE,WAAU;8FAAsB;;;;;;8FACnC,8OAAC;oFAAI,WAAU;8FACb,cAAA,8OAAC;wFAAE,WAAU;;4FAA4C;0GAChD,8OAAC;;;;;4FAAK;;;;;;;;;;;;8FAGjB,8OAAC;oFAAE,WAAU;8FAAgC;;;;;;;;;;;;;;;;;;;;;;;;sEAQrD,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;oEAAC,WAAU;;sFACpB,8OAAC,gIAAA,CAAA,YAAS;sFAAC;;;;;;sFACX,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAU,MAAK;;8FAC7B,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;8EAIvC,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC;wEAAI,WAAU;kFACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,8OAAC;gFAEC,WAAU;;kGAEV,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAI,WAAU;0GACZ,QAAQ,MAAM,CAAC,QAAQ,CAAC,0BACvB,8OAAC,8MAAA,CAAA,aAAU;oGAAC,WAAU;;;;;yHAEtB,8OAAC,wMAAA,CAAA,UAAO;oGAAC,WAAU;;;;;;;;;;;0GAGvB,8OAAC;;kHACC,8OAAC;wGAAE,WAAU;kHAAe,QAAQ,MAAM;;;;;;kHAC1C,8OAAC;wGAAE,WAAU;;4GACV,QAAQ,QAAQ;4GAAC;4GAAI,gBAAgB,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;kGAI9D,8OAAC;wFAAI,WAAU;;4FACZ,QAAQ,OAAO,kBACd,8OAAC,iIAAA,CAAA,QAAK;gGAAC,WAAU;0GAAqD;;;;;;4FAIvE,CAAC,QAAQ,OAAO,kBACf,8OAAC,kIAAA,CAAA,SAAM;gGAAC,SAAQ;gGAAQ,MAAK;0GAC3B,cAAA,8OAAC,0MAAA,CAAA,SAAM;oGAAC,WAAU;;;;;;;;;;;;;;;;;;+EA1BnB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;sEAoCzB,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;8EACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;8EAEb,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;;sFACrB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;8FAAC;;;;;;8FACP,8OAAC,kIAAA,CAAA,SAAM;oFAAC,cAAa;;sGACnB,8OAAC,kIAAA,CAAA,gBAAa;sGACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sGAEd,8OAAC,kIAAA,CAAA,gBAAa;;8GACZ,8OAAC,kIAAA,CAAA,aAAU;oGAAC,OAAM;8GAAI;;;;;;8GACtB,8OAAC,kIAAA,CAAA,aAAU;oGAAC,OAAM;8GAAK;;;;;;8GACvB,8OAAC,kIAAA,CAAA,aAAU;oGAAC,OAAM;8GAAK;;;;;;8GACvB,8OAAC,kIAAA,CAAA,aAAU;oGAAC,OAAM;8GAAK;;;;;;;;;;;;;;;;;;8FAG3B,8OAAC;oFAAE,WAAU;8FAAgC;;;;;;;;;;;;sFAK/C,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAU,WAAU;;8FAClC,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;+CAvH5B;;;;;;;;;;;;;;;;8CAkIrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,8NAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;oDACpB,cAAc,2BACb,8OAAC;wDAAuB,WAAU;;0EAChC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAA2C;;;;;;kFACxD,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAI/C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAA2C;;;;;;kFACxD,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;;uDATxC;;;;;oDAgBV,cAAc,+BACb,8OAAC;wDAA2B,WAAU;kEACpC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;uDAHxC;;;;;oDAUV,cAAc,gCACb,8OAAC;wDAA4B,WAAU;kEACrC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;uDAHxC;;;;;oDAUV,cAAc,4BACb,8OAAC;wDAAwB,WAAU;kEACjC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;uDAHxC;;;;;kEAUX,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,aAAY;4EAAc,WAAU;;;;;;sFAC3C,8OAAC,kIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;sFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BActD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCAAgC;;;;;;kCAG/C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;;0CAC7B,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAMhD", "debugId": null}}]}