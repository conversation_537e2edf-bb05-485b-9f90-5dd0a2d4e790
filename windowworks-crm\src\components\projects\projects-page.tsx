'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  Clipboard, 
  PlayCircle, 
  Clock, 
  Calendar,
  Search,
  Grid3X3,
  List,
  Plus,
  Eye,
  Edit,
  UserPlus,
  Trash2,
  MoreHorizontal,
  Download,
  User,
  CheckCircle,
  AlertCircle,
  Timer,
  ArrowRight
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { NewProjectModal } from './new-project-modal'
import { ProjectFormData, Customer, User as UserType } from '@/types'

// Types
interface Project {
  id: string
  name: string
  customer: {
    id: string
    name: string
    email: string
    phone: string
  }
  status: 'Pending' | 'Assigned' | 'In-Progress' | 'Completed'
  installer: {
    id: string
    name: string
    avatar: string | null
  } | null
  dueDate: string
  productType: string[]
  description: string
  address: string
  totalValue: number
  createdAt: string
  completionTime?: number
}

// Mock data for projects
const mockProjects: Project[] = [
  {
    id: 'proj_001',
    name: 'Living Room Shutters Installation',
    customer: {
      id: 'cust_001',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '(*************'
    },
    status: 'In-Progress',
    installer: {
      id: 'inst_001',
      name: 'Mike Rodriguez',
      avatar: null
    },
    dueDate: '2025-07-15',
    productType: ['Shutters'],
    description: 'Custom wood shutters for living room bay windows',
    address: '123 Maple Street, Springfield, IL 62704',
    totalValue: 2840,
    createdAt: '2025-07-01',
    completionTime: 3
  },
  {
    id: 'proj_002',
    name: 'Office Blinds Setup',
    customer: {
      id: 'cust_002',
      name: 'Robert Chen',
      email: '<EMAIL>',
      phone: '(*************'
    },
    status: 'Assigned',
    installer: {
      id: 'inst_002',
      name: 'Lisa Anderson',
      avatar: null
    },
    dueDate: '2025-07-18',
    productType: ['Blinds'],
    description: 'Motorized blinds for home office windows',
    address: '456 Oak Avenue, Springfield, IL 62701',
    totalValue: 1560,
    createdAt: '2025-07-03'
  },
  {
    id: 'proj_003',
    name: 'Bedroom Shades Installation',
    customer: {
      id: 'cust_003',
      name: 'Emma Davis',
      email: '<EMAIL>',
      phone: '(*************'
    },
    status: 'Pending',
    installer: null,
    dueDate: '2025-07-12',
    productType: ['Shades'],
    description: 'Blackout shades for master bedroom',
    address: '789 Pine Road, Springfield, IL 62702',
    totalValue: 890,
    createdAt: '2025-07-08'
  },
  {
    id: 'proj_004',
    name: 'Kitchen Window Treatments',
    customer: {
      id: 'cust_004',
      name: 'Michael Williams',
      email: '<EMAIL>',
      phone: '(*************'
    },
    status: 'Completed',
    installer: {
      id: 'inst_001',
      name: 'Mike Rodriguez',
      avatar: null
    },
    dueDate: '2025-07-10',
    productType: ['Blinds', 'Shades'],
    description: 'Water-resistant blinds and café shades',
    address: '321 Elm Street, Springfield, IL 62703',
    totalValue: 1250,
    createdAt: '2025-06-28',
    completionTime: 4
  },
  {
    id: 'proj_005',
    name: 'Sunroom Shutters',
    customer: {
      id: 'cust_005',
      name: 'David Thompson',
      email: '<EMAIL>',
      phone: '(*************'
    },
    status: 'In-Progress',
    installer: {
      id: 'inst_003',
      name: 'Carlos Martinez',
      avatar: null
    },
    dueDate: '2025-07-20',
    productType: ['Shutters'],
    description: 'Full-height shutters for sunroom',
    address: '654 Cedar Lane, Springfield, IL 62705',
    totalValue: 3200,
    createdAt: '2025-07-05',
    completionTime: 6
  }
]

const statsData = [
  {
    title: 'Total Projects',
    value: '156',
    change: '-1%',
    icon: Clipboard,
    trend: 'down'
  },
  {
    title: 'Active Projects',
    value: '24',
    change: '+5%',
    icon: PlayCircle,
    trend: 'up'
  },
  {
    title: 'Overdue',
    value: '3',
    change: '',
    icon: AlertCircle,
    trend: 'alert'
  },
  {
    title: 'Avg Completion Time',
    value: '5 days',
    change: '+2%',
    icon: Timer,
    trend: 'up'
  }
]

export default function ProjectsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [productFilter, setProductFilter] = useState('all')
  const [sortBy, setSortBy] = useState('dueDate')
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'table' | 'kanban'>('table')
  const [currentPage, setCurrentPage] = useState(1)
  const [isNewProjectModalOpen, setIsNewProjectModalOpen] = useState(false)
  const itemsPerPage = 10

  // Mock data for customers and installers
  const mockCustomers: Customer[] = [
    {
      id: 'cust_001',
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      address: {
        street: '123 Maple Street',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62704',
        country: 'United States'
      },
      preferences: {
        windowTreatmentTypes: ['shutters', 'blinds'],
        preferredColors: ['white', 'beige'],
        budget: '2500-5000',
        communication: 'email'
      },
      createdAt: '2025-07-01T00:00:00Z',
      updatedAt: '2025-07-01T00:00:00Z',
      createdBy: 'admin'
    }
  ]

  const mockInstallers: UserType[] = [
    {
      id: 'inst_001',
      email: '<EMAIL>',
      role: 'installer',
      profile: {
        firstName: 'Mike',
        lastName: 'Rodriguez',
        phone: '(*************'
      },
      createdAt: '2025-07-01T00:00:00Z',
      updatedAt: '2025-07-01T00:00:00Z'
    }
  ]

  // Handle new project creation
  const handleCreateProject = async (projectData: ProjectFormData) => {
    try {
      console.log('Creating new project:', projectData)
      // Here you would typically make an API call to create the project
      // await createProject(projectData)
      
      // For now, just log and close modal
      setIsNewProjectModalOpen(false)
      
      // You might want to refresh the projects list here
      // await refreshProjects()
    } catch (error) {
      console.error('Error creating project:', error)
      throw error
    }
  }

  // Filter and sort projects
  const filteredProjects = mockProjects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         project.customer.name.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || project.status.toLowerCase() === statusFilter.toLowerCase()
    
    const matchesProduct = productFilter === 'all' || 
                          project.productType.some(type => type.toLowerCase() === productFilter.toLowerCase())
    
    return matchesSearch && matchesStatus && matchesProduct
  }).sort((a, b) => {
    switch (sortBy) {
      case 'dueDate':
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
      case 'name':
        return a.name.localeCompare(b.name)
      case 'customer':
        return a.customer.name.localeCompare(b.customer.name)
      default:
        return 0
    }
  })

  const totalPages = Math.ceil(filteredProjects.length / itemsPerPage)
  const paginatedProjects = filteredProjects.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const handleSelectProject = (projectId: string) => {
    setSelectedProjects(prev => 
      prev.includes(projectId)
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    )
  }

  const handleSelectAll = () => {
    if (selectedProjects.length === paginatedProjects.length) {
      setSelectedProjects([])
    } else {
      setSelectedProjects(paginatedProjects.map(project => project.id))
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'default'
      case 'in-progress':
        return 'secondary'
      case 'assigned':
        return 'outline'
      case 'pending':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return CheckCircle
      case 'in-progress':
        return PlayCircle
      case 'assigned':
        return User
      case 'pending':
        return Clock
      default:
        return Clock
    }
  }

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date() && !mockProjects.find(p => p.dueDate === dueDate && p.status === 'Completed')
  }

  // Kanban view organization
  const kanbanColumns = {
    'Pending': filteredProjects.filter(p => p.status === 'Pending'),
    'Assigned': filteredProjects.filter(p => p.status === 'Assigned'),
    'In-Progress': filteredProjects.filter(p => p.status === 'In-Progress'),
    'Completed': filteredProjects.filter(p => p.status === 'Completed')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">Projects</h1>
          <p className="text-muted-foreground">Track installations, workflows, and statuses</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="secondary" className="text-xs">
            Saturday, July 12, 2025
          </Badge>
          <Button
            className="bg-primary hover:bg-primary/90"
            onClick={() => setIsNewProjectModalOpen(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Button>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:border-border/80 transition-colors">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className={`p-2 rounded-md ${
                    stat.trend === 'alert' ? 'bg-destructive/10' : 'bg-primary/10'
                  }`}>
                    <stat.icon className={`h-5 w-5 ${
                      stat.trend === 'alert' ? 'text-destructive' : 'text-primary'
                    }`} />
                  </div>
                  {stat.change && (
                    <Badge 
                      variant={
                        stat.trend === 'up' ? 'default' : 
                        stat.trend === 'down' ? 'destructive' : 
                        stat.trend === 'alert' ? 'destructive' : 'secondary'
                      } 
                      className="text-xs"
                    >
                      {stat.change}
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <h3 className="text-2xl font-bold text-foreground">{stat.value}</h3>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Filters and View Toggle */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-1 items-center space-x-4">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search projects, customers..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="assigned">Assigned</SelectItem>
                    <SelectItem value="in-progress">In-Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={productFilter} onValueChange={setProductFilter}>
                  <SelectTrigger className="w-36">
                    <SelectValue placeholder="Product" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Products</SelectItem>
                    <SelectItem value="blinds">Blinds</SelectItem>
                    <SelectItem value="shutters">Shutters</SelectItem>
                    <SelectItem value="shades">Shades</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="dueDate">Due Date</SelectItem>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="customer">Customer</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <div className="flex items-center bg-accent rounded-md p-1">
                  <Button
                    variant={viewMode === 'table' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('table')}
                    className="h-8 px-3"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'kanban' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('kanban')}
                    className="h-8 px-3"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                </div>
                
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                
                {selectedProjects.length > 0 && (
                  <Badge variant="secondary">
                    {selectedProjects.length} selected
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Main Content - Table or Kanban View */}
      {viewMode === 'table' ? (
        // Table View
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="border-b border-border">
                    <tr className="bg-accent/20">
                      <th className="text-left p-4 w-12">
                        <Checkbox
                          checked={selectedProjects.length === paginatedProjects.length && paginatedProjects.length > 0}
                          onCheckedChange={handleSelectAll}
                        />
                      </th>
                      <th className="text-left p-4 font-medium text-foreground">Project</th>
                      <th className="text-left p-4 font-medium text-foreground">Customer</th>
                      <th className="text-left p-4 font-medium text-foreground">Status</th>
                      <th className="text-left p-4 font-medium text-foreground">Installer</th>
                      <th className="text-left p-4 font-medium text-foreground">Due Date</th>
                      <th className="text-left p-4 font-medium text-foreground">Value</th>
                      <th className="text-right p-4 w-16"></th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedProjects.map((project, index) => {
                      const StatusIcon = getStatusIcon(project.status)
                      const overdue = isOverdue(project.dueDate)
                      return (
                        <motion.tr
                          key={project.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="border-b border-border hover:bg-accent/30 transition-colors cursor-pointer"
                        >
                          <td className="p-4">
                            <Checkbox
                              checked={selectedProjects.includes(project.id)}
                              onCheckedChange={() => handleSelectProject(project.id)}
                              onClick={(e: React.MouseEvent) => e.stopPropagation()}
                            />
                          </td>
                          <td className="p-4">
                            <div>
                              <Link 
                                href={`/projects/${project.id}`}
                                className="font-medium text-foreground hover:text-primary transition-colors"
                              >
                                {project.name}
                              </Link>
                              <p className="text-xs text-muted-foreground mt-1">
                                {project.productType.join(', ')}
                              </p>
                            </div>
                          </td>
                          <td className="p-4">
                            <div>
                              <Link 
                                href={`/customers/${project.customer.id}`}
                                className="font-medium text-foreground hover:text-primary transition-colors"
                              >
                                {project.customer.name}
                              </Link>
                              <p className="text-xs text-muted-foreground">{project.customer.phone}</p>
                            </div>
                          </td>
                          <td className="p-4">
                            <Badge variant={getStatusBadgeVariant(project.status)} className="text-xs">
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {project.status}
                            </Badge>
                          </td>
                          <td className="p-4">
                            {project.installer ? (
                              <div className="flex items-center space-x-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={project.installer.avatar || undefined} />
                                  <AvatarFallback className="bg-primary/10 text-primary text-xs">
                                    {project.installer.name.split(' ').map(n => n[0]).join('')}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="text-sm text-foreground">{project.installer.name}</span>
                              </div>
                            ) : (
                              <span className="text-sm text-muted-foreground">Unassigned</span>
                            )}
                          </td>
                          <td className="p-4">
                            <div className={`flex items-center text-sm ${overdue ? 'text-destructive' : 'text-muted-foreground'}`}>
                              <Calendar className="h-3 w-3 mr-2" />
                              {new Date(project.dueDate).toLocaleDateString()}
                              {overdue && <AlertCircle className="h-3 w-3 ml-1 text-destructive" />}
                            </div>
                          </td>
                          <td className="p-4">
                            <span className="font-medium text-foreground">
                              ${project.totalValue.toLocaleString()}
                            </span>
                          </td>
                          <td className="p-4">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit Project
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <UserPlus className="h-4 w-4 mr-2" />
                                  Assign Installer
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-destructive">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </td>
                        </motion.tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between p-4 border-t border-border">
                <div className="text-sm text-muted-foreground">
                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredProjects.length)} of {filteredProjects.length} projects
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(page)}
                          className="w-8 h-8 p-0"
                        >
                          {page}
                        </Button>
                      )
                    })}
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ) : (
        // Kanban View
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {Object.entries(kanbanColumns).map(([status, projects]) => (
            <Card key={status} className="h-fit">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-foreground">{status}</h3>
                  <Badge variant="secondary" className="text-xs">
                    {projects.length}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {projects.map(project => {
                  const StatusIcon = getStatusIcon(project.status)
                  const overdue = isOverdue(project.dueDate)
                  return (
                    <motion.div
                      key={project.id}
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      whileHover={{ scale: 1.02 }}
                      className="p-3 bg-background border border-border rounded-md cursor-pointer hover:border-primary/50 transition-colors"
                    >
                      <div className="space-y-2">
                        <div className="flex items-start justify-between">
                          <h4 className="font-medium text-sm text-foreground leading-tight">
                            {project.name}
                          </h4>
                          <Badge variant={getStatusBadgeVariant(project.status)} className="text-xs">
                            <StatusIcon className="h-3 w-3" />
                          </Badge>
                        </div>
                        
                        <p className="text-xs text-muted-foreground">
                          {project.customer.name}
                        </p>
                        
                        <div className="flex items-center justify-between text-xs">
                          <div className={`flex items-center ${overdue ? 'text-destructive' : 'text-muted-foreground'}`}>
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(project.dueDate).toLocaleDateString()}
                          </div>
                          <span className="font-medium text-foreground">
                            ${project.totalValue.toLocaleString()}
                          </span>
                        </div>
                        
                        {project.installer && (
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-5 w-5">
                              <AvatarImage src={project.installer.avatar || undefined} />
                              <AvatarFallback className="bg-primary/10 text-primary text-xs">
                                {project.installer.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-xs text-muted-foreground">
                              {project.installer.name}
                            </span>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )
                })}
              </CardContent>
            </Card>
          ))}
        </motion.div>
      )}

      {/* Quick Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="grid grid-cols-1 lg:grid-cols-3 gap-6"
      >
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Upcoming Schedules</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockProjects
                .filter(p => p.status !== 'Completed')
                .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
                .slice(0, 5)
                .map((project) => {
                  const overdue = isOverdue(project.dueDate)
                  return (
                    <div key={project.id} className="flex items-center justify-between p-3 rounded-md bg-accent/20">
                      <div className="flex items-center space-x-3">
                        <div className={`w-2 h-2 rounded-full ${
                          overdue ? 'bg-destructive' : 'bg-primary'
                        }`}></div>
                        <div>
                          <p className="text-sm font-medium text-foreground">{project.name}</p>
                          <p className="text-xs text-muted-foreground">{project.customer.name}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-sm font-medium ${overdue ? 'text-destructive' : 'text-foreground'}`}>
                          {new Date(project.dueDate).toLocaleDateString()}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {project.installer ? project.installer.name : 'Unassigned'}
                        </p>
                      </div>
                    </div>
                  )
                })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 rounded-md bg-destructive/5 border border-destructive/20">
                <p className="text-sm text-foreground font-medium mb-1">Assignment Alert</p>
                <p className="text-xs text-muted-foreground">
                  2 overdue projects need immediate installer assignment
                </p>
                <Button size="sm" variant="outline" className="mt-2 h-7 text-xs">
                  Assign Now <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
              </div>
              
              <div className="p-3 rounded-md bg-primary/5 border border-primary/20">
                <p className="text-sm text-foreground font-medium mb-1">Efficiency Tip</p>
                <p className="text-xs text-muted-foreground">
                  Mike Rodriguez has 20% faster completion time. Consider more assignments.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* New Project Modal */}
      <NewProjectModal
        isOpen={isNewProjectModalOpen}
        onClose={() => setIsNewProjectModalOpen(false)}
        onSave={handleCreateProject}
        customers={mockCustomers}
        installers={mockInstallers}
      />
    </div>
  )
}
