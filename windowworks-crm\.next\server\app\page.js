(()=>{var e={};e.id=974,e.ids=[974],e.modules={132:(e,t,r)=>{Promise.resolve().then(r.bind(r,1457))},276:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Source Code\\\\GoogleGemini\\\\windowworks-crm\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\page.tsx","default")},1457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>B});var s=r(687),a=r(3210),i=r(2912),n=r(7634),l=r(6001),o=r(8179),d=r(6349),c=r(1312),m=r(3928);let u=(0,r(2688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var x=r(228),p=r(5336),h=r(7992),j=r(4493),v=r(6834),g=r(9523),f=r(1273),y=r(4163),b="Progress",[N,k]=(0,f.A)(b),[w,P]=N(b),A=a.forwardRef((e,t)=>{var r,a;let{__scopeProgress:i,value:n=null,max:l,getValueLabel:o=G,...d}=e;(l||0===l)&&!S(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let c=S(l)?l:100;null===n||I(n,c)||console.error((a=`${n}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=I(n,c)?n:null,u=R(m)?o(m,c):void 0;return(0,s.jsx)(w,{scope:i,value:m,max:c,children:(0,s.jsx)(y.sG.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":R(m)?m:void 0,"aria-valuetext":u,role:"progressbar","data-state":_(m,c),"data-value":m??void 0,"data-max":c,...d,ref:t})})});A.displayName=b;var M="ProgressIndicator",C=a.forwardRef((e,t)=>{let{__scopeProgress:r,...a}=e,i=P(M,r);return(0,s.jsx)(y.sG.div,{"data-state":_(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...a,ref:t})});function G(e,t){return`${Math.round(e/t*100)}%`}function _(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function R(e){return"number"==typeof e}function S(e){return R(e)&&!isNaN(e)&&e>0}function I(e,t){return R(e)&&!isNaN(e)&&e<=t&&e>=0}C.displayName=M;var T=r(4780);let q=a.forwardRef(({className:e,value:t,...r},a)=>(0,s.jsx)(A,{ref:a,className:(0,T.cn)("relative h-2 w-full overflow-hidden bg-secondary border border-border rounded-none",e),...r,children:(0,s.jsx)(C,{className:"h-full w-full flex-1 bg-primary transition-all duration-500 ease-in-out",style:{transform:`translateX(-${100-(t||0)}%)`}})}));q.displayName=A.displayName;let D={totalProjects:156,activeProjects:24,completedProjects:132,totalCustomers:89,revenueThisMonth:45600,revenueLastMonth:38200},L=[{id:"1",type:"project-created",title:"New project created",description:"Kitchen blinds installation for Sarah Johnson",timestamp:"2 hours ago",user:"John Admin",entityId:"proj_123"},{id:"2",type:"task-completed",title:"Installation completed",description:"Living room shutters installed at 123 Oak Street",timestamp:"4 hours ago",user:"Mike Installer",entityId:"task_456"},{id:"3",type:"customer-added",title:"New customer added",description:"Emma Davis - Interested in bedroom shades",timestamp:"1 day ago",user:"Lisa Sales",entityId:"cust_789"}],$=[{title:"Total Projects",value:D.totalProjects,change:"+12%",icon:o.A},{title:"Active Projects",value:D.activeProjects,change:"+8%",icon:d.A},{title:"Total Customers",value:D.totalCustomers,change:"+23%",icon:c.A},{title:"Monthly Revenue",value:`$${D.revenueThisMonth.toLocaleString()}`,change:"+19%",icon:m.A}],E=[{id:"1",title:"Install bedroom blinds",customer:"Sarah Johnson",time:"10:00 AM",location:"123 Maple Street",status:"scheduled"},{id:"2",title:"Measure living room windows",customer:"Robert Chen",time:"2:30 PM",location:"456 Oak Avenue",status:"in-progress"},{id:"3",title:"Final inspection",customer:"Mary Williams",time:"4:00 PM",location:"789 Pine Road",status:"pending"}];function Z(){let e=((D.revenueThisMonth-D.revenueLastMonth)/D.revenueLastMonth*100).toFixed(1);return(0,s.jsxs)("div",{className:"space-y-6 p-1",children:[(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Welcome back! Here's what's happening today."})]}),(0,s.jsx)("div",{className:"flex items-center space-x-3",children:(0,s.jsx)(v.E,{variant:"secondary",className:"text-xs dark:bg-secondary/80",children:new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:$.map((e,t)=>(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:(0,s.jsxs)(j.Zp,{className:"hover:border-border/60 transition-all duration-200 dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsx)(j.aR,{className:"pb-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"p-2 rounded-md bg-primary/10 dark:bg-primary/20",children:(0,s.jsx)(e.icon,{className:"h-5 w-5 text-primary"})}),(0,s.jsx)(v.E,{variant:"secondary",className:"text-xs dark:bg-secondary/80",children:e.change})]})}),(0,s.jsx)(j.Wu,{children:(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-foreground",children:e.value}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title})]})})]})},e.title))}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},className:"lg:col-span-2",children:(0,s.jsxs)(j.Zp,{className:"dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsxs)(j.aR,{children:[(0,s.jsxs)(j.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(u,{className:"h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Revenue Trend"})]}),(0,s.jsxs)(j.BT,{children:["Monthly revenue comparison (+",e,"% from last month)"]})]}),(0,s.jsx)(j.Wu,{children:(0,s.jsx)("div",{className:"h-64 bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-md flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(u,{className:"h-12 w-12 text-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Revenue chart will be implemented here"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground/70",children:"Integration with chart library needed"})]})})})]})}),(0,s.jsx)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.5},children:(0,s.jsxs)(j.Zp,{className:"dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsxs)(j.aR,{children:[(0,s.jsx)(j.ZB,{children:"Project Status"}),(0,s.jsx)(j.BT,{children:"Current project distribution"})]}),(0,s.jsxs)(j.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,s.jsx)("span",{children:"Completed"}),(0,s.jsxs)("span",{children:[D.completedProjects,"/",D.totalProjects]})]}),(0,s.jsx)(q,{value:D.completedProjects/D.totalProjects*100,className:"h-2"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,s.jsx)("span",{children:"In Progress"}),(0,s.jsxs)("span",{children:[D.activeProjects,"/",D.totalProjects]})]}),(0,s.jsx)(q,{value:D.activeProjects/D.totalProjects*100,className:"h-2"})]})]})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,s.jsxs)(j.Zp,{className:"dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsxs)(j.aR,{children:[(0,s.jsxs)(j.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Today's Schedule"})]}),(0,s.jsx)(j.BT,{children:"Upcoming installations and appointments"})]}),(0,s.jsxs)(j.Wu,{children:[(0,s.jsx)("div",{className:"space-y-4",children:E.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-md bg-accent/30 dark:bg-accent/20 dark:border dark:border-border/30",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:"completed"===e.status?(0,s.jsx)(p.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"}):(0,s.jsx)(d.A,{className:"h-5 w-5 text-amber-600 dark:text-amber-400"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-foreground truncate",children:e.title}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.customer}),(0,s.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground mt-1",children:[(0,s.jsx)(d.A,{className:"h-3 w-3 mr-1"}),e.time,(0,s.jsx)(h.A,{className:"h-3 w-3 ml-2 mr-1"}),e.location]})]}),(0,s.jsx)(v.E,{variant:"completed"===e.status?"default":"secondary",className:"text-xs dark:bg-secondary/80",children:e.status})]},e.id))}),(0,s.jsx)(g.$,{variant:"outline",className:"w-full mt-4 dark:bg-background/50 dark:hover:bg-accent/50",children:"View Full Schedule"})]})]})}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:(0,s.jsxs)(j.Zp,{className:"dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsxs)(j.aR,{children:[(0,s.jsx)(j.ZB,{children:"Recent Activity"}),(0,s.jsx)(j.BT,{children:"Latest updates across the platform"})]}),(0,s.jsxs)(j.Wu,{children:[(0,s.jsx)("div",{className:"space-y-4",children:L.map(e=>(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-foreground",children:e.title}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground/70 mt-1",children:e.timestamp})]})]},e.id))}),(0,s.jsx)(g.$,{variant:"outline",className:"w-full mt-4 dark:bg-background/50 dark:hover:bg-accent/50",children:"View All Activity"})]})]})})]})]})}function B(){let{setUser:e,isAuthenticated:t}=(0,i.n)();return(0,s.jsx)(n.DashboardLayout,{children:(0,s.jsx)(Z,{})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3928:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7992:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8779:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(5239),a=r(8088),i=r(8170),n=r.n(i),l=r(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,145,861,171],()=>r(8779));module.exports=s})();