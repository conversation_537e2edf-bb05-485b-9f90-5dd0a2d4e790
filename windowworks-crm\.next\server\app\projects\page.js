(()=>{var e={};e.id=893,e.ids=[893],e.modules={22:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},983:(e,s,t)=>{Promise.resolve().then(t.bind(t,8054)),Promise.resolve().then(t.bind(t,3407))},2235:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=t(5239),r=t(8088),i=t(8170),l=t.n(i),n=t(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o={children:["",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2417)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\projects\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\projects\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/projects/page",pathname:"/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2417:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(7413),r=t(8054),i=t(3407);function l(){return(0,a.jsx)(r.DashboardLayout,{children:(0,a.jsx)(i.default,{})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3407:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Source Code\\\\GoogleGemini\\\\windowworks-crm\\\\src\\\\components\\\\projects\\\\projects-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\projects\\projects-page.tsx","default")},3503:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>c,L3:()=>x,c7:()=>m,lG:()=>n});var a=t(687);t(3210);var r=t(6134),i=t(1860),l=t(4780);function n({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e})}function d({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function o({className:e,...s}){return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...s})}function c({className:e,children:s,showCloseButton:t=!0,...n}){return(0,a.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,a.jsx)(o,{}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...n,children:[s,t&&(0,a.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(i.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...s})}function x({className:e,...s}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",e),...s})}},3873:e=>{"use strict";e.exports=require("path")},5279:(e,s,t)=>{"use strict";t.d(s,{default:()=>et});var a=t(687),r=t(3210),i=t(6001),l=t(5814),n=t.n(l),d=t(2688);let o=(0,d.A)("clipboard",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}]]),c=(0,d.A)("circle-play",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]]);var m=t(3613),x=t(8469),h=t(5336),u=t(8869),p=t(6349),j=t(6474),g=t(9270);let v=(0,d.A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),N=(0,d.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);var f=t(1158),y=t(228),b=t(3661),w=t(3861),C=t(3143),k=t(3026),A=t(8233);let D=(0,d.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var S=t(9523),$=t(9667),P=t(4493),T=t(6834),I=t(2584),M=t(1342),q=t(5079),_=t(6896),L=t(8920),z=t(22),E=t(2192),R=t(4027),V=t(3928),W=t(1862),B=t(8819),J=t(13),Z=t(4729),G=t(5763),F=t(3503),O=t(2587);let H=[{value:"low",label:"Low",color:"bg-gray-100 text-gray-700"},{value:"medium",label:"Medium",color:"bg-blue-100 text-blue-700"},{value:"high",label:"High",color:"bg-orange-100 text-orange-700"},{value:"urgent",label:"Urgent",color:"bg-red-100 text-red-700"}],U=[{value:"blinds",label:"Blinds"},{value:"shutters",label:"Shutters"},{value:"shades",label:"Shades"},{value:"curtains",label:"Curtains"},{value:"drapes",label:"Drapes"}],K=["Wood","Vinyl","Aluminum","Fabric","Bamboo","Faux Wood","Composite"],X=["Motorized","Blackout","Light Filtering","UV Protection","Energy Efficient","Cordless","Top Down Bottom Up"],Q=[{name:"Living Room",windows:2},{name:"Bedroom",windows:1},{name:"Kitchen",windows:1},{name:"Dining Room",windows:2},{name:"Office",windows:1},{name:"Bathroom",windows:1}];function Y({isOpen:e,onClose:s,onSave:t,customers:l,installers:n}){let{accentColor:d}=(0,O.D)(),[o,c]=(0,r.useState)("overview"),[x,h]=(0,r.useState)(!1),[g,v]=(0,r.useState)({}),[N,f]=(0,r.useState)({customerId:"",title:"",description:"",priority:"medium",estimatedCost:0,estimatedDuration:5,startDate:"",installerId:"",rooms:[],notes:""}),b=()=>`temp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,w=(0,r.useCallback)(e=>{if(!e.width||!e.height||!e.treatmentType)return 0;let s=Number(e.width)*Number(e.height)*({blinds:15,shutters:35,shades:25,curtains:20,drapes:30})[e.treatmentType];return s*=({Wood:1.3,Vinyl:.8,Aluminum:.9,Fabric:1.1,Bamboo:1.2,"Faux Wood":1,Composite:1.1})[e.material]||1,Math.round(s+=50*e.features.length)},[]);(0,r.useCallback)(()=>{let e=N.rooms.reduce((e,s)=>e+s.windows.reduce((e,s)=>e+w(s),0),0);f(s=>({...s,estimatedCost:e}))},[N.rooms,w]);let C=(0,r.useCallback)((e,s)=>{f(t=>{let a=e.split("."),r={...t},i=r;for(let e=0;e<a.length-1;e++)void 0===i[a[e]]&&(i[a[e]]={}),i=i[a[e]];return i[a[a.length-1]]=s,r}),v(s=>{if(s[e]){let t={...s};return delete t[e],t}return s})},[]),k=e=>{let s={id:b(),name:e||`Room ${N.rooms.length+1}`,windows:[]};if(e){let t=Q.find(s=>s.name===e);if(t)for(let e=0;e<t.windows;e++)s.windows.push({id:b(),width:"",height:"",treatmentType:"",color:"White",material:"Vinyl",installationType:"inside-mount",features:[],cost:0})}f(e=>({...e,rooms:[...e.rooms,s]}))},D=e=>{f(s=>({...s,rooms:s.rooms.filter(s=>s.id!==e)}))},M=e=>{let s={id:b(),width:"",height:"",treatmentType:"",color:"White",material:"Vinyl",installationType:"inside-mount",features:[],cost:0};f(t=>({...t,rooms:t.rooms.map(t=>t.id===e?{...t,windows:[...t.windows,s]}:t)}))},Y=(e,s)=>{f(t=>({...t,rooms:t.rooms.map(t=>t.id===e?{...t,windows:t.windows.filter(e=>e.id!==s)}:t)}))},ee=(e,s,t,a)=>{f(r=>({...r,rooms:r.rooms.map(r=>r.id===e?{...r,windows:r.windows.map(e=>e.id===s?{...e,[t]:a}:e)}:r)}))},es=(0,r.useCallback)(()=>{let e={};return N.title.trim()||(e.title="Project title is required"),N.customerId||(e.customerId="Customer selection is required"),N.description.trim()||(e.description="Project description is required"),0===N.rooms.length&&(e.rooms="At least one room is required"),N.rooms.forEach((s,t)=>{s.name.trim()||(e[`rooms.${t}.name`]="Room name is required"),0===s.windows.length&&(e[`rooms.${t}.windows`]="At least one window is required per room"),s.windows.forEach((s,a)=>{(!s.width||s.width<=0)&&(e[`rooms.${t}.windows.${a}.width`]="Valid width is required"),(!s.height||s.height<=0)&&(e[`rooms.${t}.windows.${a}.height`]="Valid height is required"),s.treatmentType||(e[`rooms.${t}.windows.${a}.treatmentType`]="Treatment type is required")})}),v(e),0===Object.keys(e).length},[N]),et=async()=>{if(!es())return void(g.title||g.customerId||g.description?c("overview"):Object.keys(g).some(e=>e.startsWith("rooms"))&&c("rooms"));h(!0);try{let e=N.rooms.map(e=>({name:e.name,windows:e.windows.filter(e=>""!==e.treatmentType).map(e=>({id:e.id,width:Number(e.width),height:Number(e.height),treatmentType:e.treatmentType,color:e.color,material:e.material,installationType:e.installationType,features:e.features,cost:w(e)}))})),a={customerId:N.customerId,title:N.title,description:N.description,priority:N.priority,estimatedCost:N.estimatedCost,estimatedDuration:N.estimatedDuration,startDate:N.startDate||void 0,installerId:N.installerId||void 0,rooms:e,notes:N.notes||void 0};await t(a),s(),f({customerId:"",title:"",description:"",priority:"medium",estimatedCost:0,estimatedDuration:5,startDate:"",installerId:"",rooms:[],notes:""}),c("overview")}catch(e){console.error("Error saving project:",e)}finally{h(!1)}};return(0,a.jsx)(F.lG,{open:e,onOpenChange:s,children:(0,a.jsx)(F.Cf,{className:"max-w-6xl sm:max-w-6xl w-[95vw] max-h-[70vh] overflow-hidden p-0",children:(0,a.jsxs)(i.P.div,{className:"flex flex-col h-full",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.2},children:[(0,a.jsx)(F.c7,{className:"px-6 py-4 border-b border-gray-200 flex-shrink-0",children:(0,a.jsx)(F.L3,{className:"text-xl font-semibold text-slate-700",children:"Create New Project"})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,a.jsxs)(G.tU,{value:o,onValueChange:c,className:"space-y-6",children:[(0,a.jsxs)(G.j7,{className:"grid w-full grid-cols-4",children:[(0,a.jsxs)(G.Xi,{value:"overview",className:"flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-4 w-4"}),"Overview"]}),(0,a.jsxs)(G.Xi,{value:"rooms",className:"flex items-center gap-2",children:[(0,a.jsx)(E.A,{className:"h-4 w-4"}),"Rooms"]}),(0,a.jsxs)(G.Xi,{value:"workflow",className:"flex items-center gap-2",children:[(0,a.jsx)(R.A,{className:"h-4 w-4"}),"Workflow"]}),(0,a.jsxs)(G.Xi,{value:"notes",className:"flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-4 w-4"}),"Notes"]})]}),(0,a.jsx)(G.av,{value:"overview",className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(P.Zp,{children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),"Basic Information"]})}),(0,a.jsxs)(P.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J.J,{htmlFor:"title",className:"text-slate-600",children:"Project Title *"}),(0,a.jsx)($.p,{id:"title",value:N.title,onChange:e=>C("title",e.target.value),className:`border-gray-200 focus:border-[var(--color-brand-primary)] ${g.title?"border-red-500":""}`,placeholder:"Enter project title"}),g.title&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),g.title]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J.J,{htmlFor:"description",className:"text-slate-600",children:"Description *"}),(0,a.jsx)(Z.T,{id:"description",value:N.description,onChange:e=>C("description",e.target.value),className:`min-h-[100px] border-gray-200 focus:border-[var(--color-brand-primary)] ${g.description?"border-red-500":""}`,placeholder:"Describe the project details..."}),g.description&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),g.description]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J.J,{className:"text-slate-600",children:"Priority"}),(0,a.jsx)("div",{className:"flex gap-2",children:H.map(e=>(0,a.jsx)(T.E,{variant:N.priority===e.value?"default":"outline",className:`cursor-pointer transition-colors ${N.priority===e.value?e.color:"border-gray-300 text-gray-600 hover:bg-gray-50"}`,onClick:()=>C("priority",e.value),children:e.label},e.value))})]})]})]}),(0,a.jsxs)(P.Zp,{children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),"Customer & Assignment"]})}),(0,a.jsxs)(P.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J.J,{className:"text-slate-600",children:"Customer *"}),(0,a.jsxs)(q.l6,{value:N.customerId,onValueChange:e=>C("customerId",e),children:[(0,a.jsx)(q.bq,{className:`border-gray-200 ${g.customerId?"border-red-500":""}`,children:(0,a.jsx)(q.yv,{placeholder:"Select customer"})}),(0,a.jsx)(q.gC,{children:l.map(e=>(0,a.jsx)(q.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(I.eu,{className:"h-6 w-6",children:(0,a.jsxs)(I.q5,{className:"text-xs",children:[e.firstName[0],e.lastName[0]]})}),e.firstName," ",e.lastName]})},e.id))})]}),g.customerId&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),g.customerId]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J.J,{className:"text-slate-600",children:"Installer"}),(0,a.jsxs)(q.l6,{value:N.installerId,onValueChange:e=>C("installerId",e),children:[(0,a.jsx)(q.bq,{className:"border-gray-200",children:(0,a.jsx)(q.yv,{placeholder:"Select installer (optional)"})}),(0,a.jsx)(q.gC,{children:n.map(e=>(0,a.jsx)(q.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(I.eu,{className:"h-6 w-6",children:[(0,a.jsx)(I.BK,{src:e.profile?.avatar}),(0,a.jsxs)(I.q5,{className:"text-xs",children:[e.profile.firstName[0],e.profile.lastName[0]]})]}),e.profile.firstName," ",e.profile.lastName]})},e.id))})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(J.J,{htmlFor:"estimatedDuration",className:"text-slate-600 flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Duration (days)"]}),(0,a.jsx)($.p,{id:"estimatedDuration",type:"number",min:"1",value:N.estimatedDuration,onChange:e=>C("estimatedDuration",Number(e.target.value)),className:"border-gray-200 focus:border-[var(--color-brand-primary)]"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(J.J,{htmlFor:"startDate",className:"text-slate-600 flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),"Start Date"]}),(0,a.jsx)($.p,{id:"startDate",type:"date",value:N.startDate,onChange:e=>C("startDate",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)]"})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-amber-50 rounded-lg border border-amber-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-amber-800",children:[(0,a.jsx)(V.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"font-medium",children:"Estimated Cost"})]}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-amber-900 mt-1",children:["$",N.estimatedCost.toLocaleString()]}),(0,a.jsx)("p",{className:"text-sm text-amber-700 mt-1",children:"Auto-calculated from room specifications"})]})]})]})]})}),(0,a.jsxs)(G.av,{value:"rooms",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-700",children:"Room Specifications"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(q.l6,{onValueChange:k,children:[(0,a.jsx)(q.bq,{className:"w-48 border-gray-200",children:(0,a.jsx)(q.yv,{placeholder:"Add room template"})}),(0,a.jsx)(q.gC,{children:Q.map(e=>(0,a.jsxs)(q.eb,{value:e.name,children:[e.name," (",e.windows," window",1!==e.windows?"s":"",")"]},e.name))})]}),(0,a.jsxs)(S.$,{onClick:()=>k(),className:"text-white",style:{backgroundColor:d,borderColor:d},children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Add Room"]})]})]}),g.rooms&&(0,a.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),g.rooms]})}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)(L.N,{children:N.rooms.map((e,s)=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.2},children:(0,a.jsxs)(P.Zp,{children:[(0,a.jsxs)(P.aR,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(E.A,{className:"h-5 w-5 text-slate-600"}),(0,a.jsx)($.p,{value:e.name,onChange:e=>C(`rooms.${s}.name`,e.target.value),className:`font-medium border-none focus:border-[var(--color-brand-primary)] bg-transparent p-0 text-lg ${g[`rooms.${s}.name`]?"border-red-500":""}`,placeholder:"Room name"})]}),(0,a.jsx)(S.$,{variant:"ghost",size:"sm",onClick:()=>D(e.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,a.jsx)(A.A,{className:"h-4 w-4"})})]}),g[`rooms.${s}.name`]&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),g[`rooms.${s}.name`]]})]}),(0,a.jsx)(P.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-slate-600",children:"Windows"}),(0,a.jsxs)(S.$,{variant:"outline",size:"sm",onClick:()=>M(e.id),className:"border-gray-200",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-1"}),"Add Window"]})]}),g[`rooms.${s}.windows`]&&(0,a.jsx)("div",{className:"p-2 bg-red-50 border border-red-200 rounded",children:(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),g[`rooms.${s}.windows`]]})}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsx)(L.N,{children:e.windows.map((t,r)=>(0,a.jsxs)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},className:"p-4 border border-gray-200 rounded-lg space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"font-medium text-slate-700",children:["Window ",r+1]}),(0,a.jsx)(S.$,{variant:"ghost",size:"sm",onClick:()=>Y(e.id,t.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,a.jsx)(A.A,{className:"h-3 w-3"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(J.J,{className:"text-xs text-slate-600",children:"Width (inches) *"}),(0,a.jsx)($.p,{type:"number",min:"1",step:"0.25",value:t.width,onChange:s=>ee(e.id,t.id,"width",Number(s.target.value)),className:`text-sm border-gray-200 ${g[`rooms.${s}.windows.${r}.width`]?"border-red-500":""}`,placeholder:"Width"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(J.J,{className:"text-xs text-slate-600",children:"Height (inches) *"}),(0,a.jsx)($.p,{type:"number",min:"1",step:"0.25",value:t.height,onChange:s=>ee(e.id,t.id,"height",Number(s.target.value)),className:`text-sm border-gray-200 ${g[`rooms.${s}.windows.${r}.height`]?"border-red-500":""}`,placeholder:"Height"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(J.J,{className:"text-xs text-slate-600",children:"Treatment Type *"}),(0,a.jsxs)(q.l6,{value:t.treatmentType,onValueChange:s=>ee(e.id,t.id,"treatmentType",s),children:[(0,a.jsx)(q.bq,{className:`text-sm border-gray-200 ${g[`rooms.${s}.windows.${r}.treatmentType`]?"border-red-500":""}`,children:(0,a.jsx)(q.yv,{placeholder:"Type"})}),(0,a.jsx)(q.gC,{children:U.map(e=>(0,a.jsx)(q.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(J.J,{className:"text-xs text-slate-600",children:"Color"}),(0,a.jsx)($.p,{value:t.color,onChange:s=>ee(e.id,t.id,"color",s.target.value),className:"text-sm border-gray-200",placeholder:"Color"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(J.J,{className:"text-xs text-slate-600",children:"Material"}),(0,a.jsxs)(q.l6,{value:t.material,onValueChange:s=>ee(e.id,t.id,"material",s),children:[(0,a.jsx)(q.bq,{className:"text-sm border-gray-200",children:(0,a.jsx)(q.yv,{})}),(0,a.jsx)(q.gC,{children:K.map(e=>(0,a.jsx)(q.eb,{value:e,children:e},e))})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(J.J,{className:"text-xs text-slate-600",children:"Installation"}),(0,a.jsxs)(q.l6,{value:t.installationType,onValueChange:s=>ee(e.id,t.id,"installationType",s),children:[(0,a.jsx)(q.bq,{className:"text-sm border-gray-200",children:(0,a.jsx)(q.yv,{})}),(0,a.jsxs)(q.gC,{children:[(0,a.jsx)(q.eb,{value:"inside-mount",children:"Inside Mount"}),(0,a.jsx)(q.eb,{value:"outside-mount",children:"Outside Mount"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(J.J,{className:"text-xs text-slate-600",children:"Estimated Cost"}),(0,a.jsxs)("div",{className:"text-sm font-medium text-green-600 bg-green-50 px-3 py-2 rounded border",children:["$",w(t).toLocaleString()]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J.J,{className:"text-xs text-slate-600",children:"Features"}),(0,a.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-2",children:X.map(s=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(_.S,{id:`${t.id}-${s}`,checked:t.features.includes(s),onCheckedChange:a=>{let r=a?[...t.features,s]:t.features.filter(e=>e!==s);ee(e.id,t.id,"features",r)},className:"border-gray-300"}),(0,a.jsx)(J.J,{htmlFor:`${t.id}-${s}`,className:"text-xs text-slate-600",children:s})]},s))})]})]},t.id))})})]})})]})},e.id))})}),0===N.rooms.length&&(0,a.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,a.jsx)(E.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,a.jsx)("p",{className:"text-lg font-medium mb-2",children:"No rooms added yet"}),(0,a.jsx)("p",{className:"text-sm",children:"Add rooms to start defining your project specifications"})]})]}),(0,a.jsx)(G.av,{value:"workflow",className:"space-y-6",children:(0,a.jsxs)(P.Zp,{children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,a.jsx)(R.A,{className:"h-5 w-5"}),"Project Workflow"]})}),(0,a.jsx)(P.Wu,{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(J.J,{className:"text-slate-600",children:"Assigned Installer"}),(0,a.jsxs)(q.l6,{value:N.installerId,onValueChange:e=>C("installerId",e),children:[(0,a.jsx)(q.bq,{className:"border-gray-200",children:(0,a.jsx)(q.yv,{placeholder:"Select installer"})}),(0,a.jsx)(q.gC,{children:n.map(e=>(0,a.jsx)(q.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(I.eu,{className:"h-8 w-8",children:[(0,a.jsx)(I.BK,{src:e.profile?.avatar}),(0,a.jsxs)(I.q5,{children:[e.profile.firstName[0],e.profile.lastName[0]]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.profile.firstName," ",e.profile.lastName]}),(0,a.jsx)("div",{className:"text-sm text-slate-500",children:e.email})]})]})},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(J.J,{htmlFor:"startDate",className:"text-slate-600 flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),"Planned Start Date"]}),(0,a.jsx)($.p,{id:"startDate",type:"date",value:N.startDate,onChange:e=>C("startDate",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)]"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(J.J,{htmlFor:"estimatedDuration",className:"text-slate-600 flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Estimated Duration (days)"]}),(0,a.jsx)($.p,{id:"estimatedDuration",type:"number",min:"1",value:N.estimatedDuration,onChange:e=>C("estimatedDuration",Number(e.target.value)),className:"border-gray-200 focus:border-[var(--color-brand-primary)]"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"Project Timeline"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-blue-700",children:"Start Date:"}),(0,a.jsx)("span",{className:"text-blue-900",children:N.startDate||"Not set"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-blue-700",children:"Duration:"}),(0,a.jsxs)("span",{className:"text-blue-900",children:[N.estimatedDuration," day",1!==N.estimatedDuration?"s":""]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-blue-700",children:"Estimated Completion:"}),(0,a.jsx)("span",{className:"text-blue-900",children:N.startDate?new Date(new Date(N.startDate).getTime()+24*N.estimatedDuration*36e5).toLocaleDateString():"Not calculated"})]})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800 mb-2",children:"Cost Summary"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-green-700",children:"Total Rooms:"}),(0,a.jsx)("span",{className:"text-green-900",children:N.rooms.length})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-green-700",children:"Total Windows:"}),(0,a.jsx)("span",{className:"text-green-900",children:N.rooms.reduce((e,s)=>e+s.windows.length,0)})]}),(0,a.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,a.jsx)("span",{className:"text-green-700",children:"Estimated Cost:"}),(0,a.jsxs)("span",{className:"text-green-900 text-lg",children:["$",N.estimatedCost.toLocaleString()]})]})]})]})]})]})})]})}),(0,a.jsx)(G.av,{value:"notes",className:"space-y-6",children:(0,a.jsxs)(P.Zp,{children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),"Project Notes"]})}),(0,a.jsx)(P.Wu,{children:(0,a.jsx)(Z.T,{value:N.notes,onChange:e=>C("notes",e.target.value),className:"min-h-[300px] border-gray-200 focus:border-[var(--color-brand-primary)]",placeholder:"Add any additional notes, special requirements, customer preferences, installation considerations, or other relevant information for this project..."})})]})})]})}),(0,a.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 flex-shrink-0",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(S.$,{variant:"outline",onClick:s,disabled:x,className:"border-gray-200",children:"Cancel"}),(0,a.jsx)(S.$,{onClick:et,disabled:x,className:"text-white min-w-[140px]",style:{backgroundColor:d,borderColor:d},children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Create Project"]})})]})})]})})})}let ee=[{id:"proj_001",name:"Living Room Shutters Installation",customer:{id:"cust_001",name:"Sarah Johnson",email:"<EMAIL>",phone:"(*************"},status:"In-Progress",installer:{id:"inst_001",name:"Mike Rodriguez",avatar:null},dueDate:"2025-07-15",productType:["Shutters"],description:"Custom wood shutters for living room bay windows",address:"123 Maple Street, Springfield, IL 62704",totalValue:2840,createdAt:"2025-07-01",completionTime:3},{id:"proj_002",name:"Office Blinds Setup",customer:{id:"cust_002",name:"Robert Chen",email:"<EMAIL>",phone:"(*************"},status:"Assigned",installer:{id:"inst_002",name:"Lisa Anderson",avatar:null},dueDate:"2025-07-18",productType:["Blinds"],description:"Motorized blinds for home office windows",address:"456 Oak Avenue, Springfield, IL 62701",totalValue:1560,createdAt:"2025-07-03"},{id:"proj_003",name:"Bedroom Shades Installation",customer:{id:"cust_003",name:"Emma Davis",email:"<EMAIL>",phone:"(*************"},status:"Pending",installer:null,dueDate:"2025-07-12",productType:["Shades"],description:"Blackout shades for master bedroom",address:"789 Pine Road, Springfield, IL 62702",totalValue:890,createdAt:"2025-07-08"},{id:"proj_004",name:"Kitchen Window Treatments",customer:{id:"cust_004",name:"Michael Williams",email:"<EMAIL>",phone:"(*************"},status:"Completed",installer:{id:"inst_001",name:"Mike Rodriguez",avatar:null},dueDate:"2025-07-10",productType:["Blinds","Shades"],description:"Water-resistant blinds and caf\xe9 shades",address:"321 Elm Street, Springfield, IL 62703",totalValue:1250,createdAt:"2025-06-28",completionTime:4},{id:"proj_005",name:"Sunroom Shutters",customer:{id:"cust_005",name:"David Thompson",email:"<EMAIL>",phone:"(*************"},status:"In-Progress",installer:{id:"inst_003",name:"Carlos Martinez",avatar:null},dueDate:"2025-07-20",productType:["Shutters"],description:"Full-height shutters for sunroom",address:"654 Cedar Lane, Springfield, IL 62705",totalValue:3200,createdAt:"2025-07-05",completionTime:6}],es=[{title:"Total Projects",value:"156",change:"-1%",icon:o,trend:"down"},{title:"Active Projects",value:"24",change:"+5%",icon:c,trend:"up"},{title:"Overdue",value:"3",change:"",icon:m.A,trend:"alert"},{title:"Avg Completion Time",value:"5 days",change:"+2%",icon:x.A,trend:"up"}];function et(){let[e,s]=(0,r.useState)(""),[t,l]=(0,r.useState)("all"),[d,o]=(0,r.useState)("all"),[x,L]=(0,r.useState)("dueDate"),[z,E]=(0,r.useState)([]),[R,V]=(0,r.useState)("table"),[W,B]=(0,r.useState)(1),[J,Z]=(0,r.useState)(!1),G=async e=>{try{console.log("Creating new project:",e),Z(!1)}catch(e){throw console.error("Error creating project:",e),e}},F=ee.filter(s=>{let a=s.name.toLowerCase().includes(e.toLowerCase())||s.customer.name.toLowerCase().includes(e.toLowerCase()),r="all"===t||s.status.toLowerCase()===t.toLowerCase(),i="all"===d||s.productType.some(e=>e.toLowerCase()===d.toLowerCase());return a&&r&&i}).sort((e,s)=>{switch(x){case"dueDate":return new Date(e.dueDate).getTime()-new Date(s.dueDate).getTime();case"name":return e.name.localeCompare(s.name);case"customer":return e.customer.name.localeCompare(s.customer.name);default:return 0}}),O=Math.ceil(F.length/10),H=F.slice((W-1)*10,10*W),U=e=>{E(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},K=e=>{switch(e.toLowerCase()){case"completed":return"default";case"in-progress":return"secondary";case"assigned":default:return"outline";case"pending":return"destructive"}},X=e=>{switch(e.toLowerCase()){case"completed":return h.A;case"in-progress":return c;case"assigned":return u.A;default:return p.A}},Q=e=>new Date(e)<new Date&&!ee.find(s=>s.dueDate===e&&"Completed"===s.status),et={Pending:F.filter(e=>"Pending"===e.status),Assigned:F.filter(e=>"Assigned"===e.status),"In-Progress":F.filter(e=>"In-Progress"===e.status),Completed:F.filter(e=>"Completed"===e.status)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Projects"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track installations, workflows, and statuses"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(T.E,{variant:"secondary",className:"text-xs",children:"Saturday, July 12, 2025"}),(0,a.jsxs)(S.$,{className:"bg-primary hover:bg-primary/90",onClick:()=>Z(!0),children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"New Project"]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:es.map((e,s)=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:(0,a.jsxs)(P.Zp,{className:"hover:border-border/80 transition-colors",children:[(0,a.jsx)(P.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:`p-2 rounded-md ${"alert"===e.trend?"bg-destructive/10":"bg-primary/10"}`,children:(0,a.jsx)(e.icon,{className:`h-5 w-5 ${"alert"===e.trend?"text-destructive":"text-primary"}`})}),e.change&&(0,a.jsx)(T.E,{variant:"up"===e.trend?"default":"down"===e.trend||"alert"===e.trend?"destructive":"secondary",className:"text-xs",children:e.change})]})}),(0,a.jsx)(P.Wu,{children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-foreground",children:e.value}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title})]})})]})},e.title))}),(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,a.jsx)(P.Zp,{children:(0,a.jsx)(P.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-1 items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,a.jsx)($.p,{placeholder:"Search projects, customers...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(q.l6,{value:t,onValueChange:l,children:[(0,a.jsx)(q.bq,{className:"w-32",children:(0,a.jsx)(q.yv,{placeholder:"Status"})}),(0,a.jsxs)(q.gC,{children:[(0,a.jsx)(q.eb,{value:"all",children:"All Status"}),(0,a.jsx)(q.eb,{value:"pending",children:"Pending"}),(0,a.jsx)(q.eb,{value:"assigned",children:"Assigned"}),(0,a.jsx)(q.eb,{value:"in-progress",children:"In-Progress"}),(0,a.jsx)(q.eb,{value:"completed",children:"Completed"})]})]}),(0,a.jsxs)(q.l6,{value:d,onValueChange:o,children:[(0,a.jsx)(q.bq,{className:"w-36",children:(0,a.jsx)(q.yv,{placeholder:"Product"})}),(0,a.jsxs)(q.gC,{children:[(0,a.jsx)(q.eb,{value:"all",children:"All Products"}),(0,a.jsx)(q.eb,{value:"blinds",children:"Blinds"}),(0,a.jsx)(q.eb,{value:"shutters",children:"Shutters"}),(0,a.jsx)(q.eb,{value:"shades",children:"Shades"})]})]}),(0,a.jsxs)(q.l6,{value:x,onValueChange:L,children:[(0,a.jsx)(q.bq,{className:"w-32",children:(0,a.jsx)(q.yv,{placeholder:"Sort by"})}),(0,a.jsxs)(q.gC,{children:[(0,a.jsx)(q.eb,{value:"dueDate",children:"Due Date"}),(0,a.jsx)(q.eb,{value:"name",children:"Name"}),(0,a.jsx)(q.eb,{value:"customer",children:"Customer"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex items-center bg-accent rounded-md p-1",children:[(0,a.jsx)(S.$,{variant:"table"===R?"default":"ghost",size:"sm",onClick:()=>V("table"),className:"h-8 px-3",children:(0,a.jsx)(v,{className:"h-4 w-4"})}),(0,a.jsx)(S.$,{variant:"kanban"===R?"default":"ghost",size:"sm",onClick:()=>V("kanban"),className:"h-8 px-3",children:(0,a.jsx)(N,{className:"h-4 w-4"})})]}),(0,a.jsxs)(S.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Export"]}),z.length>0&&(0,a.jsxs)(T.E,{variant:"secondary",children:[z.length," selected"]})]})]})})})}),"table"===R?(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,a.jsx)(P.Zp,{children:(0,a.jsxs)(P.Wu,{className:"p-0",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"border-b border-border",children:(0,a.jsxs)("tr",{className:"bg-accent/20",children:[(0,a.jsx)("th",{className:"text-left p-4 w-12",children:(0,a.jsx)(_.S,{checked:z.length===H.length&&H.length>0,onCheckedChange:()=>{z.length===H.length?E([]):E(H.map(e=>e.id))}})}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Project"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Customer"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Status"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Installer"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Due Date"}),(0,a.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Value"}),(0,a.jsx)("th",{className:"text-right p-4 w-16"})]})}),(0,a.jsx)("tbody",{children:H.map((e,s)=>{let t=X(e.status),r=Q(e.dueDate);return(0,a.jsxs)(i.P.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},className:"border-b border-border hover:bg-accent/30 transition-colors cursor-pointer",children:[(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)(_.S,{checked:z.includes(e.id),onCheckedChange:()=>U(e.id),onClick:e=>e.stopPropagation()})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:`/projects/${e.id}`,className:"font-medium text-foreground hover:text-primary transition-colors",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.productType.join(", ")})]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)(n(),{href:`/customers/${e.customer.id}`,className:"font-medium text-foreground hover:text-primary transition-colors",children:e.customer.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.customer.phone})]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)(T.E,{variant:K(e.status),className:"text-xs",children:[(0,a.jsx)(t,{className:"h-3 w-3 mr-1"}),e.status]})}),(0,a.jsx)("td",{className:"p-4",children:e.installer?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(I.eu,{className:"h-6 w-6",children:[(0,a.jsx)(I.BK,{src:e.installer.avatar||void 0}),(0,a.jsx)(I.q5,{className:"bg-primary/10 text-primary text-xs",children:e.installer.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsx)("span",{className:"text-sm text-foreground",children:e.installer.name})]}):(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Unassigned"})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("div",{className:`flex items-center text-sm ${r?"text-destructive":"text-muted-foreground"}`,children:[(0,a.jsx)(y.A,{className:"h-3 w-3 mr-2"}),new Date(e.dueDate).toLocaleDateString(),r&&(0,a.jsx)(m.A,{className:"h-3 w-3 ml-1 text-destructive"})]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("span",{className:"font-medium text-foreground",children:["$",e.totalValue.toLocaleString()]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)(M.rI,{children:[(0,a.jsx)(M.ty,{asChild:!0,onClick:e=>e.stopPropagation(),children:(0,a.jsx)(S.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(M.SQ,{align:"end",children:[(0,a.jsxs)(M._2,{children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),(0,a.jsxs)(M._2,{children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Edit Project"]}),(0,a.jsxs)(M._2,{children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Assign Installer"]}),(0,a.jsx)(M.mB,{}),(0,a.jsxs)(M._2,{className:"text-destructive",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})})]},e.id)})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-border",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",(W-1)*10+1," to ",Math.min(10*W,F.length)," of ",F.length," projects"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.$,{variant:"outline",size:"sm",onClick:()=>B(e=>Math.max(1,e-1)),disabled:1===W,children:"Previous"}),(0,a.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,O)},(e,s)=>{let t=s+1;return(0,a.jsx)(S.$,{variant:W===t?"default":"outline",size:"sm",onClick:()=>B(t),className:"w-8 h-8 p-0",children:t},t)})}),(0,a.jsx)(S.$,{variant:"outline",size:"sm",onClick:()=>B(e=>Math.min(O,e+1)),disabled:W===O,children:"Next"})]})]})]})})}):(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Object.entries(et).map(([e,s])=>(0,a.jsxs)(P.Zp,{className:"h-fit",children:[(0,a.jsx)(P.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"font-semibold text-foreground",children:e}),(0,a.jsx)(T.E,{variant:"secondary",className:"text-xs",children:s.length})]})}),(0,a.jsx)(P.Wu,{className:"space-y-3",children:s.map(e=>{let s=X(e.status),t=Q(e.dueDate);return(0,a.jsx)(i.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},whileHover:{scale:1.02},className:"p-3 bg-background border border-border rounded-md cursor-pointer hover:border-primary/50 transition-colors",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-foreground leading-tight",children:e.name}),(0,a.jsx)(T.E,{variant:K(e.status),className:"text-xs",children:(0,a.jsx)(s,{className:"h-3 w-3"})})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.customer.name}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("div",{className:`flex items-center ${t?"text-destructive":"text-muted-foreground"}`,children:[(0,a.jsx)(y.A,{className:"h-3 w-3 mr-1"}),new Date(e.dueDate).toLocaleDateString()]}),(0,a.jsxs)("span",{className:"font-medium text-foreground",children:["$",e.totalValue.toLocaleString()]})]}),e.installer&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(I.eu,{className:"h-5 w-5",children:[(0,a.jsx)(I.BK,{src:e.installer.avatar||void 0}),(0,a.jsx)(I.q5,{className:"bg-primary/10 text-primary text-xs",children:e.installer.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.installer.name})]})]})},e.id)})})]},e))}),(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(P.Zp,{className:"lg:col-span-2",children:[(0,a.jsx)(P.aR,{children:(0,a.jsx)(P.ZB,{children:"Upcoming Schedules"})}),(0,a.jsx)(P.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:ee.filter(e=>"Completed"!==e.status).sort((e,s)=>new Date(e.dueDate).getTime()-new Date(s.dueDate).getTime()).slice(0,5).map(e=>{let s=Q(e.dueDate);return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-md bg-accent/20",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${s?"bg-destructive":"bg-primary"}`}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-foreground",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.customer.name})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:`text-sm font-medium ${s?"text-destructive":"text-foreground"}`,children:new Date(e.dueDate).toLocaleDateString()}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.installer?e.installer.name:"Unassigned"})]})]},e.id)})})})]}),(0,a.jsxs)(P.Zp,{children:[(0,a.jsx)(P.aR,{children:(0,a.jsx)(P.ZB,{children:"AI Insights"})}),(0,a.jsx)(P.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-3 rounded-md bg-destructive/5 border border-destructive/20",children:[(0,a.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Assignment Alert"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"2 overdue projects need immediate installer assignment"}),(0,a.jsxs)(S.$,{size:"sm",variant:"outline",className:"mt-2 h-7 text-xs",children:["Assign Now ",(0,a.jsx)(D,{className:"h-3 w-3 ml-1"})]})]}),(0,a.jsxs)("div",{className:"p-3 rounded-md bg-primary/5 border border-primary/20",children:[(0,a.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Efficiency Tip"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Mike Rodriguez has 20% faster completion time. Consider more assignments."})]})]})})]})]}),(0,a.jsx)(Y,{isOpen:J,onClose:()=>Z(!1),onSave:G,customers:[{id:"cust_001",firstName:"Sarah",lastName:"Johnson",email:"<EMAIL>",phone:"(*************",address:{street:"123 Maple Street",city:"Springfield",state:"IL",zipCode:"62704",country:"United States"},preferences:{windowTreatmentTypes:["shutters","blinds"],preferredColors:["white","beige"],budget:"2500-5000",communication:"email"},createdAt:"2025-07-01T00:00:00Z",updatedAt:"2025-07-01T00:00:00Z",createdBy:"admin"}],installers:[{id:"inst_001",email:"<EMAIL>",role:"installer",profile:{firstName:"Mike",lastName:"Rodriguez",phone:"(*************"},createdAt:"2025-07-01T00:00:00Z",updatedAt:"2025-07-01T00:00:00Z"}]})]})}},6896:(e,s,t)=>{"use strict";t.d(s,{S:()=>d});var a=t(687),r=t(3210),i=t(211),l=t(3964),n=t(4780);let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i.bL,{ref:t,className:(0,n.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:(0,a.jsx)(i.C1,{className:(0,n.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}));d.displayName=i.bL.displayName},8469:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2688).A)("timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9359:(e,s,t)=>{Promise.resolve().then(t.bind(t,7634)),Promise.resolve().then(t.bind(t,5279))},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,145,861,979,121,171,556],()=>t(2235));module.exports=a})();