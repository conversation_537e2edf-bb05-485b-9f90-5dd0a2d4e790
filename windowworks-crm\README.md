# WindowWorks CRM

A modern SaaS platform for window treatment installation companies, built with Next.js, TypeScript, Tailwind CSS, and Supabase.

## 🚀 Features

- **Customer Management**: Complete CRUD operations for customer profiles with preferences
- **Project Management**: Track jobs, statuses, timelines, and installer assignments  
- **Installer Workflows**: Mobile-optimized interfaces for job management
- **Real-time Notifications**: In-app alerts for assignments and updates
- **Role-based Access**: Admin, installer, and customer portals with Supabase Auth
- **Responsive Design**: Mobile-first design with modern UI components

## 🛠️ Tech Stack

- **Framework**: Next.js 14+ with App Router and Turbopack
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS v4 with custom WindowWorks theme
- **UI Components**: Shadcn/UI with Radix UI primitives
- **Authentication**: Supabase Auth with SSR support
- **State Management**: Zustand
- **Animations**: Framer Motion
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React

## 🎨 Design System

- **Primary Color**: Amber (#D97706) - Representing warmth and light through windows
- **Secondary Color**: Slate (#4A5563) - Professional and modern
- **Accent**: Light Gray (#F9FAFB) - Clean backgrounds
- **Typography**: Inter font family for readability
- **Components**: Consistent radius (10px) and spacing scale

## 📦 Project Structure

```
src/
├── app/                    # Next.js App Router pages
├── components/
│   ├── ui/                # Shadcn/UI components
│   ├── auth/              # Authentication components
│   ├── dashboard/         # Dashboard-specific components
│   ├── customers/         # Customer management components
│   └── projects/          # Project management components
├── lib/
│   ├── supabase/          # Supabase client configuration
│   ├── stores/            # Zustand stores
│   └── utils.ts           # Utility functions
├── types/                 # TypeScript type definitions
└── hooks/                 # Custom React hooks
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm, pnpm, yarn, or bun
- Supabase account (for authentication and database)

### Installation

1. **Clone and install dependencies:**
   \`\`\`bash
   cd windowworks-crm
   npm install
   \`\`\`

2. **Set up environment variables:**
   \`\`\`bash
   cp .env.local.example .env.local
   \`\`\`
   
   Update `.env.local` with your Supabase credentials:
   \`\`\`
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   \`\`\`

3. **Run the development server:**
   \`\`\`bash
   npm run dev
   \`\`\`

4. **Open [http://localhost:3000](http://localhost:3000) in your browser**

## 🔐 Authentication Setup

The app uses Supabase for authentication with role-based access control:

- **Admin**: Full access to customers, projects, and installer management
- **Installer**: Access to assigned projects and mobile workflows  
- **Customer**: Limited access to own projects and status updates

### Mock Development User

For development, a mock admin user is automatically set:
- Email: <EMAIL>
- Role: Admin
- Name: John Admin

## 📱 Mobile-First Design

The application is designed mobile-first with:
- Responsive sidebar that converts to bottom navigation on mobile
- Touch-friendly interfaces for installers in the field
- Optimized forms and workflows for small screens
- Progressive Web App (PWA) capabilities

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler

### Code Standards

- TypeScript strict mode enabled
- ESLint + Prettier for code formatting
- Husky for git hooks (when configured)
- Semantic commit messages preferred

## 🚦 Roadmap

### Phase 1 (Current)
- [x] Project setup and basic structure
- [x] Dashboard layout and navigation
- [x] Authentication foundation
- [x] TypeScript types and stores
- [ ] Customer management CRUD
- [ ] Project management system

### Phase 2 (Next)
- [ ] Supabase database integration
- [ ] Real-time notifications
- [ ] Mobile installer workflows
- [ ] Photo upload for installations
- [ ] Calendar scheduling system

### Phase 3 (Future)
- [ ] AI-driven scheduling optimization
- [ ] Customer portal
- [ ] Reporting and analytics
- [ ] Integration with accounting systems
- [ ] PWA and offline capabilities

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙋‍♂️ Support

For support and questions:
- Create an issue in this repository
- Contact the development team
- Check the documentation in `/docs`

---

**WindowWorks CRM** - Streamlining window treatment installations with modern technology 🪟✨
