(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[826],{333:(e,s,t)=>{"use strict";t.d(s,{d:()=>n});var a=t(5155),r=t(2115),l=t(9434);let n=r.forwardRef((e,s)=>{let{className:t,checked:r=!1,onCheckedChange:n,disabled:i=!1,id:c,...d}=e;return(0,a.jsx)("button",{ref:s,type:"button",role:"switch","aria-checked":r,disabled:i,onClick:()=>{!i&&n&&n(!r)},id:c,className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-sm border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",r?"bg-primary":"bg-input",t),...d,children:(0,a.jsx)("span",{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded bg-background shadow-lg ring-0 transition-transform",r?"translate-x-5":"translate-x-0")})})});n.displayName="Switch"},2905:(e,s,t)=>{"use strict";t.d(s,{D:()=>c,ThemeProvider:()=>i});var a=t(5155),r=t(2115);let l=(0,r.createContext)(void 0),n=()=>{try{let e=localStorage.getItem("windowworks-theme");if(e&&["light","dark"].includes(e))return e;return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}catch(e){return"light"}};function i(e){let{children:s}=e,[t,i]=(0,r.useState)("light"),[c,d]=(0,r.useState)("#D97706"),[o,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{m(!0);let e=n();i(e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark");let s=localStorage.getItem("windowworks-accent-color");s&&d(s)},[]);let x=(0,r.useCallback)(e=>{i(e);try{localStorage.setItem("windowworks-theme",e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}catch(e){console.error("Failed to set theme in localStorage",e)}},[]),u=(0,r.useCallback)(e=>{d(e);try{localStorage.setItem("windowworks-accent-color",e);let s=document.documentElement,t="oklch(from ".concat(e," l c h)");s.style.setProperty("--primary",t),s.style.setProperty("--color-brand-primary",e)}catch(e){console.error("Failed to set accent color in localStorage",e)}},[]);return(0,a.jsx)(l.Provider,{value:{theme:t,setTheme:x,accentColor:c,setAccentColor:u,mounted:o},children:s})}function c(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},3972:(e,s,t)=>{"use strict";t.d(s,{default:()=>H});var a=t(5155),r=t(2115),l=t(6408),n=t(6932),i=t(1788),c=t(4616),d=t(9074),o=t(2713),m=t(1243),x=t(6561),u=t(4186),h=t(7924),p=t(4516),g=t(285),j=t(2523),f=t(6695),b=t(6126),N=t(1394),v=t(2355),y=t(3052);function w(e){let{selectedDate:s,events:t,onDateSelect:r,onEventClick:n,onNavigate:i,onNewAppointment:d}=e,o=new Date,m=s.getMonth(),x=s.getFullYear(),u=new Date(x,m,1),h=new Date(x,m+1,0),p=u.getDay(),j=h.getDate(),f=[],b=new Date(x,m-1,0);for(let e=p-1;e>=0;e--)f.push({date:b.getDate()-e,isCurrentMonth:!1,isToday:!1,fullDate:new Date(x,m-1,b.getDate()-e)});for(let e=1;e<=j;e++){let s=new Date(x,m,e);f.push({date:e,isCurrentMonth:!0,isToday:s.toDateString()===o.toDateString(),fullDate:s})}let N=42-f.length;for(let e=1;e<=N;e++)f.push({date:e,isCurrentMonth:!1,isToday:!1,fullDate:new Date(x,m+1,e)});let w=e=>{let s=e.toISOString().split("T")[0];return t.filter(e=>e.date===s)},k=e=>{switch(e){case"pending":return"bg-amber-500";case"assigned":return"bg-blue-500";case"in-progress":return"bg-orange-500";case"completed":return"bg-green-500";case"conflict":return"bg-red-500";default:return"bg-gray-400"}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-slate-700",children:[["January","February","March","April","May","June","July","August","September","October","November","December"][m]," ",x]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.$,{variant:"outline",size:"sm",onClick:()=>i("prev"),className:"h-8 w-8 p-0",children:(0,a.jsx)(v.A,{className:"h-4 w-4"})}),(0,a.jsx)(g.$,{variant:"outline",size:"sm",onClick:()=>i("next"),className:"h-8 w-8 p-0",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("div",{className:"grid grid-cols-7 mb-2",children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>(0,a.jsx)("div",{className:"text-center text-sm font-medium text-slate-600 py-2",children:e},e))}),(0,a.jsx)("div",{className:"grid grid-cols-7 gap-1",children:f.map((e,t)=>{let i=w(e.fullDate),o=s.toDateString()===e.fullDate.toDateString();return(0,a.jsxs)(l.P.div,{className:"\n                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer\n                  transition-all duration-200 hover:bg-gray-50 group\n                  ".concat(!e.isCurrentMonth?"opacity-40":"","\n                  ").concat(e.isToday?"ring-2 ring-amber-400 bg-amber-50":"","\n                  ").concat(o?"ring-2 ring-blue-400 bg-blue-50":"","\n                "),whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>r(e.fullDate),children:[(0,a.jsx)("div",{className:"\n                  text-sm font-medium mb-1\n                  ".concat(e.isToday?"text-amber-700":e.isCurrentMonth?"text-slate-700":"text-slate-400","\n                "),children:e.date}),(0,a.jsxs)("div",{className:"space-y-1",children:[i.slice(0,3).map(e=>(0,a.jsxs)(l.P.div,{className:"\n                        text-xs p-1 rounded text-white truncate cursor-pointer\n                        ".concat(k(e.status),"\n                      "),whileHover:{scale:1.05},onClick:s=>{s.stopPropagation(),n(e)},title:"".concat(e.time," - ").concat(e.title),children:[e.time," ",e.title]},e.id)),i.length>3&&(0,a.jsxs)("div",{className:"text-xs text-slate-500 font-medium",children:["+",i.length-3," more"]})]}),(0,a.jsx)(l.P.button,{className:"absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200",initial:!1,animate:{opacity:0},whileHover:{opacity:1},onClick:s=>{s.stopPropagation(),d(e.fullDate)},title:"Add appointment",children:(0,a.jsx)(c.A,{className:"h-3 w-3"})})]},t)})})]}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-4 p-4 border-t border-gray-200 bg-gray-50",children:[{status:"pending",label:"Pending"},{status:"assigned",label:"Assigned"},{status:"in-progress",label:"In Progress"},{status:"completed",label:"Completed"},{status:"conflict",label:"Conflict"}].map(e=>{let{status:s,label:t}=e;return(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded ".concat(k(s))}),(0,a.jsx)("span",{className:"text-xs text-slate-600",children:t})]},s)})})]})}var k=t(1007);function S(e){let{date:s,events:t,onEventClick:r,onTimeSlotClick:n}=e,i=[];for(let e=8;e<=18;e++)i.push({time:"".concat(e.toString().padStart(2,"0"),":00"),hour:e});let d=e=>{let[s,t]=e.startTime.split(":").map(Number),a=((s-8)*60+t)/600*100,r=e.duration/600*100;return{top:"".concat(Math.max(0,a),"%"),height:"".concat(Math.min(r,100-a),"%")}},o=e=>{switch(e){case"pending":return"bg-amber-500 border-amber-600";case"assigned":return"bg-blue-500 border-blue-600";case"in-progress":return"bg-orange-500 border-orange-600";case"completed":return"bg-green-500 border-green-600";case"conflict":return"bg-red-500 border-red-600 animate-pulse";default:return"bg-gray-400 border-gray-500"}},m=e=>{let[s,t]=e.split(":").map(Number);return"".concat(s%12||12,":").concat(t.toString().padStart(2,"0")," ").concat(s>=12?"PM":"AM")},x=(()=>{let e=new Date;if(e.toDateString()!==s.toDateString())return null;let t=e.getHours(),a=e.getMinutes();return t<8||t>18?null:Math.min(Math.max(((t-8)*60+a)/10/60*100,0),100)})();return(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-slate-700",children:s.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})}),(0,a.jsxs)("p",{className:"text-sm text-slate-500",children:[t.length," ",1===t.length?"appointment":"appointments"," scheduled"]})]}),(0,a.jsxs)(g.$,{onClick:()=>n("09:00"),className:"bg-amber-500 hover:bg-amber-600 text-white",size:"sm",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Add Appointment"]})]}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-20 flex-shrink-0",children:i.map(e=>{let{time:s}=e;return(0,a.jsx)("div",{className:"h-16 flex items-start pt-2",children:(0,a.jsx)("span",{className:"text-xs text-slate-500 font-medium",children:m(s)})},s)})}),(0,a.jsxs)("div",{className:"flex-1 relative border-l border-gray-200",children:[i.map(e=>{let{time:s}=e;return(0,a.jsx)(l.P.div,{className:"h-16 border-b border-gray-100 hover:bg-gray-50 cursor-pointer group relative",whileHover:{backgroundColor:"#f9fafb"},onClick:()=>n(s),children:(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsxs)(g.$,{variant:"outline",size:"sm",className:"text-xs h-6 px-2 border-amber-200 text-amber-600 hover:bg-amber-50",children:[(0,a.jsx)(c.A,{className:"h-3 w-3 mr-1"}),"Add"]})})},s)}),t.map(e=>{let s=d(e);return(0,a.jsx)(l.P.div,{className:"\n                    absolute left-2 right-2 rounded-md border-2 cursor-pointer\n                    ".concat(o(e.status)," text-white p-2 shadow-sm\n                    overflow-hidden z-10\n                  "),style:s,whileHover:{scale:1.02,zIndex:20},whileTap:{scale:.98},onClick:()=>r(e),initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.3},children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"font-medium text-sm truncate",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs opacity-90",children:[(0,a.jsx)(u.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[m(e.startTime)," (",e.duration,"m)"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs opacity-90",children:[(0,a.jsx)(k.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"truncate",children:e.customer})]}),e.installer&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsxs)(N.eu,{className:"h-5 w-5",children:[(0,a.jsx)(N.BK,{src:"/avatars/".concat(e.installer.toLowerCase().replace(" ","-"),".jpg")}),(0,a.jsx)(N.q5,{className:"bg-white text-slate-700 text-xs",children:e.installer.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsx)("span",{className:"text-xs truncate",children:e.installer})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs opacity-90",children:[(0,a.jsx)(p.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"truncate",children:e.address})]})]})},e.id)}),null!==x&&(0,a.jsxs)(l.P.div,{className:"absolute left-0 right-0 z-30 flex items-center",style:{top:"".concat(x,"%")},initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full border-2 border-white shadow-md"}),(0,a.jsx)("div",{className:"flex-1 h-0.5 bg-red-500"}),(0,a.jsx)("div",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-md ml-2 shadow-md",children:"Now"})]})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-slate-600",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("span",{className:"font-medium",children:t.length})," appointments"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("span",{className:"font-medium",children:t.reduce((e,s)=>e+s.duration,0)})," minutes total"]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:[{status:"pending",count:t.filter(e=>"pending"===e.status).length},{status:"assigned",count:t.filter(e=>"assigned"===e.status).length},{status:"in-progress",count:t.filter(e=>"in-progress"===e.status).length},{status:"completed",count:t.filter(e=>"completed"===e.status).length},{status:"conflict",count:t.filter(e=>"conflict"===e.status).length}].filter(e=>{let{count:s}=e;return s>0}).map(e=>{let{status:s,count:t}=e;return(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(o(s).split(" ")[0])}),(0,a.jsx)("span",{className:"text-xs",children:t})]},s)})})]})]})}function C(e){let{date:s,events:t,onEventClick:r,onTimeSlotClick:n,onNavigate:i,onNewAppointment:d}=e,o=(e,s)=>{let[t,a]=e.split(":").map(Number);return{top:"".concat(((t-8)*60+a)/600*100,"%"),height:"".concat(Math.max(s/600*100,8),"%")}},m=e=>{switch(e){case"assigned":return"bg-blue-500";case"pending":return"bg-amber-500";case"in-progress":return"bg-orange-500";case"completed":return"bg-green-500";default:return"bg-gray-500"}},x=e=>e.toISOString().split("T")[0]===new Date().toISOString().split("T")[0]?t:[],u=(e=>{let s=new Date(e),t=s.getDay(),a=s.getDate()-t;return new Date(s.setDate(a))})(s),h=(e=>{let s=[];for(let t=0;t<7;t++){let a=new Date(e);a.setDate(e.getDate()+t),s.push(a)}return s})(u),p=(()=>{let e=[];for(let s=8;s<=18;s++)e.push("".concat(s.toString().padStart(2,"0"),":00")),s<18&&e.push("".concat(s.toString().padStart(2,"0"),":30"));return e})(),j=new Date;return(0,a.jsx)(f.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsxs)(f.Wu,{className:"p-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsx)(g.$,{variant:"outline",size:"sm",onClick:()=>i("prev"),children:(0,a.jsx)(v.A,{className:"h-4 w-4"})}),(0,a.jsxs)("h3",{className:"font-semibold text-slate-700",children:[u.toLocaleDateString("en-US",{month:"long",day:"numeric"})," - "," ",h[6].toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]}),(0,a.jsx)(g.$,{variant:"outline",size:"sm",onClick:()=>i("next"),children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-8 min-h-[600px]",children:[(0,a.jsxs)("div",{className:"border-r border-gray-200 bg-gray-50",children:[(0,a.jsx)("div",{className:"h-16 border-b border-gray-200"})," ",p.map(e=>(0,a.jsx)("div",{className:"h-12 px-2 py-1 text-xs text-slate-500 border-b border-gray-100 flex items-center justify-end",children:e},e))]}),h.map(e=>{let s=x(e),t=e.toDateString()===j.toDateString(),i=0===e.getDay()||6===e.getDay();return(0,a.jsxs)("div",{className:"border-r border-gray-200 relative ".concat(i?"bg-gray-50":"bg-white"),children:[(0,a.jsxs)("div",{className:"h-16 p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors ".concat(t?"bg-amber-50":""),onClick:()=>d(e),children:[(0,a.jsx)("div",{className:"text-xs text-slate-500 uppercase font-medium",children:e.toLocaleDateString("en-US",{weekday:"short"})}),(0,a.jsx)("div",{className:"text-lg font-semibold mt-1 ".concat(t?"text-amber-600":"text-slate-700"),children:e.getDate()}),s.length>0&&(0,a.jsxs)("div",{className:"text-xs text-slate-500 mt-1",children:[s.length," appointment",s.length>1?"s":""]})]}),(0,a.jsxs)("div",{className:"relative",children:[p.map(s=>(0,a.jsx)("div",{className:"h-12 border-b border-gray-100 hover:bg-amber-50 cursor-pointer transition-colors group",onClick:()=>n(e,s),children:(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity p-1",children:(0,a.jsx)(c.A,{className:"h-3 w-3 text-amber-500"})})},s)),s.map(e=>{let s=o(e.startTime,e.duration);return(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.2},className:"absolute left-1 right-1 rounded-lg p-2 cursor-pointer hover:shadow-md transition-shadow z-10 ".concat(m(e.status)," text-white text-xs"),style:s,onClick:()=>r(e),children:[(0,a.jsx)("div",{className:"font-medium truncate",children:e.title}),(0,a.jsx)("div",{className:"opacity-90 truncate",children:e.customer}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(N.eu,{className:"h-4 w-4 mr-1",children:(0,a.jsx)(N.q5,{className:"text-[10px] bg-white/20",children:e.installer.split(" ").map(e=>e[0]).join("")})}),(0,a.jsx)("span",{className:"text-[10px] opacity-90 truncate",children:e.installer})]})]},e.id)})]})]},e.toISOString())})]})]})})}var D=t(7580),A=t(3861),T=t(492),z=t(8136),I=t(1539),M=t(7434),P=t(4229),R=t(5057),J=t(8539),E=t(333),W=t(7313),L=t(9409),Z=t(4165);function q(e){let{isOpen:s,onClose:t,onSave:n,selectedDate:i,selectedTime:c,projects:o=[],installers:m=[],customers:x=[]}=e,[u,h]=(0,r.useState)("details"),[v,y]=(0,r.useState)(!1),[w,S]=(0,r.useState)({projectId:"",customerId:"",date:(null==i?void 0:i.toISOString().split("T")[0])||"",startTime:c||"09:00",endTime:"12:00",duration:180,installerId:"",location:"",productTypes:[],notes:"",estimatedDuration:180,sendNotification:!0,autoReminder:!0,smsReminder:!1,emailReminder:!0}),[C,q]=(0,r.useState)({}),U=()=>{let e={};return w.projectId||(e.projectId="Project is required"),w.date||(e.date="Date is required"),w.startTime||(e.startTime="Start time is required"),w.endTime||(e.endTime="End time is required"),w.installerId||(e.installerId="Installer is required"),q(e),0===Object.keys(e).length},$=async()=>{y(!0),await new Promise(e=>setTimeout(e,2e3)),S(e=>{var s;return{...e,startTime:"14:00",endTime:"16:30",installerId:(null==(s=m.find(e=>"available"===e.availability))?void 0:s.id)||e.installerId}}),y(!1)},O=o.find(e=>e.id===w.projectId),B=m.find(e=>e.id===w.installerId),F=e=>{switch(e){case"available":return"bg-green-500";case"busy":return"bg-amber-500";case"unavailable":return"bg-red-500";default:return"bg-gray-500"}};return(0,a.jsx)(Z.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(Z.Cf,{className:"max-w-4xl max-h-[90vh] overflow-hidden p-0",children:[(0,a.jsx)(Z.c7,{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)(Z.L3,{className:"text-xl font-bold text-slate-700",children:"Schedule New Appointment"})}),(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsxs)(W.tU,{value:u,onValueChange:h,className:"w-full",children:[(0,a.jsxs)(W.j7,{className:"grid grid-cols-4 w-full px-6 py-2",children:[(0,a.jsxs)(W.Xi,{value:"details",className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Details"})]}),(0,a.jsxs)(W.Xi,{value:"workflow",className:"flex items-center space-x-2",children:[(0,a.jsx)(D.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Team"})]}),(0,a.jsxs)(W.Xi,{value:"notifications",className:"flex items-center space-x-2",children:[(0,a.jsx)(A.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Notifications"})]}),(0,a.jsxs)(W.Xi,{value:"attachments",className:"flex items-center space-x-2",children:[(0,a.jsx)(T.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Files"})]})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)(W.av,{value:"details",className:"space-y-6 mt-0",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Project *"}),(0,a.jsxs)(L.l6,{value:w.projectId,onValueChange:e=>{let s=o.find(s=>s.id===e);S(t=>({...t,projectId:e,location:(null==s?void 0:s.address)||""}))},children:[(0,a.jsx)(L.bq,{className:C.projectId?"border-red-500":"",children:(0,a.jsx)(L.yv,{placeholder:"Select project..."})}),(0,a.jsx)(L.gC,{children:o.map(e=>(0,a.jsx)(L.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.customer," • ",e.address]})]})},e.id))})]}),C.projectId&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:C.projectId}),O&&(0,a.jsx)(f.Zp,{className:"mt-3",children:(0,a.jsx)(f.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:O.customer})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:O.address})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(z.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:O.productTypes.map(e=>(0,a.jsx)(b.E,{variant:"secondary",className:"text-xs",children:e},e))})]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Date *"}),(0,a.jsx)(j.p,{type:"date",value:w.date,onChange:e=>S(s=>({...s,date:e.target.value})),className:C.date?"border-red-500":""}),C.date&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:C.date})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Time Range *"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)(j.p,{type:"time",value:w.startTime,onChange:e=>S(s=>({...s,startTime:e.target.value})),className:C.startTime?"border-red-500":""}),(0,a.jsx)(j.p,{type:"time",value:w.endTime,onChange:e=>S(s=>({...s,endTime:e.target.value})),className:C.endTime?"border-red-500":""})]}),(C.startTime||C.endTime)&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:"Start and end times are required"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(R.J,{className:"text-sm font-medium",children:["Estimated Duration: ",w.estimatedDuration," minutes"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(g.$,{type:"button",variant:"outline",size:"sm",onClick:()=>S(e=>({...e,estimatedDuration:Math.max(30,(e.estimatedDuration||180)-15)})),children:"-15m"}),(0,a.jsx)(j.p,{type:"number",min:"30",max:"480",step:"15",value:w.estimatedDuration,onChange:e=>S(s=>({...s,estimatedDuration:parseInt(e.target.value)||180})),className:"text-center"}),(0,a.jsx)(g.$,{type:"button",variant:"outline",size:"sm",onClick:()=>S(e=>({...e,estimatedDuration:Math.min(480,(e.estimatedDuration||180)+15)})),children:"+15m"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,a.jsx)("span",{children:"30 min minimum"}),(0,a.jsx)("span",{children:"8 hours maximum"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Product Types"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:["Windows","Blinds","Shutters","Curtains","Shades","Awnings","Security Screens","Repair","Maintenance"].map(e=>{var s;return(0,a.jsx)(g.$,{variant:(null==(s=w.productTypes)?void 0:s.includes(e))?"default":"outline",size:"sm",onClick:()=>{let s=w.productTypes||[],t=s.includes(e)?s.filter(s=>s!==e):[...s,e];S(e=>({...e,productTypes:t}))},children:e},e)})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Location"}),(0,a.jsx)(j.p,{placeholder:"Enter appointment location...",value:w.location,onChange:e=>S(s=>({...s,location:e.target.value}))})]}),(0,a.jsx)(f.Zp,{className:"bg-amber-50 border-amber-200",children:(0,a.jsx)(f.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-amber-100 rounded-full",children:(0,a.jsx)(I.A,{className:"h-4 w-4 text-amber-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-700",children:"AI Schedule Optimization"}),(0,a.jsx)("p",{className:"text-xs text-slate-600",children:"Find the optimal time slot based on installer availability and location"})]})]}),(0,a.jsx)(g.$,{variant:"outline",size:"sm",onClick:$,disabled:v,className:"bg-amber-500 text-white hover:bg-amber-600 border-amber-500",children:v?(0,a.jsx)(l.P.div,{className:"h-3 w-3 border-2 border-white border-t-transparent rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(I.A,{className:"h-3 w-3 mr-1"}),"Optimize"]})})]})})})]}),(0,a.jsxs)(W.av,{value:"workflow",className:"space-y-6 mt-0",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Assign Installer *"}),(0,a.jsxs)(L.l6,{value:w.installerId,onValueChange:e=>S(s=>({...s,installerId:e})),children:[(0,a.jsx)(L.bq,{className:C.installerId?"border-red-500":"",children:(0,a.jsx)(L.yv,{placeholder:"Select an installer..."})}),(0,a.jsx)(L.gC,{children:m.map(e=>(0,a.jsx)(L.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(N.eu,{className:"h-6 w-6",children:[(0,a.jsx)(N.BK,{src:e.avatar||void 0}),(0,a.jsx)(N.q5,{className:"text-xs",children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(F(e.availability))}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.utilization,"%"]})]})]})},e.id))})]}),C.installerId&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:C.installerId}),B&&(0,a.jsx)(f.Zp,{className:"mt-3",children:(0,a.jsx)(f.Wu,{className:"pt-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(N.eu,{className:"h-8 w-8",children:[(0,a.jsx)(N.BK,{src:B.avatar||void 0}),(0,a.jsx)(N.q5,{children:B.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:B.name}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground capitalize",children:[B.availability," • ",B.utilization,"% utilized"]})]})]}),(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(F(B.availability))})]})})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Customer"}),(0,a.jsxs)(L.l6,{value:w.customerId,onValueChange:e=>S(s=>({...s,customerId:e})),children:[(0,a.jsx)(L.bq,{children:(0,a.jsx)(L.yv,{placeholder:"Select customer..."})}),(0,a.jsx)(L.gC,{children:x.map(e=>(0,a.jsx)(L.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.email," • ",e.phone]})]})},e.id))})]})]})]}),(0,a.jsx)(W.av,{value:"notifications",className:"space-y-6 mt-0",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Send Initial Notification"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Notify customer and installer about this appointment"})]}),(0,a.jsx)(E.d,{checked:w.sendNotification,onCheckedChange:e=>S(s=>({...s,sendNotification:e}))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Auto Reminder"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Send automatic reminder 24 hours before appointment"})]}),(0,a.jsx)(E.d,{checked:w.autoReminder,onCheckedChange:e=>S(s=>({...s,autoReminder:e}))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"SMS Notifications"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Send SMS updates to customer and installer"})]}),(0,a.jsx)(E.d,{checked:w.smsReminder,onCheckedChange:e=>S(s=>({...s,smsReminder:e}))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Send email confirmations and updates"})]}),(0,a.jsx)(E.d,{checked:w.emailReminder,onCheckedChange:e=>S(s=>({...s,emailReminder:e}))})]})]})}),(0,a.jsx)(W.av,{value:"attachments",className:"space-y-6 mt-0",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"Appointment Notes"}),(0,a.jsx)(J.T,{placeholder:"Add any special instructions, notes, or requirements for this appointment...",value:w.notes,onChange:e=>S(s=>({...s,notes:e.target.value})),rows:4})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(R.J,{className:"text-sm font-medium",children:"File Attachments"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-amber-400 transition-colors",children:[(0,a.jsx)(M.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Drag and drop files here, or click to browse"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Support for images, PDFs, and documents up to 10MB"}),(0,a.jsx)(g.$,{variant:"outline",className:"mt-4",size:"sm",children:"Choose Files"})]})]})]})})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-gray-50",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"* Required fields"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(g.$,{variant:"outline",onClick:t,children:"Cancel"}),(0,a.jsxs)(g.$,{onClick:()=>{if(!U())return void h("details");n({...w,id:"appt_".concat(Date.now())}),t()},className:"bg-amber-500 hover:bg-amber-600 text-white",children:[(0,a.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Schedule Appointment"]})]})]})]})})}var U=t(2905);let $=[{id:"project-1",name:"Kitchen Window Installation",customer:"Sarah Johnson",address:"123 Oak Street, Portland, OR",productTypes:["Windows","Blinds"],status:"active"},{id:"project-2",name:"Bathroom Renovation",customer:"Mike Chen",address:"456 Pine Avenue, Seattle, WA",productTypes:["Shutters","Blinds"],status:"active"},{id:"project-3",name:"Living Room Makeover",customer:"Emily Davis",address:"789 Maple Drive, Vancouver, WA",productTypes:["Curtains","Shades"],status:"active"}],O=[{id:"installer-1",name:"Alex Thompson",avatar:null,availability:"available",utilization:65},{id:"installer-2",name:"Jordan Martinez",avatar:null,availability:"busy",utilization:85},{id:"installer-3",name:"Sam Wilson",avatar:null,availability:"available",utilization:45}],B=[{id:"customer-1",name:"Sarah Johnson",email:"<EMAIL>",phone:"(*************",address:"123 Oak Street, Portland, OR"},{id:"customer-2",name:"Mike Chen",email:"<EMAIL>",phone:"(*************",address:"456 Pine Avenue, Seattle, WA"},{id:"customer-3",name:"Emily Davis",email:"<EMAIL>",phone:"(*************",address:"789 Maple Drive, Vancouver, WA"}],F=new Date("2025-07-12"),_=[{id:"1",title:"Kitchen Window Installation",date:"2025-07-12",time:"09:00",status:"assigned",customer:"Sarah Johnson",installer:"Alex Thompson"},{id:"2",title:"Bathroom Consultation",date:"2025-07-12",time:"14:00",status:"pending",customer:"Mike Chen",installer:"Jordan Martinez"},{id:"3",title:"Living Room Curtains",date:"2025-07-13",time:"10:00",status:"in-progress",customer:"Emily Davis",installer:"Sam Wilson"},{id:"4",title:"Window Maintenance",date:"2025-07-14",time:"15:00",status:"completed",customer:"Sarah Johnson",installer:"Alex Thompson"}],V=[{id:"1",title:"Kitchen Window Installation",startTime:"09:00",duration:180,customer:"Sarah Johnson",address:"123 Oak Street",installer:"Alex Thompson",status:"assigned"},{id:"2",title:"Bathroom Consultation",startTime:"14:00",duration:120,customer:"Mike Chen",address:"456 Pine Avenue",installer:"Jordan Martinez",status:"pending"}];function H(){let{accentColor:e}=(0,U.D)(),[s,t]=(0,r.useState)("month"),[v,y]=(0,r.useState)(F),[k,D]=(0,r.useState)(!1),[A,T]=(0,r.useState)(""),z=e=>{e&&y(e),D(!0)},I=e=>{let t=new Date(v);"month"===s?t.setMonth(t.getMonth()+("next"===e?1:-1)):"week"===s?t.setDate(t.getDate()+("next"===e?7:-7)):t.setDate(t.getDate()+("next"===e?1:-1)),y(t)},M=_.filter(e=>e.date===F.toISOString().split("T")[0]),P=_.filter(e=>new Date(e.date)>F).slice(0,5);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-slate-700",children:"Schedule"}),(0,a.jsx)("p",{className:"text-slate-600 mt-1",children:"Manage installer appointments and project timelines"}),(0,a.jsx)("p",{className:"text-sm font-medium mt-1",style:{color:e},children:F.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Filter"]}),(0,a.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,a.jsxs)(g.$,{onClick:()=>z(),className:"text-white",style:{backgroundColor:e,borderColor:e},children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"New Appointment"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)(f.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(f.ZB,{className:"text-sm font-medium text-slate-600",children:"Upcoming Appointments"}),(0,a.jsx)(d.A,{className:"h-4 w-4",style:{color:e}})]}),(0,a.jsxs)(f.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-700",children:"15"}),(0,a.jsx)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:"+10% from last week"})]})]}),(0,a.jsxs)(f.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(f.ZB,{className:"text-sm font-medium text-slate-600",children:"Installer Utilization"}),(0,a.jsx)(o.A,{className:"h-4 w-4",style:{color:e}})]}),(0,a.jsxs)(f.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-700",children:"75%"}),(0,a.jsx)("p",{className:"text-xs text-green-600 flex items-center mt-1",children:"+5% from last month"})]})]}),(0,a.jsxs)(f.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(f.ZB,{className:"text-sm font-medium text-slate-600",children:"Conflicts"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-red-500"})]}),(0,a.jsxs)(f.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-700",children:"2"}),(0,a.jsx)("p",{className:"text-xs text-red-600 flex items-center mt-1",children:"Requires attention"})]})]}),(0,a.jsxs)(f.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsxs)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(f.ZB,{className:"text-sm font-medium text-slate-600",children:"Avg Job Duration"}),(0,a.jsx)(x.A,{className:"h-4 w-4",style:{color:e}})]}),(0,a.jsxs)(f.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-700",children:"4h"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 flex items-center mt-1",children:"Per appointment"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"xl:col-span-3 space-y-6",children:[(0,a.jsx)(f.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,a.jsx)(f.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(g.$,{variant:"month"===s?"default":"outline",size:"sm",onClick:()=>t("month"),className:"month"===s?"text-white":"",style:"month"===s?{backgroundColor:e,borderColor:e}:{},children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Month"]}),(0,a.jsxs)(g.$,{variant:"week"===s?"default":"outline",size:"sm",onClick:()=>t("week"),className:"week"===s?"text-white":"",style:"week"===s?{backgroundColor:e,borderColor:e}:{},children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Week"]}),(0,a.jsxs)(g.$,{variant:"day"===s?"default":"outline",size:"sm",onClick:()=>t("day"),className:"day"===s?"text-white":"",style:"day"===s?{backgroundColor:e,borderColor:e}:{},children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Day"]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("h2",{className:"font-semibold text-slate-700",children:"month"===s?v.toLocaleDateString("en-US",{month:"long",year:"numeric"}):"week"===s?(()=>{let e=new Date(v),s=e.getDay(),t=e.getDate()-s;e.setDate(t);let a=new Date(e);return a.setDate(e.getDate()+6),"".concat(e.toLocaleDateString("en-US",{month:"short",day:"numeric"})," - ").concat(a.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}))})():v.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})})}),(0,a.jsx)(g.$,{variant:"outline",size:"sm",onClick:()=>y(F),children:"Today"})]})})}),(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:"month"===s?(0,a.jsx)(w,{selectedDate:v,events:_,onDateSelect:e=>{y(e),t("day")},onEventClick:e=>console.log("Event clicked:",e),onNavigate:I,onNewAppointment:z}):"week"===s?(0,a.jsx)(C,{date:v,events:V,onEventClick:e=>console.log("Event clicked:",e),onTimeSlotClick:e=>z(e),onNavigate:I,onNewAppointment:z}):(0,a.jsx)(S,{date:v,events:v.toDateString()===F.toDateString()?V:[],onEventClick:e=>console.log("Event clicked:",e),onTimeSlotClick:()=>z(v)})},s)]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(f.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(f.aR,{className:"pb-3",children:(0,a.jsx)(f.ZB,{className:"text-lg text-slate-700",children:"Quick Search"})}),(0,a.jsx)(f.Wu,{className:"space-y-3",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)(j.p,{placeholder:"Search appointments...",value:A,onChange:e=>T(e.target.value),className:"pl-10 border-gray-200"})]})})]}),(0,a.jsxs)(f.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(f.aR,{className:"pb-3",children:(0,a.jsx)(f.ZB,{className:"text-lg text-slate-700",children:"Today's Schedule"})}),(0,a.jsx)(f.Wu,{className:"space-y-3",children:M.length>0?M.map(s=>(0,a.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:s.title}),(0,a.jsx)(b.E,{variant:"completed"===s.status?"default":"secondary",className:"assigned"===s.status?"bg-blue-100 text-blue-700":"pending"===s.status?"bg-opacity-20 text-slate-700":"in-progress"===s.status?"bg-orange-100 text-orange-700":"completed"===s.status?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700",style:"pending"===s.status?{backgroundColor:"".concat(e,"33"),color:e}:{},children:s.status})]}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-slate-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-3 w-3 mr-1"}),s.time]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-3 w-3 mr-1"}),s.customer]})]}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(N.eu,{className:"h-5 w-5 mr-2",children:(0,a.jsx)(N.q5,{className:"text-xs bg-opacity-20 text-slate-700",style:{backgroundColor:"".concat(e,"33"),color:e},children:s.installer.split(" ").map(e=>e[0]).join("")})}),(0,a.jsx)("span",{className:"text-xs text-slate-600",children:s.installer})]})]},s.id)):(0,a.jsx)("p",{className:"text-sm text-slate-500",children:"No appointments today"})})]}),(0,a.jsxs)(f.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,a.jsx)(f.aR,{className:"pb-3",children:(0,a.jsx)(f.ZB,{className:"text-lg text-slate-700",children:"Upcoming"})}),(0,a.jsx)(f.Wu,{className:"space-y-3",children:P.map(e=>(0,a.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-slate-700",children:e.title}),(0,a.jsx)(b.E,{variant:"secondary",className:"bg-gray-100 text-gray-700",children:e.status})]}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-slate-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"h-3 w-3 mr-1"}),new Date(e.date).toLocaleDateString()]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-3 w-3 mr-1"}),e.time]})]})]},e.id))})]})]})]}),(0,a.jsx)(q,{isOpen:k,onClose:()=>D(!1),onSave:e=>{console.log("Saving appointment:",e),D(!1)},selectedDate:v,projects:$,installers:O,customers:B})]})}},4165:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>o,L3:()=>x,c7:()=>m,lG:()=>i});var a=t(5155);t(2115);var r=t(5452),l=t(4416),n=t(9434);function i(e){let{...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"dialog",...s})}function c(e){let{...s}=e;return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...s})}function d(e){let{className:s,...t}=e;return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...t})}function o(e){let{className:s,children:t,showCloseButton:i=!0,...o}=e;return(0,a.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,a.jsx)(d,{}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...o,children:[t,i&&(0,a.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(l.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",s),...t})}function x(e){let{className:s,...t}=e;return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",s),...t})}},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>n});var a=t(5155);t(2115);var r=t(968),l=t(9434);function n(e){let{className:s,...t}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}},7313:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>c,av:()=>d,j7:()=>i,tU:()=>n});var a=t(5155);t(2115);var r=t(704),l=t(9434);function n(e){let{className:s,...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",s),...t})}function i(e){let{className:s,...t}=e;return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",s),...t})}},7785:(e,s,t)=>{Promise.resolve().then(t.bind(t,871)),Promise.resolve().then(t.bind(t,3972))},8539:(e,s,t)=>{"use strict";t.d(s,{T:()=>n});var a=t(5155),r=t(2115),l=t(9434);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...r})});n.displayName="Textarea"},9409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>x,eb:()=>g,gC:()=>p,l6:()=>o,yv:()=>m});var a=t(5155),r=t(2115),l=t(8715),n=t(6474),i=t(7863),c=t(5196),d=t(9434);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.l9,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.l9.displayName;let u=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=l.PP.displayName;let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,position:n="popper",...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:s,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,a.jsx)(u,{}),(0,a.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=l.JU.displayName;let g=r.forwardRef((e,s)=>{let{className:t,children:r,...n}=e;return(0,a.jsxs)(l.q7,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});g.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=l.wv.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[418,0,376,205,441,684,358],()=>s(7785)),_N_E=e.O()}]);