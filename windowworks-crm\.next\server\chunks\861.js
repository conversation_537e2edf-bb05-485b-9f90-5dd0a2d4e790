exports.id=861,exports.ids=[861],exports.modules={43:(e,t,r)=>{"use strict";r.d(t,{jH:()=>o});var n=r(3210);r(687);var i=n.createContext(void 0);function o(e){let t=n.useContext(i);return e||t||"ltr"}},83:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let n=r(740)._(r(6715)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:r}=e,o=e.protocol||"",a=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return o(e)}},228:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},569:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{m:()=>n})},593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return f},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return a},navigate:function(){return i},prefetch:function(){return n},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return o},schedulePrefetchTask:function(){return s}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,i=r,o=r,a=r,s=r,l=r,u=r,c=r;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],o=Array.isArray(t),a=o?t[1]:t;!a||a.startsWith(i.PAGE_SEGMENT_KEY)||(o&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):o&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(2859),i=r(3913),o=r(4077),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===i.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(i.PAGE_SEGMENT_KEY))return"";let o=[s(r)],a=null!=(t=e[1])?t:{},c=a.children?u(a.children):void 0;if(void 0!==c)o.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=u(t);void 0!==r&&o.push(r)}return l(o)}function c(e,t){let r=function e(t,r){let[i,a]=t,[l,c]=r,d=s(i),f=s(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,o.matchSegment)(i,l)){var h;return null!=(h=u(r))?h:""}for(let t in a)if(c[t]){let r=e(a[t],c[t]);if(null!==r)return s(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},1096:(e,t,r)=>{"use strict";r.d(t,{H4:()=>R,_V:()=>P,bL:()=>E});var n=r(3210),i=r(1273),o=r(3495),a=r(6156),s=r(4163),l=r(7379);function u(){return()=>{}}var c=r(687),d="Avatar",[f,h]=(0,i.A)(d),[p,m]=f(d),g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...i}=e,[o,a]=n.useState("idle");return(0,c.jsx)(p,{scope:r,imageLoadingStatus:o,onImageLoadingStatusChange:a,children:(0,c.jsx)(s.sG.span,{...i,ref:t})})});g.displayName=d;var y="AvatarImage",v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:i,onLoadingStatusChange:d=()=>{},...f}=e,h=m(y,r),p=function(e,{referrerPolicy:t,crossOrigin:r}){let i=(0,l.useSyncExternalStore)(u,()=>!0,()=>!1),o=n.useRef(null),s=i?(o.current||(o.current=new window.Image),o.current):null,[c,d]=n.useState(()=>x(s,e));return(0,a.N)(()=>{d(x(s,e))},[s,e]),(0,a.N)(()=>{let e=e=>()=>{d(e)};if(!s)return;let n=e("loaded"),i=e("error");return s.addEventListener("load",n),s.addEventListener("error",i),t&&(s.referrerPolicy=t),"string"==typeof r&&(s.crossOrigin=r),()=>{s.removeEventListener("load",n),s.removeEventListener("error",i)}},[s,r,t]),c}(i,f),g=(0,o.c)(e=>{d(e),h.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==p&&g(p)},[p,g]),"loaded"===p?(0,c.jsx)(s.sG.img,{...f,ref:t,src:i}):null});v.displayName=y;var b="AvatarFallback",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:i,...o}=e,a=m(b,r),[l,u]=n.useState(void 0===i);return n.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>u(!0),i);return()=>window.clearTimeout(e)}},[i]),l&&"loaded"!==a.imageLoadingStatus?(0,c.jsx)(s.sG.span,{...o,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=b;var E=g,P=v,R=w},1273:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,q:()=>o});var n=r(3210),i=r(687);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,a=n.useMemo(()=>o,Object.values(o));return(0,i.jsx)(r.Provider,{value:a,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function a(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return o.scopeName=e,[function(t,o){let a=n.createContext(o),s=r.length;r=[...r,o];let l=t=>{let{scope:r,children:o,...l}=t,u=r?.[e]?.[s]||a,c=n.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(r,i){let l=i?.[e]?.[s]||a,u=n.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(o,...t)]}},1279:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});let n=(0,r(3210).createContext)(null)},1312:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},1355:(e,t,r)=>{"use strict";r.d(t,{qW:()=>f});var n,i=r(3210),o=r(569),a=r(4163),s=r(8599),l=r(3495),u=r(687),c="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:g,onInteractOutside:y,onDismiss:v,...b}=e,w=i.useContext(d),[x,E]=i.useState(null),P=x?.ownerDocument??globalThis?.document,[,R]=i.useState({}),T=(0,s.s)(t,e=>E(e)),S=Array.from(w.layers),[A]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),_=S.indexOf(A),M=x?S.indexOf(x):-1,O=w.layersWithOutsidePointerEventsDisabled.size>0,j=M>=_,k=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){p("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));j&&!r&&(m?.(e),y?.(e),e.defaultPrevented||v?.())},P),C=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(g?.(e),y?.(e),e.defaultPrevented||v?.())},P);return!function(e,t=globalThis?.document){let r=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{M===w.layers.size-1&&(f?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},P),i.useEffect(()=>{if(x)return r&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(n=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),h(),()=>{r&&1===w.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=n)}},[x,P,r,w]),i.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),h())},[x,w]),i.useEffect(()=>{let e=()=>R({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.sG.div,{...b,ref:T,style:{pointerEvents:O?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,C.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,k.onPointerDownCapture)})});function h(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,r,{discrete:n}){let i=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,a.hO)(i,o):i.dispatchEvent(o)}f.displayName="DismissableLayer",i.forwardRef((e,t)=>{let r=i.useContext(d),n=i.useRef(null),o=(0,s.s)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(a.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},1359:(e,t,r)=>{"use strict";r.d(t,{Oh:()=>o});var n=r(3210),i=0;function o(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return o}});let n=r(4722),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function a(e){let t,r,o;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,o]=e.split(r,2);break}if(!t||!r||!o)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":o="/"===t?"/"+o:t+"/"+o;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});o=a.slice(0,-2).concat(o).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:o}}},1500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,o,a,s,l,u){if(0===Object.keys(a[1]).length){r.head=l;return}for(let c in a[1]){let d,f=a[1][c],h=f[0],p=(0,n.createRouterCacheKey)(h),m=null!==s&&void 0!==s[2][c]?s[2][c]:null;if(o){let n=o.parallelRoutes.get(c);if(n){let o,a=(null==u?void 0:u.kind)==="auto"&&u.status===i.PrefetchCacheEntryStatus.reusable,s=new Map(n),d=s.get(p);o=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:a&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},s.set(p,o),e(t,o,d,f,m||null,l,u),r.parallelRoutes.set(c,s);continue}}if(null!==m){let e=m[1],r=m[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=r.parallelRoutes.get(c);g?g.set(p,d):r.parallelRoutes.set(c,new Map([[p,d]])),e(t,d,void 0,f,m,l,u)}}}});let n=r(3123),i=r(9154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return f},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return h}});let n=r(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),o=r(6341),a=r(4396),s=r(660),l=r(4722),u=r(2958),c=r(5499);function d(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(r=(0,s.djb2Hash)(t).toString(36).slice(0,6)),r}function f(e,t,r){let n=(0,l.normalizeAppPath)(e),s=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),c=(0,o.interpolateDynamicPath)(n,t,s),{name:f,ext:h}=i.default.parse(r),p=d(i.default.posix.join(e,f)),m=p?`-${p}`:"";return(0,u.normalizePathSep)(i.default.join(c,`${f}${m}${h}`))}function h(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=d(e),!t.endsWith("/route")){let{dir:e,name:n,ext:o}=i.default.parse(t);t=i.default.posix.join(e,`${n}${r?`-${r}`:""}${o}`,"route")}return t}function p(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(r?"/route":"")}},1794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let n=r(9289),i=r(6736);function o(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,i.hasBasePath)(r.pathname)}catch(e){return!1}}},2026:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],i=r[0];if(Array.isArray(n)&&Array.isArray(i)){if(n[0]!==i[0]||n[2]!==i[2])return!0}else if(n!==i)return!0;if(t[4])return!r[4];if(r[4])return!0;let o=Object.values(t[1])[0],a=Object.values(r[1])[0];return!o||!a||e(o,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2157:(e,t,r)=>{"use strict";r.d(t,{L:()=>n});let n=(0,r(3210).createContext)({})},2192:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},2247:(e,t,r)=>{"use strict";r.d(t,{A:()=>K});var n,i,o=function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function a(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}Object.create;Object.create;var s=("function"==typeof SuppressedError&&SuppressedError,r(3210)),l="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,f=new WeakMap;function h(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,i,a=(t=null,void 0===r&&(r=h),n=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,i);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){i=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var o=function(){var r=t;t=[],r.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return a.options=o({async:!0,ssr:!1},e),a}(),m=function(){},g=s.forwardRef(function(e,t){var r,n,i,l,u=s.useRef(null),h=s.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),g=h[0],y=h[1],v=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,E=e.enabled,P=e.shards,R=e.sideCar,T=e.noRelative,S=e.noIsolation,A=e.inert,_=e.allowPinchZoom,M=e.as,O=e.gapMode,j=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(r=[u,t],n=function(e){return r.forEach(function(t){return c(t,e)})},(i=(0,s.useState)(function(){return{value:null,callback:n,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=n,l=i.facade,d(function(){var e=f.get(l);if(e){var t=new Set(e),n=new Set(r),i=l.current;t.forEach(function(e){n.has(e)||c(e,null)}),n.forEach(function(e){t.has(e)||c(e,i)})}f.set(l,r)},[r]),l),C=o(o({},j),g);return s.createElement(s.Fragment,null,E&&s.createElement(R,{sideCar:p,removeScrollBar:x,shards:P,noRelative:T,noIsolation:S,inert:A,setCallbacks:y,allowPinchZoom:!!_,lockRef:u,gapMode:O}),v?s.cloneElement(s.Children.only(b),o(o({},C),{ref:k})):s.createElement(void 0===M?"div":M,o({},C,{className:w,ref:k}),b))});g.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},g.classNames={fullWidth:u,zeroRight:l};var y=function(e){var t=e.sideCar,r=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return s.createElement(n,o({},r))};y.isSideCarExport=!0;var v=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=v();return function(t,r){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},P=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[E(r),E(n),E(i)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=P(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},T=w(),S="data-scroll-locked",A=function(e,t,r,n){var i=e.left,o=e.top,a=e.right,s=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(s,"px ").concat(n,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(s,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},_=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},M=function(){s.useEffect(function(){return document.body.setAttribute(S,(_()+1).toString()),function(){var e=_()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},O=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,i=void 0===n?"margin":n;M();var o=s.useMemo(function(){return R(i)},[i]);return s.createElement(T,{styles:A(o,!t,i,r?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var k=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",k,k),window.removeEventListener("test",k,k)}catch(e){j=!1}var C=!!j&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},N=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),L(e,n)){var i=I(e,n);if(i[1]>i[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},L=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,r,n,i){var o,a=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),s=a*n,l=r.target,u=t.contains(l),c=!1,d=s>0,f=0,h=0;do{if(!l)break;var p=I(e,l),m=p[0],g=p[1]-p[2]-a*m;(m||g)&&L(e,l)&&(f+=g,h+=m);var y=l.parentNode;l=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(i&&1>Math.abs(f)||!i&&s>f)?c=!0:!d&&(i&&1>Math.abs(h)||!i&&-s>h)&&(c=!0),c},U=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},V=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},z=0,H=[];let $=(n=function(e){var t=s.useRef([]),r=s.useRef([0,0]),n=s.useRef(),i=s.useState(z++)[0],o=s.useState(w)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,o=U(e),s=r.current,l="deltaX"in e?e.deltaX:s[0]-o[0],u="deltaY"in e?e.deltaY:s[1]-o[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=N(d,c);if(!f)return!0;if(f?i=d:(i="v"===d?"h":"v",f=N(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(l||u)&&(n.current=i),!i)return!0;var h=n.current||i;return F(h,t,e,"h"===h?l:u,!0)},[]),u=s.useCallback(function(e){if(H.length&&H[H.length-1]===o){var r="deltaY"in e?V(e):U(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var i=(a.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,r,n,i){var o={name:e,delta:r,target:n,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=s.useCallback(function(e){r.current=U(e),n.current=void 0},[]),f=s.useCallback(function(t){c(t.type,V(t),t.target,l(t,e.lockRef.current))},[]),h=s.useCallback(function(t){c(t.type,U(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return H.push(o),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:h}),document.addEventListener("wheel",u,C),document.addEventListener("touchmove",u,C),document.addEventListener("touchstart",d,C),function(){H=H.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,C),document.removeEventListener("touchmove",u,C),document.removeEventListener("touchstart",d,C)}},[]);var p=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?s.createElement(O,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},p.useMedium(n),y);var W=s.forwardRef(function(e,t){return s.createElement(g,o({},e,{ref:t,sideCar:$}))});W.classNames=g.classNames;let K=W},2255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(3931);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},2308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,i,,a]=t;for(let s in n.includes(o.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=r,t[3]="refresh"),i)e(i[s],r)}},refreshInactiveParallelSegments:function(){return a}});let n=r(6928),i=r(9008),o=r(3913);async function a(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{navigatedAt:t,state:r,updatedTree:o,updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:c=o,canonicalUrl:d}=e,[,f,h,p]=o,m=[];if(h&&h!==d&&"refresh"===p&&!u.has(h)){u.add(h);let e=(0,i.fetchServerResponse)(new URL(h,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,n.applyFlightData)(t,a,a,e)});m.push(e)}for(let e in f){let n=s({navigatedAt:t,state:r,updatedTree:f[e],updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:d});m.push(n)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2348:(e,t,r)=>{"use strict";r.d(t,{QP:()=>eu});let n=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),i(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let i=r[e]||[];return t&&n[e]?[...i,...n[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),o=n?i(e.slice(1),n):void 0;if(o)return o;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},o=/^\[(.+)\]$/,a=e=>{if(o.test(e)){let t=o.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)l(r[e],n,e,t);return n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void l(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,i=(i,o)=>{r.set(i,o),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(i(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):i(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r=[],n=0,i=0,o=0;for(let a=0;a<e.length;a++){let s=e[a];if(0===n&&0===i){if(":"===s){r.push(e.slice(o,a)),o=a+1;continue}if("/"===s){t=a;continue}}"["===s?n++:"]"===s?n--:"("===s?i++:")"===s&&i--}let a=0===r.length?e:e.substring(o),s=h(a);return{modifiers:r,hasImportantModifier:s!==a,baseClassName:s,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},h=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,p=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},m=e=>({cache:d(e.cacheSize),parseClassName:f(e),sortModifiers:p(e),...n(e)}),g=/\s+/,y=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i,sortModifiers:o}=t,a=[],s=e.trim().split(g),l="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:h}=r(t);if(u){l=t+(l.length>0?" "+l:l);continue}let p=!!h,m=n(p?f.substring(0,h):f);if(!m){if(!p||!(m=n(f))){l=t+(l.length>0?" "+l:l);continue}p=!1}let g=o(c).join(":"),y=d?g+"!":g,v=y+m;if(a.includes(v))continue;a.push(v);let b=i(m,p);for(let e=0;e<b.length;++e){let t=b[e];a.push(y+t)}l=t+(l.length>0?" "+l:l)}return l};function v(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,E=/^\((?:(\w[\w-]*):)?(.+)\)$/i,P=/^\d+\/\d+$/,R=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,T=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,_=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=e=>P.test(e),O=e=>!!e&&!Number.isNaN(Number(e)),j=e=>!!e&&Number.isInteger(Number(e)),k=e=>e.endsWith("%")&&O(e.slice(0,-1)),C=e=>R.test(e),D=()=>!0,N=e=>T.test(e)&&!S.test(e),L=()=>!1,I=e=>A.test(e),F=e=>_.test(e),U=e=>!B(e)&&!G(e),V=e=>ee(e,ei,L),B=e=>x.test(e),z=e=>ee(e,eo,N),H=e=>ee(e,ea,O),$=e=>ee(e,er,L),W=e=>ee(e,en,F),K=e=>ee(e,el,I),G=e=>E.test(e),X=e=>et(e,eo),q=e=>et(e,es),Y=e=>et(e,er),Z=e=>et(e,ei),Q=e=>et(e,en),J=e=>et(e,el,!0),ee=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=E.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,ei=e=>"length"===e||"size"===e||"bg-size"===e,eo=e=>"length"===e,ea=e=>"number"===e,es=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let r,n,i,o=function(s){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,i=r.cache.set,o=a,a(s)};function a(e){let t=n(e);if(t)return t;let o=y(e,r);return i(e,o),o}return function(){return o(v.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),n=w("font-weight"),i=w("tracking"),o=w("leading"),a=w("breakpoint"),s=w("container"),l=w("spacing"),u=w("radius"),c=w("shadow"),d=w("inset-shadow"),f=w("text-shadow"),h=w("drop-shadow"),p=w("blur"),m=w("perspective"),g=w("aspect"),y=w("ease"),v=w("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...x(),G,B],P=()=>["auto","hidden","clip","visible","scroll"],R=()=>["auto","contain","none"],T=()=>[G,B,l],S=()=>[M,"full","auto",...T()],A=()=>[j,"none","subgrid",G,B],_=()=>["auto",{span:["full",j,G,B]},j,G,B],N=()=>[j,"auto",G,B],L=()=>["auto","min","max","fr",G,B],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...T()],et=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],er=()=>[e,G,B],en=()=>[...x(),Y,$,{position:[G,B]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],eo=()=>["auto","cover","contain",Z,V,{size:[G,B]}],ea=()=>[k,X,z],es=()=>["","none","full",u,G,B],el=()=>["",O,X,z],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[O,k,Y,$],ef=()=>["","none",p,G,B],eh=()=>["none",O,G,B],ep=()=>["none",O,G,B],em=()=>[O,G,B],eg=()=>[M,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[C],breakpoint:[C],color:[D],container:[C],"drop-shadow":[C],ease:["in","out","in-out"],font:[U],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[C],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[C],shadow:[C],spacing:["px",O],text:[C],"text-shadow":[C],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,B,G,g]}],container:["container"],columns:[{columns:[O,B,G,s]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[j,"auto",G,B]}],basis:[{basis:[M,"full","auto",s,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[O,M,"auto","initial","none",B]}],grow:[{grow:["",O,G,B]}],shrink:[{shrink:["",O,G,B]}],order:[{order:[j,"first","last","none",G,B]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:_()}],"col-start":[{"col-start":N()}],"col-end":[{"col-end":N()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:_()}],"row-start":[{"row-start":N()}],"row-end":[{"row-end":N()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":L()}],"auto-rows":[{"auto-rows":L()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[s,"screen",...et()]}],"min-w":[{"min-w":[s,"screen","none",...et()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,X,z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,G,H]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",k,B]}],"font-family":[{font:[q,B,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,G,B]}],"line-clamp":[{"line-clamp":[O,"none",G,H]}],leading:[{leading:[o,...T()]}],"list-image":[{"list-image":["none",G,B]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,B]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[O,"from-font","auto",G,z]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[O,"auto",G,B]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:eo()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},j,G,B],radial:["",G,B],conic:[j,G,B]},Q,W]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[O,G,B]}],"outline-w":[{outline:["",O,X,z]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",c,J,K]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,J,K]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[O,z]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,J,K]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[O,G,B]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[O]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[G,B]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[O]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:eo()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,B]}],filter:[{filter:["","none",G,B]}],blur:[{blur:ef()}],brightness:[{brightness:[O,G,B]}],contrast:[{contrast:[O,G,B]}],"drop-shadow":[{"drop-shadow":["","none",h,J,K]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",O,G,B]}],"hue-rotate":[{"hue-rotate":[O,G,B]}],invert:[{invert:["",O,G,B]}],saturate:[{saturate:[O,G,B]}],sepia:[{sepia:["",O,G,B]}],"backdrop-filter":[{"backdrop-filter":["","none",G,B]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[O,G,B]}],"backdrop-contrast":[{"backdrop-contrast":[O,G,B]}],"backdrop-grayscale":[{"backdrop-grayscale":["",O,G,B]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[O,G,B]}],"backdrop-invert":[{"backdrop-invert":["",O,G,B]}],"backdrop-opacity":[{"backdrop-opacity":[O,G,B]}],"backdrop-saturate":[{"backdrop-saturate":[O,G,B]}],"backdrop-sepia":[{"backdrop-sepia":["",O,G,B]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,B]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[O,"initial",G,B]}],ease:[{ease:["linear","initial",y,G,B]}],delay:[{delay:[O,G,B]}],animate:[{animate:["none",v,G,B]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,G,B]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:eh()}],"rotate-x":[{"rotate-x":eh()}],"rotate-y":[{"rotate-y":eh()}],"rotate-z":[{"rotate-z":eh()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[G,B,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,B]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,B]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[O,X,z,H]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(5362);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),o=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=o(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},2547:(e,t,r)=>{"use strict";r.d(t,{n:()=>d});var n=r(3210),i=r(8599),o=r(4163),a=r(3495),s=r(687),l="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:g,onUnmountAutoFocus:y,...v}=e,[b,w]=n.useState(null),x=(0,a.c)(g),E=(0,a.c)(y),P=n.useRef(null),R=(0,i.s)(t,e=>w(e)),T=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(T.paused||!b)return;let t=e.target;b.contains(t)?P.current=t:p(P.current,{select:!0})},t=function(e){if(T.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||p(P.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,b,T.paused]),n.useEffect(()=>{if(b){m.add(T);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(l,c);b.addEventListener(l,x),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(p(n,{select:t}),document.activeElement!==r)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(b))}return()=>{b.removeEventListener(l,x),setTimeout(()=>{let t=new CustomEvent(u,c);b.addEventListener(u,E),b.dispatchEvent(t),t.defaultPrevented||p(e??document.body,{select:!0}),b.removeEventListener(u,E),m.remove(T)},0)}}},[b,x,E,T]);let S=n.useCallback(e=>{if(!r&&!d||T.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[i,o]=function(e){let t=f(e);return[h(t,e),h(t.reverse(),e)]}(t);i&&o?e.shiftKey||n!==o?e.shiftKey&&n===i&&(e.preventDefault(),r&&p(o,{select:!0})):(e.preventDefault(),r&&p(i,{select:!0})):n===t&&e.preventDefault()}},[r,d,T.paused]);return(0,s.jsx)(o.sG.div,{tabIndex:-1,...v,ref:R,onKeyDown:S})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function h(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function p(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=g(e,t)).unshift(t)},remove(t){e=g(e,t),e[0]?.resume()}}}();function g(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},2582:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});let n=(0,r(3210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(3210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),a=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:o="",children:a,iconNode:c,...d},f)=>(0,n.createElement)("svg",{ref:f,...u,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:s("lucide",o),...!a&&!l(d)&&{"aria-hidden":"true"},...d},[...c.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(a)?a:[a]])),d=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...o},l)=>(0,n.createElement)(c,{ref:l,iconNode:t,className:s(`lucide-${i(a(e))}`,`lucide-${e}`,r),...o}));return r.displayName=a(e),r}},2708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},2743:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(3210);let i=r(7044).B?n.useLayoutEffect:n.useEffect},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},2789:(e,t,r)=>{"use strict";r.d(t,{M:()=>i});var n=r(3210);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},2941:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},2942:(e,t,r)=>{"use strict";r.d(t,{RG:()=>x,bL:()=>O,q7:()=>j});var n=r(3210),i=r(569),o=r(9510),a=r(8599),s=r(1273),l=r(6963),u=r(4163),c=r(3495),d=r(5551),f=r(43),h=r(687),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[y,v,b]=(0,o.N)(g),[w,x]=(0,s.A)(g,[b]),[E,P]=w(g),R=n.forwardRef((e,t)=>(0,h.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(T,{...e,ref:t})})}));R.displayName=g;var T=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:s=!1,dir:l,currentTabStopId:y,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:P=!1,...R}=e,T=n.useRef(null),S=(0,a.s)(t,T),A=(0,f.jH)(l),[_,O]=(0,d.i)({prop:y,defaultProp:b??null,onChange:w,caller:g}),[j,k]=n.useState(!1),C=(0,c.c)(x),D=v(r),N=n.useRef(!1),[L,I]=n.useState(0);return n.useEffect(()=>{let e=T.current;if(e)return e.addEventListener(p,C),()=>e.removeEventListener(p,C)},[C]),(0,h.jsx)(E,{scope:r,orientation:o,dir:A,loop:s,currentTabStopId:_,onItemFocus:n.useCallback(e=>O(e),[O]),onItemShiftTab:n.useCallback(()=>k(!0),[]),onFocusableItemAdd:n.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>I(e=>e-1),[]),children:(0,h.jsx)(u.sG.div,{tabIndex:j||0===L?-1:0,"data-orientation":o,...R,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===_),...e].filter(Boolean).map(e=>e.ref.current),P)}}N.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>k(!1))})})}),S="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:a=!1,tabStopId:s,children:c,...d}=e,f=(0,l.B)(),p=s||f,m=P(S,r),g=m.currentTabStopId===p,b=v(r),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:E}=m;return n.useEffect(()=>{if(o)return w(),()=>x()},[o,w,x]),(0,h.jsx)(y.ItemSlot,{scope:r,id:p,focusable:o,active:a,children:(0,h.jsx)(u.sG.span,{tabIndex:g?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let i=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return _[i]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>M(r))}}),children:"function"==typeof c?c({isCurrentTabStop:g,hasTabStop:null!=E}):c})})});A.displayName=S;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var O=R,j=A},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let n=r(3210);function i(e,t){let r=(0,n.useRef)(null),i=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(r.current=o(e,n)),t&&(i.current=o(t,n))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},3332:(e,t,r)=>{"use strict";var n=r(3210),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,a=n.useEffect,s=n.useLayoutEffect,l=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=o({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return s(function(){i.value=r,i.getSnapshot=t,u(i)&&c({inst:i})},[e,r,t]),a(function(){return u(i)&&c({inst:i}),e(function(){u(i)&&c({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},3376:(e,t,r)=>{"use strict";r.d(t,{Eq:()=>c});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},i=new WeakMap,o=new WeakMap,a={},s=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,r,n){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=l(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[r]||(a[r]=new WeakMap);var c=a[r],d=[],f=new Set,h=new Set(u),p=function(e){!e||f.has(e)||(f.add(e),p(e.parentNode))};u.forEach(p);var m=function(e){!e||h.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(n),a=null!==t&&"false"!==t,s=(i.get(e)||0)+1,l=(c.get(e)||0)+1;i.set(e,s),c.set(e,l),d.push(e),1===s&&a&&o.set(e,!0),1===l&&e.setAttribute(r,"true"),a||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),s++,function(){d.forEach(function(e){var t=i.get(e)-1,a=c.get(e)-1;i.set(e,t),c.set(e,a),t||(o.has(e)||e.removeAttribute(n),o.delete(e)),a||e.removeAttribute(r)}),--s||(i=new WeakMap,i=new WeakMap,o=new WeakMap,a={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),o=t||n(e);return o?(i.push.apply(i,Array.from(o.querySelectorAll("[aria-live], script"))),u(i,o,r,"aria-hidden")):function(){return null}}},3406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return v},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return w},onNavigationIntent:function(){return x},pingVisibleLinks:function(){return P},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return b}}),r(3690);let n=r(9752),i=r(9154),o=r(593),a=r(3210),s=null,l={pending:!0},u={pending:!1};function c(e){(0,a.startTransition)(()=>{null==s||s.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),s=e})}function d(e){s===e&&(s=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,h=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;w(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==f.get(e)&&b(e),f.set(e,t),null!==p&&p.observe(e)}function g(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,r,n,i,o){if(i){let i=g(t);if(null!==i){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:o};return m(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:o}}function v(e,t,r,n){let i=g(t);null!==i&&m(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function b(e){let t=f.get(e);if(void 0!==t){f.delete(e),h.delete(t);let r=t.prefetchTask;null!==r&&(0,o.cancelPrefetchTask)(r)}null!==p&&p.unobserve(e)}function w(e,t){let r=f.get(e);void 0!==r&&(r.isVisible=t,t?h.add(r):h.delete(r),E(r))}function x(e,t){let r=f.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,E(r))}function E(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,o.cancelPrefetchTask)(t);return}}function P(e,t){let r=(0,o.getCurrentCacheVersion)();for(let n of h){let a=n.prefetchTask;if(null!==a&&n.cacheVersion===r&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,o.cancelPrefetchTask)(a);let s=(0,o.createCacheKey)(n.prefetchHref,e),l=n.wasHoveredOrTouched?o.PrefetchPriority.Intent:o.PrefetchPriority.Default;n.prefetchTask=(0,o.schedulePrefetchTask)(s,t,n.kind===i.PrefetchKind.FULL,l),n.cacheVersion=(0,o.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3495:(e,t,r)=>{"use strict";r.d(t,{c:()=>i});var n=r(3210);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},3690:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return b}});let n=r(9154),i=r(8830),o=r(3210),a=r(1992);r(593);let s=r(9129),l=r(6127),u=r(9752),c=r(5076),d=r(3406);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?h({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function h(e){let{actionQueue:t,action:r,setState:n}=e,i=t.state;t.pending=r;let o=r.payload,s=t.action(i,o);function l(e){r.discarded||(t.state=e,f(t,n),r.resolve(e))}(0,a.isThenable)(s)?s.then(l,e=>{f(t,n),r.reject(e)}):l(s)}function p(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let i={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,o.startTransition)(()=>{r(e)})}let a={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=a,h({actionQueue:e,action:a,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),h({actionQueue:e,action:a,setState:r})):(null!==e.last&&(e.last.next=a),e.last=a)})(r,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function m(){return null}function g(){return null}function y(e,t,r,i){let o=new URL((0,l.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(i);(0,s.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:o,isExternalUrl:(0,u.isExternalURL)(o),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function v(e,t){(0,s.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,u.createPrefetchURL)(e);if(null!==i){var o;(0,c.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:i,kind:null!=(o=null==t?void 0:t.kind)?o:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,o.startTransition)(()=>{var r;y(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,o.startTransition)(()=>{var r;y(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,o.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(4827);let n=r(2785);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),o=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:a,searchParams:s,search:l,hash:u,href:c,origin:d}=new URL(e,o);if(d!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:r?(0,n.searchParamsToUrlQuery)(s):void 0,search:l,hash:u,href:c.slice(d.length)}}},3898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let n=r(4400),i=r(1500),o=r(3123),a=r(3913);function s(e,t,r,s,l,u){let{segmentPath:c,seedData:d,tree:f,head:h}=s,p=t,m=r;for(let t=0;t<c.length;t+=2){let r=c[t],s=c[t+1],g=t===c.length-2,y=(0,o.createRouterCacheKey)(s),v=m.parallelRoutes.get(r);if(!v)continue;let b=p.parallelRoutes.get(r);b&&b!==v||(b=new Map(v),p.parallelRoutes.set(r,b));let w=v.get(y),x=b.get(y);if(g){if(d&&(!x||!x.lazyData||x===w)){let t=d[0],r=d[1],o=d[3];x={lazyData:null,rsc:u||t!==a.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:o,parallelRoutes:u&&w?new Map(w.parallelRoutes):new Map,navigatedAt:e},w&&u&&(0,n.invalidateCacheByRouterState)(x,w,f),u&&(0,i.fillLazyItemsTillLeafWithHead)(e,x,w,f,d,h,l),b.set(y,x)}continue}x&&w&&(x===w&&(x={lazyData:x.lazyData,rsc:x.rsc,prefetchRsc:x.prefetchRsc,head:x.head,prefetchHead:x.prefetchHead,parallelRoutes:new Map(x.parallelRoutes),loading:x.loading},b.set(y,x)),p=x,m=w)}}function l(e,t,r,n,i){s(e,t,r,n,i,!0)}function u(e,t,r,n,i){s(e,t,r,n,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3931:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},4027:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>s});var n=r(3210),i=r(1215),o=r(8730),a=r(687),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:t,{...o,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},4224:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(9384);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:s}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let o=i(t)||i(n);return a[e][o]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=r(6143),i=r(1437),o=r(3293),a=r(2887),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(s);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},l=1,c=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),a=d.match(s);if(e&&a&&a[2]){let{key:t,optional:r,repeat:i}=u(a[2]);n[t]={pos:l++,repeat:i,optional:r},c.push("/"+(0,o.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:i}=u(a[2]);n[e]={pos:l++,repeat:t,optional:i},r&&a[1]&&c.push("/"+(0,o.escapeStringRegexp)(a[1]));let s=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&a[1]&&(s=s.substring(1)),c.push(s)}else c.push("/"+(0,o.escapeStringRegexp)(d));t&&a&&a[3]&&c.push((0,o.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:n}}function d(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:o,groups:a}=c(e,r,n),s=o;return i||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:a}}function f(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:a,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:f}=u(i),h=c.replace(/\W/g,"");s&&(h=""+s+h);let p=!1;(0===h.length||h.length>30)&&(p=!0),isNaN(parseInt(h.slice(0,1)))||(p=!0),p&&(h=n());let m=h in a;s?a[h]=""+s+c:a[h]=c;let g=r?(0,o.escapeStringRegexp)(r):"";return t=m&&l?"\\k<"+h+">":f?"(?<"+h+">.+?)":"(?<"+h+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function h(e,t,r,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),h={},p=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),a=c.match(s);if(e&&a&&a[2])p.push(f({getSafeRouteKey:d,interceptionMarker:a[1],segment:a[2],routeKeys:h,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&p.push("/"+(0,o.escapeStringRegexp)(a[1]));let e=f({getSafeRouteKey:d,segment:a[2],routeKeys:h,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,o.escapeStringRegexp)(c));r&&a&&a[3]&&p.push((0,o.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:h}}function p(e,t){var r,n,i;let o=h(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),a=o.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...d(e,t),namedRegex:"^"+a+"$",routeKeys:o.routeKeys}}function m(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=h(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},4397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let n=r(3123);function i(e,t){return function e(t,r,i){if(0===Object.keys(r).length)return[t,i];let o=Object.keys(r).filter(e=>"children"!==e);for(let a of("children"in r&&o.unshift("children"),o)){let[o,s]=r[a],l=t.parallelRoutes.get(a);if(!l)continue;let u=(0,n.createRouterCacheKey)(o),c=l.get(u);if(!c)continue;let d=e(c,s,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let n=r(3123);function i(e,t,r){for(let i in r[1]){let o=r[1][i][0],a=(0,n.createRouterCacheKey)(o),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4479:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e}r.d(t,{G:()=>n})},4642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},4674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let n=r(4949),i=r(3931),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:o}=(0,i.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return a}});let n=r(5531),i=r(5499);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,h=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},4949:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},5028:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(3210),i=r(1215),o=r(4163),a=r(6156),s=r(687),l=n.forwardRef((e,t)=>{let{container:r,...l}=e,[u,c]=n.useState(!1);(0,a.N)(()=>c(!0),[]);let d=r||u&&globalThis?.document?.body;return d?i.createPortal((0,s.jsx)(o.sG.div,{...l,ref:t}),d):null});l.displayName="Portal"},5076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return o},prefetchReducer:function(){return a}});let n=r(5144),i=r(5334),o=new n.PromiseQueue(5),a=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(2026),i=r(9656);var o=i._("_maxConcurrency"),a=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,r,i=new Promise((e,n)=>{t=e,r=n}),o=async()=>{try{n._(this,a)[a]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,a)[a]--,n._(this,l)[l]()}};return n._(this,s)[s].push({promiseFn:i,task:o}),n._(this,l)[l](),i}bump(e){let t=n._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,s)[s].splice(t,1)[0];n._(this,s)[s].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),n._(this,o)[o]=e,n._(this,a)[a]=0,n._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,a)[a]<n._(this,o)[o]||e)&&n._(this,s)[s].length>0){var t;null==(t=n._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,r){let{url:x,isExternalUrl:E,navigateType:P,shouldScroll:R,allowAliasing:T}=r,S={},{hash:A}=x,_=(0,i.createHrefFromUrl)(x),M="push"===P;if((0,g.prunePrefetchCache)(t.prefetchCache),S.preserveCustomHistoryState=!1,S.pendingPush=M,E)return b(t,S,x.toString(),M);if(document.getElementById("__next-page-redirect"))return b(t,S,_,M);let O=(0,g.getOrCreatePrefetchCacheEntry)({url:x,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:T}),{treeAtTimeOfPrefetch:j,data:k}=O;return f.prefetchQueue.bump(k),k.then(f=>{let{flightData:g,canonicalUrl:E,postponed:P}=f,T=Date.now(),k=!1;if(O.lastUsedTime||(O.lastUsedTime=T,k=!0),O.aliased){let n=(0,v.handleAliasedPrefetchEntry)(T,t,g,x,S);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof g)return b(t,S,g,M);let C=E?(0,i.createHrefFromUrl)(E):_;if(A&&t.canonicalUrl.split("#",1)[0]===C.split("#",1)[0])return S.onlyHashChange=!0,S.canonicalUrl=C,S.shouldScroll=R,S.hashFragment=A,S.scrollableSegments=[],(0,c.handleMutable)(t,S);let D=t.tree,N=t.cache,L=[];for(let e of g){let{pathToSegment:r,seedData:i,head:c,isHeadPartial:f,isRootRender:g}=e,v=e.tree,E=["",...r],R=(0,a.applyRouterStatePatchToTree)(E,D,v,_);if(null===R&&(R=(0,a.applyRouterStatePatchToTree)(E,j,v,_)),null!==R){if(i&&g&&P){let e=(0,m.startPPRNavigation)(T,N,D,v,i,c,f,!1,L);if(null!==e){if(null===e.route)return b(t,S,_,M);R=e.route;let r=e.node;null!==r&&(S.cache=r);let i=e.dynamicRequestTree;if(null!==i){let r=(0,n.fetchServerResponse)(x,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,r)}}else R=v}else{if((0,l.isNavigatingToNewRootLayout)(D,R))return b(t,S,_,M);let n=(0,h.createEmptyCacheNode)(),i=!1;for(let t of(O.status!==u.PrefetchCacheEntryStatus.stale||k?i=(0,d.applyFlightData)(T,N,n,e,O):(i=function(e,t,r,n){let i=!1;for(let o of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),w(n).map(e=>[...r,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,o),i=!0;return i}(n,N,r,v),O.lastUsedTime=T),(0,s.shouldHardNavigate)(E,D)?(n.rsc=N.rsc,n.prefetchRsc=N.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(n,N,r),S.cache=n):i&&(S.cache=n,N=n),w(v))){let e=[...r,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&L.push(e)}}D=R}}return S.patchedTree=D,S.canonicalUrl=C,S.scrollableSegments=L,S.hashFragment=A,S.shouldScroll=R,(0,c.handleMutable)(t,S)},()=>t)}}});let n=r(9008),i=r(7391),o=r(8468),a=r(6770),s=r(5951),l=r(2030),u=r(9154),c=r(9435),d=r(6928),f=r(5076),h=r(9752),p=r(3913),m=r(5956),g=r(5334),y=r(7464),v=r(9707);function b(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function w(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,i]of Object.entries(n))for(let n of w(i))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return f},STATIC_STALETIME_MS:function(){return h},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let n=r(9008),i=r(9154),o=r(5076);function a(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function s(e,t,r){return a(e,t===i.PrefetchKind.FULL,r)}function l(e){let{url:t,nextUrl:r,tree:n,prefetchCache:o,kind:s,allowAliasing:l=!0}=e,u=function(e,t,r,n,o){for(let s of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[r,null])){let r=a(e,!0,s),l=a(e,!1,s),u=e.search?r:l,c=n.get(u);if(c&&o){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=n.get(l);if(o&&e.search&&t!==i.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==i.PrefetchKind.FULL&&o){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,r,o,l);return u?(u.status=p(u),u.kind!==i.PrefetchKind.FULL&&s===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:o,kind:null!=s?s:i.PrefetchKind.TEMPORARY})}),s&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=s),u):c({tree:n,url:t,nextUrl:r,prefetchCache:o,kind:s||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:r,prefetchCache:n,url:o,data:a,kind:l}=e,u=a.couldBeIntercepted?s(o,l,t):s(o,l),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(a),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:a.staleTime,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:o};return n.set(u,c),c}function c(e){let{url:t,kind:r,tree:a,nextUrl:l,prefetchCache:u}=e,c=s(t,r),d=o.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:a,nextUrl:l,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:i}=e,o=n.get(i);if(!o)return;let a=s(t,o.kind,r);return n.set(a,{...o,key:a}),n.delete(i),a}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=r?r:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:a,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,f),f}function d(e){for(let[t,r]of e)p(r)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),h=1e3*Number("300");function p(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:o}=e;return -1!==o?Date.now()<r+o?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+f?n?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<r+h?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<r+h?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",o=r+1;o<e.length;){var a=e.charCodeAt(o);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[o++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=o;continue}if("("===n){var s=1,l="",o=r+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '+o);for(;o<e.length;){if("\\"===e[o]){l+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--s){o++;break}}else if("("===e[o]&&(s++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at "+o);l+=e[o++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=o;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,o=void 0===n?"./":n,a="[^"+i(t.delimiter||"/#?")+"]+?",s=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},f=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},h=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var p=d("CHAR"),m=d("NAME"),g=d("PATTERN");if(m||g){var y=p||"";-1===o.indexOf(y)&&(c+=y,y=""),c&&(s.push(c),c=""),s.push({name:m||l++,prefix:y,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var v=p||d("ESCAPED_CHAR");if(v){c+=v;continue}if(c&&(s.push(c),c=""),d("OPEN")){var y=h(),b=d("NAME")||"",w=d("PATTERN")||"",x=h();f("CLOSE"),s.push({name:b||(w?l++:""),pattern:b&&!w?a:w,prefix:y,suffix:x,modifier:d("MODIFIER")||""});continue}f("END")}return s}function r(e,t){void 0===t&&(t={});var r=o(t),n=t.encode,i=void 0===n?function(e){return e}:n,a=t.validate,s=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var o=e[n];if("string"==typeof o){r+=o;continue}var a=t?t[o.name]:void 0,u="?"===o.modifier||"*"===o.modifier,c="*"===o.modifier||"+"===o.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+o.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+o.name+'" to not be empty')}for(var d=0;d<a.length;d++){var f=i(a[d],o);if(s&&!l[n].test(f))throw TypeError('Expected all "'+o.name+'" to match "'+o.pattern+'", but got "'+f+'"');r+=o.prefix+f+o.suffix}continue}if("string"==typeof a||"number"==typeof a){var f=i(String(a),o);if(s&&!l[n].test(f))throw TypeError('Expected "'+o.name+'" to match "'+o.pattern+'", but got "'+f+'"');r+=o.prefix+f+o.suffix;continue}if(!u){var h=c?"an array":"a string";throw TypeError('Expected "'+o.name+'" to be '+h)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var o=n[0],a=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):s[r.name]=i(n[e],r)}}(l);return{path:o,index:a,params:s}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function o(e){return e&&e.sensitive?"":"i"}function a(e,t,r){void 0===r&&(r={});for(var n=r.strict,a=void 0!==n&&n,s=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+i(r.endsWith||"")+"]|$",f="["+i(r.delimiter||"/#?")+"]",h=void 0===s||s?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)h+=i(c(m));else{var g=i(c(m.prefix)),y=i(c(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var v="*"===m.modifier?"?":"";h+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+v}else h+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier;else h+="("+m.pattern+")"+m.modifier;else h+="(?:"+g+y+")"+m.modifier}}if(void 0===l||l)a||(h+=f+"?"),h+=r.endsWith?"(?="+d+")":"$";else{var b=e[e.length-1],w="string"==typeof b?f.indexOf(b[b.length-1])>-1:void 0===b;a||(h+="(?:"+f+"(?="+d+"))?"),w||(h+="(?="+f+"|"+d+")")}return new RegExp(h,o(r))}function s(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,r,n).source}).join("|")+")",o(n)):a(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(s(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=s})(),e.exports=t})()},5416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return o},getBotType:function(){return l},isBot:function(){return s}});let n=r(5796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,o=n.HTML_LIMITED_BOT_UA_RE.source;function a(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return i.test(e)||a(e)}function l(e){return i.test(e)?"dom":a(e)?"html":void 0}},5509:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>te,i3:()=>tr,UC:()=>tt,bL:()=>e8,Bk:()=>eG});var n=r(3210);let i=["top","right","bottom","left"],o=Math.min,a=Math.max,s=Math.round,l=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function h(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}let y=new Set(["top","bottom"]);function v(e){return y.has(h(e))?"y":"x"}function b(e){return e.replace(/start|end/g,e=>d[e])}let w=["left","right"],x=["right","left"],E=["top","bottom"],P=["bottom","top"];function R(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function T(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function S(e){let{x:t,y:r,width:n,height:i}=e;return{width:n,height:i,top:r,left:t,right:t+n,bottom:r+i,x:t,y:r}}function A(e,t,r){let n,{reference:i,floating:o}=e,a=v(t),s=m(v(t)),l=g(s),u=h(t),c="y"===a,d=i.x+i.width/2-o.width/2,f=i.y+i.height/2-o.height/2,y=i[l]/2-o[l]/2;switch(u){case"top":n={x:d,y:i.y-o.height};break;case"bottom":n={x:d,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:f};break;case"left":n={x:i.x-o.width,y:f};break;default:n={x:i.x,y:i.y}}switch(p(t)){case"start":n[s]-=y*(r&&c?-1:1);break;case"end":n[s]+=y*(r&&c?-1:1)}return n}let _=async(e,t,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:o=[],platform:a}=r,s=o.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:d}=A(u,n,l),f=n,h={},p=0;for(let r=0;r<s.length;r++){let{name:o,fn:m}=s[r],{x:g,y:y,data:v,reset:b}=await m({x:c,y:d,initialPlacement:n,placement:f,strategy:i,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=g?g:c,d=null!=y?y:d,h={...h,[o]:{...h[o],...v}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:c,y:d}=A(u,f,l)),r=-1)}return{x:c,y:d,placement:f,strategy:i,middlewareData:h}};async function M(e,t){var r;void 0===t&&(t={});let{x:n,y:i,platform:o,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=f(t,e),m=T(p),g=s[h?"floating"===d?"reference":"floating":d],y=S(await o.getClippingRect({element:null==(r=await (null==o.isElement?void 0:o.isElement(g)))||r?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),v="floating"===d?{x:n,y:i,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),w=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},x=S(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:b,strategy:l}):v);return{top:(y.top-x.top+m.top)/w.y,bottom:(x.bottom-y.bottom+m.bottom)/w.y,left:(y.left-x.left+m.left)/w.x,right:(x.right-y.right+m.right)/w.x}}function O(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function j(e){return i.some(t=>e[t]>=0)}let k=new Set(["left","top"]);async function C(e,t){let{placement:r,platform:n,elements:i}=e,o=await (null==n.isRTL?void 0:n.isRTL(i.floating)),a=h(r),s=p(r),l="y"===v(r),u=k.has(a)?-1:1,c=o&&l?-1:1,d=f(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof y&&(g="end"===s?-1*y:y),l?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function D(){return"undefined"!=typeof window}function N(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function L(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!D()&&(e instanceof Node||e instanceof L(e).Node)}function U(e){return!!D()&&(e instanceof Element||e instanceof L(e).Element)}function V(e){return!!D()&&(e instanceof HTMLElement||e instanceof L(e).HTMLElement)}function B(e){return!!D()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof L(e).ShadowRoot)}let z=new Set(["inline","contents"]);function H(e){let{overflow:t,overflowX:r,overflowY:n,display:i}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!z.has(i)}let $=new Set(["table","td","th"]),W=[":popover-open",":modal"];function K(e){return W.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let G=["transform","translate","scale","rotate","perspective"],X=["transform","translate","scale","rotate","perspective","filter"],q=["paint","layout","strict","content"];function Y(e){let t=Z(),r=U(e)?ee(e):e;return G.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||X.some(e=>(r.willChange||"").includes(e))||q.some(e=>(r.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function J(e){return Q.has(N(e))}function ee(e){return L(e).getComputedStyle(e)}function et(e){return U(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function er(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||B(e)&&e.host||I(e);return B(t)?t.host:t}function en(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let i=function e(t){let r=er(t);return J(r)?t.ownerDocument?t.ownerDocument.body:t.body:V(r)&&H(r)?r:e(r)}(e),o=i===(null==(n=e.ownerDocument)?void 0:n.body),a=L(i);if(o){let e=ei(a);return t.concat(a,a.visualViewport||[],H(i)?i:[],e&&r?en(e):[])}return t.concat(i,en(i,[],r))}function ei(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eo(e){let t=ee(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=V(e),o=i?e.offsetWidth:r,a=i?e.offsetHeight:n,l=s(r)!==o||s(n)!==a;return l&&(r=o,n=a),{width:r,height:n,$:l}}function ea(e){return U(e)?e:e.contextElement}function es(e){let t=ea(e);if(!V(t))return u(1);let r=t.getBoundingClientRect(),{width:n,height:i,$:o}=eo(t),a=(o?s(r.width):r.width)/n,l=(o?s(r.height):r.height)/i;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let el=u(0);function eu(e){let t=L(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function ec(e,t,r,n){var i;void 0===t&&(t=!1),void 0===r&&(r=!1);let o=e.getBoundingClientRect(),a=ea(e),s=u(1);t&&(n?U(n)&&(s=es(n)):s=es(e));let l=(void 0===(i=r)&&(i=!1),n&&(!i||n===L(a))&&i)?eu(a):u(0),c=(o.left+l.x)/s.x,d=(o.top+l.y)/s.y,f=o.width/s.x,h=o.height/s.y;if(a){let e=L(a),t=n&&U(n)?L(n):n,r=e,i=ei(r);for(;i&&n&&t!==r;){let e=es(i),t=i.getBoundingClientRect(),n=ee(i),o=t.left+(i.clientLeft+parseFloat(n.paddingLeft))*e.x,a=t.top+(i.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,h*=e.y,c+=o,d+=a,i=ei(r=L(i))}}return S({width:f,height:h,x:c,y:d})}function ed(e,t){let r=et(e).scrollLeft;return t?t.left+r:ec(I(e)).left+r}function ef(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:ed(e,n)),y:n.top+t.scrollTop}}let eh=new Set(["absolute","fixed"]);function ep(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=L(e),n=I(e),i=r.visualViewport,o=n.clientWidth,a=n.clientHeight,s=0,l=0;if(i){o=i.width,a=i.height;let e=Z();(!e||e&&"fixed"===t)&&(s=i.offsetLeft,l=i.offsetTop)}return{width:o,height:a,x:s,y:l}}(e,r);else if("document"===t)n=function(e){let t=I(e),r=et(e),n=e.ownerDocument.body,i=a(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),o=a(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),s=-r.scrollLeft+ed(e),l=-r.scrollTop;return"rtl"===ee(n).direction&&(s+=a(t.clientWidth,n.clientWidth)-i),{width:i,height:o,x:s,y:l}}(I(e));else if(U(t))n=function(e,t){let r=ec(e,!0,"fixed"===t),n=r.top+e.clientTop,i=r.left+e.clientLeft,o=V(e)?es(e):u(1),a=e.clientWidth*o.x,s=e.clientHeight*o.y;return{width:a,height:s,x:i*o.x,y:n*o.y}}(t,r);else{let r=eu(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return S(n)}function em(e){return"static"===ee(e).position}function eg(e,t){if(!V(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let r=e.offsetParent;return I(e)===r&&(r=r.ownerDocument.body),r}function ey(e,t){var r;let n=L(e);if(K(e))return n;if(!V(e)){let t=er(e);for(;t&&!J(t);){if(U(t)&&!em(t))return t;t=er(t)}return n}let i=eg(e,t);for(;i&&(r=i,$.has(N(r)))&&em(i);)i=eg(i,t);return i&&J(i)&&em(i)&&!Y(i)?n:i||function(e){let t=er(e);for(;V(t)&&!J(t);){if(Y(t))return t;if(K(t))break;t=er(t)}return null}(e)||n}let ev=async function(e){let t=this.getOffsetParent||ey,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=V(t),i=I(t),o="fixed"===r,a=ec(e,!0,o,t),s={scrollLeft:0,scrollTop:0},l=u(0);if(n||!n&&!o)if(("body"!==N(t)||H(i))&&(s=et(t)),n){let e=ec(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=ed(i));o&&!n&&i&&(l.x=ed(i));let c=!i||n||o?u(0):ef(i,s);return{x:a.left+s.scrollLeft-l.x-c.x,y:a.top+s.scrollTop-l.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eb={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:i}=e,o="fixed"===i,a=I(n),s=!!t&&K(t.floating);if(n===a||s&&o)return r;let l={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=V(n);if((f||!f&&!o)&&(("body"!==N(n)||H(a))&&(l=et(n)),V(n))){let e=ec(n);c=es(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let h=!a||f||o?u(0):ef(a,l,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-l.scrollLeft*c.x+d.x+h.x,y:r.y*c.y-l.scrollTop*c.y+d.y+h.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:i}=e,s=[..."clippingAncestors"===r?K(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=en(e,[],!1).filter(e=>U(e)&&"body"!==N(e)),i=null,o="fixed"===ee(e).position,a=o?er(e):e;for(;U(a)&&!J(a);){let t=ee(a),r=Y(a);r||"fixed"!==t.position||(i=null),(o?!r&&!i:!r&&"static"===t.position&&!!i&&eh.has(i.position)||H(a)&&!r&&function e(t,r){let n=er(t);return!(n===r||!U(n)||J(n))&&("fixed"===ee(n).position||e(n,r))}(e,a))?n=n.filter(e=>e!==a):i=t,a=er(a)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=s[0],u=s.reduce((e,r)=>{let n=ep(t,r,i);return e.top=a(n.top,e.top),e.right=o(n.right,e.right),e.bottom=o(n.bottom,e.bottom),e.left=a(n.left,e.left),e},ep(t,l,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:ey,getElementRects:ev,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=eo(e);return{width:t,height:r}},getScale:es,isElement:U,isRTL:function(e){return"rtl"===ee(e).direction}};function ew(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:i,rects:s,platform:l,elements:u,middlewareData:c}=t,{element:d,padding:h=0}=f(e,t)||{};if(null==d)return{};let y=T(h),b={x:r,y:n},w=m(v(i)),x=g(w),E=await l.getDimensions(d),P="y"===w,R=P?"clientHeight":"clientWidth",S=s.reference[x]+s.reference[w]-b[w]-s.floating[x],A=b[w]-s.reference[w],_=await (null==l.getOffsetParent?void 0:l.getOffsetParent(d)),M=_?_[R]:0;M&&await (null==l.isElement?void 0:l.isElement(_))||(M=u.floating[R]||s.floating[x]);let O=M/2-E[x]/2-1,j=o(y[P?"top":"left"],O),k=o(y[P?"bottom":"right"],O),C=M-E[x]-k,D=M/2-E[x]/2+(S/2-A/2),N=a(j,o(D,C)),L=!c.arrow&&null!=p(i)&&D!==N&&s.reference[x]/2-(D<j?j:k)-E[x]/2<0,I=L?D<j?D-j:D-C:0;return{[w]:b[w]+I,data:{[w]:N,centerOffset:D-N-I,...L&&{alignmentOffset:I}},reset:L}}}),eE=(e,t,r)=>{let n=new Map,i={platform:eb,...r},o={...i.platform,_c:n};return _(e,t,{...i,platform:o})};var eP=r(1215),eR="undefined"!=typeof document?n.useLayoutEffect:function(){};function eT(e,t){let r,n,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eT(e[n],t[n]))return!1;return!0}if((r=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,i[n]))return!1;for(n=r;0!=n--;){let r=i[n];if(("_owner"!==r||!e.$$typeof)&&!eT(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eS(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eA(e,t){let r=eS(e);return Math.round(t*r)/r}function e_(e){let t=n.useRef(e);return eR(()=>{t.current=e}),t}let eM=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ex({element:r.current,padding:n}).fn(t):{}:r?ex({element:r,padding:n}).fn(t):{}}}),eO=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:i,y:o,placement:a,middlewareData:s}=t,l=await C(t,e);return a===(null==(r=s.offset)?void 0:r.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:a}}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:i}=t,{mainAxis:s=!0,crossAxis:l=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...c}=f(e,t),d={x:r,y:n},p=await M(t,c),g=v(h(i)),y=m(g),b=d[y],w=d[g];if(s){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",r=b+p[e],n=b-p[t];b=a(r,o(b,n))}if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",r=w+p[e],n=w-p[t];w=a(r,o(w,n))}let x=u.fn({...t,[y]:b,[g]:w});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[y]:s,[g]:l}}}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:i,rects:o,middlewareData:a}=t,{offset:s=0,mainAxis:l=!0,crossAxis:u=!0}=f(e,t),c={x:r,y:n},d=v(i),p=m(d),g=c[p],y=c[d],b=f(s,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(l){let e="y"===p?"height":"width",t=o.reference[p]-o.floating[e]+w.mainAxis,r=o.reference[p]+o.reference[e]-w.mainAxis;g<t?g=t:g>r&&(g=r)}if(u){var x,E;let e="y"===p?"width":"height",t=k.has(h(i)),r=o.reference[d]-o.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),n=o.reference[d]+o.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?w.crossAxis:0);y<r?y=r:y>n&&(y=n)}return{[p]:g,[d]:y}}}}(e),options:[e,t]}),eC=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,i,o,a;let{placement:s,middlewareData:l,rects:u,initialPlacement:c,platform:d,elements:y}=t,{mainAxis:T=!0,crossAxis:S=!0,fallbackPlacements:A,fallbackStrategy:_="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:j=!0,...k}=f(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let C=h(s),D=v(c),N=h(c)===c,L=await (null==d.isRTL?void 0:d.isRTL(y.floating)),I=A||(N||!j?[R(c)]:function(e){let t=R(e);return[b(e),t,b(t)]}(c)),F="none"!==O;!A&&F&&I.push(...function(e,t,r,n){let i=p(e),o=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?x:w;return t?w:x;case"left":case"right":return t?E:P;default:return[]}}(h(e),"start"===r,n);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(b)))),o}(c,j,O,L));let U=[c,...I],V=await M(t,k),B=[],z=(null==(n=l.flip)?void 0:n.overflows)||[];if(T&&B.push(V[C]),S){let e=function(e,t,r){void 0===r&&(r=!1);let n=p(e),i=m(v(e)),o=g(i),a="x"===i?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=R(a)),[a,R(a)]}(s,u,L);B.push(V[e[0]],V[e[1]])}if(z=[...z,{placement:s,overflows:B}],!B.every(e=>e<=0)){let e=((null==(i=l.flip)?void 0:i.index)||0)+1,t=U[e];if(t&&("alignment"!==S||D===v(t)||z.every(e=>e.overflows[0]>0&&v(e.placement)===D)))return{data:{index:e,overflows:z},reset:{placement:t}};let r=null==(o=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!r)switch(_){case"bestFit":{let e=null==(a=z.filter(e=>{if(F){let t=v(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(r=e);break}case"initialPlacement":r=c}if(s!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let i,s,{placement:l,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...g}=f(e,t),y=await M(t,g),b=h(l),w=p(l),x="y"===v(l),{width:E,height:P}=u.floating;"top"===b||"bottom"===b?(i=b,s=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(s=b,i="end"===w?"top":"bottom");let R=P-y.top-y.bottom,T=E-y.left-y.right,S=o(P-y[i],R),A=o(E-y[s],T),_=!t.middlewareData.shift,O=S,j=A;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(j=T),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(O=R),_&&!w){let e=a(y.left,0),t=a(y.right,0),r=a(y.top,0),n=a(y.bottom,0);x?j=E-2*(0!==e||0!==t?e+t:a(y.left,y.right)):O=P-2*(0!==r||0!==n?r+n:a(y.top,y.bottom))}await m({...t,availableWidth:j,availableHeight:O});let k=await c.getDimensions(d.floating);return E!==k.width||P!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...i}=f(e,t);switch(n){case"referenceHidden":{let e=O(await M(t,{...i,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:j(e)}}}case"escaped":{let e=O(await M(t,{...i,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:j(e)}}}default:return{}}}}}(e),options:[e,t]}),eL=(e,t)=>({...eM(e),options:[e,t]});var eI=r(4163),eF=r(687),eU=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:i=5,...o}=e;return(0,eF.jsx)(eI.sG.svg,{...o,ref:t,width:n,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eU.displayName="Arrow";var eV=r(8599),eB=r(1273),ez=r(3495),eH=r(6156),e$=r(8853),eW="Popper",[eK,eG]=(0,eB.A)(eW),[eX,eq]=eK(eW),eY=e=>{let{__scopePopper:t,children:r}=e,[i,o]=n.useState(null);return(0,eF.jsx)(eX,{scope:t,anchor:i,onAnchorChange:o,children:r})};eY.displayName=eW;var eZ="PopperAnchor",eQ=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:i,...o}=e,a=eq(eZ,r),s=n.useRef(null),l=(0,eV.s)(t,s);return n.useEffect(()=>{a.onAnchorChange(i?.current||s.current)}),i?null:(0,eF.jsx)(eI.sG.div,{...o,ref:l})});eQ.displayName=eZ;var eJ="PopperContent",[e0,e1]=eK(eJ),e2=n.forwardRef((e,t)=>{let{__scopePopper:r,side:i="bottom",sideOffset:s=0,align:u="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:h=[],collisionPadding:p=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:y="optimized",onPlaced:v,...b}=e,w=eq(eJ,r),[x,E]=n.useState(null),P=(0,eV.s)(t,e=>E(e)),[R,T]=n.useState(null),S=(0,e$.X)(R),A=S?.width??0,_=S?.height??0,M="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},O=Array.isArray(h)?h:[h],j=O.length>0,k={padding:M,boundary:O.filter(e9),altBoundary:j},{refs:C,floatingStyles:D,placement:N,isPositioned:L,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:i=[],platform:o,elements:{reference:a,floating:s}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=n.useState(i);eT(h,i)||p(i);let[m,g]=n.useState(null),[y,v]=n.useState(null),b=n.useCallback(e=>{e!==P.current&&(P.current=e,g(e))},[]),w=n.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),x=a||m,E=s||y,P=n.useRef(null),R=n.useRef(null),T=n.useRef(d),S=null!=u,A=e_(u),_=e_(o),M=e_(c),O=n.useCallback(()=>{if(!P.current||!R.current)return;let e={placement:t,strategy:r,middleware:h};_.current&&(e.platform=_.current),eE(P.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};j.current&&!eT(T.current,t)&&(T.current=t,eP.flushSync(()=>{f(t)}))})},[h,t,r,_,M]);eR(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let j=n.useRef(!1);eR(()=>(j.current=!0,()=>{j.current=!1}),[]),eR(()=>{if(x&&(P.current=x),E&&(R.current=E),x&&E){if(A.current)return A.current(x,E,O);O()}},[x,E,O,A,S]);let k=n.useMemo(()=>({reference:P,floating:R,setReference:b,setFloating:w}),[b,w]),C=n.useMemo(()=>({reference:x,floating:E}),[x,E]),D=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!C.floating)return e;let t=eA(C.floating,d.x),n=eA(C.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...eS(C.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,l,C.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:O,refs:k,elements:C,floatingStyles:D}),[d,O,k,C,D])}({strategy:"fixed",placement:i+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let i;void 0===n&&(n={});let{ancestorScroll:s=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=n,h=ea(e),p=s||u?[...h?en(h):[],...en(t)]:[];p.forEach(e=>{s&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let m=h&&d?function(e,t){let r,n=null,i=I(e);function s(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),s();let f=e.getBoundingClientRect(),{left:h,top:p,width:m,height:g}=f;if(c||t(),!m||!g)return;let y=l(p),v=l(i.clientWidth-(h+m)),b={rootMargin:-y+"px "+-v+"px "+-l(i.clientHeight-(p+g))+"px "+-l(h)+"px",threshold:a(0,o(1,d))||1},w=!0;function x(t){let n=t[0].intersectionRatio;if(n!==d){if(!w)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||ew(f,e.getBoundingClientRect())||u(),w=!1}try{n=new IntersectionObserver(x,{...b,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(x,b)}n.observe(e)}(!0),s}(h,r):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[n]=e;n&&n.target===h&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),r()}),h&&!f&&y.observe(h),y.observe(t));let v=f?ec(e):null;return f&&function t(){let n=ec(e);v&&!ew(v,n)&&r(),v=n,i=requestAnimationFrame(t)}(),r(),()=>{var e;p.forEach(e=>{s&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,f&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===y}),elements:{reference:w.anchor},middleware:[eO({mainAxis:s+_,alignmentAxis:c}),f&&ej({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ek():void 0,...k}),f&&eC({...k}),eD({...k,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:i,height:o}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${i}px`),a.setProperty("--radix-popper-anchor-height",`${o}px`)}}),R&&eL({element:R,padding:d}),e6({arrowWidth:A,arrowHeight:_}),g&&eN({strategy:"referenceHidden",...k})]}),[U,V]=e7(N),B=(0,ez.c)(v);(0,eH.N)(()=>{L&&B?.()},[L,B]);let z=F.arrow?.x,H=F.arrow?.y,$=F.arrow?.centerOffset!==0,[W,K]=n.useState();return(0,eH.N)(()=>{x&&K(window.getComputedStyle(x).zIndex)},[x]),(0,eF.jsx)("div",{ref:C.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:L?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:W,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(e0,{scope:r,placedSide:U,onArrowChange:T,arrowX:z,arrowY:H,shouldHideArrow:$,children:(0,eF.jsx)(eI.sG.div,{"data-side":U,"data-align":V,...b,ref:P,style:{...b.style,animation:L?void 0:"none"}})})})});e2.displayName=eJ;var e3="PopperArrow",e5={top:"bottom",right:"left",bottom:"top",left:"right"},e4=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,i=e1(e3,r),o=e5[i.placedSide];return(0,eF.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eU,{...n,ref:t,style:{...n.style,display:"block"}})})});function e9(e){return null!==e}e4.displayName=e3;var e6=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,a=o?0:e.arrowWidth,s=o?0:e.arrowHeight,[l,u]=e7(r),c={start:"0%",center:"50%",end:"100%"}[u],d=(i.arrow?.x??0)+a/2,f=(i.arrow?.y??0)+s/2,h="",p="";return"bottom"===l?(h=o?c:`${d}px`,p=`${-s}px`):"top"===l?(h=o?c:`${d}px`,p=`${n.floating.height+s}px`):"right"===l?(h=`${-s}px`,p=o?c:`${f}px`):"left"===l&&(h=`${n.floating.width+s}px`,p=o?c:`${f}px`),{data:{x:h,y:p}}}});function e7(e){let[t,r="center"]=e.split("-");return[t,r]}var e8=eY,te=eQ,tt=e2,tr=e4},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return f}});let n=r(5362),i=r(3293),o=r(6759),a=r(1437),s=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},o=r=>{let n,o=r.key;switch(r.type){case"header":o=o.toLowerCase(),n=e.headers[o];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,s.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[o];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(o)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>o(e))||n.some(e=>o(e)))&&i}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,o.parseUrl)(t),n=r.pathname;n&&(n=l(n));let a=r.href;a&&(a=l(a));let s=r.hostname;s&&(s=l(s));let u=r.hash;return u&&(u=l(u)),{...r,pathname:n,hostname:s,href:a,hash:u}}function f(e){let t,r,i=Object.assign({},e.query),o=d(e),{hostname:s,query:u}=o,f=o.pathname;o.hash&&(f=""+f+o.hash);let h=[],p=[];for(let e of((0,n.pathToRegexp)(f,p),p))h.push(e.name);if(s){let e=[];for(let t of((0,n.pathToRegexp)(s,e),e))h.push(t.name)}let m=(0,n.compile)(f,{validate:!1});for(let[r,i]of(s&&(t=(0,n.compile)(s,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[r]=i.map(t=>c(l(t),e.params)):"string"==typeof i&&(u[r]=c(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>h.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(f))for(let t of f.split("/")){let r=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=m(e.params)).split("#",2);t&&(o.hostname=t(e.params)),o.pathname=n,o.hash=(i?"#":"")+(i||""),delete o.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return o.query={...i,...o.query},{newUrl:r,destQuery:u,parsedDestination:o}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},5551:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});var n,i=r(3210),o=r(6156),a=(n||(n=r.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function s({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,s,l]=function({defaultProp:e,onChange:t}){let[r,n]=i.useState(e),o=i.useRef(r),s=i.useRef(t);return a(()=>{s.current=t},[t]),i.useEffect(()=>{o.current!==r&&(s.current?.(r),o.current=r)},[r,o]),[r,n,s]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[c,i.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else s(t)},[u,e,s,l])]}Symbol("RADIX:SYNC_STATE")},5796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},5814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let n=r(740),i=r(687),o=n._(r(3210)),a=r(195),s=r(2142),l=r(9154),u=r(3038),c=r(9289),d=r(6127);r(148);let f=r(3406),h=r(1794),p=r(3690);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function g(e){let t,r,n,[a,g]=(0,o.useOptimistic)(f.IDLE_LINK_STATUS),v=(0,o.useRef)(null),{href:b,as:w,children:x,prefetch:E=null,passHref:P,replace:R,shallow:T,scroll:S,onClick:A,onMouseEnter:_,onTouchStart:M,legacyBehavior:O=!1,onNavigate:j,ref:k,unstable_dynamicOnHover:C,...D}=e;t=x,O&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let N=o.default.useContext(s.AppRouterContext),L=!1!==E,I=null===E?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:F,as:U}=o.default.useMemo(()=>{let e=m(b);return{href:e,as:w?m(w):e}},[b,w]);O&&(r=o.default.Children.only(t));let V=O?r&&"object"==typeof r&&r.ref:k,B=o.default.useCallback(e=>(null!==N&&(v.current=(0,f.mountLinkInstance)(e,F,N,I,L,g)),()=>{v.current&&((0,f.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,f.unmountPrefetchableInstance)(e)}),[L,F,N,I,g]),z={ref:(0,u.useMergedRef)(B,V),onClick(e){O||"function"!=typeof A||A(e),O&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),N&&(e.defaultPrevented||function(e,t,r,n,i,a,s){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,h.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),o.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(r||t,i?"replace":"push",null==a||a,n.current)})}}(e,F,U,v,R,S,j))},onMouseEnter(e){O||"function"!=typeof _||_(e),O&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),N&&L&&(0,f.onNavigationIntent)(e.currentTarget,!0===C)},onTouchStart:function(e){O||"function"!=typeof M||M(e),O&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),N&&L&&(0,f.onNavigationIntent)(e.currentTarget,!0===C)}};return(0,c.isAbsoluteUrl)(U)?z.href=U:O&&!P&&("a"!==r.type||"href"in r.props)||(z.href=(0,d.addBasePath)(U)),n=O?o.default.cloneElement(r,z):(0,i.jsx)("a",{...D,...z,children:t}),(0,i.jsx)(y.Provider,{value:a,children:n})}r(2708);let y=(0,o.createContext)(f.IDLE_LINK_STATUS),v=()=>(0,o.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5942:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(6736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[o,a]=r,[s,l]=t;return(0,i.matchSegment)(s,o)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),a[l]):!!Array.isArray(s)}}});let n=r(4007),i=r(4077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return h},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],i=t.parallelRoutes,a=new Map(i);for(let t in n){let r=n[t],s=r[0],l=(0,o.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let n=u.get(l);if(void 0!==n){let i=e(n,r),o=new Map(u);o.set(l,i),a.set(t,o)}}}let s=t.rsc,l=y(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a,navigatedAt:t.navigatedAt}}}});let n=r(3913),i=r(4077),o=r(3123),a=r(2030),s=r(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,r,a,s,u,f,h,p){return function e(t,r,a,s,u,f,h,p,m,g,y){let v=a[1],b=s[1],w=null!==f?f[2]:null;u||!0===s[4]&&(u=!0);let x=r.parallelRoutes,E=new Map(x),P={},R=null,T=!1,S={};for(let r in b){let a,s=b[r],d=v[r],f=x.get(r),A=null!==w?w[r]:null,_=s[0],M=g.concat([r,_]),O=(0,o.createRouterCacheKey)(_),j=void 0!==d?d[0]:void 0,k=void 0!==f?f.get(O):void 0;if(null!==(a=_===n.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:c(t,d,s,k,u,void 0!==A?A:null,h,p,M,y):m&&0===Object.keys(s[1]).length?c(t,d,s,k,u,void 0!==A?A:null,h,p,M,y):void 0!==d&&void 0!==j&&(0,i.matchSegment)(_,j)&&void 0!==k&&void 0!==d?e(t,k,d,s,u,A,h,p,m,M,y):c(t,d,s,k,u,void 0!==A?A:null,h,p,M,y))){if(null===a.route)return l;null===R&&(R=new Map),R.set(r,a);let e=a.node;if(null!==e){let t=new Map(f);t.set(O,e),E.set(r,t)}let t=a.route;P[r]=t;let n=a.dynamicRequestTree;null!==n?(T=!0,S[r]=n):S[r]=t}else P[r]=s,S[r]=s}if(null===R)return null;let A={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:E,navigatedAt:t};return{route:d(s,P),node:A,dynamicRequestTree:T?d(s,S):null,children:R}}(e,t,r,a,!1,s,u,f,h,[],p)}function c(e,t,r,n,i,u,c,h,p,m){return!i&&(void 0===t||(0,a.isNavigatingToNewRootLayout)(t,r))?l:function e(t,r,n,i,a,l,u,c){let h,p,m,g,y=r[1],v=0===Object.keys(y).length;if(void 0!==n&&n.navigatedAt+s.DYNAMIC_STALETIME_MS>t)h=n.rsc,p=n.loading,m=n.head,g=n.navigatedAt;else if(null===i)return f(t,r,null,a,l,u,c);else if(h=i[1],p=i[3],m=v?a:null,g=t,i[4]||l&&v)return f(t,r,i,a,l,u,c);let b=null!==i?i[2]:null,w=new Map,x=void 0!==n?n.parallelRoutes:null,E=new Map(x),P={},R=!1;if(v)c.push(u);else for(let r in y){let n=y[r],i=null!==b?b[r]:null,s=null!==x?x.get(r):void 0,d=n[0],f=u.concat([r,d]),h=(0,o.createRouterCacheKey)(d),p=e(t,n,void 0!==s?s.get(h):void 0,i,a,l,f,c);w.set(r,p);let m=p.dynamicRequestTree;null!==m?(R=!0,P[r]=m):P[r]=n;let g=p.node;if(null!==g){let e=new Map;e.set(h,g),E.set(r,e)}}return{route:r,node:{lazyData:null,rsc:h,prefetchRsc:null,head:m,prefetchHead:null,loading:p,parallelRoutes:E,navigatedAt:g},dynamicRequestTree:R?d(r,P):null,children:w}}(e,r,n,u,c,h,p,m)}function d(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function f(e,t,r,n,i,a,s){let l=d(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,r,n,i,a,s,l){let u=r[1],c=null!==n?n[2]:null,d=new Map;for(let r in u){let n=u[r],f=null!==c?c[r]:null,h=n[0],p=s.concat([r,h]),m=(0,o.createRouterCacheKey)(h),g=e(t,n,void 0===f?null:f,i,a,p,l),y=new Map;y.set(m,g),d.set(r,y)}let f=0===d.size;f&&l.push(s);let h=null!==n?n[1]:null,p=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==h?h:null,prefetchHead:f?i:[null,null],loading:void 0!==p?p:null,rsc:v(),head:f?v():null,navigatedAt:t}}(e,t,r,n,i,a,s),dynamicRequestTree:l,children:null}}function h(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:a,head:s}=t;a&&function(e,t,r,n,a){let s=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],o=s.children;if(null!==o){let e=o.get(r);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(n,t)){s=e;continue}}}return}!function e(t,r,n,a){if(null===t.dynamicRequestTree)return;let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,r,n,a,s){let l=r[1],u=n[1],c=a[2],d=t.parallelRoutes;for(let t in l){let r=l[t],n=u[t],a=c[t],f=d.get(t),h=r[0],p=(0,o.createRouterCacheKey)(h),g=void 0!==f?f.get(p):void 0;void 0!==g&&(void 0!==n&&(0,i.matchSegment)(h,n[0])&&null!=a?e(g,r,n,a,s):m(r,g,null))}let f=t.rsc,h=a[1];null===f?t.rsc=h:y(f)&&f.resolve(h);let p=t.head;y(p)&&p.resolve(s)}(l,t.route,r,n,a),t.dynamicRequestTree=null);return}let u=r[1],c=n[2];for(let t in r){let r=u[t],n=c[t],o=s.get(t);if(void 0!==o){let t=o.route[0];if((0,i.matchSegment)(r[0],t)&&null!=n)return e(o,r,n,a)}}}(s,r,n,a)}(e,r,n,a,s)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)m(e.route,r,t);else for(let e of n.values())p(e,t);e.dynamicRequestTree=null}function m(e,t,r){let n=e[1],i=t.parallelRoutes;for(let e in n){let t=n[e],a=i.get(e);if(void 0===a)continue;let s=t[0],l=(0,o.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&m(t,u,r)}let a=t.rsc;y(a)&&(null===r?a.resolve(null):a.reject(r));let s=t.head;y(s)&&s.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function v(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=g,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6001:(e,t,r)=>{"use strict";let n;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function o(e){let t=[{},{}];return e?.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function a(e,t,r,n){if("function"==typeof t){let[i,a]=o(n);t=t(void 0!==r?r:e.custom,i,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,a]=o(n);t=t(void 0!==r?r:e.custom,i,a)}return t}function s(e,t,r){let n=e.getProps();return a(n,t,void 0!==r?r:n.custom,e)}function l(e,t){return e?.[t]??e?.default??e}r.d(t,{P:()=>oA});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],f={value:null,addProjectionMetrics:null};function h(e,t){let r=!1,n=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>r=!0,a=d.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,n=new Set,i=!1,o=!1,a=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){a.has(t)&&(c.schedule(t),e()),l++,t(s)}let c={schedule:(e,t=!1,o=!1)=>{let s=o&&i?r:n;return t&&a.add(e),s.has(e)||s.add(e),e},cancel:e=>{n.delete(e),a.delete(e)},process:e=>{if(s=e,i){o=!0;return}i=!0,[r,n]=[n,r],r.forEach(u),t&&f.value&&f.value.frameloop[t].push(l),l=0,r.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?r:void 0),e),{}),{setup:s,read:l,resolveKeyframes:u,preUpdate:h,update:p,preRender:m,render:g,postRender:y}=a,v=()=>{let o=c.useManualTiming?i.timestamp:performance.now();r=!1,c.useManualTiming||(i.delta=n?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),h.process(i),p.process(i),m.process(i),g.process(i),y.process(i),i.isProcessing=!1,r&&t&&(n=!1,e(v))},b=()=>{r=!0,n=!0,i.isProcessing||e(v)};return{schedule:d.reduce((e,t)=>{let n=a[t];return e[t]=(e,t=!1,i=!1)=>(r||b(),n.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)a[d[t]].cancel(e)},state:i,steps:a}}let{schedule:p,cancel:m,state:g,steps:y}=h("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),w=new Set(["width","height","top","left","right","bottom",...v]);function x(e,t){-1===e.indexOf(t)&&e.push(t)}function E(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class P{constructor(){this.subscriptions=[]}add(e){return x(this.subscriptions,e),()=>E(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function R(){n=void 0}let T={now:()=>(void 0===n&&T.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(R)}},S=e=>!isNaN(parseFloat(e)),A={current:void 0};class _{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=T.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=T.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new P);let r=this.events[e].add(t);return"change"===e?()=>{r(),p.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return A.current&&A.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=T.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function M(e,t){return new _(e,t)}let O=e=>Array.isArray(e),j=e=>!!(e&&e.getVelocity);function k(e,t){let r=e.getValue("willChange");if(j(r)&&r.add)return r.add(t);if(!r&&c.WillChange){let r=new c.WillChange("auto");e.addValue("willChange",r),r.add(t)}}let C=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),D="data-"+C("framerAppearId"),N=(e,t)=>r=>t(e(r)),L=(...e)=>e.reduce(N),I=(e,t,r)=>r>t?t:r<e?e:r,F=e=>1e3*e,U=e=>e/1e3,V={layout:0,mainThread:0,waapi:0},B=()=>{},z=()=>{},H=e=>t=>"string"==typeof t&&t.startsWith(e),$=H("--"),W=H("var(--"),K=e=>!!W(e)&&G.test(e.split("/*")[0].trim()),G=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,X={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},q={...X,transform:e=>I(0,1,e)},Y={...X,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>r=>!!("string"==typeof r&&J.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),et=(e,t,r)=>n=>{if("string"!=typeof n)return n;let[i,o,a,s]=n.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(o),[r]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},er=e=>I(0,255,e),en={...X,transform:e=>Math.round(er(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+en.transform(e)+", "+en.transform(t)+", "+en.transform(r)+", "+Z(q.transform(n))+")"},eo={test:ee("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},ea=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),es=ea("deg"),el=ea("%"),eu=ea("px"),ec=ea("vh"),ed=ea("vw"),ef={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},eh={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(r))+", "+Z(q.transform(n))+")"},ep={test:e=>ei.test(e)||eo.test(e)||eh.test(e),parse:e=>ei.test(e)?ei.parse(e):eh.test(e)?eh.parse(e):eo.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):eh.transform(e),getAnimatableNone:e=>{let t=ep.parse(e);return t.alpha=0,ep.transform(t)}},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ey="color",ev=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eb(e){let t=e.toString(),r=[],n={color:[],number:[],var:[]},i=[],o=0,a=t.replace(ev,e=>(ep.test(e)?(n.color.push(o),i.push(ey),r.push(ep.parse(e))):e.startsWith("var(")?(n.var.push(o),i.push("var"),r.push(e)):(n.number.push(o),i.push(eg),r.push(parseFloat(e))),++o,"${}")).split("${}");return{values:r,split:a,indexes:n,types:i}}function ew(e){return eb(e).values}function ex(e){let{split:t,types:r}=eb(e),n=t.length;return e=>{let i="";for(let o=0;o<n;o++)if(i+=t[o],void 0!==e[o]){let t=r[o];t===eg?i+=Z(e[o]):t===ey?i+=ep.transform(e[o]):i+=e[o]}return i}}let eE=e=>"number"==typeof e?0:ep.test(e)?ep.getAnimatableNone(e):e,eP={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(em)?.length||0)>0},parse:ew,createTransformer:ex,getAnimatableNone:function(e){let t=ew(e);return ex(e)(t.map(eE))}};function eR(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function eT(e,t){return r=>r>0?t:e}let eS=(e,t,r)=>e+(t-e)*r,eA=(e,t,r)=>{let n=e*e,i=r*(t*t-n)+n;return i<0?0:Math.sqrt(i)},e_=[eo,ei,eh],eM=e=>e_.find(t=>t.test(e));function eO(e){let t=eM(e);if(B(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!t)return!1;let r=t.parse(e);return t===eh&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,o=0,a=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,s=2*r-n;i=eR(s,n,e+1/3),o=eR(s,n,e),a=eR(s,n,e-1/3)}else i=o=a=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:n}}(r)),r}let ej=(e,t)=>{let r=eO(e),n=eO(t);if(!r||!n)return eT(e,t);let i={...r};return e=>(i.red=eA(r.red,n.red,e),i.green=eA(r.green,n.green,e),i.blue=eA(r.blue,n.blue,e),i.alpha=eS(r.alpha,n.alpha,e),ei.transform(i))},ek=new Set(["none","hidden"]);function eC(e,t){return r=>eS(e,t,r)}function eD(e){return"number"==typeof e?eC:"string"==typeof e?K(e)?eT:ep.test(e)?ej:eI:Array.isArray(e)?eN:"object"==typeof e?ep.test(e)?ej:eL:eT}function eN(e,t){let r=[...e],n=r.length,i=e.map((e,r)=>eD(e)(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}}function eL(e,t){let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=eD(e[i])(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}}let eI=(e,t)=>{let r=eP.createTransformer(t),n=eb(e),i=eb(t);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?ek.has(e)&&!i.values.length||ek.has(t)&&!n.values.length?function(e,t){return ek.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):L(eN(function(e,t){let r=[],n={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],a=e.indexes[o][n[o]],s=e.values[a]??0;r[i]=s,n[o]++}return r}(n,i),i.values),r):(B(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),eT(e,t))};function eF(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?eS(e,t,r):eD(e)(e,t)}let eU=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>p.update(t,e),stop:()=>m(t),now:()=>g.isProcessing?g.timestamp:T.now()}},eV=(e,t,r=10)=>{let n="",i=Math.max(Math.round(t/r),2);for(let t=0;t<i;t++)n+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function eB(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}function ez(e,t,r){var n,i;let o=Math.max(t-5,0);return n=r-e(o),(i=t-o)?1e3/i*n:0}let eH={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function e$(e,t){return e*Math.sqrt(1-t*t)}let eW=["duration","bounce"],eK=["stiffness","damping","mass"];function eG(e,t){return t.some(t=>void 0!==e[t])}function eX(e=eH.visualDuration,t=eH.bounce){let r,n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=n,a=n.keyframes[0],s=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:d,duration:f,velocity:h,isResolvedFromDuration:p}=function(e){let t={velocity:eH.velocity,stiffness:eH.stiffness,damping:eH.damping,mass:eH.mass,isResolvedFromDuration:!1,...e};if(!eG(e,eK)&&eG(e,eW))if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),n=r*r,i=2*I(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:eH.mass,stiffness:n,damping:i}}else{let r=function({duration:e=eH.duration,bounce:t=eH.bounce,velocity:r=eH.velocity,mass:n=eH.mass}){let i,o;B(e<=F(eH.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let a=1-t;a=I(eH.minDamping,eH.maxDamping,a),e=I(eH.minDuration,eH.maxDuration,U(e)),a<1?(i=t=>{let n=t*a,i=n*e;return .001-(n-r)/e$(t,a)*Math.exp(-i)},o=t=>{let n=t*a*e,o=Math.pow(a,2)*Math.pow(t,2)*e,s=Math.exp(-n),l=e$(Math.pow(t,2),a);return(n*r+r-o)*s*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),o=t=>e*e*(r-t)*Math.exp(-t*e));let s=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,o,5/e);if(e=F(e),isNaN(s))return{stiffness:eH.stiffness,damping:eH.damping,duration:e};{let t=Math.pow(s,2)*n;return{stiffness:t,damping:2*a*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:eH.mass}).isResolvedFromDuration=!0}return t}({...n,velocity:-U(n.velocity||0)}),m=h||0,g=c/(2*Math.sqrt(u*d)),y=s-a,v=U(Math.sqrt(u/d)),b=5>Math.abs(y);if(i||(i=b?eH.restSpeed.granular:eH.restSpeed.default),o||(o=b?eH.restDelta.granular:eH.restDelta.default),g<1){let e=e$(v,g);r=t=>s-Math.exp(-g*v*t)*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)r=e=>s-Math.exp(-v*e)*(y+(m+v*y)*e);else{let e=v*Math.sqrt(g*g-1);r=t=>{let r=Math.exp(-g*v*t),n=Math.min(e*t,300);return s-r*((m+g*v*y)*Math.sinh(n)+e*y*Math.cosh(n))/e}}let w={calculatedDuration:p&&f||null,next:e=>{let t=r(e);if(p)l.done=e>=f;else{let n=0===e?m:0;g<1&&(n=0===e?F(m):ez(r,e,t));let a=Math.abs(s-t)<=o;l.done=Math.abs(n)<=i&&a}return l.value=l.done?s:t,l},toString:()=>{let e=Math.min(eB(w),2e4),t=eV(t=>w.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return w}function eq({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){let d,f,h=e[0],p={done:!1,value:h},m=e=>void 0!==s&&e<s||void 0!==l&&e>l,g=e=>void 0===s?l:void 0===l||Math.abs(s-e)<Math.abs(l-e)?s:l,y=r*t,v=h+y,b=void 0===a?v:a(v);b!==v&&(y=b-h);let w=e=>-y*Math.exp(-e/n),x=e=>b+w(e),E=e=>{let t=w(e),r=x(e);p.done=Math.abs(t)<=u,p.value=p.done?b:r},P=e=>{m(p.value)&&(d=e,f=eX({keyframes:[p.value,g(p.value)],velocity:ez(x,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:e=>{let t=!1;return(f||void 0!==d||(t=!0,E(e),P(e)),void 0!==d&&e>=d)?f.next(e-d):(t||E(e),p)}}}eX.applyToOptions=e=>{let t=function(e,t=100,r){let n=r({...e,keyframes:[0,t]}),i=Math.min(eB(n),2e4);return{type:"keyframes",ease:e=>n.next(i*e).value/t,duration:U(i)}}(e,100,eX);return e.ease=t.ease,e.duration=F(t.duration),e.type="keyframes",e};let eY=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function eZ(e,t,r,n){if(e===t&&r===n)return u;let i=t=>(function(e,t,r,n,i){let o,a,s=0;do(o=eY(a=t+(r-t)/2,n,i)-e)>0?r=a:t=a;while(Math.abs(o)>1e-7&&++s<12);return a})(t,0,1,e,r);return e=>0===e||1===e?e:eY(i(e),t,n)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e3=e=>t=>1-e(1-t),e5=eZ(.33,1.53,.69,.99),e4=e3(e5),e9=e2(e4),e6=e=>(e*=2)<1?.5*e4(e):.5*(2-Math.pow(2,-10*(e-1))),e7=e=>1-Math.sin(Math.acos(e)),e8=e3(e7),te=e2(e7),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tr={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e7,circInOut:te,circOut:e8,backIn:e4,backInOut:e9,backOut:e5,anticipate:e6},tn=e=>"string"==typeof e,ti=e=>{if(tt(e)){z(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,r,n,i]=e;return eZ(t,r,n,i)}return tn(e)?(z(void 0!==tr[e],`Invalid easing type '${e}'`,"invalid-easing-type"),tr[e]):e},to=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n};function ta({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){var i;let o=e1(n)?n.map(ti):ti(n),a={done:!1,value:t[0]},s=function(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let o=e.length;if(z(o===t.length,"Both input and output ranges must be the same length","range-length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=function(e,t,r){let n=[],i=r||c.mix||eF,o=e.length-1;for(let r=0;r<o;r++){let o=i(e[r],e[r+1]);t&&(o=L(Array.isArray(t)?t[r]||u:t,o)),n.push(o)}return n}(t,n,i),l=s.length,d=r=>{if(a&&r<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(r<e[n+1]);n++);let i=to(e[n],e[n+1],r);return s[n](i)};return r?t=>d(I(e[0],e[o-1],t)):d}((i=r&&r.length===t.length?r:function(e){let t=[0];return!function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=to(0,t,n);e.push(eS(r,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=s(t),a.done=t>=e,a)}}let ts=e=>null!==e;function tl(e,{repeat:t,repeatType:r="loop"},n,i=1){let o=e.filter(ts),a=i<0||t&&"loop"!==r&&t%2==1?0:o.length-1;return a&&void 0!==n?n:o[a]}let tu={decay:eq,inertia:eq,tween:ta,keyframes:ta,spring:eX};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let tf=e=>e/100;class th extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==T.now()&&this.tick(T.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},V.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ta,repeat:r=0,repeatDelay:n=0,repeatType:i,velocity:o=0}=e,{keyframes:a}=e,s=t||ta;s!==ta&&"number"!=typeof a[0]&&(this.mixKeyframes=L(tf,eF(a[0],a[1])),a=[0,100]);let l=s({...e,keyframes:a});"mirror"===i&&(this.mirroredGenerator=s({...e,keyframes:[...a].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=eB(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(r+1)-n,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:r,totalDuration:n,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:s}=this;if(null===this.startTime)return r.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:f,type:h,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,b=r;if(c){let e=Math.min(this.currentTime,n)/a,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(r=1-r,f&&(r-=f/a)):"mirror"===d&&(b=o)),v=I(0,1,r)*a}let w=y?{done:!1,value:u[0]}:b.next(v);i&&(w.value=i(w.value));let{done:x}=w;y||null===s||(x=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let E=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return E&&h!==eq&&(w.value=tl(u,this.options,m,this.speed)),p&&p(w.value),E&&this.finish(),w}then(e,t){return this.finished.then(e,t)}get duration(){return U(this.calculatedDuration)}get time(){return U(this.currentTime)}set time(e){e=F(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(T.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=U(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eU,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=t??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(T.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,V.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tp=e=>180*e/Math.PI,tm=e=>ty(tp(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tp(Math.atan(e[1])),skewY:e=>tp(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},ty=e=>((e%=360)<0&&(e+=360),e),tv=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tb=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tw={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tv,scaleY:tb,scale:e=>(tv(e)+tb(e))/2,rotateX:e=>ty(tp(Math.atan2(e[6],e[5]))),rotateY:e=>ty(tp(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tp(Math.atan(e[4])),skewY:e=>tp(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tx(e){return+!!e.includes("scale")}function tE(e,t){let r,n;if(!e||"none"===e)return tx(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)r=tw,n=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=tg,n=t}if(!n)return tx(t);let o=r[t],a=n[1].split(",").map(tR);return"function"==typeof o?o(a):a[o]}let tP=(e,t)=>{let{transform:r="none"}=getComputedStyle(e);return tE(r,t)};function tR(e){return parseFloat(e.trim())}let tT=e=>e===X||e===eu,tS=new Set(["x","y","z"]),tA=v.filter(e=>!tS.has(e)),t_={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tE(t,"x"),y:(e,{transform:t})=>tE(t,"y")};t_.translateX=t_.x,t_.translateY=t_.y;let tM=new Set,tO=!1,tj=!1,tk=!1;function tC(){if(tj){let e=Array.from(tM).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=function(e){let t=[];return tA.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(+!!r.startsWith("scale")))}),t}(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{e.getValue(t)?.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tj=!1,tO=!1,tM.forEach(e=>e.complete(tk)),tM.clear()}function tD(){tM.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tj=!0)})}class tN{constructor(e,t,r,n,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=n,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tM.add(this),tO||(tO=!0,p.read(tD),p.resolveKeyframes(tC))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:n}=this;if(null===e[0]){let i=n?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(r&&t){let n=r.readValue(t,o);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=o),n&&void 0===i&&n.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tM.delete(this)}cancel(){"scheduled"===this.state&&(tM.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tL=e=>e.startsWith("--");function tI(e){let t;return()=>(void 0===t&&(t=e()),t)}let tF=tI(()=>void 0!==window.ScrollTimeline),tU={},tV=function(e,t){let r=tI(e);return()=>tU[t]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tB=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,tz={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tB([0,.65,.55,1]),circOut:tB([.55,0,1,.45]),backIn:tB([.31,.01,.66,-.59]),backOut:tB([.33,1.53,.69,.99])};function tH(e){return"function"==typeof e&&"applyToOptions"in e}class t$ extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:r,keyframes:n,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:a,onComplete:s}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,z("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:e,...t}){return tH(e)&&tV()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,r,{delay:n=0,duration:i=300,repeat:o=0,repeatType:a="loop",ease:s="easeOut",times:l}={},u){let c={[t]:r};l&&(c.offset=l);let d=function e(t,r){if(t)return"function"==typeof t?tV()?eV(t,r):"ease-out":tt(t)?tB(t):Array.isArray(t)?t.map(t=>e(t,r)||tz.easeOut):tz[t]}(s,i);Array.isArray(d)&&(c.easing=d),f.value&&V.waapi++;let h={delay:n,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"};u&&(h.pseudoElement=u);let p=e.animate(c,h);return f.value&&p.finished.finally(()=>{V.waapi--}),p}(t,r,n,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,r){tL(t)?e.style.setProperty(t,r):e.style[t]=r}(t,r,e),this.animation.cancel()}s?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return U(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return U(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=F(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tF())?(this.animation.timeline=e,u):t(this)}}let tW={anticipate:e6,backInOut:e9,circInOut:te};class tK extends t${constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tW&&(e.ease=tW[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:r,onComplete:n,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new th({...o,autoplay:!1}),s=F(this.finishedTime??this.time);t.setWithVelocity(a.sample(s-10).value,a.sample(s).value,10),a.stop()}}let tG=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eP.test(e)||"0"===e)&&!e.startsWith("url("));var tX,tq,tY=r(8171);let tZ=new Set(["opacity","clipPath","filter","transform"]),tQ=tI(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends td{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:o="loop",keyframes:a,name:s,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=T.now();let d={autoplay:e,delay:t,type:r,repeat:n,repeatDelay:i,repeatType:o,name:s,motionValue:l,element:u,...c},f=u?.KeyframeResolver||tN;this.keyframeResolver=new f(a,(e,t,r)=>this.onKeyframesResolved(e,t,d,!r),s,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,r,n){this.keyframeResolver=void 0;let{name:i,type:o,velocity:a,delay:s,isHandoff:l,onUpdate:d}=r;this.resolvedAt=T.now(),!function(e,t,r,n){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],a=tG(i,t),s=tG(o,t);return B(a===s,`You are trying to animate ${t} from "${i}" to "${o}". "${a?o:i}" is not an animatable value.`,"value-not-animatable"),!!a&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||tH(r))&&n)}(e,i,o,a)&&((c.instantAnimations||!s)&&d?.(tl(e,r,t)),e[0]=e[e.length-1],r.duration=0,r.repeat=0);let f={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...r,keyframes:e},h=!l&&function(e){let{motionValue:t,name:r,repeatDelay:n,repeatType:i,damping:o,type:a}=e;if(!(0,tY.s)(t?.owner?.current))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return tQ()&&r&&tZ.has(r)&&("transform"!==r||!l)&&!s&&!n&&"mirror"!==i&&0!==o&&"inertia"!==a}(f)?new tK({...f,element:f.motionValue.owner.current}):new th(f);h.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tk=!0,tD(),tC(),tk=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t3={type:"keyframes",duration:.8},t5={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t4=(e,{keyframes:t})=>t.length>2?t3:b.has(e)?e.startsWith("scale")?t2(t[1]):t1:t5,t9=(e,t,r,n={},i,o)=>a=>{let s=l(n,e)||{},u=s.delay||n.delay||0,{elapsed:d=0}=n;d-=F(u);let f={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...s,delay:-d,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{a(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&Object.assign(f,t4(e,f)),f.duration&&(f.duration=F(f.duration)),f.repeatDelay&&(f.repeatDelay=F(f.repeatDelay)),void 0!==f.from&&(f.keyframes[0]=f.from);let h=!1;if(!1!==f.type&&(0!==f.duration||f.repeatDelay)||(f.duration=0,0===f.delay&&(h=!0)),(c.instantAnimations||c.skipAnimations)&&(h=!0,f.duration=0,f.delay=0),f.allowFlatten=!s.type&&!s.ease,h&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:r="loop"},n){let i=e.filter(t0),o=t&&"loop"!==r&&t%2==1?0:i.length-1;return i[o]}(f.keyframes,s);if(void 0!==e)return void p.update(()=>{f.onUpdate(e),f.onComplete()})}return s.isSync?new th(f):new tJ(f)};function t6(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:a,...u}=t;n&&(o=n);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let n=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(d,t))continue;let a={delay:r,...l(o||{},t)},s=n.get();if(void 0!==s&&!n.isAnimating&&!Array.isArray(i)&&i===s&&!a.velocity)continue;let f=!1;if(window.MotionHandoffAnimation){let r=e.props[D];if(r){let e=window.MotionHandoffAnimation(r,t,p);null!==e&&(a.startTime=e,f=!0)}}k(e,t),n.start(t9(t,n,i,e.shouldReduceMotion&&w.has(t)?{type:!1}:a,e,f));let h=n.animation;h&&c.push(h)}return a&&Promise.all(c).then(()=>{p.update(()=>{a&&function(e,t){let{transitionEnd:r={},transition:n={},...i}=s(e,t)||{};for(let t in i={...i,...r}){var o;let r=O(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,M(r))}}(e,a)})}),c}function t7(e,t,r={}){let n=s(e,t,"exit"===r.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let o=n?()=>Promise.all(t6(e,n,r)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:o=0,staggerChildren:a,staggerDirection:s}=i;return function(e,t,r=0,n=0,i=0,o=1,a){let s=[],l=e.variantChildren.size,u=(l-1)*i,c="function"==typeof n,d=c?e=>n(e,l):1===o?(e=0)=>e*i:(e=0)=>u-e*i;return Array.from(e.variantChildren).sort(t8).forEach((e,i)=>{e.notify("AnimationStart",t),s.push(t7(e,t,{...a,delay:r+(c?0:n)+d(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,n,o,a,s,r)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([o(),a(r.delay)]);{let[e,t]="beforeChildren"===l?[o,a]:[a,o];return e().then(()=>t())}}function t8(e,t){return e.sortNodePosition(t)}function re(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function rt(e){return"string"==typeof e||Array.isArray(e)}let rr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],rn=["initial",...rr],ri=rn.length,ro=[...rr].reverse(),ra=rr.length;function rs(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rl(){return{animate:rs(!0),whileInView:rs(),whileHover:rs(),whileTap:rs(),whileDrag:rs(),whileFocus:rs(),exit:rs()}}class ru{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rc extends ru{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>t7(e,t,r)));else if("string"==typeof t)n=t7(e,t,r);else{let i="function"==typeof t?s(e,t,r.custom):t;n=Promise.all(t6(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=rl(),n=!0,o=t=>(r,n)=>{let i=s(e,n,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...n}=i;r={...r,...n,...t}}return r};function a(a){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<ri;e++){let n=rn[e],i=t.props[n];(rt(i)||!1===i)&&(r[n]=i)}return r}(e.parent)||{},c=[],d=new Set,f={},h=1/0;for(let t=0;t<ra;t++){var p,m;let s=ro[t],g=r[s],y=void 0!==l[s]?l[s]:u[s],v=rt(y),b=s===a?g.isActive:null;!1===b&&(h=t);let w=y===u[s]&&y!==l[s]&&v;if(w&&n&&e.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...f},!g.isActive&&null===b||!y&&!g.prevProp||i(y)||"boolean"==typeof y)continue;let x=(p=g.prevProp,"string"==typeof(m=y)?m!==p:!!Array.isArray(m)&&!re(m,p)),E=x||s===a&&g.isActive&&!w&&v||t>h&&v,P=!1,R=Array.isArray(y)?y:[y],T=R.reduce(o(s),{});!1===b&&(T={});let{prevResolvedValues:S={}}=g,A={...S,...T},_=t=>{E=!0,d.has(t)&&(P=!0,d.delete(t)),g.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in A){let t=T[e],r=S[e];if(f.hasOwnProperty(e))continue;let n=!1;(O(t)&&O(r)?re(t,r):t===r)?void 0!==t&&d.has(e)?_(e):g.protectedKeys[e]=!0:null!=t?_(e):d.add(e)}g.prevProp=y,g.prevResolvedValues=T,g.isActive&&(f={...f,...T}),n&&e.blockInitialAnimation&&(E=!1);let M=!(w&&x)||P;E&&M&&c.push(...R.map(e=>({animation:e,options:{type:s}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let r=s(e,Array.isArray(l.initial)?l.initial[0]:l.initial);r&&r.transition&&(t.transition=r.transition)}d.forEach(r=>{let n=e.getBaseTarget(r),i=e.getValue(r);i&&(i.liveStyle=!0),t[r]=n??null}),c.push({animation:t})}let g=!!c.length;return n&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),n=!1,g?t(c):Promise.resolve()}return{animateChanges:a,setActive:function(t,n){if(r[t].isActive===n)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,n)),r[t].isActive=n;let i=a(t);for(let e in r)r[e].protectedKeys={};return i},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=rl(),n=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rd=0;class rf extends ru{constructor(){super(...arguments),this.id=rd++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let rh={x:!1,y:!1};function rp(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let rm=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rg(e){return{point:{x:e.pageX,y:e.pageY}}}let ry=e=>t=>rm(t)&&e(t,rg(t));function rv(e,t,r,n){return rp(e,t,ry(r),n)}function rb({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function rw(e){return e.max-e.min}function rx(e,t,r,n=.5){e.origin=n,e.originPoint=eS(t.min,t.max,e.origin),e.scale=rw(r)/rw(t),e.translate=eS(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rE(e,t,r,n){rx(e.x,t.x,r.x,n?n.originX:void 0),rx(e.y,t.y,r.y,n?n.originY:void 0)}function rP(e,t,r){e.min=r.min+t.min,e.max=e.min+rw(t)}function rR(e,t,r){e.min=t.min-r.min,e.max=e.min+rw(t)}function rT(e,t,r){rR(e.x,t.x,r.x),rR(e.y,t.y,r.y)}let rS=()=>({translate:0,scale:1,origin:0,originPoint:0}),rA=()=>({x:rS(),y:rS()}),r_=()=>({min:0,max:0}),rM=()=>({x:r_(),y:r_()});function rO(e){return[e("x"),e("y")]}function rj(e){return void 0===e||1===e}function rk({scale:e,scaleX:t,scaleY:r}){return!rj(e)||!rj(t)||!rj(r)}function rC(e){return rk(e)||rD(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function rD(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function rN(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function rL(e,t=0,r=1,n,i){e.min=rN(e.min,t,r,n,i),e.max=rN(e.max,t,r,n,i)}function rI(e,{x:t,y:r}){rL(e.x,t.translate,t.scale,t.originPoint),rL(e.y,r.translate,r.scale,r.originPoint)}function rF(e,t){e.min=e.min+t,e.max=e.max+t}function rU(e,t,r,n,i=.5){let o=eS(e.min,e.max,i);rL(e,t,r,o,n)}function rV(e,t){rU(e.x,t.x,t.scaleX,t.scale,t.originX),rU(e.y,t.y,t.scaleY,t.scale,t.originY)}function rB(e,t){return rb(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let rz=({current:e})=>e?e.ownerDocument.defaultView:null;function rH(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let r$=(e,t)=>Math.abs(e-t);class rW{constructor(e,t,{transformPagePoint:r,contextWindow:n=window,dragSnapToOrigin:i=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rX(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(r$(e.x,t.x)**2+r$(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=g;this.history.push({...n,timestamp:i});let{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rK(t,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rX("pointercancel"===e.type?this.lastMoveEventInfo:rK(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,o),n&&n(e,o)},!rm(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.distanceThreshold=o,this.contextWindow=n||window;let a=rK(rg(e),this.transformPagePoint),{point:s}=a,{timestamp:l}=g;this.history=[{...s,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,rX(a,this.history)),this.removeListeners=L(rv(this.contextWindow,"pointermove",this.handlePointerMove),rv(this.contextWindow,"pointerup",this.handlePointerUp),rv(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function rK(e,t){return t?{point:t(e.point)}:e}function rG(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rX({point:e},t){return{point:e,delta:rG(e,rq(t)),offset:rG(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=rq(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>F(.1)));)r--;if(!n)return{x:0,y:0};let o=U(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let a={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function rq(e){return e[e.length-1]}function rY(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function rZ(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function rQ(e,t,r){return{min:rJ(e,t),max:rJ(e,r)}}function rJ(e,t){return"number"==typeof e?e:e[t]||0}let r0=new WeakMap;class r1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rM(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:r}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rW(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rg(e).point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(rh[e])return null;else return rh[e]=!0,()=>{rh[e]=!1};return rh.x||rh.y?null:(rh.x=rh.y=!0,()=>{rh.x=rh.y=!1})}(r),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rO(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];n&&(t=rw(n)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&p.postRender(()=>i(e,t)),k(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(a),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>rO(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:r,contextWindow:rz(this.visualElement)})}stop(e,t){let r=e||this.latestPointerEvent,n=t||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!n||!r)return;let{velocity:o}=n;this.startAnimation(o);let{onDragEnd:a}=this.getProps();a&&p.postRender(()=>a(r,n))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!r2(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?eS(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?eS(r,e,n.max):Math.min(e,r)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;e&&rH(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:rY(e.x,r,i),y:rY(e.y,t,n)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:rQ(e,"left","right"),y:rQ(e,"top","bottom")}}(t),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&rO(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!rH(t))return!1;let n=t.current;z(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,r){let n=rB(e,r),{scroll:i}=t;return i&&(rF(n.x,i.offset.x),rF(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),a=(e=i.layout.layoutBox,{x:rZ(e.x,o.x),y:rZ(e.y,o.y)});if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=rb(e))}return a}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{};return Promise.all(rO(a=>{if(!r2(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return k(this.visualElement,e),r.start(t9(e,r,0,t,this.visualElement,!1))}stopAnimation(){rO(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rO(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){rO(t=>{let{drag:r}=this.getProps();if(!r2(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[t];i.set(e[t]-eS(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!rH(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};rO(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();n[e]=function(e,t){let r=.5,n=rw(e),i=rw(t);return i>n?r=to(t.min,t.max-n,e.min):n>i&&(r=to(e.min,e.max-i,t.min)),I(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rO(t=>{if(!r2(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];r.set(eS(i,o,n[t]))})}addListeners(){if(!this.visualElement.current)return;r0.set(this.visualElement,this);let e=rv(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();rH(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),p.read(t);let i=rp(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rO(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function r2(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class r3 extends ru{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new r1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let r5=e=>(t,r)=>{e&&p.postRender(()=>e(t,r))};class r4 extends ru{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new rW(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rz(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:r5(e),onStart:r5(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&p.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=rv(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var r9=r(687);let{schedule:r6}=h(queueMicrotask,!1);var r7=r(3210),r8=r(6044),ne=r(2157);let nt=(0,r7.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nn(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ni={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let r=nn(e,t.target.x),n=nn(e,t.target.y);return`${r}% ${n}%`}},no={},na=!1;class ns extends r7.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;for(let e in nu)no[e]=nu[e],$(e)&&(no[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),na&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,{projection:o}=r;return o&&(o.isPresent=i,na=!0,n||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||p.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),r6.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nl(e){let[t,r]=(0,r8.xQ)(),n=(0,r7.useContext)(ne.L);return(0,r9.jsx)(ns,{...e,layoutGroup:n,switchLayoutGroup:(0,r7.useContext)(nt),isPresent:t,safeToRemove:r})}let nu={borderRadius:{...ni,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ni,borderTopRightRadius:ni,borderBottomLeftRadius:ni,borderBottomRightRadius:ni,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=eP.parse(e);if(n.length>5)return e;let i=eP.createTransformer(e),o=+("number"!=typeof n[0]),a=r.x.scale*t.x,s=r.y.scale*t.y;n[0+o]/=a,n[1+o]/=s;let l=eS(a,s,.5);return"number"==typeof n[2+o]&&(n[2+o]/=l),"number"==typeof n[3+o]&&(n[3+o]/=l),i(n)}}};var nc=r(4479);function nd(e){return(0,nc.G)(e)&&"ownerSVGElement"in e}let nf=(e,t)=>e.depth-t.depth;class nh{constructor(){this.children=[],this.isDirty=!1}add(e){x(this.children,e),this.isDirty=!0}remove(e){E(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nf),this.isDirty=!1,this.children.forEach(e)}}function np(e){return j(e)?e.get():e}let nm=["TopLeft","TopRight","BottomLeft","BottomRight"],ng=nm.length,ny=e=>"string"==typeof e?parseFloat(e):e,nv=e=>"number"==typeof e||eu.test(e);function nb(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nw=nE(0,.5,e8),nx=nE(.5,.95,u);function nE(e,t,r){return n=>n<e?0:n>t?1:r(to(e,t,n))}function nP(e,t){e.min=t.min,e.max=t.max}function nR(e,t){nP(e.x,t.x),nP(e.y,t.y)}function nT(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nS(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function nA(e,t,[r,n,i],o,a){!function(e,t=0,r=1,n=.5,i,o=e,a=e){if(el.test(t)&&(t=parseFloat(t),t=eS(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let s=eS(o.min,o.max,n);e===o&&(s-=t),e.min=nS(e.min,t,r,s,i),e.max=nS(e.max,t,r,s,i)}(e,t[r],t[n],t[i],t.scale,o,a)}let n_=["x","scaleX","originX"],nM=["y","scaleY","originY"];function nO(e,t,r,n){nA(e.x,t,n_,r?r.x:void 0,n?n.x:void 0),nA(e.y,t,nM,r?r.y:void 0,n?n.y:void 0)}function nj(e){return 0===e.translate&&1===e.scale}function nk(e){return nj(e.x)&&nj(e.y)}function nC(e,t){return e.min===t.min&&e.max===t.max}function nD(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nN(e,t){return nD(e.x,t.x)&&nD(e.y,t.y)}function nL(e){return rw(e.x)/rw(e.y)}function nI(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nF{constructor(){this.members=[]}add(e){x(this.members,e),e.scheduleRender()}remove(e){if(E(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nV=["","X","Y","Z"],nB=0;function nz(e,t,r,n){let{latestValues:i}=t;i[e]&&(r[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function nH({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=t?.()){this.id=nB++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,f.value&&(nU.nodes=nU.calculatedTargetDeltas=nU.calculatedProjections=0),this.nodes.forEach(nK),this.nodes.forEach(nJ),this.nodes.forEach(n0),this.nodes.forEach(nG),f.addProjectionMetrics&&f.addProjectionMetrics(nU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nh)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new P),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=nd(t)&&!(nd(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:r,layout:n,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||r)&&(this.isLayoutDirty=!0),e){let r,n=0,i=()=>this.root.updateBlockedByResize=!1;p.read(()=>{n=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==n&&(n=e,this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=T.now(),n=({timestamp:i})=>{let o=i-r;o>=250&&(m(n),e(o-t))};return p.setup(n,!0),()=>m(n)}(i,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(nQ)))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&i&&(r||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||n9,{onLayoutAnimationStart:a,onLayoutAnimationComplete:s}=i.getProps(),u=!this.targetLayout||!nN(this.targetLayout,n),c=!t&&r;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(o,"layout"),onPlay:a,onComplete:s};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||nQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let n=r.props[D];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",p,!(e||r))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nq);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nY);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nZ),this.nodes.forEach(n$),this.nodes.forEach(nW)):this.nodes.forEach(nY),this.clearAllSnapshots();let e=T.now();g.delta=I(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,r6.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nX),this.sharedNodes.forEach(n2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rw(this.snapshot.measuredBox.x)||rw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rM(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!nk(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;e&&this.instance&&(t||rC(this.latestValues)||o)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),n8((t=n).x),n8(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return rM();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(rF(t.x,e.offset.x),rF(t.y,e.offset.y))}return t}removeElementScroll(e){let t=rM();if(nR(t,e),this.scroll?.wasRoot)return t;for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:i,options:o}=n;n!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&nR(t,e),rF(t.x,i.offset.x),rF(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let r=rM();nR(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rV(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),rC(n.latestValues)&&rV(r,n.latestValues)}return rC(this.latestValues)&&rV(r,this.latestValues),r}removeTransform(e){let t=rM();nR(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!rC(r.latestValues))continue;rk(r.latestValues)&&r.updateSnapshot();let n=rM();nR(n,r.measurePageBox()),nO(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return rC(this.latestValues)&&nO(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==t;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:i}=this.options;if(this.layout&&(n||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rM(),this.relativeTargetOrigin=rM(),rT(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nR(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=rM(),this.targetWithTransforms=rM()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,a,s;this.forceRelativeParentToResolveTarget(),o=this.target,a=this.relativeTarget,s=this.relativeParent.target,rP(o.x,a.x,s.x),rP(o.y,a.y,s.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nR(this.target,this.layout.layoutBox),rI(this.target,this.targetDelta)):nR(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rM(),this.relativeTargetOrigin=rM(),rT(this.relativeTargetOrigin,this.target,e.target),nR(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}f.value&&nU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||rk(this.parent.latestValues)||rD(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===g.timestamp&&(r=!1),r)return;let{layout:n,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||i))return;nR(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(e,t,r,n=!1){let i,o,a=r.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){o=(i=r[s]).projectionDelta;let{visualElement:a}=i.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rV(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,rI(e,o)),n&&rC(i.latestValues)&&rV(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=rM());let{target:s}=e;if(!s){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nT(this.prevProjectionDelta.x,this.projectionDelta.x),nT(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rE(this.projectionDelta,this.layoutCorrected,s,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&nI(this.projectionDelta.x,this.prevProjectionDelta.x)&&nI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",s)),f.value&&nU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rA(),this.projectionDelta=rA(),this.projectionDeltaWithTransform=rA()}setAnimationOrigin(e,t=!1){let r,n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},a=rA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=rM(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(n4));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(n3(a.x,e.x,n),n3(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,f,h,p,m,g;rT(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,m=s,g=n,n5(h.x,p.x,m.x,g),n5(h.y,p.y,m.y,g),r&&(u=this.relativeTarget,f=r,nC(u.x,f.x)&&nC(u.y,f.y))&&(this.isProjectionDirty=!1),r||(r=rM()),nR(r,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,r,n,i,o){i?(e.opacity=eS(0,r.opacity??1,nw(n)),e.opacityExit=eS(t.opacity??1,0,nx(n))):o&&(e.opacity=eS(t.opacity??1,r.opacity??1,n));for(let i=0;i<ng;i++){let o=`border${nm[i]}Radius`,a=nb(t,o),s=nb(r,o);(void 0!==a||void 0!==s)&&(a||(a=0),s||(s=0),0===a||0===s||nv(a)===nv(s)?(e[o]=Math.max(eS(ny(a),ny(s),n),0),(el.test(s)||el.test(a))&&(e[o]+="%")):e[o]=s)}(t.rotate||r.rotate)&&(e.rotate=eS(t.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{nr.hasAnimatedSinceResize=!0,V.layout++,this.motionValue||(this.motionValue=M(0)),this.currentAnimation=function(e,t,r){let n=j(e)?e:M(e);return n.start(t9("",n,t,r)),n.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{V.layout--},onComplete:()=>{V.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&ie(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rM();let t=rw(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=rw(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nR(t,r),rV(t,i),rE(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nF),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let n={};r.z&&nz("z",e,n,this.animationValues);for(let t=0;t<nV.length;t++)nz(`rotate${nV[t]}`,e,n,this.animationValues),nz(`skew${nV[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let r=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=np(t?.pointerEvents)||"",e.transform=r?r(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=np(t?.pointerEvents)||""),this.hasProjected&&!rC(this.latestValues)&&(e.transform=r?r({},""):"none",this.hasProjected=!1);return}e.visibility="";let i=n.animationValues||n.latestValues;this.applyTransformsToTarget();let o=function(e,t,r){let n="",i=e.x.translate/t.x,o=e.y.translate/t.y,a=r?.z||0;if((i||o||a)&&(n=`translate3d(${i}px, ${o}px, ${a}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:a,skewY:s}=r;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),i&&(n+=`rotateX(${i}deg) `),o&&(n+=`rotateY(${o}deg) `),a&&(n+=`skewX(${a}deg) `),s&&(n+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(n+=`scale(${s}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);r&&(o=r(i,o)),e.transform=o;let{x:a,y:s}=this.projectionDelta;for(let t in e.transformOrigin=`${100*a.origin}% ${100*s.origin}% 0`,n.animationValues?e.opacity=n===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=n===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,no){if(void 0===i[t])continue;let{correct:r,applyTo:a,isCSSVariable:s}=no[t],l="none"===o?i[t]:r(i[t],n);if(a){let t=a.length;for(let r=0;r<t;r++)e[a[r]]=l}else s?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=n===this?np(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(nq),this.root.sharedNodes.clear()}}}function n$(e){e.updateLayout()}function nW(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:n}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?rO(e=>{let n=o?t.measuredBox[e]:t.layoutBox[e],i=rw(n);n.min=r[e].min,n.max=n.min+i}):ie(i,t.layoutBox,r)&&rO(n=>{let i=o?t.measuredBox[n]:t.layoutBox[n],a=rw(r[n]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=rA();rE(a,r,t.layoutBox);let s=rA();o?rE(s,e.applyTransform(n,!0),t.measuredBox):rE(s,r,t.layoutBox);let l=!nk(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let a=rM();rT(a,t.layoutBox,i.layoutBox);let s=rM();rT(s,r,o.layoutBox),nN(a,s)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:t,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function nK(e){f.value&&nU.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nG(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nX(e){e.clearSnapshot()}function nq(e){e.clearMeasurements()}function nY(e){e.isLayoutDirty=!1}function nZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function nQ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function nJ(e){e.resolveTargetDelta()}function n0(e){e.calcProjection()}function n1(e){e.resetSkewAndRotation()}function n2(e){e.removeLeadSnapshot()}function n3(e,t,r){e.translate=eS(t.translate,0,r),e.scale=eS(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function n5(e,t,r,n){e.min=eS(t.min,r.min,n),e.max=eS(t.max,r.max,n)}function n4(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let n9={duration:.45,ease:[.4,0,.1,1]},n6=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),n7=n6("applewebkit/")&&!n6("chrome/")?Math.round:u;function n8(e){e.min=n7(e.min),e.max=n7(e.max)}function ie(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nL(t)-nL(r)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=nH({attachResizeListener:(e,t)=>rp(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},io=nH({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function ia(e,t){let r=function(e,t,r){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,r=(void 0)??t.querySelectorAll(e);return r?Array.from(r):[]}return Array.from(e)}(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function is(e){return!("touch"===e.pointerType||rh.x||rh.y)}function il(e,t,r){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&p.postRender(()=>i(t,rg(t)))}class iu extends ru{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=ia(e,r),a=e=>{if(!is(e))return;let{target:r}=e,n=t(r,e);if("function"!=typeof n||!r)return;let o=e=>{is(e)&&(n(e),r.removeEventListener("pointerleave",o))};r.addEventListener("pointerleave",o,i)};return n.forEach(e=>{e.addEventListener("pointerenter",a,i)}),o}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends ru{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=L(rp(this.node.current,"focus",()=>this.onFocus()),rp(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let id=(e,t)=>!!t&&(e===t||id(e,t.parentElement)),ih=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function ig(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iy=(e,t)=>{let r=e.currentTarget;if(!r)return;let n=im(()=>{if(ip.has(r))return;ig(r,"down");let e=im(()=>{ig(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>ig(r,"cancel"),t)});r.addEventListener("keydown",n,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),t)};function iv(e){return rm(e)&&!(rh.x||rh.y)}function ib(e,t,r){let{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&p.postRender(()=>i(t,rg(t)))}class iw extends ru{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=ia(e,r),a=e=>{let n=e.currentTarget;if(!iv(e))return;ip.add(n);let o=t(n,e),a=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),ip.has(n)&&ip.delete(n),iv(e)&&"function"==typeof o&&o(e,{success:t})},s=e=>{a(e,n===window||n===document||r.useGlobalTarget||id(n,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return n.forEach(e=>{((r.useGlobalTarget?window:e).addEventListener("pointerdown",a,i),(0,tY.s)(e))&&(e.addEventListener("focus",e=>iy(e,i)),ih.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(ib(this.node,t,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ix=new WeakMap,iE=new WeakMap,iP=e=>{let t=ix.get(e.target);t&&t(e)},iR=e=>{e.forEach(iP)},iT={some:0,all:1};class iS extends ru{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:iT[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;iE.has(r)||iE.set(r,{});let n=iE.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(iR,{root:e,...t})),n[i]}(t);return ix.set(e,r),n.observe(e),()=>{ix.delete(e),n.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=t?r:n;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let iA=(0,r7.createContext)({strict:!1});var i_=r(2582);let iM=(0,r7.createContext)({});function iO(e){return i(e.animate)||rn.some(t=>rt(e[t]))}function ij(e){return!!(iO(e)||e.variants)}function ik(e){return Array.isArray(e)?e.join(" "):e}var iC=r(7044);let iD={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iN={};for(let e in iD)iN[e]={isEnabled:t=>iD[e].some(e=>!!t[e])};let iL=Symbol.for("motionComponentSymbol");var iI=r(1279),iF=r(2743);function iU(e,{layout:t,layoutId:r}){return b.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!no[e]||"opacity"===e)}let iV=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iB={...X,transform:Math.round},iz={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:es,rotateX:es,rotateY:es,rotateZ:es,scale:Y,scaleX:Y,scaleY:Y,scaleZ:Y,skew:es,skewX:es,skewY:es,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:q,originX:ef,originY:ef,originZ:eu,zIndex:iB,fillOpacity:q,strokeOpacity:q,numOctaves:iB},iH={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},i$=v.length;function iW(e,t,r){let{style:n,vars:i,transformOrigin:o}=e,a=!1,s=!1;for(let e in t){let r=t[e];if(b.has(e)){a=!0;continue}if($(e)){i[e]=r;continue}{let t=iV(r,iz[e]);e.startsWith("origin")?(s=!0,o[e]=t):n[e]=t}}if(!t.transform&&(a||r?n.transform=function(e,t,r){let n="",i=!0;for(let o=0;o<i$;o++){let a=v[o],s=e[a];if(void 0===s)continue;let l=!0;if(!(l="number"==typeof s?s===+!!a.startsWith("scale"):0===parseFloat(s))||r){let e=iV(s,iz[a]);if(!l){i=!1;let t=iH[a]||a;n+=`${t}(${e}) `}r&&(t[a]=e)}}return n=n.trim(),r?n=r(t,i?"":n):i&&(n="none"),n}(t,e.transform,r):n.transform&&(n.transform="none")),s){let{originX:e="50%",originY:t="50%",originZ:r=0}=o;n.transformOrigin=`${e} ${t} ${r}`}}let iK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iG(e,t,r){for(let n in t)j(t[n])||iU(n,r)||(e[n]=t[n])}let iX={offset:"stroke-dashoffset",array:"stroke-dasharray"},iq={offset:"strokeDashoffset",array:"strokeDasharray"};function iY(e,{attrX:t,attrY:r,attrScale:n,pathLength:i,pathSpacing:o=1,pathOffset:a=0,...s},l,u,c){if(iW(e,s,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:f}=e;d.transform&&(f.transform=d.transform,delete d.transform),(f.transform||d.transformOrigin)&&(f.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),f.transform&&(f.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==r&&(d.y=r),void 0!==n&&(d.scale=n),void 0!==i&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let o=i?iX:iq;e[o.offset]=eu.transform(-n);let a=eu.transform(t),s=eu.transform(r);e[o.array]=`${a} ${s}`}(d,i,o,a,!1)}let iZ=()=>({...iK(),attrs:{}}),iQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i3(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i5=r(2789);let i4=e=>(t,r)=>{let n=(0,r7.useContext)(iM),o=(0,r7.useContext)(iI.t),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},r,n,o){return{latestValues:function(e,t,r,n){let o={},s=n(e,{});for(let e in s)o[e]=np(s[e]);let{initial:l,animate:u}=e,c=iO(e),d=ij(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let f=!!r&&!1===r.initial,h=(f=f||!1===l)?u:l;if(h&&"boolean"!=typeof h&&!i(h)){let t=Array.isArray(h)?h:[h];for(let r=0;r<t.length;r++){let n=a(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=f?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(r,n,o,e),renderState:t()}})(e,t,n,o);return r?s():(0,i5.M)(s)};function i9(e,t,r){let{style:n}=e,i={};for(let o in n)(j(n[o])||t.style&&j(t.style[o])||iU(o,e)||r?.getValue(o)?.liveStyle!==void 0)&&(i[o]=n[o]);return i}let i6={useVisualState:i4({scrapeMotionValuesFromProps:i9,createRenderState:iK})};function i7(e,t,r){let n=i9(e,t,r);for(let r in e)(j(e[r])||j(t[r]))&&(n[-1!==v.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return n}let i8={useVisualState:i4({scrapeMotionValuesFromProps:i7,createRenderState:iZ})},oe=e=>t=>t.test(e),ot=[X,eu,el,es,ed,ec,{test:e=>"auto"===e,parse:e=>e}],or=e=>ot.find(oe(e)),on=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),oi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,oo=e=>/^0[^.\s]+$/u.test(e),oa=new Set(["brightness","contrast","saturate","opacity"]);function os(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(Q)||[];if(!n)return e;let i=r.replace(n,""),o=+!!oa.has(t);return n!==r&&(o*=100),t+"("+o+i+")"}let ol=/\b([a-z-]*)\(.*?\)/gu,ou={...eP,getAnimatableNone:e=>{let t=e.match(ol);return t?t.map(os).join(" "):e}},oc={...iz,color:ep,backgroundColor:ep,outlineColor:ep,fill:ep,stroke:ep,borderColor:ep,borderTopColor:ep,borderRightColor:ep,borderBottomColor:ep,borderLeftColor:ep,filter:ou,WebkitFilter:ou},od=e=>oc[e];function of(e,t){let r=od(e);return r!==ou&&(r=eP),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let oh=new Set(["auto","none","0"]);class op extends tN{constructor(e,t,r,n,i){super(e,t,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let n=e[r];if("string"==typeof n&&K(n=n.trim())){let i=function e(t,r,n=1){z(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[i,o]=function(e){let t=oi.exec(e);if(!t)return[,];let[,r,n,i]=t;return[`--${r??n}`,i]}(t);if(!i)return;let a=window.getComputedStyle(r).getPropertyValue(i);if(a){let e=a.trim();return on(e)?parseFloat(e):e}return K(o)?e(o,r,n+1):o}(n,t.current);void 0!==i&&(e[r]=i),r===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!w.has(r)||2!==e.length)return;let[n,i]=e,o=or(n),a=or(i);if(o!==a)if(tT(o)&&tT(a))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else t_[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var n;(null===e[t]||("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||oo(n)))&&r.push(t)}r.length&&function(e,t,r){let n,i=0;for(;i<e.length&&!n;){let t=e[i];"string"==typeof t&&!oh.has(t)&&eb(t).values.length&&(n=e[i]),i++}if(n&&r)for(let i of t)e[i]=of(r,n)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=t_[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(r,n).jump(n,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let n=e.getValue(t);n&&n.jump(this.measuredOrigin,!1);let i=r.length-1,o=r[i];r[i]=t_[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,r])=>{e.getValue(t).set(r)}),this.resolveNoneKeyframes()}}let om=[...ot,ep,eP],og=e=>om.find(oe(e)),oy={current:null},ov={current:!1},ob=new WeakMap,ow=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ox{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tN,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=T.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,p.render(this.render,!1,!0))};let{latestValues:s,renderState:l}=o;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=iO(t),this.isVariantNode=ij(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==s[e]&&j(t)&&t.set(s[e],!1)}}mount(e){this.current=e,ob.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ov.current||function(){if(ov.current=!0,iC.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>oy.current=e.matches;e.addEventListener("change",t),t()}else oy.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||oy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=b.has(e);n&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&p.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iN){let t=iN[e];if(!t)continue;let{isEnabled:r,Feature:n}=t;if(!this.features[e]&&n&&r(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rM()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ow.length;t++){let r=ow[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){for(let n in t){let i=t[n],o=r[n];if(j(i))e.addValue(n,i);else if(j(o))e.addValue(n,M(i,{owner:e}));else if(o!==i)if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(n);e.addValue(n,M(void 0!==t?t:i,{owner:e}))}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=M(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){let r=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(on(r)||oo(r))?r=parseFloat(r):!og(r)&&eP.test(t)&&(r=of(e,t)),this.setBaseTarget(e,j(r)?r.get():r)),j(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let n=a(this.props,r,this.presenceContext?.custom);n&&(t=n[e])}if(r&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||j(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new P),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class oE extends ox{constructor(){super(...arguments),this.KeyframeResolver=op}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;j(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function oP(e,{style:t,vars:r},n,i){let o,a=e.style;for(o in t)a[o]=t[o];for(o in i?.applyProjectionStyles(a,n),r)a.setProperty(o,r[o])}class oR extends oE{constructor(){super(...arguments),this.type="html",this.renderInstance=oP}readValueFromInstance(e,t){if(b.has(t))return this.projection?.isProjecting?tx(t):tP(e,t);{let r=window.getComputedStyle(e),n=($(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return rB(e,t)}build(e,t,r){iW(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return i9(e,t,r)}}let oT=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oS extends oE{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rM}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(b.has(t)){let e=od(t);return e&&e.default||0}return t=oT.has(t)?t:C(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return i7(e,t,r)}build(e,t,r){iY(e,t,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(e,t,r,n){for(let r in oP(e,t,void 0,n),t.attrs)e.setAttribute(oT.has(r)?r:C(r),t.attrs[r])}mount(e){this.isSVGTag=iQ(e.tagName),super.mount(e)}}let oA=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((tX={animation:{Feature:rc},exit:{Feature:rf},inView:{Feature:iS},tap:{Feature:iw},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:r4},drag:{Feature:r3,ProjectionNode:io,MeasureLayout:nl},layout:{ProjectionNode:io,MeasureLayout:nl}},tq=(e,t)=>i3(e)?new oS(t):new oR(t,{allowProjection:e!==r7.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){function o(e,o){var a,s,l;let u,c={...(0,r7.useContext)(i_.Q),...e,layoutId:function({layoutId:e}){let t=(0,r7.useContext)(ne.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=c,f=function(e){let{initial:t,animate:r}=function(e,t){if(iO(e)){let{initial:t,animate:r}=e;return{initial:!1===t||rt(t)?t:void 0,animate:rt(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,r7.useContext)(iM));return(0,r7.useMemo)(()=>({initial:t,animate:r}),[ik(t),ik(r)])}(e),h=n(e,d);if(!d&&iC.B){s=0,l=0,(0,r7.useContext)(iA).strict;let e=function(e){let{drag:t,layout:r}=iN;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:t?.isEnabled(e)||r?.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(c);u=e.MeasureLayout,f.visualElement=function(e,t,r,n,i){let{visualElement:o}=(0,r7.useContext)(iM),a=(0,r7.useContext)(iA),s=(0,r7.useContext)(iI.t),l=(0,r7.useContext)(i_.Q).reducedMotion,u=(0,r7.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(e,{visualState:t,parent:o,props:r,presenceContext:s,blockInitialAnimation:!!s&&!1===s.initial,reducedMotionConfig:l}));let c=u.current,d=(0,r7.useContext)(nt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,r,n){let{layoutId:i,layout:o,drag:a,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!a||s&&rH(s),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:n,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,r,i,d);let f=(0,r7.useRef)(!1);(0,r7.useInsertionEffect)(()=>{c&&f.current&&c.update(r,s)});let h=r[D],p=(0,r7.useRef)(!!h&&!window.MotionHandoffIsComplete?.(h)&&window.MotionHasOptimisedAnimation?.(h));return(0,iF.E)(()=>{c&&(f.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),r6.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,r7.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(h)}),p.current=!1))}),c}(i,h,c,t,e.ProjectionNode)}return(0,r9.jsxs)(iM.Provider,{value:f,children:[u&&f.visualElement?(0,r9.jsx)(u,{visualElement:f.visualElement,...c}):null,r(i,e,(a=f.visualElement,(0,r7.useCallback)(e=>{e&&h.onMount&&h.onMount(e),a&&(e?a.mount(e):a.unmount()),o&&("function"==typeof o?o(e):rH(o)&&(o.current=e))},[a])),h,d,f.visualElement)]})}e&&function(e){for(let t in e)iN[t]={...iN[t],...e[t]}}(e),o.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let a=(0,r7.forwardRef)(o);return a[iL]=i,a}({...i3(e)?i8:i6,preloadedFeatures:tX,useRender:function(e=!1){return(t,r,n,{latestValues:i},o)=>{let a=(i3(t)?function(e,t,r,n){let i=(0,r7.useMemo)(()=>{let r=iZ();return iY(r,t,iQ(n),e.transformTemplate,e.style),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};iG(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return iG(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,r7.useMemo)(()=>{let r=iK();return iW(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,o,t),s=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===r&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),l=t!==r7.Fragment?{...s,...a,ref:n}:{},{children:u}=r,c=(0,r7.useMemo)(()=>j(u)?u.get():u,[u]);return(0,r7.createElement)(t,{...l,children:c})}}(t),createVisualElement:tq,Component:e})}))},6044:(e,t,r)=>{"use strict";r.d(t,{xQ:()=>o});var n=r(3210),i=r(1279);function o(e=!0){let t=(0,n.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:a,register:s}=t,l=(0,n.useId)();(0,n.useEffect)(()=>{if(e)return s(l)},[e]);let u=(0,n.useCallback)(()=>e&&a&&a(l),[l,a,e]);return!r&&a?[!1,u]:[!0]}},6059:(e,t,r)=>{"use strict";r.d(t,{C:()=>a});var n=r(3210),i=r(8599),o=r(6156),a=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[i,a]=n.useState(),l=n.useRef(null),u=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=l.current,r=u.current;if(r!==e){let n=c.current,i=s(t);e?f("MOUNT"):"none"===i||t?.display==="none"?f("UNMOUNT"):r&&n!==i?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,o.N)(()=>{if(i){let e,t=i.ownerDocument.defaultView??window,r=r=>{let n=s(l.current).includes(r.animationName);if(r.target===i&&n&&(f("ANIMATION_END"),!u.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=e=>{e.target===i&&(c.current=s(l.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(t),l="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),u=(0,i.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||a.isPresent?n.cloneElement(l,{ref:u}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},6127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let n=r(8834),i=r(4674);function o(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6156:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var n=r(3210),i=globalThis?.document?n.useLayoutEffect:()=>{}},6189:(e,t,r)=>{"use strict";var n=r(5773);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}})},6312:(e,t,r)=>{"use strict";r.d(t,{UC:()=>eG,q7:()=>eX,ZL:()=>eK,bL:()=>e$,wv:()=>eq,l9:()=>eW});var n=r(3210),i=r(569),o=r(8599),a=r(1273),s=r(5551),l=r(4163),u=r(9510),c=r(43),d=r(1355),f=r(1359),h=r(2547),p=r(6963),m=r(5509),g=r(5028),y=r(6059),v=r(2942),b=r(8730),w=r(3495),x=r(3376),E=r(2247),P=r(687),R=["Enter"," "],T=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...T],A={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},_={ltr:["ArrowLeft"],rtl:["ArrowRight"]},M="Menu",[O,j,k]=(0,u.N)(M),[C,D]=(0,a.A)(M,[k,m.Bk,v.RG]),N=(0,m.Bk)(),L=(0,v.RG)(),[I,F]=C(M),[U,V]=C(M),B=e=>{let{__scopeMenu:t,open:r=!1,children:i,dir:o,onOpenChange:a,modal:s=!0}=e,l=N(t),[u,d]=n.useState(null),f=n.useRef(!1),h=(0,w.c)(a),p=(0,c.jH)(o);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,P.jsx)(m.bL,{...l,children:(0,P.jsx)(I,{scope:t,open:r,onOpenChange:h,content:u,onContentChange:d,children:(0,P.jsx)(U,{scope:t,onClose:n.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:f,dir:p,modal:s,children:i})})})};B.displayName=M;var z=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=N(r);return(0,P.jsx)(m.Mz,{...i,...n,ref:t})});z.displayName="MenuAnchor";var H="MenuPortal",[$,W]=C(H,{forceMount:void 0}),K=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:i}=e,o=F(H,t);return(0,P.jsx)($,{scope:t,forceMount:r,children:(0,P.jsx)(y.C,{present:r||o.open,children:(0,P.jsx)(g.Z,{asChild:!0,container:i,children:n})})})};K.displayName=H;var G="MenuContent",[X,q]=C(G),Y=n.forwardRef((e,t)=>{let r=W(G,e.__scopeMenu),{forceMount:n=r.forceMount,...i}=e,o=F(G,e.__scopeMenu),a=V(G,e.__scopeMenu);return(0,P.jsx)(O.Provider,{scope:e.__scopeMenu,children:(0,P.jsx)(y.C,{present:n||o.open,children:(0,P.jsx)(O.Slot,{scope:e.__scopeMenu,children:a.modal?(0,P.jsx)(Z,{...i,ref:t}):(0,P.jsx)(Q,{...i,ref:t})})})})}),Z=n.forwardRef((e,t)=>{let r=F(G,e.__scopeMenu),a=n.useRef(null),s=(0,o.s)(t,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,x.Eq)(e)},[]),(0,P.jsx)(ee,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=F(G,e.__scopeMenu);return(0,P.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),J=(0,b.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:a=!1,trapFocus:s,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:p,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:w,onDismiss:x,disableOutsideScroll:R,...A}=e,_=F(G,r),M=V(G,r),O=N(r),k=L(r),C=j(r),[D,I]=n.useState(null),U=n.useRef(null),B=(0,o.s)(t,U,_.onContentChange),z=n.useRef(0),H=n.useRef(""),$=n.useRef(0),W=n.useRef(null),K=n.useRef("right"),q=n.useRef(0),Y=R?E.A:n.Fragment,Z=e=>{let t=H.current+e,r=C().filter(e=>!e.disabled),n=document.activeElement,i=r.find(e=>e.ref.current===n)?.textValue,o=function(e,t,r){var n;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=r?e.indexOf(r):-1,a=(n=Math.max(o,0),e.map((t,r)=>e[(n+r)%e.length]));1===i.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}(r.map(e=>e.textValue),t,i),a=r.find(e=>e.textValue===o)?.ref.current;!function e(t){H.current=t,window.clearTimeout(z.current),""!==t&&(z.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};n.useEffect(()=>()=>window.clearTimeout(z.current),[]),(0,f.Oh)();let Q=n.useCallback(e=>K.current===W.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let a=t[e],s=t[o],l=a.x,u=a.y,c=s.x,d=s.y;u>n!=d>n&&r<(c-l)*(n-u)/(d-u)+l&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,W.current?.area),[]);return(0,P.jsx)(X,{scope:r,searchRef:H,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{Q(e)||(U.current?.focus(),I(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:$,onPointerGraceIntentChange:n.useCallback(e=>{W.current=e},[]),children:(0,P.jsx)(Y,{...R?{as:J,allowPinchZoom:!0}:void 0,children:(0,P.jsx)(h.n,{asChild:!0,trapped:s,onMountAutoFocus:(0,i.m)(l,e=>{e.preventDefault(),U.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,P.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:w,onDismiss:x,children:(0,P.jsx)(v.bL,{asChild:!0,...k,dir:M.dir,orientation:"vertical",loop:a,currentTabStopId:D,onCurrentTabStopIdChange:I,onEntryFocus:(0,i.m)(p,e=>{M.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,P.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eS(_.open),"data-radix-menu-content":"",dir:M.dir,...O,...A,ref:B,style:{outline:"none",...A.style},onKeyDown:(0,i.m)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Z(e.key));let i=U.current;if(e.target!==i||!S.includes(e.key))return;e.preventDefault();let o=C().filter(e=>!e.disabled).map(e=>e.ref.current);T.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(z.current),H.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,eM(e=>{let t=e.target,r=q.current!==e.clientX;e.currentTarget.contains(t)&&r&&(K.current=e.clientX>q.current?"right":"left",q.current=e.clientX)}))})})})})})})});Y.displayName=G;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,P.jsx)(l.sG.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,P.jsx)(l.sG.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",ei="menu.itemSelect",eo=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:a,...s}=e,u=n.useRef(null),c=V(en,e.__scopeMenu),d=q(en,e.__scopeMenu),f=(0,o.s)(t,u),h=n.useRef(!1);return(0,P.jsx)(ea,{...s,ref:f,disabled:r,onClick:(0,i.m)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(ei,{bubbles:!0,cancelable:!0});e.addEventListener(ei,e=>a?.(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?h.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),h.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{h.current||e.currentTarget?.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;r||t&&" "===e.key||R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=en;var ea=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:a=!1,textValue:s,...u}=e,c=q(en,r),d=L(r),f=n.useRef(null),h=(0,o.s)(t,f),[p,m]=n.useState(!1),[g,y]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&y((e.textContent??"").trim())},[u.children]),(0,P.jsx)(O.ItemSlot,{scope:r,disabled:a,textValue:s??g,children:(0,P.jsx)(v.q7,{asChild:!0,...d,focusable:!a,children:(0,P.jsx)(l.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...u,ref:h,onPointerMove:(0,i.m)(e.onPointerMove,eM(e=>{a?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eM(e=>c.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>m(!0)),onBlur:(0,i.m)(e.onBlur,()=>m(!1))})})})}),es=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...o}=e;return(0,P.jsx)(em,{scope:e.__scopeMenu,checked:r,children:(0,P.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eA(r)?"mixed":r,...o,ref:t,"data-state":e_(r),onSelect:(0,i.m)(o.onSelect,()=>n?.(!!eA(r)||!r),{checkForDefaultPrevented:!1})})})});es.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[eu,ec]=C(el,{value:void 0,onValueChange:()=>{}}),ed=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...i}=e,o=(0,w.c)(n);return(0,P.jsx)(eu,{scope:e.__scopeMenu,value:r,onValueChange:o,children:(0,P.jsx)(et,{...i,ref:t})})});ed.displayName=el;var ef="MenuRadioItem",eh=n.forwardRef((e,t)=>{let{value:r,...n}=e,o=ec(ef,e.__scopeMenu),a=r===o.value;return(0,P.jsx)(em,{scope:e.__scopeMenu,checked:a,children:(0,P.jsx)(eo,{role:"menuitemradio","aria-checked":a,...n,ref:t,"data-state":e_(a),onSelect:(0,i.m)(n.onSelect,()=>o.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});eh.displayName=ef;var ep="MenuItemIndicator",[em,eg]=C(ep,{checked:!1}),ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...i}=e,o=eg(ep,r);return(0,P.jsx)(y.C,{present:n||eA(o.checked)||!0===o.checked,children:(0,P.jsx)(l.sG.span,{...i,ref:t,"data-state":e_(o.checked)})})});ey.displayName=ep;var ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,P.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ev.displayName="MenuSeparator";var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=N(r);return(0,P.jsx)(m.i3,{...i,...n,ref:t})});eb.displayName="MenuArrow";var[ew,ex]=C("MenuSub"),eE="MenuSubTrigger",eP=n.forwardRef((e,t)=>{let r=F(eE,e.__scopeMenu),a=V(eE,e.__scopeMenu),s=ex(eE,e.__scopeMenu),l=q(eE,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=l,f={__scopeMenu:e.__scopeMenu},h=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>h,[h]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,P.jsx)(z,{asChild:!0,...f,children:(0,P.jsx)(ea,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":s.contentId,"data-state":eS(r.open),...e,ref:(0,o.t)(t,s.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,eM(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),h()},100)))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eM(e=>{h();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,i="right"===n,o=t[i?"left":"right"],a=t[i?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:o,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:o,y:t.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let n=""!==l.searchRef.current;e.disabled||n&&" "===t.key||A[a.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eP.displayName=eE;var eR="MenuSubContent",eT=n.forwardRef((e,t)=>{let r=W(G,e.__scopeMenu),{forceMount:a=r.forceMount,...s}=e,l=F(G,e.__scopeMenu),u=V(G,e.__scopeMenu),c=ex(eR,e.__scopeMenu),d=n.useRef(null),f=(0,o.s)(t,d);return(0,P.jsx)(O.Provider,{scope:e.__scopeMenu,children:(0,P.jsx)(y.C,{present:a||l.open,children:(0,P.jsx)(O.Slot,{scope:e.__scopeMenu,children:(0,P.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...s,ref:f,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=_[u.dir].includes(e.key);t&&r&&(l.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eS(e){return e?"open":"closed"}function eA(e){return"indeterminate"===e}function e_(e){return eA(e)?"indeterminate":e?"checked":"unchecked"}function eM(e){return t=>"mouse"===t.pointerType?e(t):void 0}eT.displayName=eR;var eO="DropdownMenu",[ej,ek]=(0,a.A)(eO,[D]),eC=D(),[eD,eN]=ej(eO),eL=e=>{let{__scopeDropdownMenu:t,children:r,dir:i,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,c=eC(t),d=n.useRef(null),[f,h]=(0,s.i)({prop:o,defaultProp:a??!1,onChange:l,caller:eO});return(0,P.jsx)(eD,{scope:t,triggerId:(0,p.B)(),triggerRef:d,contentId:(0,p.B)(),open:f,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),modal:u,children:(0,P.jsx)(B,{...c,open:f,onOpenChange:h,dir:i,modal:u,children:r})})};eL.displayName=eO;var eI="DropdownMenuTrigger",eF=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...a}=e,s=eN(eI,r),u=eC(r);return(0,P.jsx)(z,{asChild:!0,...u,children:(0,P.jsx)(l.sG.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...a,ref:(0,o.t)(t,s.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(s.onOpenToggle(),s.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&s.onOpenToggle(),"ArrowDown"===e.key&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eI;var eU=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eC(t);return(0,P.jsx)(K,{...n,...r})};eU.displayName="DropdownMenuPortal";var eV="DropdownMenuContent",eB=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=eN(eV,r),s=eC(r),l=n.useRef(!1);return(0,P.jsx)(Y,{id:a.contentId,"aria-labelledby":a.triggerId,...s,...o,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{l.current||a.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!a.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eB.displayName=eV,n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(et,{...i,...n,ref:t})}).displayName="DropdownMenuGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(er,{...i,...n,ref:t})}).displayName="DropdownMenuLabel";var ez=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(eo,{...i,...n,ref:t})});ez.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(es,{...i,...n,ref:t})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(ed,{...i,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(eh,{...i,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(ey,{...i,...n,ref:t})}).displayName="DropdownMenuItemIndicator";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(ev,{...i,...n,ref:t})});eH.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(eb,{...i,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(eP,{...i,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eC(r);return(0,P.jsx)(eT,{...i,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e$=eL,eW=eF,eK=eU,eG=eB,eX=ez,eq=eH},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return h}});let n=r(9551),i=r(1959),o=r(2437),a=r(4396),s=r(8034),l=r(5526),u=r(2887),c=r(4722),d=r(6143),f=r(7912);function h(e,t,r){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),o=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(n||o||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function p(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i,{optional:o,repeat:a}=r.groups[n],s=`[${a?"...":""}${n}]`;o&&(s=`[${s}]`);let l=t[n];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(s,i)}return e}function m(e,t,r,n){let i={};for(let o of Object.keys(t.groups)){let a=e[o];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let s=r[o],l=t.groups[o].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(s))||void 0===a&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${o}]]`))&&(a=void 0,delete e[o]),a&&"string"==typeof a&&t.groups[o].repeat&&(a=a.split("/")),a&&(i[o]=a)}return{params:i,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:c,trailingSlash:d,caseSensitive:g}){let y,v,b;return c&&(y=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(v=(0,s.getRouteMatcher)(y))(e)),{handleRewrites:function(a,s){let f={},h=s.pathname,p=n=>{let u=(0,o.getPathMatch)(n.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!s.pathname)return!1;let p=u(s.pathname);if((n.has||n.missing)&&p){let e=(0,l.matchHas)(a,s.query,n.has,n.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:o,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:p,query:s.query});if(o.protocol)return!0;if(Object.assign(f,a,p),Object.assign(s.query,o.query),delete o.query,Object.assign(s,o),!(h=s.pathname))return!1;if(r&&(h=h.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(h,t.locales);h=e.pathname,s.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(h===e)return!0;if(c&&v){let e=v(h);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(h!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(h||"");return t===(0,u.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return f},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:r}=y,n=(0,s.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,f.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let o=r[e];if(!o)continue;let a=t[o],s=n[e];if(!a.optional&&!s)return null;i[a.pos]=s}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>y&&b?m(e,y,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>h(e,t,y),interpolateDynamicPath:(e,t)=>p(e,t,y)}}function y(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6349:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let n=r(6127);function i(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},o=t.split(n),a=(r||{}).decode||e,s=0;s<o.length;s++){var l=o[s],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var o=n||{},a=o.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=a(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(o.domain){if(!i.test(o.domain))throw TypeError("option domain is invalid");l+="; Domain="+o.domain}if(o.path){if(!i.test(o.path))throw TypeError("option path is invalid");l+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(l+="; HttpOnly"),o.secure&&(l+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let n=r(5232);function i(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6715:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},6736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=r(2255);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return o}});let n=r(2785),i=r(3736);function o(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,l){let u,[c,d,f,h,p]=r;if(1===t.length){let e=s(r,n);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,o.matchSegment)(m,c))return null;if(2===t.length)u=s(d[g],n);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),d[g],n,l)))return null;let y=[t[0],{...d,[g]:u},f,h];return p&&(y[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let n=r(3913),i=r(4007),o=r(4077),a=r(2308);function s(e,t){let[r,i]=e,[a,l]=t;if(a===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,o.matchSegment)(r,a)){let t={};for(let e in i)void 0!==l[e]?t[e]=s(i[e],l[e]):t[e]=i[e];for(let e in l)t[e]||(t[e]=l[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6787:(e,t,r)=>{"use strict";r.d(t,{v:()=>l});var n=r(3210);let i=e=>{let t,r=new Set,n=(e,n)=>{let i="function"==typeof e?e(t):e;if(!Object.is(i,t)){let e=t;t=(null!=n?n:"object"!=typeof i||null===i)?i:Object.assign({},t,i),r.forEach(r=>r(t,e))}},i=()=>t,o={setState:n,getState:i,getInitialState:()=>a,subscribe:e=>(r.add(e),()=>r.delete(e))},a=t=e(n,i,o);return o},o=e=>e?i(e):i,a=e=>e,s=e=>{let t=o(e),r=e=>(function(e,t=a){let r=n.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return n.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},l=e=>e?s(e):s},6928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let n=r(1500),i=r(3898);function o(e,t,r,o,a){let{tree:s,seedData:l,head:u,isRootRender:c}=o;if(null===l)return!1;if(c){let i=l[1];r.loading=l[3],r.rsc=i,r.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,r,t,s,l,u,a)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,r,t,o,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6963:(e,t,r)=>{"use strict";r.d(t,{B:()=>l});var n,i=r(3210),o=r(6156),a=(n||(n=r.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function l(e){let[t,r]=i.useState(a());return(0,o.N)(()=>{e||r(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},7022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(3210),i=r(1215),o="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(o)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,n.useState)(""),u=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),r?(0,i.createPortal)(s,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7044:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});let n="undefined"!=typeof window},7051:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},7379:(e,t,r)=>{"use strict";e.exports=r(3332)},7464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let a=o.length<=2,[s,l]=o,u=(0,i.createRouterCacheKey)(l),c=r.parallelRoutes.get(s),d=t.parallelRoutes.get(s);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d));let f=null==c?void 0:c.get(u),h=d.get(u);if(a){h&&h.lazyData&&h!==f||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!h||!f){h||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return h===f&&(h={lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,head:h.head,prefetchHead:h.prefetchHead,parallelRoutes:new Map(h.parallelRoutes),loading:h.loading},d.set(u,h)),e(h,f,(0,n.getNextFlightSegmentPath)(o))}}});let n=r(4007),i=r(3123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7810:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return M}});let n=r(1264),i=r(1448),o=r(1563),a=r(9154),s=r(6361),l=r(7391),u=r(5232),c=r(6770),d=r(2030),f=r(9435),h=r(1500),p=r(9752),m=r(8214),g=r(6493),y=r(2308),v=r(4007),b=r(6875),w=r(7860),x=r(5334),E=r(5942),P=r(6736),R=r(4642);r(593);let{createFromFetch:T,createTemporaryReferenceSet:S,encodeReply:A}=r(9357);async function _(e,t,r){let a,l,{actionId:u,actionArgs:c}=r,d=S(),f=(0,R.extractInfoFromServerReferenceId)(u),h="use-cache"===f.type?(0,R.omitUnusedArgs)(c,f):c,p=await A(h,{temporaryReferences:d}),m=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION_HEADER]:u,[o.NEXT_ROUTER_STATE_TREE_HEADER]:(0,v.prepareFlightRouterStateForRequest)(e.tree),...{},...t?{[o.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,b]=(null==g?void 0:g.split(";"))||[];switch(b){case"push":a=w.RedirectType.push;break;case"replace":a=w.RedirectType.replace;break;default:a=void 0}let x=!!m.headers.get(o.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let E=y?(0,s.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,P=m.headers.get("content-type");if(null==P?void 0:P.startsWith(o.RSC_CONTENT_TYPE_HEADER)){let e=await T(Promise.resolve(m),{callServer:n.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:d});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:E,redirectType:a,revalidatedParts:l,isPrerender:x}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:E,redirectType:a,revalidatedParts:l,isPrerender:x}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===P?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:E,redirectType:a,revalidatedParts:l,isPrerender:x}}function M(e,t){let{resolve:r,reject:n}=t,i={},o=e.tree;i.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return _(e,s,t).then(async m=>{let R,{actionResult:T,actionFlightData:S,redirectLocation:A,redirectType:_,isPrerender:M,revalidatedParts:O}=m;if(A&&(_===w.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=R=(0,l.createHrefFromUrl)(A,!1)),!S)return(r(T),A)?(0,u.handleExternalUrl)(e,i,A.href,e.pushRef.pendingPush):e;if("string"==typeof S)return r(T),(0,u.handleExternalUrl)(e,i,S,e.pushRef.pendingPush);let j=O.paths.length>0||O.tag||O.cookie;for(let n of S){let{tree:a,seedData:l,head:f,isRootRender:m}=n;if(!m)return console.log("SERVER ACTION APPLY FAILED"),r(T),e;let b=(0,c.applyRouterStatePatchToTree)([""],o,a,R||e.canonicalUrl);if(null===b)return r(T),(0,g.handleSegmentMismatch)(e,t,a);if((0,d.isNavigatingToNewRootLayout)(o,b))return r(T),(0,u.handleExternalUrl)(e,i,R||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],r=(0,p.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=l[3],(0,h.fillLazyItemsTillLeafWithHead)(v,r,void 0,a,l,f,void 0),i.cache=r,i.prefetchCache=new Map,j&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:b,updatedCache:r,includeNextUrl:!!s,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=b,o=b}return A&&R?(j||((0,x.createSeededPrefetchCacheEntry)({url:A,data:{flightData:S,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:M?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),n((0,b.getRedirectError)((0,P.hasBasePath)(R)?(0,E.removeBasePath)(R):R,_||w.RedirectType.push))):r(T),(0,f.handleMutable)(e,i)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(9008),r(7391),r(6770),r(2030),r(5232),r(9435),r(6928),r(9752),r(6493),r(8214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(4827);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?a[e]=r.split("/").map(e=>o(e)):a[e]=o(r))}return a}}},8171:(e,t,r)=>{"use strict";r.d(t,{s:()=>i});var n=r(4479);function i(e){return(0,n.G)(e)&&"offsetHeight"in e}},8179:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return s},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return f},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let n=r(2958),i=r(4722),o=r(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,r){let i=(r?"":"?")+"$",o=`\\d?${r?"":"(-\\w{6})?"}`,s=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${a.icon.filename}${o}${l(a.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${a.apple.filename}${o}${l(a.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${a.openGraph.filename}${o}${l(a.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${a.twitter.filename}${o}${l(a.twitter.extensions,t)}${i}`)],u=(0,n.normalizePathSep)(e);return s.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,o.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,o.isAppRouteRoute)(e)&&u(e,[],!1)}function f(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,o.isAppRouteRoute)(e)&&u(t,[],!1)}},8468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let a=o.length<=2,[s,l]=o,u=(0,n.createRouterCacheKey)(l),c=r.parallelRoutes.get(s);if(!c)return;let d=t.parallelRoutes.get(s);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d)),a)return void d.delete(u);let f=c.get(u),h=d.get(u);h&&f&&(h===f&&(h={lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,head:h.head,prefetchHead:h.prefetchHead,parallelRoutes:new Map(h.parallelRoutes)},d.set(u,h)),e(h,f,(0,i.getNextFlightSegmentPath)(o)))}}});let n=r(3123),i=r(4007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8599:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>o});var n=r(3210);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function a(...e){return n.useCallback(o(...e),e)}},8627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let n=r(7391),i=r(642);function o(e,t){var r;let{url:o,tree:a}=t,s=(0,n.createHrefFromUrl)(o),l=a||e.tree,u=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(r=(0,i.extractPathFromFlightRouterState)(l))?r:o.pathname}}r(5956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8730:(e,t,r)=>{"use strict";r.d(t,{DX:()=>s,TL:()=>a});var n=r(3210),i=r(8599),o=r(687);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var a;let e,s,l=(a=r,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,i.t)(t,l):l),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...a}=e,s=n.Children.toArray(i),l=s.find(u);if(l){let e=l.props.children,i=s.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var s=a("Slot"),l=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(9154),r(5232),r(9651),r(8627),r(8866),r(5076),r(7936),r(7810);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(3931);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:o}=(0,n.parsePath)(e);return""+t+r+i+o}},8853:(e,t,r)=>{"use strict";r.d(t,{X:()=>o});var n=r(3210),i=r(6156);function o(e){let[t,r]=n.useState(void 0);return(0,i.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},8866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(9008),i=r(7391),o=r(6770),a=r(2030),s=r(5232),l=r(9435),u=r(1500),c=r(9752),d=r(6493),f=r(8214),h=r(2308);function p(e,t){let{origin:r}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,n.fetchServerResponse)(new URL(m,r),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null});let b=Date.now();return y.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,s.handleExternalUrl)(e,p,n,e.pushRef.pendingPush);for(let r of(y.lazyData=null,n)){let{tree:n,seedData:l,head:f,isRootRender:w}=r;if(!w)return console.log("REFRESH FAILED"),e;let x=(0,o.applyRouterStatePatchToTree)([""],g,n,e.canonicalUrl);if(null===x)return(0,d.handleSegmentMismatch)(e,t,n);if((0,a.isNavigatingToNewRootLayout)(g,x))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let E=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=E),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(b,y,void 0,n,l,f,void 0),p.prefetchCache=new Map}await (0,h.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:x,updatedCache:y,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=x,g=x}return(0,l.handleMutable)(e,p)},()=>e)}r(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9270:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,h=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},9350:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>d,lt:()=>l});let n=new Map,i=e=>{let t=n.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},o=(e,t,r)=>{if(void 0===e)return{type:"untracked",connection:t.connect(r)};let i=n.get(r.name);if(i)return{type:"tracked",store:e,...i};let o={connection:t.connect(r),stores:{}};return n.set(r.name,o),{type:"tracked",store:e,...o}},a=(e,t)=>{if(void 0===t)return;let r=n.get(e);r&&(delete r.stores[t],0===Object.keys(r.stores).length&&n.delete(e))},s=e=>{var t,r;if(!e)return;let n=e.split("\n"),i=n.findIndex(e=>e.includes("api.setState"));if(i<0)return;let o=(null==(t=n[i+1])?void 0:t.trim())||"";return null==(r=/.+ (.+) .+/.exec(o))?void 0:r[1]},l=(e,t={})=>(r,n,l)=>{let c,{enabled:d,anonymousActionType:f,store:h,...p}=t;try{c=(null==d||d)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!c)return e(r,n,l);let{connection:m,...g}=o(h,c,p),y=!0;l.setState=(e,t,o)=>{let a=r(e,t);if(!y)return a;let u=void 0===o?{type:f||s(Error().stack)||"anonymous"}:"string"==typeof o?{type:o}:o;return void 0===h?null==m||m.send(u,n()):null==m||m.send({...u,type:`${h}/${u.type}`},{...i(p.name),[h]:l.getState()}),a},l.devtools={cleanup:()=>{m&&"function"==typeof m.unsubscribe&&m.unsubscribe(),a(p.name,h)}};let v=(...e)=>{let t=y;y=!1,r(...e),y=t},b=e(l.setState,n,l);if("untracked"===g.type?null==m||m.init(b):(g.stores[g.store]=l,null==m||m.init(Object.fromEntries(Object.entries(g.stores).map(([e,t])=>[e,e===g.store?b:t.getState()])))),l.dispatchFromDevtools&&"function"==typeof l.dispatch){let e=!1,t=l.dispatch;l.dispatch=(...r)=>{"__setState"!==r[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...r)}}return m.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return u(e.payload,e=>{if("__setState"===e.type){if(void 0===h)return void v(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[h];return void(null==t||JSON.stringify(l.getState())!==JSON.stringify(t)&&v(t))}l.dispatchFromDevtools&&"function"==typeof l.dispatch&&l.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(v(b),void 0===h)return null==m?void 0:m.init(l.getState());return null==m?void 0:m.init(i(p.name));case"COMMIT":if(void 0===h){null==m||m.init(l.getState());break}return null==m?void 0:m.init(i(p.name));case"ROLLBACK":return u(e.state,e=>{if(void 0===h){v(e),null==m||m.init(l.getState());return}v(e[h]),null==m||m.init(i(p.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return u(e.state,e=>{if(void 0===h)return void v(e);JSON.stringify(l.getState())!==JSON.stringify(e[h])&&v(e[h])});case"IMPORT_STATE":{let{nextLiftedState:r}=e.payload,n=null==(t=r.computedStates.slice(-1)[0])?void 0:t.state;if(!n)return;void 0===h?v(n):v(n[h]),null==m||m.send(null,r);break}case"PAUSE_RECORDING":return y=!y}return}}),b},u=(e,t)=>{let r;try{r=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==r&&t(r)},c=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>c(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>c(t)(e)}}},d=(e,t)=>(r,n,i)=>{let o,a={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let n=e=>null===e?null:JSON.parse(e,void 0),i=null!=(t=r.getItem(e))?t:null;return i instanceof Promise?i.then(n):n(i)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},s=!1,l=new Set,u=new Set,d=a.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),r(...e)},n,i);let f=()=>{let e=a.partialize({...n()});return d.setItem(a.name,{state:e,version:a.version})},h=i.setState;i.setState=(e,t)=>{h(e,t),f()};let p=e((...e)=>{r(...e),f()},n,i);i.getInitialState=()=>p;let m=()=>{var e,t;if(!d)return;s=!1,l.forEach(e=>{var t;return e(null!=(t=n())?t:p)});let i=(null==(t=a.onRehydrateStorage)?void 0:t.call(a,null!=(e=n())?e:p))||void 0;return c(d.getItem.bind(d))(a.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===a.version)return[!1,e.state];else{if(a.migrate){let t=a.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[i,s]=e;if(r(o=a.merge(s,null!=(t=n())?t:p),!0),i)return f()}).then(()=>{null==i||i(o,void 0),o=n(),s=!0,u.forEach(e=>e(o))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{a={...a,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>m(),hasHydrated:()=>s,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},a.skipHydration||m(),o||p}},9384:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n})},9435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let n=r(642);function i(e){return void 0!==e}function o(e,t){var r,o;let a=null==(r=t.shouldScroll)||r,s=e.nextUrl;if(i(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?s=r:s||(s=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(o=null==t?void 0:t.scrollableSegments)?o:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9510:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(3210),i=r(1273),o=r(8599),a=r(8730),s=r(687);function l(e){let t=e+"CollectionProvider",[r,l]=(0,i.A)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,i=n.useRef(null),o=n.useRef(new Map).current;return(0,s.jsx)(u,{scope:t,itemMap:o,collectionRef:i,children:r})};d.displayName=t;let f=e+"CollectionSlot",h=(0,a.TL)(f),p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=c(f,r),a=(0,o.s)(t,i.collectionRef);return(0,s.jsx)(h,{ref:a,children:n})});p.displayName=f;let m=e+"CollectionItemSlot",g="data-radix-collection-item",y=(0,a.TL)(m),v=n.forwardRef((e,t)=>{let{scope:r,children:i,...a}=e,l=n.useRef(null),u=(0,o.s)(t,l),d=c(m,r);return n.useEffect(()=>(d.itemMap.set(l,{ref:l,...a}),()=>void d.itemMap.delete(l))),(0,s.jsx)(y,{...{[g]:""},ref:u,children:i})});return v.displayName=m,[{Provider:d,Slot:p,ItemSlot:v},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}var u=new WeakMap;function c(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=d(t),i=n>=0?n:r+n;return i<0||i>=r?-1:i}(e,t);return -1===r?void 0:e[r]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},9651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(7391),i=r(6770),o=r(2030),a=r(5232),s=r(6928),l=r(9435),u=r(9752);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c},navigatedAt:d}=t,f={};if(f.preserveCustomHistoryState=!1,"string"==typeof r)return(0,a.handleExternalUrl)(e,f,r,e.pushRef.pendingPush);let h=e.tree,p=e.cache;for(let t of r){let{segmentPath:r,tree:l}=t,m=(0,i.applyRouterStatePatchToTree)(["",...r],h,l,e.canonicalUrl);if(null===m)return e;if((0,o.isNavigatingToNewRootLayout)(h,m))return(0,a.handleExternalUrl)(e,f,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,n.createHrefFromUrl)(c):void 0;g&&(f.canonicalUrl=g);let y=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(d,p,y,t),f.patchedTree=m,f.cache=y,p=y,h=m}return(0,l.handleMutable)(e,f)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>i});var n=0;function i(e){return"__private_"+n+++"_"+e}},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let n=r(3913),i=r(9752),o=r(6770),a=r(7391),s=r(3123),l=r(3898),u=r(9435);function c(e,t,r,c,f){let h,p=t.tree,m=t.cache,g=(0,a.createHrefFromUrl)(c);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=d(r,Object.fromEntries(c.searchParams));let{seedData:a,isRootRender:u,pathToSegment:f}=t,y=["",...f];r=d(r,Object.fromEntries(c.searchParams));let v=(0,o.applyRouterStatePatchToTree)(y,p,r,g),b=(0,i.createEmptyCacheNode)();if(u&&a){let t=a[1];b.loading=a[3],b.rsc=t,function e(t,r,i,o,a){if(0!==Object.keys(o[1]).length)for(let l in o[1]){let u,c=o[1][l],d=c[0],f=(0,s.createRouterCacheKey)(d),h=null!==a&&void 0!==a[2][l]?a[2][l]:null;if(null!==h){let e=h[1],r=h[3];u={lazyData:null,rsc:d.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=r.parallelRoutes.get(l);p?p.set(f,u):r.parallelRoutes.set(l,new Map([[f,u]])),e(t,u,i,c,h)}}(e,b,m,r,a)}else b.rsc=m.rsc,b.prefetchRsc=m.prefetchRsc,b.loading=m.loading,b.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,m,t);v&&(p=v,m=b,h=!0)}return!!h&&(f.patchedTree=p,f.cache=m,f.canonicalUrl=g,f.hashFragment=c.hash,(0,u.handleMutable)(t,f))}function d(e,t){let[r,i,...o]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),i,...o];let a={};for(let[e,r]of Object.entries(i))a[e]=d(r,t);return[r,a,...o]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return M},createPrefetchURL:function(){return A},default:function(){return C},isExternalURL:function(){return S}});let n=r(740),i=r(687),o=n._(r(3210)),a=r(2142),s=r(9154),l=r(7391),u=r(449),c=r(9129),d=n._(r(5656)),f=r(5416),h=r(6127),p=r(7022),m=r(7086),g=r(4397),y=r(9330),v=r(5942),b=r(6736),w=r(642),x=r(2776),E=r(3690),P=r(6875),R=r(7860);r(3406);let T={};function S(e){return e.origin!==window.location.origin}function A(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,h.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return S(t)?null:t}function _(e){let{appRouterState:t}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,i={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(i,"",n)):window.history.replaceState(i,"",n)},[t]),(0,o.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function M(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function O(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function j(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,i=null!==n?n:r;return(0,o.useDeferredValue)(r,i)}function k(e){let t,{actionQueue:r,assetPrefix:n,globalError:l}=e,f=(0,c.useActionQueue)(r),{canonicalUrl:h}=f,{searchParams:x,pathname:S}=(0,o.useMemo)(()=>{let e=new URL(h,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[h]);(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(T.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,o.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,R.isRedirectError)(t)){e.preventDefault();let r=(0,P.getURLFromRedirectError)(t);(0,P.getRedirectTypeFromError)(t)===R.RedirectType.push?E.publicAppRouterInstance.push(r,{}):E.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:A}=f;if(A.mpaNavigation){if(T.pendingMpaPath!==h){let e=window.location;A.pendingPush?e.assign(h):e.replace(h),T.pendingMpaPath=h}(0,o.use)(y.unresolvedThenable)}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,o.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=O(t),i&&r(i)),e(t,n,i)},window.history.replaceState=function(e,n,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=O(e),i&&r(i)),t(e,n,i)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,o.startTransition)(()=>{(0,E.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:M,tree:k,nextUrl:C,focusAndScrollRef:D}=f,N=(0,o.useMemo)(()=>(0,g.findHeadInCache)(M,k[1]),[M,k]),I=(0,o.useMemo)(()=>(0,w.getSelectedParams)(k),[k]),F=(0,o.useMemo)(()=>({parentTree:k,parentCacheNode:M,parentSegmentPath:null,url:h}),[k,M,h]),U=(0,o.useMemo)(()=>({tree:k,focusAndScrollRef:D,nextUrl:C}),[k,D,C]);if(null!==N){let[e,r]=N;t=(0,i.jsx)(j,{headCacheNode:e},r)}else t=null;let V=(0,i.jsxs)(m.RedirectBoundary,{children:[t,M.rsc,(0,i.jsx)(p.AppRouterAnnouncer,{tree:k})]});return V=(0,i.jsx)(d.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:V}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(_,{appRouterState:f}),(0,i.jsx)(L,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:I,children:(0,i.jsx)(u.PathnameContext.Provider,{value:S,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:x,children:(0,i.jsx)(a.GlobalLayoutRouterContext.Provider,{value:U,children:(0,i.jsx)(a.AppRouterContext.Provider,{value:E.publicAppRouterInstance,children:(0,i.jsx)(a.LayoutRouterContext.Provider,{value:F,children:V})})})})})})]})}function C(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:o}=e;return(0,x.useNavFailureHandler)(),(0,i.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,i.jsx)(k,{actionQueue:t,assetPrefix:o,globalError:[r,n]})})}let D=new Set,N=new Set;function L(){let[,e]=o.default.useState(0),t=D.size;return(0,o.useEffect)(()=>{let r=()=>e(e=>e+1);return N.add(r),t!==D.size&&r(),()=>{N.delete(r)}},[t,e]),[...D].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=D.size;return D.add(e),D.size!==t&&N.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};