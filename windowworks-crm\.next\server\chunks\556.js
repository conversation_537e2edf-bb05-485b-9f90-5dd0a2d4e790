"use strict";exports.id=556,exports.ids=[556],exports.modules={13:(e,t,s)=>{s.d(t,{J:()=>o});var r=s(687);s(3210);var a=s(8148),n=s(4780);function o({className:e,...t}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},3026:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(2688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},4729:(e,t,s)=>{s.d(t,{T:()=>o});var r=s(687),a=s(3210),n=s(4780);let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));o.displayName="Textarea"},5079:(e,t,s)=>{s.d(t,{bq:()=>f,eb:()=>x,gC:()=>h,l6:()=>c,yv:()=>u});var r=s(687),a=s(3210),n=s(7822),o=s(5891),i=s(3589),l=s(3964),d=s(4780);let c=n.bL;n.YJ;let u=n.WT,f=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(n.l9,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]}));f.displayName=n.l9.displayName;let p=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}));p.displayName=n.PP.displayName;let m=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"h-4 w-4"})}));m.displayName=n.wn.displayName;let h=a.forwardRef(({className:e,children:t,position:s="popper",...a},o)=>(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{ref:o,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...a,children:[(0,r.jsx)(p,{}),(0,r.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(m,{})]})}));h.displayName=n.UC.displayName,a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.JU.displayName;let x=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(n.q7,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})}),(0,r.jsx)(n.p4,{children:t})]}));x.displayName=n.q7.displayName,a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.wv.displayName},5763:(e,t,s)=>{s.d(t,{Xi:()=>l,av:()=>d,j7:()=>i,tU:()=>o});var r=s(687);s(3210);var a=s(5146),n=s(4780);function o({className:e,...t}){return(0,r.jsx)(a.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",e),...t})}function i({className:e,...t}){return(0,r.jsx)(a.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function l({className:e,...t}){return(0,r.jsx)(a.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function d({className:e,...t}){return(0,r.jsx)(a.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",e),...t})}},8054:(e,t,s)=>{s.d(t,{DashboardLayout:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\dashboard\\dashboard-layout.tsx","DashboardLayout")},8920:(e,t,s)=>{s.d(t,{N:()=>b});var r=s(687),a=s(3210),n=s(2157),o=s(2789),i=s(2743),l=s(1279),d=s(8171),c=s(2582);class u extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,s=(0,d.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=s-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f({children:e,isPresent:t,anchorX:s,root:n}){let o=(0,a.useId)(),i=(0,a.useRef)(null),l=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,a.useContext)(c.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:r,top:a,left:c,right:u}=l.current;if(t||!i.current||!e||!r)return;let f="left"===s?`left: ${c}`:`right: ${u}`;i.current.dataset.motionPopId=o;let p=document.createElement("style");d&&(p.nonce=d);let m=n??document.head;return m.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${f}px !important;
            top: ${a}px !important;
          }
        `),()=>{m.removeChild(p),m.contains(p)&&m.removeChild(p)}},[t]),(0,r.jsx)(u,{isPresent:t,childRef:i,sizeRef:l,children:a.cloneElement(e,{ref:i})})}let p=({children:e,initial:t,isPresent:s,onExitComplete:n,custom:i,presenceAffectsLayout:d,mode:c,anchorX:u,root:p})=>{let h=(0,o.M)(m),x=(0,a.useId)(),g=!0,b=(0,a.useMemo)(()=>(g=!1,{id:x,initial:t,isPresent:s,custom:i,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;n&&n()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[s,h,n]);return d&&g&&(b={...b}),(0,a.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[s]),a.useEffect(()=>{s||h.size||!n||n()},[s]),"popLayout"===c&&(e=(0,r.jsx)(f,{isPresent:s,anchorX:u,root:p,children:e})),(0,r.jsx)(l.t.Provider,{value:b,children:e})};function m(){return new Map}var h=s(6044);let x=e=>e.key||"";function g(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}let b=({children:e,custom:t,initial:s=!0,onExitComplete:l,presenceAffectsLayout:d=!0,mode:c="sync",propagate:u=!1,anchorX:f="left",root:m})=>{let[b,y]=(0,h.xQ)(u),v=(0,a.useMemo)(()=>g(e),[e]),w=u&&!b?[]:v.map(x),j=(0,a.useRef)(!0),N=(0,a.useRef)(v),C=(0,o.M)(()=>new Map),[k,R]=(0,a.useState)(v),[E,L]=(0,a.useState)(v);(0,i.E)(()=>{j.current=!1,N.current=v;for(let e=0;e<E.length;e++){let t=x(E[e]);w.includes(t)?C.delete(t):!0!==C.get(t)&&C.set(t,!1)}},[E,w.length,w.join("-")]);let P=[];if(v!==k){let e=[...v];for(let t=0;t<E.length;t++){let s=E[t],r=x(s);w.includes(r)||(e.splice(t,0,s),P.push(s))}return"wait"===c&&P.length&&(e=P),L(g(e)),R(v),null}let{forceRender:M}=(0,a.useContext)(n.L);return(0,r.jsx)(r.Fragment,{children:E.map(e=>{let a=x(e),n=(!u||!!b)&&(v===E||w.includes(a));return(0,r.jsx)(p,{isPresent:n,initial:(!j.current||!!s)&&void 0,custom:t,presenceAffectsLayout:d,mode:c,root:m,onExitComplete:n?void 0:()=>{if(!C.has(a))return;C.set(a,!0);let e=!0;C.forEach(t=>{t||(e=!1)}),e&&(M?.(),L(N.current),u&&y?.(),l&&l())},anchorX:f,children:e},a)})})}}};