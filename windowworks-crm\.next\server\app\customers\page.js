(()=>{var e={};e.id=812,e.ids=[812],e.modules={13:(e,s,a)=>{"use strict";a.d(s,{J:()=>i});var t=a(687);a(3210);var r=a(8148),l=a(4780);function i({className:e,...s}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},169:(e,s,a)=>{"use strict";a.d(s,{default:()=>t});let t=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Source Code\\\\GoogleGemini\\\\windowworks-crm\\\\src\\\\components\\\\customers\\\\customers-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\customers\\customers-page.tsx","default")},506:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>i});var t=a(7413),r=a(8054),l=a(169);function i(){return(0,t.jsx)(r.DashboardLayout,{children:(0,t.jsx)(l.default,{})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1193:(e,s,a)=>{Promise.resolve().then(a.bind(a,5494)),Promise.resolve().then(a.bind(a,7634))},1550:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3503:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>o,L3:()=>x,c7:()=>m,lG:()=>n});var t=a(687);a(3210);var r=a(6134),l=a(1860),i=a(4780);function n({...e}){return(0,t.jsx)(r.bL,{"data-slot":"dialog",...e})}function d({...e}){return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...s}){return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...s})}function o({className:e,children:s,showCloseButton:a=!0,...n}){return(0,t.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,t.jsx)(c,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...n,children:[s,a&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(l.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...s})}function x({className:e,...s}){return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...s})}},3873:e=>{"use strict";e.exports=require("path")},4729:(e,s,a)=>{"use strict";a.d(s,{T:()=>i});var t=a(687),r=a(3210),l=a(4780);let i=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));i.displayName="Textarea"},4745:(e,s,a)=>{Promise.resolve().then(a.bind(a,169)),Promise.resolve().then(a.bind(a,8054))},5079:(e,s,a)=>{"use strict";a.d(s,{bq:()=>x,eb:()=>f,gC:()=>p,l6:()=>o,yv:()=>m});var t=a(687),r=a(3210),l=a(7822),i=a(5891),n=a(3589),d=a(3964),c=a(4780);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(l.l9,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.l9.displayName;let u=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.PP,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}));u.displayName=l.PP.displayName;let h=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.wn,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));h.displayName=l.wn.displayName;let p=r.forwardRef(({className:e,children:s,position:a="popper",...r},i)=>(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,t.jsx)(u,{}),(0,t.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,t.jsx)(h,{})]})}));p.displayName=l.UC.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.JU,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.JU.displayName;let f=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(l.q7,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:s})]}));f.displayName=l.q7.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.wv,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.wv.displayName},5494:(e,s,a)=>{"use strict";a.d(s,{default:()=>Y});var t=a(687),r=a(3210),l=a(6001),i=a(1312),n=a(2688);let d=(0,n.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),c=(0,n.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var o=a(3928),m=a(5336),x=a(6349),u=a(1860),h=a(6474),p=a(9270),f=a(1158),j=a(1550);let g=(0,n.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var v=a(7992),b=a(228),N=a(3661),y=a(3861),w=a(3143);let C=(0,n.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var A=a(8233),k=a(9523),S=a(9667),P=a(4493),R=a(6834),L=a(2584),q=a(1342),z=a(5079),M=a(6896),$=a(8869);let F=(0,n.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var Z=a(3613),_=a(1862);let D=(0,n.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),E=(0,n.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var B=a(8819),G=a(13),I=a(4729),T=a(5763),J=a(3503),V=a(2587);let W=[{id:"blinds",label:"Blinds",popular:!0},{id:"shutters",label:"Shutters",popular:!0},{id:"shades",label:"Shades",popular:!0},{id:"curtains",label:"Curtains",popular:!1},{id:"drapes",label:"Drapes",popular:!1},{id:"valances",label:"Valances",popular:!1}],U=[{id:"white",label:"White",hex:"#FFFFFF"},{id:"cream",label:"Cream",hex:"#F5F5DC"},{id:"beige",label:"Beige",hex:"#F5F5DC"},{id:"gray",label:"Gray",hex:"#808080"},{id:"brown",label:"Brown",hex:"#A52A2A"},{id:"black",label:"Black",hex:"#000000"}],O=[{value:"under-500",label:"Under $500"},{value:"500-1000",label:"$500 - $1,000"},{value:"1000-2500",label:"$1,000 - $2,500"},{value:"2500-5000",label:"$2,500 - $5,000"},{value:"over-5000",label:"Over $5,000"}],H=[{value:"email",label:"Email"},{value:"phone",label:"Phone"},{value:"text",label:"Text/SMS"},{value:"app",label:"In-App Notifications"}];function X({isOpen:e,onClose:s,onSave:a}){let{accentColor:l}=(0,V.D)(),[i,n]=(0,r.useState)("details"),[d,c]=(0,r.useState)(!1),[m,x]=(0,r.useState)({}),[u,h]=(0,r.useState)(!1),[p,f]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:"",address:{street:"",city:"",state:"",zipCode:"",country:"United States"},preferences:{windowTypes:[],preferredColors:[],budgetRange:"",communicationMethod:"email"},notes:""}),b=(0,r.useCallback)(()=>{let e={};return p.firstName.trim()||(e.firstName="First name is required"),p.lastName.trim()||(e.lastName="Last name is required"),p.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(p.email)||(e.email="Please enter a valid email address"):e.email="Email is required",p.phone.trim()||(e.phone="Phone number is required"),p.address.street.trim()||(e.street="Street address is required"),p.address.city.trim()||(e.city="City is required"),p.address.state.trim()||(e.state="State is required"),p.address.zipCode.trim()||(e.zipCode="ZIP code is required"),x(e),0===Object.keys(e).length},[p]),N=(0,r.useCallback)(async()=>{h(!0),await new Promise(e=>setTimeout(e,1500));let e={windowTypes:["blinds","shutters"],preferredColors:["white","gray"],budgetRange:"1000-2500",communicationMethod:"email"};f(s=>({...s,preferences:{...s.preferences,...e}})),h(!1)},[]),y=async()=>{if(!b())return void(m.firstName||m.lastName||m.email||m.phone?n("details"):(m.street||m.city||m.state||m.zipCode)&&n("address"));c(!0);try{await a(p),s(),f({firstName:"",lastName:"",email:"",phone:"",address:{street:"",city:"",state:"",zipCode:"",country:"United States"},preferences:{windowTypes:[],preferredColors:[],budgetRange:"",communicationMethod:"email"},notes:""})}catch(e){console.error("Error saving customer:",e)}finally{c(!1)}},w=(e,s)=>{f(a=>{let t=e.split("."),r={...a},l=r;for(let e=0;e<t.length-1;e++)void 0===l[t[e]]&&(l[t[e]]={}),l=l[t[e]];return l[t[t.length-1]]=s,r}),m[e]&&x(s=>({...s,[e]:""}))},C=e=>{let s=p.preferences.windowTypes;w("preferences.windowTypes",s.includes(e)?s.filter(s=>s!==e):[...s,e])},A=e=>{let s=p.preferences.preferredColors;w("preferences.preferredColors",s.includes(e)?s.filter(s=>s!==e):[...s,e])};return(0,t.jsx)(J.lG,{open:e,onOpenChange:s,children:(0,t.jsx)(J.Cf,{className:"max-w-6xl sm:max-w-6xl max-h-[70vh] overflow-hidden p-0",children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsx)(J.c7,{className:"px-6 py-4 border-b border-gray-200 flex-shrink-0",children:(0,t.jsx)(J.L3,{className:"text-xl font-semibold text-slate-700",children:"Add New Customer"})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,t.jsxs)(T.tU,{value:i,onValueChange:n,className:"space-y-6",children:[(0,t.jsxs)(T.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsxs)(T.Xi,{value:"details",className:"flex items-center gap-2",children:[(0,t.jsx)($.A,{className:"h-4 w-4"}),"Details & Address"]}),(0,t.jsxs)(T.Xi,{value:"preferences",className:"flex items-center gap-2",children:[(0,t.jsx)(F,{className:"h-4 w-4"}),"Preferences & Notes"]})]}),(0,t.jsx)(T.av,{value:"details",className:"space-y-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,t.jsx)($.A,{className:"h-5 w-5"}),"Personal Information"]})}),(0,t.jsxs)(P.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"firstName",className:"text-slate-600",children:"First Name *"}),(0,t.jsx)(S.p,{id:"firstName",value:p.firstName,onChange:e=>w("firstName",e.target.value),className:`border-gray-200 focus:border-[var(--color-brand-primary)] ${m.firstName?"border-red-500":""}`,placeholder:"Enter first name"}),m.firstName&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),m.firstName]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"lastName",className:"text-slate-600",children:"Last Name *"}),(0,t.jsx)(S.p,{id:"lastName",value:p.lastName,onChange:e=>w("lastName",e.target.value),className:`border-gray-200 focus:border-[var(--color-brand-primary)] ${m.lastName?"border-red-500":""}`,placeholder:"Enter last name"}),m.lastName&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),m.lastName]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"email",className:"text-slate-600",children:"Email Address *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(j.A,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),(0,t.jsx)(S.p,{id:"email",type:"email",value:p.email,onChange:e=>w("email",e.target.value),className:`pl-10 border-gray-200 focus:border-[var(--color-brand-primary)] ${m.email?"border-red-500":""}`,placeholder:"<EMAIL>"})]}),m.email&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),m.email]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"phone",className:"text-slate-600",children:"Phone Number *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),(0,t.jsx)(S.p,{id:"phone",type:"tel",value:p.phone,onChange:e=>w("phone",e.target.value),className:`pl-10 border-gray-200 focus:border-[var(--color-brand-primary)] ${m.phone?"border-red-500":""}`,placeholder:"(*************"})]}),m.phone&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),m.phone]})]})]})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5"}),"Address Information"]})}),(0,t.jsxs)(P.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"street",className:"text-slate-600",children:"Street Address *"}),(0,t.jsx)(S.p,{id:"street",value:p.address.street,onChange:e=>w("address.street",e.target.value),className:`border-gray-200 focus:border-[var(--color-brand-primary)] ${m.street?"border-red-500":""}`,placeholder:"123 Main Street"}),m.street&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),m.street]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"city",className:"text-slate-600",children:"City *"}),(0,t.jsx)(S.p,{id:"city",value:p.address.city,onChange:e=>w("address.city",e.target.value),className:`border-gray-200 focus:border-[var(--color-brand-primary)] ${m.city?"border-red-500":""}`,placeholder:"Springfield"}),m.city&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),m.city]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"state",className:"text-slate-600",children:"State *"}),(0,t.jsx)(S.p,{id:"state",value:p.address.state,onChange:e=>w("address.state",e.target.value),className:`border-gray-200 focus:border-[var(--color-brand-primary)] ${m.state?"border-red-500":""}`,placeholder:"IL"}),m.state&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),m.state]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"zipCode",className:"text-slate-600",children:"ZIP Code *"}),(0,t.jsx)(S.p,{id:"zipCode",value:p.address.zipCode,onChange:e=>w("address.zipCode",e.target.value),className:`border-gray-200 focus:border-[var(--color-brand-primary)] ${m.zipCode?"border-red-500":""}`,placeholder:"62704"}),m.zipCode&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),m.zipCode]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"country",className:"text-slate-600",children:"Country"}),(0,t.jsxs)(z.l6,{value:p.address.country,onValueChange:e=>w("address.country",e),children:[(0,t.jsx)(z.bq,{className:"border-gray-200",children:(0,t.jsx)(z.yv,{})}),(0,t.jsxs)(z.gC,{children:[(0,t.jsx)(z.eb,{value:"United States",children:"United States"}),(0,t.jsx)(z.eb,{value:"Canada",children:"Canada"}),(0,t.jsx)(z.eb,{value:"Mexico",children:"Mexico"})]})]})]})]})]})]})]})}),(0,t.jsxs)(T.av,{value:"preferences",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-700",children:"Customer Preferences & Notes"}),(0,t.jsxs)(k.$,{variant:"outline",onClick:N,disabled:u,className:"border-gray-200",style:{borderColor:l,color:l},children:[u?(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(D,{className:"h-4 w-4 mr-2"}),u?"Analyzing...":"AI Suggest"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsx)(P.ZB,{className:"text-base text-slate-700",children:"Window Types"}),(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Select preferred window treatment types"})]}),(0,t.jsx)(P.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 gap-3",children:W.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(M.S,{id:e.id,checked:p.preferences.windowTypes.includes(e.id),onCheckedChange:()=>C(e.id),className:"border-gray-300"}),(0,t.jsxs)(G.J,{htmlFor:e.id,className:"text-sm font-normal text-slate-600 flex items-center gap-2",children:[e.label,e.popular&&(0,t.jsx)(R.E,{variant:"secondary",className:"text-xs px-1.5 py-0.5",children:"Popular"})]})]},e.id))})})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsx)(P.ZB,{className:"text-base text-slate-700",children:"Preferred Colors"}),(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Select preferred color schemes"})]}),(0,t.jsx)(P.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-2 gap-3",children:U.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(M.S,{id:e.id,checked:p.preferences.preferredColors.includes(e.id),onCheckedChange:()=>A(e.id),className:"border-gray-300"}),(0,t.jsxs)(G.J,{htmlFor:e.id,className:"text-sm font-normal text-slate-600 flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border border-gray-300",style:{backgroundColor:e.hex}}),e.label]})]},e.id))})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsxs)(P.ZB,{className:"text-base text-slate-700 flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),"Budget Range"]})}),(0,t.jsx)(P.Wu,{children:(0,t.jsxs)(z.l6,{value:p.preferences.budgetRange,onValueChange:e=>w("preferences.budgetRange",e),children:[(0,t.jsx)(z.bq,{className:"border-gray-200",children:(0,t.jsx)(z.yv,{placeholder:"Select budget range"})}),(0,t.jsx)(z.gC,{children:O.map(e=>(0,t.jsx)(z.eb,{value:e.value,children:e.label},e.value))})]})})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsx)(P.ZB,{className:"text-base text-slate-700",children:"Communication"})}),(0,t.jsx)(P.Wu,{children:(0,t.jsxs)(z.l6,{value:p.preferences.communicationMethod,onValueChange:e=>w("preferences.communicationMethod",e),children:[(0,t.jsx)(z.bq,{className:"border-gray-200",children:(0,t.jsx)(z.yv,{})}),(0,t.jsx)(z.gC,{children:H.map(e=>(0,t.jsx)(z.eb,{value:e.value,children:e.label},e.value))})]})})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,t.jsx)(E,{className:"h-5 w-5"}),"Additional Notes"]}),(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Add any additional information about the customer"})]}),(0,t.jsx)(P.Wu,{children:(0,t.jsx)(I.T,{value:p.notes,onChange:e=>w("notes",e.target.value),className:"min-h-[200px] border-gray-200 focus:border-[var(--color-brand-primary)]",placeholder:"Special requirements, previous interactions, design preferences, installation considerations..."})})]})]})]})]})]})}),(0,t.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 flex-shrink-0",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(k.$,{variant:"outline",onClick:s,disabled:d,className:"border-gray-200",children:"Cancel"}),(0,t.jsx)(k.$,{onClick:y,disabled:d,className:"text-white min-w-[120px]",style:{backgroundColor:l,borderColor:l},children:d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Save Customer"]})})]})})]})})})}let K=[{id:"1",name:"Sarah Johnson",email:"<EMAIL>",phone:"(*************",address:"123 Maple Street, Springfield, IL 62704",status:"Active",lastProject:"2024-12-15",totalRevenue:2840,projectCount:3,preferences:["Blinds","Shutters"],avatar:null},{id:"2",name:"Robert Chen",email:"<EMAIL>",phone:"(*************",address:"456 Oak Avenue, Springfield, IL 62701",status:"Active",lastProject:"2025-01-08",totalRevenue:1560,projectCount:2,preferences:["Shades"],avatar:null},{id:"3",name:"Emma Davis",email:"<EMAIL>",phone:"(*************",address:"789 Pine Road, Springfield, IL 62702",status:"Pending",lastProject:null,totalRevenue:0,projectCount:0,preferences:["Shutters","Shades"],avatar:null},{id:"4",name:"Michael Williams",email:"<EMAIL>",phone:"(*************",address:"321 Elm Street, Springfield, IL 62703",status:"Active",lastProject:"2024-11-22",totalRevenue:4250,projectCount:5,preferences:["Blinds","Shutters","Shades"],avatar:null},{id:"5",name:"Lisa Anderson",email:"<EMAIL>",phone:"(*************",address:"654 Cedar Lane, Springfield, IL 62705",status:"Inactive",lastProject:"2024-08-14",totalRevenue:890,projectCount:1,preferences:["Blinds"],avatar:null}],Q=[{title:"Total Customers",value:"89",change:"-2%",icon:i.A,trend:"down"},{title:"New Customers",value:"12",change:"+5%",icon:d,trend:"up"},{title:"High-Value Clients",value:"25",change:"",icon:c,trend:"neutral"},{title:"Avg Revenue/Customer",value:"$520",change:"+8%",icon:o.A,trend:"up"}];function Y(){let[e,s]=(0,r.useState)(""),[a,i]=(0,r.useState)("all"),[n,d]=(0,r.useState)("all"),[c,o]=(0,r.useState)("name"),[$,F]=(0,r.useState)([]),[Z,_]=(0,r.useState)(!1),[D,E]=(0,r.useState)(1),B=K.filter(s=>{let t=s.name.toLowerCase().includes(e.toLowerCase())||s.email.toLowerCase().includes(e.toLowerCase()),r="all"===a||s.status.toLowerCase()===a.toLowerCase(),l="all"===n||s.preferences.some(e=>e.toLowerCase()===n.toLowerCase());return t&&r&&l}).sort((e,s)=>{switch(c){case"name":return e.name.localeCompare(s.name);case"date":return new Date(s.lastProject||0).getTime()-new Date(e.lastProject||0).getTime();case"revenue":return s.totalRevenue-e.totalRevenue;default:return 0}}),G=Math.ceil(B.length/10),I=B.slice((D-1)*10,10*D),T=e=>{F(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},J=e=>{switch(e.toLowerCase()){case"active":return"default";case"pending":return"secondary";default:return"outline"}},V=e=>{switch(e.toLowerCase()){case"active":return m.A;case"pending":return x.A;default:return u.A}},W=async e=>{try{console.log("Saving customer:",e),await new Promise(e=>setTimeout(e,1e3)),console.log("Customer saved successfully")}catch(e){throw console.error("Error saving customer:",e),e}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Customers"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage your client base and preferences"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(R.E,{variant:"secondary",className:"text-xs",children:"Saturday, July 12, 2025"}),(0,t.jsxs)(k.$,{className:"bg-primary hover:bg-primary/90",onClick:()=>_(!0),children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"New Customer"]})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Q.map((e,s)=>(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:(0,t.jsxs)(P.Zp,{className:"hover:border-border/80 transition-colors",children:[(0,t.jsx)(P.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"p-2 rounded-md bg-primary/10",children:(0,t.jsx)(e.icon,{className:"h-5 w-5 text-primary"})}),e.change&&(0,t.jsx)(R.E,{variant:"up"===e.trend?"default":"down"===e.trend?"destructive":"secondary",className:"text-xs",children:e.change})]})}),(0,t.jsx)(P.Wu,{children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-foreground",children:e.value}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title})]})})]})},e.title))}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,t.jsx)(P.Zp,{children:(0,t.jsx)(P.aR,{className:"pb-4",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex flex-1 items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,t.jsx)(S.p,{placeholder:"Search customers...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(z.l6,{value:a,onValueChange:i,children:[(0,t.jsx)(z.bq,{className:"w-32",children:(0,t.jsx)(z.yv,{placeholder:"Status"})}),(0,t.jsxs)(z.gC,{children:[(0,t.jsx)(z.eb,{value:"all",children:"All Status"}),(0,t.jsx)(z.eb,{value:"active",children:"Active"}),(0,t.jsx)(z.eb,{value:"pending",children:"Pending"}),(0,t.jsx)(z.eb,{value:"inactive",children:"Inactive"})]})]}),(0,t.jsxs)(z.l6,{value:n,onValueChange:d,children:[(0,t.jsx)(z.bq,{className:"w-36",children:(0,t.jsx)(z.yv,{placeholder:"Preference"})}),(0,t.jsxs)(z.gC,{children:[(0,t.jsx)(z.eb,{value:"all",children:"All Products"}),(0,t.jsx)(z.eb,{value:"blinds",children:"Blinds"}),(0,t.jsx)(z.eb,{value:"shutters",children:"Shutters"}),(0,t.jsx)(z.eb,{value:"shades",children:"Shades"})]})]}),(0,t.jsxs)(z.l6,{value:c,onValueChange:o,children:[(0,t.jsx)(z.bq,{className:"w-32",children:(0,t.jsx)(z.yv,{placeholder:"Sort by"})}),(0,t.jsxs)(z.gC,{children:[(0,t.jsx)(z.eb,{value:"name",children:"Name"}),(0,t.jsx)(z.eb,{value:"date",children:"Date Added"}),(0,t.jsx)(z.eb,{value:"revenue",children:"Revenue"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(k.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Export"]}),$.length>0&&(0,t.jsxs)(R.E,{variant:"secondary",children:[$.length," selected"]})]})]})})})}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,t.jsx)(P.Zp,{children:(0,t.jsxs)(P.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"border-b border-border",children:(0,t.jsxs)("tr",{className:"bg-accent/20",children:[(0,t.jsx)("th",{className:"text-left p-4 w-12",children:(0,t.jsx)(M.S,{checked:$.length===I.length&&I.length>0,onCheckedChange:()=>{$.length===I.length?F([]):F(I.map(e=>e.id))}})}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Customer"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Contact"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Address"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Last Project"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Status"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Revenue"}),(0,t.jsx)("th",{className:"text-right p-4 w-16"})]})}),(0,t.jsx)("tbody",{children:I.map((e,s)=>{let a=V(e.status);return(0,t.jsxs)(l.P.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},className:"border-b border-border hover:bg-accent/30 transition-colors",children:[(0,t.jsxs)("td",{className:"p-4",children:["                            ",(0,t.jsx)(M.S,{checked:$.includes(e.id),onCheckedChange:()=>T(e.id),onClick:e=>e.stopPropagation()})]}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(L.eu,{className:"h-8 w-8",children:[(0,t.jsx)(L.BK,{src:e.avatar||void 0}),(0,t.jsx)(L.q5,{className:"bg-primary/10 text-primary",children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.projectCount," project",1!==e.projectCount?"s":""]})]})]})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(j.A,{className:"h-3 w-3 mr-2"}),e.email]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(g,{className:"h-3 w-3 mr-2"}),e.phone]})]})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(v.A,{className:"h-3 w-3 mr-2 flex-shrink-0"}),(0,t.jsx)("span",{className:"truncate max-w-48",children:e.address})]})}),(0,t.jsx)("td",{className:"p-4",children:e.lastProject?(0,t.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(b.A,{className:"h-3 w-3 mr-2"}),new Date(e.lastProject).toLocaleDateString()]}):(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"No projects"})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)(R.E,{variant:J(e.status),className:"text-xs",children:[(0,t.jsx)(a,{className:"h-3 w-3 mr-1"}),e.status]})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)("span",{className:"font-medium text-foreground",children:["$",e.totalRevenue.toLocaleString()]})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)(q.rI,{children:[(0,t.jsx)(q.ty,{asChild:!0,onClick:e=>e.stopPropagation(),children:(0,t.jsx)(k.$,{variant:"ghost",size:"sm",children:(0,t.jsx)(N.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(q.SQ,{align:"end",children:[(0,t.jsxs)(q._2,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),(0,t.jsxs)(q._2,{children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Edit Customer"]}),(0,t.jsxs)(q._2,{children:[(0,t.jsx)(C,{className:"h-4 w-4 mr-2"}),"View Projects"]}),(0,t.jsx)(q.mB,{}),(0,t.jsxs)(q._2,{className:"text-destructive",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})})]},e.id)})})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-border",children:[(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",(D-1)*10+1," to ",Math.min(10*D,B.length)," of ",B.length," customers"]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.$,{variant:"outline",size:"sm",onClick:()=>E(e=>Math.max(1,e-1)),disabled:1===D,children:"Previous"}),(0,t.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,G)},(e,s)=>{let a=s+1;return(0,t.jsx)(k.$,{variant:D===a?"default":"outline",size:"sm",onClick:()=>E(a),className:"w-8 h-8 p-0",children:a},a)})}),(0,t.jsx)(k.$,{variant:"outline",size:"sm",onClick:()=>E(e=>Math.min(G,e+1)),disabled:D===G,children:"Next"})]})]})]})})}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(P.Zp,{className:"lg:col-span-2",children:[(0,t.jsx)(P.aR,{children:(0,t.jsx)(P.ZB,{children:"Top Customers by Revenue"})}),(0,t.jsx)(P.Wu,{children:(0,t.jsx)("div",{className:"space-y-3",children:K.sort((e,s)=>s.totalRevenue-e.totalRevenue).slice(0,5).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-md bg-accent/20",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("span",{className:"text-sm font-medium text-muted-foreground w-4",children:["#",s+1]}),(0,t.jsx)(L.eu,{className:"h-6 w-6",children:(0,t.jsx)(L.q5,{className:"bg-primary/10 text-primary text-xs",children:e.name.split(" ").map(e=>e[0]).join("")})}),(0,t.jsx)("span",{className:"text-sm font-medium text-foreground",children:e.name})]}),(0,t.jsxs)("span",{className:"text-sm font-medium text-foreground",children:["$",e.totalRevenue.toLocaleString()]})]},e.id))})})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsx)(P.ZB,{children:"AI Insights"})}),(0,t.jsx)(P.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-3 rounded-md bg-primary/5 border border-primary/20",children:[(0,t.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Outreach Recommendation"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"5 inactive clients haven't been contacted in 90+ days. Consider a follow-up campaign."})]}),(0,t.jsxs)("div",{className:"p-3 rounded-md bg-accent/20",children:[(0,t.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Seasonal Opportunity"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"12 customers have shown interest in shutters. Summer promotion recommended."})]})]})})]})]}),(0,t.jsx)(X,{isOpen:Z,onClose:()=>_(!1),onSave:W})]})}},5763:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>c,j7:()=>n,tU:()=>i});var t=a(687);a(3210);var r=a(5146),l=a(4780);function i({className:e,...s}){return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",e),...s})}function n({className:e,...s}){return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...s})}function d({className:e,...s}){return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function c({className:e,...s}){return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",e),...s})}},6896:(e,s,a)=>{"use strict";a.d(s,{S:()=>d});var t=a(687),r=a(3210),l=a(211),i=a(3964),n=a(4780);let d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.bL,{ref:a,className:(0,n.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:(0,t.jsx)(l.C1,{className:(0,n.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})}));d.displayName=l.bL.displayName},7992:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8054:(e,s,a)=>{"use strict";a.d(s,{DashboardLayout:()=>t});let t=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\dashboard\\dashboard-layout.tsx","DashboardLayout")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9451:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=a(5239),r=a(8088),l=a(8170),i=a.n(l),n=a(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let c={children:["",{children:["customers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,506)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\customers\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\customers\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/customers/page",pathname:"/customers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,145,861,979,121,171],()=>a(9451));module.exports=t})();