@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: 3px;
  --radius-md: 4px;
  --radius-lg: 5px;
  --radius-xl: 6px;
  
  /* WindowWorks CRM Brand Colors */
  --color-brand-primary: #D97706;
  --color-brand-secondary: #4A5563;
  --color-brand-accent: #F9FAFB;
}

:root {
  --radius: 5px;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.637 0.237 25.331);
  --destructive-foreground: oklch(0.985 0 0);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.871 0.006 286.286);
  --ring: oklch(0.871 0.006 286.286);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.871 0.006 286.286);
}

.dark {
  --background: oklch(0.09 0.005 285.823);
  --foreground: oklch(0.98 0 0);
  --card: oklch(0.15 0.005 285.823);
  --card-foreground: oklch(0.98 0 0);
  --popover: oklch(0.15 0.005 285.823);
  --popover-foreground: oklch(0.98 0 0);
  --primary: oklch(0.52 0.16 41.116);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.20 0.006 286.033);
  --secondary-foreground: oklch(0.98 0 0);
  --muted: oklch(0.18 0.006 285.885);
  --muted-foreground: oklch(0.70 0.01 286);
  --accent: oklch(0.20 0.006 285.885);
  --accent-foreground: oklch(0.98 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.18 0.005 286);
  --input: oklch(0.16 0.005 286);
  --ring: oklch(0.442 0.017 285.786);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.10 0 0);
  --sidebar-foreground: oklch(0.98 0 0);
  --sidebar-primary: oklch(0.52 0.16 41.116);
  --sidebar-primary-foreground: oklch(0.98 0 0);
  --sidebar-accent: oklch(0.15 0 0);
  --sidebar-accent-foreground: oklch(0.98 0 0);
  --sidebar-border: oklch(0.18 0.005 286);
  --sidebar-ring: oklch(0.442 0.017 285.786);
}

/* WindowWorks CRM - Origin UI Theme Overrides */
.shadow-xs,
.shadow-sm,
.shadow,
.shadow-md,
.shadow-lg,
.shadow-xl,
.shadow-2xl {
  box-shadow: none !important;
}

/* Origin UI inspired no-shadows design */
.hover\:shadow-lg:hover,
.hover\:shadow-md:hover,
.hover\:shadow:hover {
  box-shadow: none !important;
  border-color: rgb(var(--border)) !important;
}

/* Focus states use rings instead of shadows */
.focus-visible\:ring-ring\/50:focus-visible {
  box-shadow: 0 0 0 3px oklch(var(--ring) / 0.5) !important;
}

/* Origin UI component styling */
.origin-card {
  background: oklch(var(--card));
  color: oklch(var(--card-foreground));
  border: 1px solid oklch(var(--border));
  border-radius: var(--radius);
}

.origin-button {
  border-radius: var(--radius);
  transition: color 0.2s, background-color 0.2s, border-color 0.2s;
}

.origin-input {
  background: oklch(var(--background));
  border: 1px solid oklch(var(--border));
  border-radius: var(--radius);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.origin-input:focus {
  outline: none;
  border-color: oklch(var(--ring));
  box-shadow: 0 0 0 3px oklch(var(--ring) / 0.2);
}

* {
  @apply border-border outline-ring/50;
}

/* Global border radius override - 5px everywhere */
.rounded,
.rounded-sm,
.rounded-md,
.rounded-lg,
.rounded-xl,
.rounded-2xl,
.rounded-3xl,
button,
input,
select,
textarea,
.card,
.border,
[class*="border-"],
[class*="rounded-"] {
  border-radius: 5px !important;
}

/* Specific overrides for elements that should maintain their radius */
.rounded-none {
  border-radius: 0 !important;
}

.rounded-full,
.avatar,
.h-8.w-8,
.h-6.w-6,
.h-4.w-4,
.h-3.w-3,
.h-2.w-2 {
  border-radius: 50% !important;
}

/* Ensure avatars and circular elements stay circular */
[class*="avatar"],
[class*="circle"] {
  border-radius: 50% !important;
}

body {
  background-color: oklch(var(--background));
  color: oklch(var(--foreground));
}

/* Origin UI Dark Mode Enhancements */
.dark .origin-card,
.dark [data-slot="card"] {
  background: oklch(var(--card));
  border-color: oklch(var(--border));
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.dark .origin-card:hover,
.dark [data-slot="card"]:hover {
  border-color: oklch(var(--border) / 0.8);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* Enhanced dark mode card styling */
.dark .backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Better dark mode badge styling */
.dark [data-badge] {
  background: oklch(var(--secondary));
  color: oklch(var(--secondary-foreground));
  border: 1px solid oklch(var(--border));
}

/* Enhanced progress bars for dark mode */
.dark [data-progress] {
  background: oklch(var(--muted));
}

.dark [data-progress] > div {
  background: oklch(var(--primary));
}
