'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import DashboardOverview from '@/components/dashboard/dashboard-overview'

export default function Home() {
  const { setUser, isAuthenticated } = useAuthStore()

  useEffect(() => {
    // Mock authentication - in a real app, you'd check with Supabase
    if (!isAuthenticated) {
      // Set a mock admin user for development
      setUser({
        id: 'user_1',
        email: '<EMAIL>',
        role: 'admin',
        profile: {
          firstName: 'John',
          lastName: 'Admin',
          phone: '******-0123',
          avatar: undefined
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })
    }
  }, [isAuthenticated, setUser])

  return (
    <DashboardLayout>
      <DashboardOverview />
    </DashboardLayout>
  )
}
