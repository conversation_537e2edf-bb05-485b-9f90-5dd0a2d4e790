(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{359:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>J});var s=t(5155),r=t(2115),i=t(8294),n=t(871),l=t(6408),d=t(4395),c=t(4186),o=t(7580),m=t(5868);let x=(0,t(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var u=t(9074),h=t(646),p=t(4516),j=t(6695),v=t(6126),y=t(285),g=t(6081),b=t(3655),f="Progress",[N,k]=(0,g.A)(f),[w,P]=N(f),A=r.forwardRef((e,a)=>{var t,r,i,n;let{__scopeProgress:l,value:d=null,max:c,getValueLabel:o=I,...m}=e;(c||0===c)&&!L(c)&&console.error((t="".concat(c),r="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let x=L(c)?c:100;null===d||_(d,x)||console.error((i="".concat(d),n="Progress","Invalid prop `value` of value `".concat(i,"` supplied to `").concat(n,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let u=_(d,x)?d:null,h=T(u)?o(u,x):void 0;return(0,s.jsx)(w,{scope:l,value:u,max:x,children:(0,s.jsx)(b.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":T(u)?u:void 0,"aria-valuetext":h,role:"progressbar","data-state":R(u,x),"data-value":null!=u?u:void 0,"data-max":x,...m,ref:a})})});A.displayName=f;var M="ProgressIndicator",S=r.forwardRef((e,a)=>{var t;let{__scopeProgress:r,...i}=e,n=P(M,r);return(0,s.jsx)(b.sG.div,{"data-state":R(n.value,n.max),"data-value":null!=(t=n.value)?t:void 0,"data-max":n.max,...i,ref:a})});function I(e,a){return"".concat(Math.round(e/a*100),"%")}function R(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function T(e){return"number"==typeof e}function L(e){return T(e)&&!isNaN(e)&&e>0}function _(e,a){return T(e)&&!isNaN(e)&&e<=a&&e>=0}S.displayName=M;var C=t(9434);let D=r.forwardRef((e,a)=>{let{className:t,value:r,...i}=e;return(0,s.jsx)(A,{ref:a,className:(0,C.cn)("relative h-2 w-full overflow-hidden bg-secondary border border-border rounded-none",t),...i,children:(0,s.jsx)(S,{className:"h-full w-full flex-1 bg-primary transition-all duration-500 ease-in-out",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});D.displayName=A.displayName;let Z={totalProjects:156,activeProjects:24,completedProjects:132,totalCustomers:89,revenueThisMonth:45600,revenueLastMonth:38200},B=[{id:"1",type:"project-created",title:"New project created",description:"Kitchen blinds installation for Sarah Johnson",timestamp:"2 hours ago",user:"John Admin",entityId:"proj_123"},{id:"2",type:"task-completed",title:"Installation completed",description:"Living room shutters installed at 123 Oak Street",timestamp:"4 hours ago",user:"Mike Installer",entityId:"task_456"},{id:"3",type:"customer-added",title:"New customer added",description:"Emma Davis - Interested in bedroom shades",timestamp:"1 day ago",user:"Lisa Sales",entityId:"cust_789"}],E=[{title:"Total Projects",value:Z.totalProjects,change:"+12%",icon:d.A},{title:"Active Projects",value:Z.activeProjects,change:"+8%",icon:c.A},{title:"Total Customers",value:Z.totalCustomers,change:"+23%",icon:o.A},{title:"Monthly Revenue",value:"$".concat(Z.revenueThisMonth.toLocaleString()),change:"+19%",icon:m.A}],O=[{id:"1",title:"Install bedroom blinds",customer:"Sarah Johnson",time:"10:00 AM",location:"123 Maple Street",status:"scheduled"},{id:"2",title:"Measure living room windows",customer:"Robert Chen",time:"2:30 PM",location:"456 Oak Avenue",status:"in-progress"},{id:"3",title:"Final inspection",customer:"Mary Williams",time:"4:00 PM",location:"789 Pine Road",status:"pending"}];function W(){let e=((Z.revenueThisMonth-Z.revenueLastMonth)/Z.revenueLastMonth*100).toFixed(1);return(0,s.jsxs)("div",{className:"space-y-6 p-1",children:[(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Welcome back! Here's what's happening today."})]}),(0,s.jsx)("div",{className:"flex items-center space-x-3",children:(0,s.jsx)(v.E,{variant:"secondary",className:"text-xs dark:bg-secondary/80",children:new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:E.map((e,a)=>(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*a},children:(0,s.jsxs)(j.Zp,{className:"hover:border-border/60 transition-all duration-200 dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsx)(j.aR,{className:"pb-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"p-2 rounded-md bg-primary/10 dark:bg-primary/20",children:(0,s.jsx)(e.icon,{className:"h-5 w-5 text-primary"})}),(0,s.jsx)(v.E,{variant:"secondary",className:"text-xs dark:bg-secondary/80",children:e.change})]})}),(0,s.jsx)(j.Wu,{children:(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-foreground",children:e.value}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title})]})})]})},e.title))}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},className:"lg:col-span-2",children:(0,s.jsxs)(j.Zp,{className:"dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsxs)(j.aR,{children:[(0,s.jsxs)(j.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(x,{className:"h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Revenue Trend"})]}),(0,s.jsxs)(j.BT,{children:["Monthly revenue comparison (+",e,"% from last month)"]})]}),(0,s.jsx)(j.Wu,{children:(0,s.jsx)("div",{className:"h-64 bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-md flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(x,{className:"h-12 w-12 text-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Revenue chart will be implemented here"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground/70",children:"Integration with chart library needed"})]})})})]})}),(0,s.jsx)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.5},children:(0,s.jsxs)(j.Zp,{className:"dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsxs)(j.aR,{children:[(0,s.jsx)(j.ZB,{children:"Project Status"}),(0,s.jsx)(j.BT,{children:"Current project distribution"})]}),(0,s.jsxs)(j.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,s.jsx)("span",{children:"Completed"}),(0,s.jsxs)("span",{children:[Z.completedProjects,"/",Z.totalProjects]})]}),(0,s.jsx)(D,{value:Z.completedProjects/Z.totalProjects*100,className:"h-2"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,s.jsx)("span",{children:"In Progress"}),(0,s.jsxs)("span",{children:[Z.activeProjects,"/",Z.totalProjects]})]}),(0,s.jsx)(D,{value:Z.activeProjects/Z.totalProjects*100,className:"h-2"})]})]})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,s.jsxs)(j.Zp,{className:"dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsxs)(j.aR,{children:[(0,s.jsxs)(j.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-primary"}),(0,s.jsx)("span",{children:"Today's Schedule"})]}),(0,s.jsx)(j.BT,{children:"Upcoming installations and appointments"})]}),(0,s.jsxs)(j.Wu,{children:[(0,s.jsx)("div",{className:"space-y-4",children:O.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-md bg-accent/30 dark:bg-accent/20 dark:border dark:border-border/30",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:"completed"===e.status?(0,s.jsx)(h.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"}):(0,s.jsx)(c.A,{className:"h-5 w-5 text-amber-600 dark:text-amber-400"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-foreground truncate",children:e.title}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.customer}),(0,s.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground mt-1",children:[(0,s.jsx)(c.A,{className:"h-3 w-3 mr-1"}),e.time,(0,s.jsx)(p.A,{className:"h-3 w-3 ml-2 mr-1"}),e.location]})]}),(0,s.jsx)(v.E,{variant:"completed"===e.status?"default":"secondary",className:"text-xs dark:bg-secondary/80",children:e.status})]},e.id))}),(0,s.jsx)(y.$,{variant:"outline",className:"w-full mt-4 dark:bg-background/50 dark:hover:bg-accent/50",children:"View Full Schedule"})]})]})}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:(0,s.jsxs)(j.Zp,{className:"dark:bg-card/50 dark:border-border/50 backdrop-blur-sm",children:[(0,s.jsxs)(j.aR,{children:[(0,s.jsx)(j.ZB,{children:"Recent Activity"}),(0,s.jsx)(j.BT,{children:"Latest updates across the platform"})]}),(0,s.jsxs)(j.Wu,{children:[(0,s.jsx)("div",{className:"space-y-4",children:B.map(e=>(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-foreground",children:e.title}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground/70 mt-1",children:e.timestamp})]})]},e.id))}),(0,s.jsx)(y.$,{variant:"outline",className:"w-full mt-4 dark:bg-background/50 dark:hover:bg-accent/50",children:"View All Activity"})]})]})})]})]})}function J(){let{setUser:e,isAuthenticated:a}=(0,i.n)();return(0,r.useEffect)(()=>{a||e({id:"user_1",email:"<EMAIL>",role:"admin",profile:{firstName:"John",lastName:"Admin",phone:"******-0123",avatar:void 0},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()})},[a,e]),(0,s.jsx)(n.DashboardLayout,{children:(0,s.jsx)(W,{})})}},646:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4516:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5868:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6108:(e,a,t)=>{Promise.resolve().then(t.bind(t,359))}},e=>{var a=a=>e(e.s=a);e.O(0,[418,205,441,684,358],()=>a(6108)),_N_E=e.O()}]);