'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useTheme } from '@/contexts/theme-context'

interface CalendarEvent {
  id: string
  title: string
  date: string
  time: string
  status: 'pending' | 'assigned' | 'in-progress' | 'completed' | 'conflict'
  customer: string
  installer?: string
}

interface CalendarMonthViewProps {
  selectedDate: Date
  events: CalendarEvent[]
  onDateSelect: (date: Date) => void
  onEventClick: (event: CalendarEvent) => void
  onNavigate: (direction: 'prev' | 'next') => void
  onNewAppointment: (date: Date) => void
}

export function CalendarMonthView({
  selectedDate,
  events,
  onDateSelect,
  onEventClick,
  onNavigate,
  onNewAppointment
}: CalendarMonthViewProps) {
  const today = new Date()
  const currentMonth = selectedDate.getMonth()
  const currentYear = selectedDate.getFullYear()

  // Generate calendar days
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1)
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)
  const firstDayOfWeek = firstDayOfMonth.getDay()
  const daysInMonth = lastDayOfMonth.getDate()

  const calendarDays = []
  
  // Previous month days
  const prevMonth = new Date(currentYear, currentMonth - 1, 0)
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    calendarDays.push({
      date: prevMonth.getDate() - i,
      isCurrentMonth: false,
      isToday: false,
      fullDate: new Date(currentYear, currentMonth - 1, prevMonth.getDate() - i)
    })
  }

  // Current month days
  for (let date = 1; date <= daysInMonth; date++) {
    const fullDate = new Date(currentYear, currentMonth, date)
    calendarDays.push({
      date,
      isCurrentMonth: true,
      isToday: fullDate.toDateString() === today.toDateString(),
      fullDate
    })
  }

  // Next month days to fill grid
  const remainingCells = 42 - calendarDays.length
  for (let date = 1; date <= remainingCells; date++) {
    calendarDays.push({
      date,
      isCurrentMonth: false,
      isToday: false,
      fullDate: new Date(currentYear, currentMonth + 1, date)
    })
  }

  const getEventsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0]
    return events.filter(event => event.date === dateStr)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-amber-500'
      case 'assigned': return 'bg-blue-500'
      case 'in-progress': return 'bg-orange-500'
      case 'completed': return 'bg-green-500'
      case 'conflict': return 'bg-red-500'
      default: return 'bg-gray-400'
    }
  }

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-slate-700">
          {monthNames[currentMonth]} {currentYear}
        </h2>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onNavigate('prev')}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onNavigate('next')}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-4">
        {/* Week days header */}
        <div className="grid grid-cols-7 mb-2">
          {weekDays.map(day => (
            <div key={day} className="text-center text-sm font-medium text-slate-600 py-2">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar days */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((day, index) => {
            const dayEvents = getEventsForDate(day.fullDate)
            const isSelected = selectedDate.toDateString() === day.fullDate.toDateString()

            return (
              <motion.div
                key={index}
                className={`
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  ${!day.isCurrentMonth ? 'opacity-40' : ''}
                  ${day.isToday ? 'ring-2 ring-amber-400 bg-amber-50' : ''}
                  ${isSelected ? 'ring-2 ring-blue-400 bg-blue-50' : ''}
                `}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onDateSelect(day.fullDate)}
              >
                {/* Date number */}
                <div className={`
                  text-sm font-medium mb-1
                  ${day.isToday ? 'text-amber-700' : day.isCurrentMonth ? 'text-slate-700' : 'text-slate-400'}
                `}>
                  {day.date}
                </div>

                {/* Events */}
                <div className="space-y-1">
                  {dayEvents.slice(0, 3).map((event) => (
                    <motion.div
                      key={event.id}
                      className={`
                        text-xs p-1 rounded text-white truncate cursor-pointer
                        ${getStatusColor(event.status)}
                      `}
                      whileHover={{ scale: 1.05 }}
                      onClick={(e) => {
                        e.stopPropagation()
                        onEventClick(event)
                      }}
                      title={`${event.time} - ${event.title}`}
                    >
                      {event.time} {event.title}
                    </motion.div>
                  ))}
                  
                  {dayEvents.length > 3 && (
                    <div className="text-xs text-slate-500 font-medium">
                      +{dayEvents.length - 3} more
                    </div>
                  )}
                </div>

                {/* New appointment button - shows on hover */}
                <motion.button
                  className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 
                           w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center
                           hover:bg-amber-600 transition-all duration-200"
                  initial={false}
                  animate={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  onClick={(e) => {
                    e.stopPropagation()
                    onNewAppointment(day.fullDate)
                  }}
                  title="Add appointment"
                >
                  <Plus className="h-3 w-3" />
                </motion.button>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-4 p-4 border-t border-gray-200 bg-gray-50">
        {[
          { status: 'pending', label: 'Pending' },
          { status: 'assigned', label: 'Assigned' },
          { status: 'in-progress', label: 'In Progress' },
          { status: 'completed', label: 'Completed' },
          { status: 'conflict', label: 'Conflict' }
        ].map(({ status, label }) => (
          <div key={status} className="flex items-center space-x-1">
            <div className={`w-3 h-3 rounded ${getStatusColor(status)}`} />
            <span className="text-xs text-slate-600">{label}</span>
          </div>
        ))}
      </div>
    </div>
  )
}
