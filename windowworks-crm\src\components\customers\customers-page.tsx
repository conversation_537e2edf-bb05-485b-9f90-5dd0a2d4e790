'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  ArrowUp, 
  Star, 
  DollarSign,
  Search,
  MoreHorizontal,
  Edit,
  ExternalLink,
  Trash2,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Plus,
  Download,
  Eye,
  CheckCircle,
  Clock,
  X
} from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { NewCustomerModal } from '@/components/customers/new-customer-modal'

// Types
interface CustomerFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  preferences: {
    windowTypes: string[]
    preferredColors: string[]
    budgetRange: string
    communicationMethod: string
  }
  notes: string
}

// Types
interface Customer {
  id: string
  name: string
  email: string
  phone: string
  address: string
  status: string
  lastProject: string | null
  totalRevenue: number
  projectCount: number
  preferences: string[]
  avatar: string | null
}

// Mock data for customers
const mockCustomers: Customer[] = [
  {
    id: '1',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Maple Street, Springfield, IL 62704',
    status: 'Active',
    lastProject: '2024-12-15',
    totalRevenue: 2840,
    projectCount: 3,
    preferences: ['Blinds', 'Shutters'],
    avatar: null
  },
  {
    id: '2',
    name: 'Robert Chen',
    email: '<EMAIL>',
    phone: '(*************',
    address: '456 Oak Avenue, Springfield, IL 62701',
    status: 'Active',
    lastProject: '2025-01-08',
    totalRevenue: 1560,
    projectCount: 2,
    preferences: ['Shades'],
    avatar: null
  },
  {
    id: '3',
    name: 'Emma Davis',
    email: '<EMAIL>',
    phone: '(*************',
    address: '789 Pine Road, Springfield, IL 62702',
    status: 'Pending',
    lastProject: null,
    totalRevenue: 0,
    projectCount: 0,
    preferences: ['Shutters', 'Shades'],
    avatar: null
  },
  {
    id: '4',
    name: 'Michael Williams',
    email: '<EMAIL>',
    phone: '(*************',
    address: '321 Elm Street, Springfield, IL 62703',
    status: 'Active',
    lastProject: '2024-11-22',
    totalRevenue: 4250,
    projectCount: 5,
    preferences: ['Blinds', 'Shutters', 'Shades'],
    avatar: null
  },
  {
    id: '5',
    name: 'Lisa Anderson',
    email: '<EMAIL>',
    phone: '(*************',
    address: '654 Cedar Lane, Springfield, IL 62705',
    status: 'Inactive',
    lastProject: '2024-08-14',
    totalRevenue: 890,
    projectCount: 1,
    preferences: ['Blinds'],
    avatar: null
  }
]

const statsData = [
  {
    title: 'Total Customers',
    value: '89',
    change: '-2%',
    icon: Users,
    trend: 'down'
  },
  {
    title: 'New Customers',
    value: '12',
    change: '+5%',
    icon: ArrowUp,
    trend: 'up'
  },
  {
    title: 'High-Value Clients',
    value: '25',
    change: '',
    icon: Star,
    trend: 'neutral'
  },
  {
    title: 'Avg Revenue/Customer',
    value: '$520',
    change: '+8%',
    icon: DollarSign,
    trend: 'up'
  }
]

export default function CustomersPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [preferenceFilter, setPreferenceFilter] = useState('all')
  const [sortBy, setSortBy] = useState('name')
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([])
  const [showNewCustomerModal, setShowNewCustomerModal] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // Filter and sort customers
  const filteredCustomers = mockCustomers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || customer.status.toLowerCase() === statusFilter.toLowerCase()
    
    const matchesPreference = preferenceFilter === 'all' || 
                             customer.preferences.some(pref => pref.toLowerCase() === preferenceFilter.toLowerCase())
    
    return matchesSearch && matchesStatus && matchesPreference
  }).sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'date':
        return new Date(b.lastProject || 0).getTime() - new Date(a.lastProject || 0).getTime()
      case 'revenue':
        return b.totalRevenue - a.totalRevenue
      default:
        return 0
    }
  })

  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage)
  const paginatedCustomers = filteredCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const handleSelectCustomer = (customerId: string) => {
    setSelectedCustomers(prev => 
      prev.includes(customerId)
        ? prev.filter(id => id !== customerId)
        : [...prev, customerId]
    )
  }

  const handleSelectAll = () => {
    if (selectedCustomers.length === paginatedCustomers.length) {
      setSelectedCustomers([])
    } else {
      setSelectedCustomers(paginatedCustomers.map(customer => customer.id))
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'default'
      case 'pending':
        return 'secondary'
      case 'inactive':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return CheckCircle
      case 'pending':
        return Clock
      default:
        return X
    }
  }

  // Add customer save handler
  const handleSaveCustomer = async (customerData: CustomerFormData) => {
    try {
      // Mock API call - in real app, this would POST to your backend
      console.log('Saving customer:', customerData)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Here you would typically:
      // 1. POST to your API endpoint
      // 2. Update the local state
      // 3. Show success notification
      
      // Mock success - in real app, you'd refresh the customer list
      console.log('Customer saved successfully')
    } catch (error) {
      console.error('Error saving customer:', error)
      throw error
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">Customers</h1>
          <p className="text-muted-foreground">Manage your client base and preferences</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="secondary" className="text-xs">
            Saturday, July 12, 2025
          </Badge>
          <Button className="bg-primary hover:bg-primary/90" onClick={() => setShowNewCustomerModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Customer
          </Button>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:border-border/80 transition-colors">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="p-2 rounded-md bg-primary/10">
                    <stat.icon className="h-5 w-5 text-primary" />
                  </div>
                  {stat.change && (
                    <Badge 
                      variant={stat.trend === 'up' ? 'default' : stat.trend === 'down' ? 'destructive' : 'secondary'} 
                      className="text-xs"
                    >
                      {stat.change}
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <h3 className="text-2xl font-bold text-foreground">{stat.value}</h3>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Filters and Search */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-1 items-center space-x-4">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search customers..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={preferenceFilter} onValueChange={setPreferenceFilter}>
                  <SelectTrigger className="w-36">
                    <SelectValue placeholder="Preference" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Products</SelectItem>
                    <SelectItem value="blinds">Blinds</SelectItem>
                    <SelectItem value="shutters">Shutters</SelectItem>
                    <SelectItem value="shades">Shades</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="date">Date Added</SelectItem>
                    <SelectItem value="revenue">Revenue</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                
                {selectedCustomers.length > 0 && (
                  <Badge variant="secondary">
                    {selectedCustomers.length} selected
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Customer Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b border-border">
                  <tr className="bg-accent/20">
                    <th className="text-left p-4 w-12">
                      <Checkbox
                        checked={selectedCustomers.length === paginatedCustomers.length && paginatedCustomers.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </th>
                    <th className="text-left p-4 font-medium text-foreground">Customer</th>
                    <th className="text-left p-4 font-medium text-foreground">Contact</th>
                    <th className="text-left p-4 font-medium text-foreground">Address</th>
                    <th className="text-left p-4 font-medium text-foreground">Last Project</th>
                    <th className="text-left p-4 font-medium text-foreground">Status</th>
                    <th className="text-left p-4 font-medium text-foreground">Revenue</th>
                    <th className="text-right p-4 w-16"></th>
                  </tr>
                </thead>
                <tbody>
                  {paginatedCustomers.map((customer, index) => {
                    const StatusIcon = getStatusIcon(customer.status)
                    return (
                      <motion.tr
                        key={customer.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className="border-b border-border hover:bg-accent/30 transition-colors"
                      >
                        <td className="p-4">                            <Checkbox
                              checked={selectedCustomers.includes(customer.id)}
                              onCheckedChange={() => handleSelectCustomer(customer.id)}
                              onClick={(e: React.MouseEvent) => e.stopPropagation()}
                            />
                        </td>
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={customer.avatar || undefined} />
                              <AvatarFallback className="bg-primary/10 text-primary">
                                {customer.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium text-foreground">{customer.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {customer.projectCount} project{customer.projectCount !== 1 ? 's' : ''}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="space-y-1">
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Mail className="h-3 w-3 mr-2" />
                              {customer.email}
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Phone className="h-3 w-3 mr-2" />
                              {customer.phone}
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center text-sm text-muted-foreground">
                            <MapPin className="h-3 w-3 mr-2 flex-shrink-0" />
                            <span className="truncate max-w-48">{customer.address}</span>
                          </div>
                        </td>
                        <td className="p-4">
                          {customer.lastProject ? (
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Calendar className="h-3 w-3 mr-2" />
                              {new Date(customer.lastProject).toLocaleDateString()}
                            </div>
                          ) : (
                            <span className="text-sm text-muted-foreground">No projects</span>
                          )}
                        </td>
                        <td className="p-4">
                          <Badge variant={getStatusBadgeVariant(customer.status)} className="text-xs">
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {customer.status}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <span className="font-medium text-foreground">
                            ${customer.totalRevenue.toLocaleString()}
                          </span>
                        </td>
                        <td className="p-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Customer
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                View Projects
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-destructive">
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </motion.tr>
                    )
                  })}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between p-4 border-t border-border">
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredCustomers.length)} of {filteredCustomers.length} customers
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className="w-8 h-8 p-0"
                      >
                        {page}
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Quick Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="grid grid-cols-1 lg:grid-cols-3 gap-6"
      >
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Top Customers by Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockCustomers
                .sort((a, b) => b.totalRevenue - a.totalRevenue)
                .slice(0, 5)
                .map((customer, index) => (
                  <div key={customer.id} className="flex items-center justify-between p-3 rounded-md bg-accent/20">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-muted-foreground w-4">
                        #{index + 1}
                      </span>
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="bg-primary/10 text-primary text-xs">
                          {customer.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium text-foreground">{customer.name}</span>
                    </div>
                    <span className="text-sm font-medium text-foreground">
                      ${customer.totalRevenue.toLocaleString()}
                    </span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 rounded-md bg-primary/5 border border-primary/20">
                <p className="text-sm text-foreground font-medium mb-1">Outreach Recommendation</p>
                <p className="text-xs text-muted-foreground">
                  5 inactive clients haven&apos;t been contacted in 90+ days. Consider a follow-up campaign.
                </p>
              </div>
              
              <div className="p-3 rounded-md bg-accent/20">
                <p className="text-sm text-foreground font-medium mb-1">Seasonal Opportunity</p>
                <p className="text-xs text-muted-foreground">
                  12 customers have shown interest in shutters. Summer promotion recommended.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* New Customer Modal */}
      <NewCustomerModal
        isOpen={showNewCustomerModal}
        onClose={() => setShowNewCustomerModal(false)}
        onSave={handleSaveCustomer}
      />
    </div>
  )
}
