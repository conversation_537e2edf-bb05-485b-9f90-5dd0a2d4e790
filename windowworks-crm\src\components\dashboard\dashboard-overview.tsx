'use client'

import { motion } from 'framer-motion'
import {
  Users,
  FolderOpen,
  DollarSign,
  TrendingUp,
  Calendar,
  Clock,
  MapPin,
  CheckCircle
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import type { DashboardStats, RecentActivity } from '@/types'

// Mock data - in a real app this would come from your API
const mockStats: DashboardStats = {
  totalProjects: 156,
  activeProjects: 24,
  completedProjects: 132,
  totalCustomers: 89,
  revenueThisMonth: 45600,
  revenueLastMonth: 38200,
  averageProjectValue: 2850,
  upcomingTasks: 12,
}

const mockRecentActivity: RecentActivity[] = [
  {
    id: '1',
    type: 'project-created',
    title: 'New project created',
    description: 'Kitchen blinds installation for <PERSON>',
    timestamp: '2 hours ago',
    user: '<PERSON>',
    entityId: 'proj_123'
  },
  {
    id: '2',
    type: 'task-completed',
    title: 'Installation completed',
    description: 'Living room shutters installed at 123 Oak Street',
    timestamp: '4 hours ago',
    user: 'Mike Installer',
    entityId: 'task_456'
  },
  {
    id: '3',
    type: 'customer-added',
    title: 'New customer added',
    description: 'Emma Davis - Interested in bedroom shades',
    timestamp: '1 day ago',
    user: 'Lisa Sales',
    entityId: 'cust_789'
  }
]

const statCards = [
  {
    title: 'Total Projects',
    value: mockStats.totalProjects,
    change: '+12%',
    icon: FolderOpen
  },
  {
    title: 'Active Projects',
    value: mockStats.activeProjects,
    change: '+8%',
    icon: Clock
  },
  {
    title: 'Total Customers',
    value: mockStats.totalCustomers,
    change: '+23%',
    icon: Users
  },
  {
    title: 'Monthly Revenue',
    value: `$${mockStats.revenueThisMonth.toLocaleString()}`,
    change: '+19%',
    icon: DollarSign
  }
]

const upcomingTasks = [
  {
    id: '1',
    title: 'Install bedroom blinds',
    customer: 'Sarah Johnson',
    time: '10:00 AM',
    location: '123 Maple Street',
    status: 'scheduled'
  },
  {
    id: '2',
    title: 'Measure living room windows',
    customer: 'Robert Chen',
    time: '2:30 PM',
    location: '456 Oak Avenue',
    status: 'in-progress'
  },
  {
    id: '3',
    title: 'Final inspection',
    customer: 'Mary Williams',
    time: '4:00 PM',
    location: '789 Pine Road',
    status: 'pending'
  }
]

export default function DashboardOverview() {
  const revenueGrowth = ((mockStats.revenueThisMonth - mockStats.revenueLastMonth) / mockStats.revenueLastMonth * 100).toFixed(1)
  
  return (
    <div className="space-y-6 p-1">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here&apos;s what&apos;s happening today.</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="secondary" className="text-xs dark:bg-secondary/80">
            {new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </Badge>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:border-border/60 transition-all duration-200 dark:bg-card/50 dark:border-border/50 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="p-2 rounded-md bg-primary/10 dark:bg-primary/20">
                    <stat.icon className="h-5 w-5 text-primary" />
                  </div>
                  <Badge variant="secondary" className="text-xs dark:bg-secondary/80">
                    {stat.change}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <h3 className="text-2xl font-bold text-foreground">{stat.value}</h3>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Revenue Chart Placeholder */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          className="lg:col-span-2"
        >
          <Card className="dark:bg-card/50 dark:border-border/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                <span>Revenue Trend</span>
              </CardTitle>
              <CardDescription>
                Monthly revenue comparison (+{revenueGrowth}% from last month)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 rounded-md flex items-center justify-center">
                <div className="text-center">
                  <TrendingUp className="h-12 w-12 text-primary mx-auto mb-4" />
                  <p className="text-muted-foreground">Revenue chart will be implemented here</p>
                  <p className="text-sm text-muted-foreground/70">Integration with chart library needed</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Project Status */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="dark:bg-card/50 dark:border-border/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Project Status</CardTitle>
              <CardDescription>Current project distribution</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Completed</span>
                  <span>{mockStats.completedProjects}/{mockStats.totalProjects}</span>
                </div>
                <Progress 
                  value={(mockStats.completedProjects / mockStats.totalProjects) * 100} 
                  className="h-2"
                />
              </div>
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>In Progress</span>
                  <span>{mockStats.activeProjects}/{mockStats.totalProjects}</span>
                </div>
                <Progress 
                  value={(mockStats.activeProjects / mockStats.totalProjects) * 100} 
                  className="h-2"
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Tasks */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card className="dark:bg-card/50 dark:border-border/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5 text-primary" />
                <span>Today&apos;s Schedule</span>
              </CardTitle>
              <CardDescription>Upcoming installations and appointments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingTasks.map((task) => (
                  <div key={task.id} className="flex items-center space-x-3 p-3 rounded-md bg-accent/30 dark:bg-accent/20 dark:border dark:border-border/30">
                    <div className="flex-shrink-0">
                      {task.status === 'completed' ? (
                        <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                      ) : (
                        <Clock className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-foreground truncate">
                        {task.title}
                      </p>
                      <p className="text-xs text-muted-foreground">{task.customer}</p>
                      <div className="flex items-center text-xs text-muted-foreground mt-1">
                        <Clock className="h-3 w-3 mr-1" />
                        {task.time}
                        <MapPin className="h-3 w-3 ml-2 mr-1" />
                        {task.location}
                      </div>
                    </div>
                    <Badge 
                      variant={task.status === 'completed' ? 'default' : 'secondary'}
                      className="text-xs dark:bg-secondary/80"
                    >
                      {task.status}
                    </Badge>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4 dark:bg-background/50 dark:hover:bg-accent/50">
                View Full Schedule
              </Button>
            </CardContent>
          </Card>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card className="dark:bg-card/50 dark:border-border/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest updates across the platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRecentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-foreground">
                        {activity.title}
                      </p>
                      <p className="text-xs text-muted-foreground">{activity.description}</p>
                      <p className="text-xs text-muted-foreground/70 mt-1">{activity.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4 dark:bg-background/50 dark:hover:bg-accent/50">
                View All Activity
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
