"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[271],{760:(e,t,n)=>{n.d(t,{N:()=>m});var r=n(5155),l=n(2115),i=n(869),h=n(2885),s=n(7494),a=n(845),o=n(7351),c=n(1508);class d extends l.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,o.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:t,isPresent:n,anchorX:i,root:h}=e,s=(0,l.useId)(),a=(0,l.useRef)(null),o=(0,l.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:p}=(0,l.useContext)(c.Q);return(0,l.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:l,right:c}=o.current;if(n||!a.current||!e||!t)return;a.current.dataset.motionPopId=s;let d=document.createElement("style");p&&(d.nonce=p);let u=null!=h?h:document.head;return u.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===i?"left: ".concat(l):"right: ".concat(c),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{u.removeChild(d),u.contains(d)&&u.removeChild(d)}},[n]),(0,r.jsx)(d,{isPresent:n,childRef:a,sizeRef:o,children:l.cloneElement(t,{ref:a})})}let u=e=>{let{children:t,initial:n,isPresent:i,onExitComplete:s,custom:o,presenceAffectsLayout:c,mode:d,anchorX:u,root:y}=e,k=(0,h.M)(f),x=(0,l.useId)(),m=!0,M=(0,l.useMemo)(()=>(m=!1,{id:x,initial:n,isPresent:i,custom:o,onExitComplete:e=>{for(let t of(k.set(e,!0),k.values()))if(!t)return;s&&s()},register:e=>(k.set(e,!1),()=>k.delete(e))}),[i,k,s]);return c&&m&&(M={...M}),(0,l.useMemo)(()=>{k.forEach((e,t)=>k.set(t,!1))},[i]),l.useEffect(()=>{i||k.size||!s||s()},[i]),"popLayout"===d&&(t=(0,r.jsx)(p,{isPresent:i,anchorX:u,root:y,children:t})),(0,r.jsx)(a.t.Provider,{value:M,children:t})};function f(){return new Map}var y=n(2082);let k=e=>e.key||"";function x(e){let t=[];return l.Children.forEach(e,e=>{(0,l.isValidElement)(e)&&t.push(e)}),t}let m=e=>{let{children:t,custom:n,initial:a=!0,onExitComplete:o,presenceAffectsLayout:c=!0,mode:d="sync",propagate:p=!1,anchorX:f="left",root:m}=e,[M,g]=(0,y.xQ)(p),v=(0,l.useMemo)(()=>x(t),[t]),A=p&&!M?[]:v.map(k),E=(0,l.useRef)(!0),w=(0,l.useRef)(v),C=(0,h.M)(()=>new Map),[z,j]=(0,l.useState)(v),[R,P]=(0,l.useState)(v);(0,s.E)(()=>{E.current=!1,w.current=v;for(let e=0;e<R.length;e++){let t=k(R[e]);A.includes(t)?C.delete(t):!0!==C.get(t)&&C.set(t,!1)}},[R,A.length,A.join("-")]);let b=[];if(v!==z){let e=[...v];for(let t=0;t<R.length;t++){let n=R[t],r=k(n);A.includes(r)||(e.splice(t,0,n),b.push(n))}return"wait"===d&&b.length&&(e=b),P(x(e)),j(v),null}let{forceRender:q}=(0,l.useContext)(i.L);return(0,r.jsx)(r.Fragment,{children:R.map(e=>{let t=k(e),l=(!p||!!M)&&(v===R||A.includes(t));return(0,r.jsx)(u,{isPresent:l,initial:(!E.current||!!a)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,root:m,onExitComplete:l?void 0:()=>{if(!C.has(t))return;C.set(t,!0);let e=!0;C.forEach(t=>{t||(e=!1)}),e&&(null==q||q(),P(w.current),p&&(null==g||g()),o&&o())},anchorX:f,children:e},t)})})}},2138:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2318:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2650:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("clipboard",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}]])},4653:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},5968:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6561:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]])},7434:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},8833:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-play",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]])}}]);