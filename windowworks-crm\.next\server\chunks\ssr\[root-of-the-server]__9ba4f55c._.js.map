{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/stores/auth-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, persist } from 'zustand/middleware'\r\nimport type { User } from '@/types'\r\n\r\ninterface AuthState {\r\n  user: User | null\r\n  isLoading: boolean\r\n  isAuthenticated: boolean\r\n}\r\n\r\ninterface AuthActions {\r\n  setUser: (user: User | null) => void\r\n  setLoading: (loading: boolean) => void\r\n  clearAuth: () => void\r\n}\r\n\r\nexport const useAuthStore = create<AuthState & AuthActions>()(\r\n  devtools(\r\n    persist(\r\n      (set) => ({\r\n        user: null,\r\n        isLoading: true,\r\n        isAuthenticated: false,\r\n        setUser: (user) =>\r\n          set(\r\n            {\r\n              user,\r\n              isAuthenticated: !!user,\r\n              isLoading: false,\r\n            },\r\n            false,\r\n            'setUser'\r\n          ),\r\n        setLoading: (isLoading) =>\r\n          set({ isLoading }, false, 'setLoading'),\r\n        clearAuth: () =>\r\n          set(\r\n            {\r\n              user: null,\r\n              isAuthenticated: false,\r\n              isLoading: false,\r\n            },\r\n            false,\r\n            'clearAuth'\r\n          ),\r\n      }),\r\n      {\r\n        name: 'auth-storage',\r\n        partialize: (state) => ({\r\n          user: state.user,\r\n          isAuthenticated: state.isAuthenticated,\r\n        }),\r\n      }\r\n    ),\r\n    {\r\n      name: 'auth-store',\r\n    }\r\n  )\r\n)\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAeO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,WAAW;QACX,iBAAiB;QACjB,SAAS,CAAC,OACR,IACE;gBACE;gBACA,iBAAiB,CAAC,CAAC;gBACnB,WAAW;YACb,GACA,OACA;QAEJ,YAAY,CAAC,YACX,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B,WAAW,IACT,IACE;gBACE,MAAM;gBACN,iBAAiB;gBACjB,WAAW;YACb,GACA,OACA;IAEN,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH,IAEF;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/stores/notification-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools } from 'zustand/middleware'\r\nimport type { Notification } from '@/types'\r\n\r\ninterface NotificationState {\r\n  notifications: Notification[]\r\n  unreadCount: number\r\n}\r\n\r\ninterface NotificationActions {\r\n  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void\r\n  markAsRead: (id: string) => void\r\n  markAllAsRead: () => void\r\n  removeNotification: (id: string) => void\r\n  setNotifications: (notifications: Notification[]) => void\r\n}\r\n\r\nexport const useNotificationStore = create<NotificationState & NotificationActions>()(\r\n  devtools(\r\n    (set) => ({\r\n      notifications: [],\r\n      unreadCount: 0,\r\n      addNotification: (notification) => {\r\n        const newNotification: Notification = {\r\n          ...notification,\r\n          id: crypto.randomUUID(),\r\n          createdAt: new Date().toISOString(),\r\n        }\r\n        set(\r\n          (state) => ({\r\n            notifications: [newNotification, ...state.notifications],\r\n            unreadCount: state.unreadCount + 1,\r\n          }),\r\n          false,\r\n          'addNotification'\r\n        )\r\n      },\r\n      markAsRead: (id) =>\r\n        set(\r\n          (state) => ({\r\n            notifications: state.notifications.map((notification) =>\r\n              notification.id === id\r\n                ? { ...notification, read: true }\r\n                : notification\r\n            ),\r\n            unreadCount: Math.max(0, state.unreadCount - 1),\r\n          }),\r\n          false,\r\n          'markAsRead'\r\n        ),\r\n      markAllAsRead: () =>\r\n        set(\r\n          (state) => ({\r\n            notifications: state.notifications.map((notification) => ({\r\n              ...notification,\r\n              read: true,\r\n            })),\r\n            unreadCount: 0,\r\n          }),\r\n          false,\r\n          'markAllAsRead'\r\n        ),\r\n      removeNotification: (id) =>\r\n        set(\r\n          (state) => {\r\n            const notification = state.notifications.find((n) => n.id === id)\r\n            const wasUnread = notification && !notification.read\r\n            return {\r\n              notifications: state.notifications.filter((n) => n.id !== id),\r\n              unreadCount: wasUnread\r\n                ? Math.max(0, state.unreadCount - 1)\r\n                : state.unreadCount,\r\n            }\r\n          },\r\n          false,\r\n          'removeNotification'\r\n        ),\r\n      setNotifications: (notifications) =>\r\n        set(\r\n          {\r\n            notifications,\r\n            unreadCount: notifications.filter((n) => !n.read).length,\r\n          },\r\n          false,\r\n          'setNotifications'\r\n        ),\r\n    }),\r\n    {\r\n      name: 'notification-store',\r\n    }\r\n  )\r\n)\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBO,MAAM,uBAAuB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACvC,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAC,MAAQ,CAAC;QACR,eAAe,EAAE;QACjB,aAAa;QACb,iBAAiB,CAAC;YAChB,MAAM,kBAAgC;gBACpC,GAAG,YAAY;gBACf,IAAI,OAAO,UAAU;gBACrB,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,IACE,CAAC,QAAU,CAAC;oBACV,eAAe;wBAAC;2BAAoB,MAAM,aAAa;qBAAC;oBACxD,aAAa,MAAM,WAAW,GAAG;gBACnC,CAAC,GACD,OACA;QAEJ;QACA,YAAY,CAAC,KACX,IACE,CAAC,QAAU,CAAC;oBACV,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eACtC,aAAa,EAAE,KAAK,KAChB;4BAAE,GAAG,YAAY;4BAAE,MAAM;wBAAK,IAC9B;oBAEN,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;gBAC/C,CAAC,GACD,OACA;QAEJ,eAAe,IACb,IACE,CAAC,QAAU,CAAC;oBACV,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eAAiB,CAAC;4BACxD,GAAG,YAAY;4BACf,MAAM;wBACR,CAAC;oBACD,aAAa;gBACf,CAAC,GACD,OACA;QAEJ,oBAAoB,CAAC,KACnB,IACE,CAAC;gBACC,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAC9D,MAAM,YAAY,gBAAgB,CAAC,aAAa,IAAI;gBACpD,OAAO;oBACL,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAC1D,aAAa,YACT,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG,KAChC,MAAM,WAAW;gBACvB;YACF,GACA,OACA;QAEJ,kBAAkB,CAAC,gBACjB,IACE;gBACE;gBACA,aAAa,cAAc,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,IAAI,EAAE,MAAM;YAC1D,GACA,OACA;IAEN,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/dashboard/dashboard-layout.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ReactNode } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { \r\n  Bell, \r\n  Home, \r\n  Users, \r\n  FolderOpen, \r\n  Calendar,\r\n  Settings,\r\n  LogOut,\r\n  Menu,\r\n  Search\r\n} from 'lucide-react'\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\nimport { useAuthStore } from '@/lib/stores/auth-store'\r\nimport { useNotificationStore } from '@/lib/stores/notification-store'\r\n\r\ninterface DashboardLayoutProps {\r\n  children: ReactNode\r\n}\r\n\r\nconst navigationItems = [\r\n  { icon: Home, label: 'Dashboard', href: '/', badge: null },\r\n  { icon: Users, label: 'Customers', href: '/customers', badge: null },\r\n  { icon: FolderOpen, label: 'Projects', href: '/projects', badge: null },\r\n  { icon: Calendar, label: 'Schedule', href: '/schedule', badge: null },\r\n  { icon: Settings, label: 'Settings', href: '/settings', badge: null },\r\n]\r\n\r\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\r\n  const { user, clearAuth } = useAuthStore()\r\n  const { unreadCount } = useNotificationStore()\r\n  const pathname = usePathname()\r\n\r\n  const handleLogout = () => {\r\n    clearAuth()\r\n    // In a real app, you'd also call Supabase signOut here\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-screen bg-background flex overflow-hidden\">\r\n      {/* Sidebar */}\r\n      <motion.aside\r\n        initial={{ x: -300 }}\r\n        animate={{ x: 0 }}\r\n        className=\"w-64 bg-card border-r border-border flex flex-col fixed left-0 top-0 h-full z-30\"\r\n      >\r\n        {/* Logo */}\r\n        <div className=\"p-6 border-b border-border flex-shrink-0\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.2 }}\r\n            className=\"flex items-center space-x-3\"\r\n          >\r\n            <div className=\"w-8 h-8 bg-[var(--color-brand-primary)] rounded-md flex items-center justify-center\">\r\n              <span className=\"text-white font-bold text-sm\">W</span>\r\n            </div>\r\n            <div>\r\n              <h1 className=\"text-xl font-bold text-foreground\">WindowWorks</h1>\r\n              <p className=\"text-xs text-muted-foreground\">CRM Platform</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <nav className=\"flex-1 p-4 space-y-2 overflow-y-auto\">\r\n          {navigationItems.map((item, index) => {\r\n            const isActive = pathname === item.href\r\n            return (\r\n              <motion.div\r\n                key={item.href}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                transition={{ delay: 0.1 * (index + 1) }}\r\n              >\r\n                <Link href={item.href}>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className={`w-full justify-start rounded-md ${\r\n                      isActive \r\n                        ? 'bg-primary/10 text-primary border-primary/20' \r\n                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'\r\n                    }`}\r\n                  >\r\n                    <item.icon className=\"mr-3 h-5 w-5\" />\r\n                    {item.label}\r\n                    {item.badge && (\r\n                      <Badge variant=\"secondary\" className=\"ml-auto\">\r\n                        {item.badge}\r\n                      </Badge>\r\n                    )}\r\n                  </Button>\r\n                </Link>\r\n              </motion.div>\r\n            )\r\n          })}\r\n        </nav>\r\n\r\n        {/* User Profile - Sticky at bottom */}\r\n        <div className=\"p-4 border-t border-border flex-shrink-0 sticky bottom-0 bg-card\">\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" className=\"w-full justify-start p-3 hover:bg-accent\">\r\n                <Avatar className=\"h-8 w-8 mr-3\">\r\n                  <AvatarImage src={user?.profile?.avatar} />\r\n                  <AvatarFallback className=\"bg-primary/10 text-primary\">\r\n                    {user?.profile?.firstName?.[0]}{user?.profile?.lastName?.[0]}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"text-left\">\r\n                  <p className=\"text-sm font-medium text-foreground\">\r\n                    {user?.profile?.firstName} {user?.profile?.lastName}\r\n                  </p>\r\n                  <p className=\"text-xs text-muted-foreground capitalize\">{user?.role}</p>\r\n                </div>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"start\" className=\"w-56\">\r\n              <DropdownMenuItem>\r\n                <Settings className=\"mr-2 h-4 w-4\" />\r\n                Account Settings\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                Sign Out\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </motion.aside>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 flex flex-col bg-background ml-64\">\r\n        {/* Top Header - Sticky */}\r\n        <motion.header\r\n          initial={{ y: -50, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"bg-card border-b border-border px-6 py-4 sticky top-0 z-20 flex-shrink-0\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\r\n                <Menu className=\"h-5 w-5\" />\r\n              </Button>\r\n              \r\n              {/* Search */}\r\n              <div className=\"relative w-96 hidden md:block\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\r\n                <Input\r\n                  placeholder=\"Search customers, projects...\"\r\n                  className=\"pl-10 bg-accent/50 border-border focus:bg-background\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Notifications */}\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\r\n                <Bell className=\"h-5 w-5\" />\r\n                {unreadCount > 0 && (\r\n                  <motion.div\r\n                    initial={{ scale: 0 }}\r\n                    animate={{ scale: 1 }}\r\n                    className=\"absolute -top-1 -right-1 h-5 w-5 bg-destructive rounded-full flex items-center justify-center\"\r\n                  >\r\n                    <span className=\"text-xs text-destructive-foreground font-medium\">\r\n                      {unreadCount > 9 ? '9+' : unreadCount}\r\n                    </span>\r\n                  </motion.div>\r\n                )}\r\n              </Button>\r\n\r\n\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content - Scrollable */}\r\n        <main className=\"flex-1 overflow-y-auto bg-background\">\r\n          <div className=\"p-6\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.3 }}\r\n            >\r\n              {children}\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AAMA;AACA;AA5BA;;;;;;;;;;;;;AAkCA,MAAM,kBAAkB;IACtB;QAAE,MAAM,mMAAA,CAAA,OAAI;QAAE,OAAO;QAAa,MAAM;QAAK,OAAO;IAAK;IACzD;QAAE,MAAM,oMAAA,CAAA,QAAK;QAAE,OAAO;QAAa,MAAM;QAAc,OAAO;IAAK;IACnE;QAAE,MAAM,kNAAA,CAAA,aAAU;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;IACtE;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;IACpE;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;CACrE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,uBAAoB,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB;IACA,uDAAuD;IACzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;kCAMnD,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM;4BAC1B,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,CAAC,QAAQ,CAAC;gCAAE;0CAEvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,IAAI;8CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAW,CAAC,gCAAgC,EAC1C,WACI,iDACA,+DACJ;;0DAEF,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,KAAK;4CACV,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,KAAK,KAAK;;;;;;;;;;;;;;;;;+BAlBd,KAAK,IAAI;;;;;wBAyBpB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;;0DAChC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDAAC,KAAK,MAAM,SAAS;;;;;;kEACjC,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;;4DACvB,MAAM,SAAS,WAAW,CAAC,EAAE;4DAAE,MAAM,SAAS,UAAU,CAAC,EAAE;;;;;;;;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DACV,MAAM,SAAS;4DAAU;4DAAE,MAAM,SAAS;;;;;;;kEAE7C,8OAAC;wDAAE,WAAU;kEAA4C,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAIrE,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAQ,WAAU;;sDAC3C,8OAAC,4IAAA,CAAA,mBAAgB;;8DACf,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BAAE,GAAG,CAAC;4BAAI,SAAS;wBAAE;wBAC9B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;;0DAC1C,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,cAAc,mBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO;gDAAE;gDACpB,WAAU;0DAEV,cAAA,8OAAC;oDAAK,WAAU;8DACb,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYxC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAExB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm transition-colors\",\n        \"border-border dark:border-muted-foreground/50 dark:hover:border-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,oFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/schedule/calendar-month-view.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport { ChevronLeft, ChevronRight, Plus } from 'lucide-react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { useTheme } from '@/contexts/theme-context'\r\n\r\ninterface CalendarEvent {\r\n  id: string\r\n  title: string\r\n  date: string\r\n  time: string\r\n  status: 'pending' | 'assigned' | 'in-progress' | 'completed' | 'conflict'\r\n  customer: string\r\n  installer?: string\r\n}\r\n\r\ninterface CalendarMonthViewProps {\r\n  selectedDate: Date\r\n  events: CalendarEvent[]\r\n  onDateSelect: (date: Date) => void\r\n  onEventClick: (event: CalendarEvent) => void\r\n  onNavigate: (direction: 'prev' | 'next') => void\r\n  onNewAppointment: (date: Date) => void\r\n}\r\n\r\nexport function CalendarMonthView({\r\n  selectedDate,\r\n  events,\r\n  onDateSelect,\r\n  onEventClick,\r\n  onNavigate,\r\n  onNewAppointment\r\n}: CalendarMonthViewProps) {\r\n  const today = new Date()\r\n  const currentMonth = selectedDate.getMonth()\r\n  const currentYear = selectedDate.getFullYear()\r\n\r\n  // Generate calendar days\r\n  const firstDayOfMonth = new Date(currentYear, currentMonth, 1)\r\n  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)\r\n  const firstDayOfWeek = firstDayOfMonth.getDay()\r\n  const daysInMonth = lastDayOfMonth.getDate()\r\n\r\n  const calendarDays = []\r\n  \r\n  // Previous month days\r\n  const prevMonth = new Date(currentYear, currentMonth - 1, 0)\r\n  for (let i = firstDayOfWeek - 1; i >= 0; i--) {\r\n    calendarDays.push({\r\n      date: prevMonth.getDate() - i,\r\n      isCurrentMonth: false,\r\n      isToday: false,\r\n      fullDate: new Date(currentYear, currentMonth - 1, prevMonth.getDate() - i)\r\n    })\r\n  }\r\n\r\n  // Current month days\r\n  for (let date = 1; date <= daysInMonth; date++) {\r\n    const fullDate = new Date(currentYear, currentMonth, date)\r\n    calendarDays.push({\r\n      date,\r\n      isCurrentMonth: true,\r\n      isToday: fullDate.toDateString() === today.toDateString(),\r\n      fullDate\r\n    })\r\n  }\r\n\r\n  // Next month days to fill grid\r\n  const remainingCells = 42 - calendarDays.length\r\n  for (let date = 1; date <= remainingCells; date++) {\r\n    calendarDays.push({\r\n      date,\r\n      isCurrentMonth: false,\r\n      isToday: false,\r\n      fullDate: new Date(currentYear, currentMonth + 1, date)\r\n    })\r\n  }\r\n\r\n  const getEventsForDate = (date: Date) => {\r\n    const dateStr = date.toISOString().split('T')[0]\r\n    return events.filter(event => event.date === dateStr)\r\n  }\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'pending': return 'bg-amber-500'\r\n      case 'assigned': return 'bg-blue-500'\r\n      case 'in-progress': return 'bg-orange-500'\r\n      case 'completed': return 'bg-green-500'\r\n      case 'conflict': return 'bg-red-500'\r\n      default: return 'bg-gray-400'\r\n    }\r\n  }\r\n\r\n  const monthNames = [\r\n    'January', 'February', 'March', 'April', 'May', 'June',\r\n    'July', 'August', 'September', 'October', 'November', 'December'\r\n  ]\r\n\r\n  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg border border-gray-200 shadow-sm\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\r\n        <h2 className=\"text-lg font-semibold text-slate-700\">\r\n          {monthNames[currentMonth]} {currentYear}\r\n        </h2>\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={() => onNavigate('prev')}\r\n            className=\"h-8 w-8 p-0\"\r\n          >\r\n            <ChevronLeft className=\"h-4 w-4\" />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={() => onNavigate('next')}\r\n            className=\"h-8 w-8 p-0\"\r\n          >\r\n            <ChevronRight className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Calendar Grid */}\r\n      <div className=\"p-4\">\r\n        {/* Week days header */}\r\n        <div className=\"grid grid-cols-7 mb-2\">\r\n          {weekDays.map(day => (\r\n            <div key={day} className=\"text-center text-sm font-medium text-slate-600 py-2\">\r\n              {day}\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Calendar days */}\r\n        <div className=\"grid grid-cols-7 gap-1\">\r\n          {calendarDays.map((day, index) => {\r\n            const dayEvents = getEventsForDate(day.fullDate)\r\n            const isSelected = selectedDate.toDateString() === day.fullDate.toDateString()\r\n\r\n            return (\r\n              <motion.div\r\n                key={index}\r\n                className={`\r\n                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer\r\n                  transition-all duration-200 hover:bg-gray-50 group\r\n                  ${!day.isCurrentMonth ? 'opacity-40' : ''}\r\n                  ${day.isToday ? 'ring-2 ring-amber-400 bg-amber-50' : ''}\r\n                  ${isSelected ? 'ring-2 ring-blue-400 bg-blue-50' : ''}\r\n                `}\r\n                whileHover={{ scale: 1.02 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                onClick={() => onDateSelect(day.fullDate)}\r\n              >\r\n                {/* Date number */}\r\n                <div className={`\r\n                  text-sm font-medium mb-1\r\n                  ${day.isToday ? 'text-amber-700' : day.isCurrentMonth ? 'text-slate-700' : 'text-slate-400'}\r\n                `}>\r\n                  {day.date}\r\n                </div>\r\n\r\n                {/* Events */}\r\n                <div className=\"space-y-1\">\r\n                  {dayEvents.slice(0, 3).map((event) => (\r\n                    <motion.div\r\n                      key={event.id}\r\n                      className={`\r\n                        text-xs p-1 rounded text-white truncate cursor-pointer\r\n                        ${getStatusColor(event.status)}\r\n                      `}\r\n                      whileHover={{ scale: 1.05 }}\r\n                      onClick={(e) => {\r\n                        e.stopPropagation()\r\n                        onEventClick(event)\r\n                      }}\r\n                      title={`${event.time} - ${event.title}`}\r\n                    >\r\n                      {event.time} {event.title}\r\n                    </motion.div>\r\n                  ))}\r\n                  \r\n                  {dayEvents.length > 3 && (\r\n                    <div className=\"text-xs text-slate-500 font-medium\">\r\n                      +{dayEvents.length - 3} more\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* New appointment button - shows on hover */}\r\n                <motion.button\r\n                  className=\"absolute top-1 right-1 opacity-0 group-hover:opacity-100 \r\n                           w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center\r\n                           hover:bg-amber-600 transition-all duration-200\"\r\n                  initial={false}\r\n                  animate={{ opacity: 0 }}\r\n                  whileHover={{ opacity: 1 }}\r\n                  onClick={(e) => {\r\n                    e.stopPropagation()\r\n                    onNewAppointment(day.fullDate)\r\n                  }}\r\n                  title=\"Add appointment\"\r\n                >\r\n                  <Plus className=\"h-3 w-3\" />\r\n                </motion.button>\r\n              </motion.div>\r\n            )\r\n          })}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Legend */}\r\n      <div className=\"flex items-center justify-center space-x-4 p-4 border-t border-gray-200 bg-gray-50\">\r\n        {[\r\n          { status: 'pending', label: 'Pending' },\r\n          { status: 'assigned', label: 'Assigned' },\r\n          { status: 'in-progress', label: 'In Progress' },\r\n          { status: 'completed', label: 'Completed' },\r\n          { status: 'conflict', label: 'Conflict' }\r\n        ].map(({ status, label }) => (\r\n          <div key={status} className=\"flex items-center space-x-1\">\r\n            <div className={`w-3 h-3 rounded ${getStatusColor(status)}`} />\r\n            <span className=\"text-xs text-slate-600\">{label}</span>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AALA;;;;;AA2BO,SAAS,kBAAkB,EAChC,YAAY,EACZ,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,gBAAgB,EACO;IACvB,MAAM,QAAQ,IAAI;IAClB,MAAM,eAAe,aAAa,QAAQ;IAC1C,MAAM,cAAc,aAAa,WAAW;IAE5C,yBAAyB;IACzB,MAAM,kBAAkB,IAAI,KAAK,aAAa,cAAc;IAC5D,MAAM,iBAAiB,IAAI,KAAK,aAAa,eAAe,GAAG;IAC/D,MAAM,iBAAiB,gBAAgB,MAAM;IAC7C,MAAM,cAAc,eAAe,OAAO;IAE1C,MAAM,eAAe,EAAE;IAEvB,sBAAsB;IACtB,MAAM,YAAY,IAAI,KAAK,aAAa,eAAe,GAAG;IAC1D,IAAK,IAAI,IAAI,iBAAiB,GAAG,KAAK,GAAG,IAAK;QAC5C,aAAa,IAAI,CAAC;YAChB,MAAM,UAAU,OAAO,KAAK;YAC5B,gBAAgB;YAChB,SAAS;YACT,UAAU,IAAI,KAAK,aAAa,eAAe,GAAG,UAAU,OAAO,KAAK;QAC1E;IACF;IAEA,qBAAqB;IACrB,IAAK,IAAI,OAAO,GAAG,QAAQ,aAAa,OAAQ;QAC9C,MAAM,WAAW,IAAI,KAAK,aAAa,cAAc;QACrD,aAAa,IAAI,CAAC;YAChB;YACA,gBAAgB;YAChB,SAAS,SAAS,YAAY,OAAO,MAAM,YAAY;YACvD;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB,KAAK,aAAa,MAAM;IAC/C,IAAK,IAAI,OAAO,GAAG,QAAQ,gBAAgB,OAAQ;QACjD,aAAa,IAAI,CAAC;YAChB;YACA,gBAAgB;YAChB,SAAS;YACT,UAAU,IAAI,KAAK,aAAa,eAAe,GAAG;QACpD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAChD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;IAC/C;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa;QACjB;QAAW;QAAY;QAAS;QAAS;QAAO;QAChD;QAAQ;QAAU;QAAa;QAAW;QAAY;KACvD;IAED,MAAM,WAAW;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAAM;IAElE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BACX,UAAU,CAAC,aAAa;4BAAC;4BAAE;;;;;;;kCAE9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,WAAW;gCAC1B,WAAU;0CAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,WAAW;gCAC1B,WAAU;0CAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAA,oBACZ,8OAAC;gCAAc,WAAU;0CACtB;+BADO;;;;;;;;;;kCAOd,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,KAAK;4BACtB,MAAM,YAAY,iBAAiB,IAAI,QAAQ;4BAC/C,MAAM,aAAa,aAAa,YAAY,OAAO,IAAI,QAAQ,CAAC,YAAY;4BAE5E,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAW,CAAC;;;kBAGV,EAAE,CAAC,IAAI,cAAc,GAAG,eAAe,GAAG;kBAC1C,EAAE,IAAI,OAAO,GAAG,sCAAsC,GAAG;kBACzD,EAAE,aAAa,oCAAoC,GAAG;gBACxD,CAAC;gCACD,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,aAAa,IAAI,QAAQ;;kDAGxC,8OAAC;wCAAI,WAAW,CAAC;;kBAEf,EAAE,IAAI,OAAO,GAAG,mBAAmB,IAAI,cAAc,GAAG,mBAAmB,iBAAiB;gBAC9F,CAAC;kDACE,IAAI,IAAI;;;;;;kDAIX,8OAAC;wCAAI,WAAU;;4CACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAW,CAAC;;wBAEV,EAAE,eAAe,MAAM,MAAM,EAAE;sBACjC,CAAC;oDACD,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,aAAa;oDACf;oDACA,OAAO,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,MAAM,KAAK,EAAE;;wDAEtC,MAAM,IAAI;wDAAC;wDAAE,MAAM,KAAK;;mDAZpB,MAAM,EAAE;;;;;4CAgBhB,UAAU,MAAM,GAAG,mBAClB,8OAAC;gDAAI,WAAU;;oDAAqC;oDAChD,UAAU,MAAM,GAAG;oDAAE;;;;;;;;;;;;;kDAM7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCAGV,SAAS;wCACT,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,SAAS;wCAAE;wCACzB,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,iBAAiB,IAAI,QAAQ;wCAC/B;wCACA,OAAM;kDAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;+BA7Db;;;;;wBAiEX;;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,QAAQ;wBAAW,OAAO;oBAAU;oBACtC;wBAAE,QAAQ;wBAAY,OAAO;oBAAW;oBACxC;wBAAE,QAAQ;wBAAe,OAAO;oBAAc;oBAC9C;wBAAE,QAAQ;wBAAa,OAAO;oBAAY;oBAC1C;wBAAE,QAAQ;wBAAY,OAAO;oBAAW;iBACzC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,iBACtB,8OAAC;wBAAiB,WAAU;;0CAC1B,8OAAC;gCAAI,WAAW,CAAC,gBAAgB,EAAE,eAAe,SAAS;;;;;;0CAC3D,8OAAC;gCAAK,WAAU;0CAA0B;;;;;;;uBAFlC;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/schedule/calendar-day-view.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport { Clock, User, MapPin, Plus } from 'lucide-react'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport { Button } from '@/components/ui/button'\r\n\r\ninterface DayViewEvent {\r\n  id: string\r\n  title: string\r\n  startTime: string\r\n  duration: number // in minutes\r\n  customer: string\r\n  installer?: string\r\n  address: string\r\n  status: 'pending' | 'assigned' | 'in-progress' | 'completed' | 'conflict'\r\n}\r\n\r\ninterface CalendarDayViewProps {\r\n  date: Date\r\n  events: DayViewEvent[]\r\n  onEventClick: (event: DayViewEvent) => void\r\n  onTimeSlotClick: (time: string) => void\r\n}\r\n\r\nexport function CalendarDayView({\r\n  date,\r\n  events,\r\n  onEventClick,\r\n  onTimeSlotClick\r\n}: CalendarDayViewProps) {\r\n  const timeSlots = []\r\n  const startHour = 8\r\n  const endHour = 18\r\n  \r\n  // Generate time slots from 8 AM to 6 PM\r\n  for (let hour = startHour; hour <= endHour; hour++) {\r\n    timeSlots.push({\r\n      time: `${hour.toString().padStart(2, '0')}:00`,\r\n      hour\r\n    })\r\n  }\r\n\r\n  const getCurrentTimePosition = () => {\r\n    const now = new Date()\r\n    const isToday = now.toDateString() === date.toDateString()\r\n    \r\n    if (!isToday) return null\r\n    \r\n    const currentHour = now.getHours()\r\n    const currentMinute = now.getMinutes()\r\n    \r\n    if (currentHour < startHour || currentHour > endHour) return null\r\n    \r\n    const position = ((currentHour - startHour) * 60 + currentMinute) / (endHour - startHour) / 60 * 100\r\n    return Math.min(Math.max(position, 0), 100)\r\n  }\r\n\r\n  const getEventPosition = (event: DayViewEvent) => {\r\n    const [hours, minutes] = event.startTime.split(':').map(Number)\r\n    const startMinutes = (hours - startHour) * 60 + minutes\r\n    const top = (startMinutes / ((endHour - startHour) * 60)) * 100\r\n    const height = (event.duration / ((endHour - startHour) * 60)) * 100\r\n    \r\n    return {\r\n      top: `${Math.max(0, top)}%`,\r\n      height: `${Math.min(height, 100 - top)}%`\r\n    }\r\n  }\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'pending': return 'bg-amber-500 border-amber-600'\r\n      case 'assigned': return 'bg-blue-500 border-blue-600'\r\n      case 'in-progress': return 'bg-orange-500 border-orange-600'\r\n      case 'completed': return 'bg-green-500 border-green-600'\r\n      case 'conflict': return 'bg-red-500 border-red-600 animate-pulse'\r\n      default: return 'bg-gray-400 border-gray-500'\r\n    }\r\n  }\r\n\r\n  const formatTime = (timeString: string) => {\r\n    const [hours, minutes] = timeString.split(':').map(Number)\r\n    const ampm = hours >= 12 ? 'PM' : 'AM'\r\n    const displayHour = hours % 12 || 12\r\n    return `${displayHour}:${minutes.toString().padStart(2, '0')} ${ampm}`\r\n  }\r\n\r\n  const currentTimePosition = getCurrentTimePosition()\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg border border-gray-200 shadow-sm\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\r\n        <div>\r\n          <h2 className=\"text-lg font-semibold text-slate-700\">\r\n            {date.toLocaleDateString('en-US', { \r\n              weekday: 'long', \r\n              month: 'long', \r\n              day: 'numeric', \r\n              year: 'numeric' \r\n            })}\r\n          </h2>\r\n          <p className=\"text-sm text-slate-500\">\r\n            {events.length} {events.length === 1 ? 'appointment' : 'appointments'} scheduled\r\n          </p>\r\n        </div>\r\n        <Button \r\n          onClick={() => onTimeSlotClick('09:00')}\r\n          className=\"bg-amber-500 hover:bg-amber-600 text-white\"\r\n          size=\"sm\"\r\n        >\r\n          <Plus className=\"h-4 w-4 mr-2\" />\r\n          Add Appointment\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Time Grid */}\r\n      <div className=\"relative\">\r\n        {/* Time slots */}\r\n        <div className=\"flex\">\r\n          {/* Time labels */}\r\n          <div className=\"w-20 flex-shrink-0\">\r\n            {timeSlots.map(({ time }) => (\r\n              <div key={time} className=\"h-16 flex items-start pt-2\">\r\n                <span className=\"text-xs text-slate-500 font-medium\">\r\n                  {formatTime(time)}\r\n                </span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Calendar column */}\r\n          <div className=\"flex-1 relative border-l border-gray-200\">\r\n            {/* Time slot grid */}\r\n            {timeSlots.map(({ time }) => (\r\n              <motion.div\r\n                key={time}\r\n                className=\"h-16 border-b border-gray-100 hover:bg-gray-50 cursor-pointer group relative\"\r\n                whileHover={{ backgroundColor: '#f9fafb' }}\r\n                onClick={() => onTimeSlotClick(time)}\r\n              >\r\n                {/* Add appointment button on hover */}\r\n                <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    className=\"text-xs h-6 px-2 border-amber-200 text-amber-600 hover:bg-amber-50\"\r\n                  >\r\n                    <Plus className=\"h-3 w-3 mr-1\" />\r\n                    Add\r\n                  </Button>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n\r\n            {/* Events */}\r\n            {events.map((event) => {\r\n              const position = getEventPosition(event)\r\n              \r\n              return (\r\n                <motion.div\r\n                  key={event.id}\r\n                  className={`\r\n                    absolute left-2 right-2 rounded-md border-2 cursor-pointer\r\n                    ${getStatusColor(event.status)} text-white p-2 shadow-sm\r\n                    overflow-hidden z-10\r\n                  `}\r\n                  style={position}\r\n                  whileHover={{ scale: 1.02, zIndex: 20 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                  onClick={() => onEventClick(event)}\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.3 }}\r\n                >\r\n                  <div className=\"space-y-1\">\r\n                    <div className=\"font-medium text-sm truncate\">\r\n                      {event.title}\r\n                    </div>\r\n                    \r\n                    <div className=\"flex items-center space-x-1 text-xs opacity-90\">\r\n                      <Clock className=\"h-3 w-3\" />\r\n                      <span>\r\n                        {formatTime(event.startTime)} ({event.duration}m)\r\n                      </span>\r\n                    </div>\r\n                    \r\n                    <div className=\"flex items-center space-x-1 text-xs opacity-90\">\r\n                      <User className=\"h-3 w-3\" />\r\n                      <span className=\"truncate\">{event.customer}</span>\r\n                    </div>\r\n                    \r\n                    {event.installer && (\r\n                      <div className=\"flex items-center space-x-2 mt-2\">\r\n                        <Avatar className=\"h-5 w-5\">\r\n                          <AvatarImage src={`/avatars/${event.installer.toLowerCase().replace(' ', '-')}.jpg`} />\r\n                          <AvatarFallback className=\"bg-white text-slate-700 text-xs\">\r\n                            {event.installer.split(' ').map(n => n[0]).join('')}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <span className=\"text-xs truncate\">{event.installer}</span>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    <div className=\"flex items-center space-x-1 text-xs opacity-90\">\r\n                      <MapPin className=\"h-3 w-3\" />\r\n                      <span className=\"truncate\">{event.address}</span>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )\r\n            })}\r\n\r\n            {/* Current time indicator */}\r\n            {currentTimePosition !== null && (\r\n              <motion.div\r\n                className=\"absolute left-0 right-0 z-30 flex items-center\"\r\n                style={{ top: `${currentTimePosition}%` }}\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.5 }}\r\n              >\r\n                <div className=\"w-3 h-3 bg-red-500 rounded-full border-2 border-white shadow-md\" />\r\n                <div className=\"flex-1 h-0.5 bg-red-500\" />\r\n                <div className=\"bg-red-500 text-white text-xs px-2 py-1 rounded-md ml-2 shadow-md\">\r\n                  Now\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Footer with stats */}\r\n      <div className=\"flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50\">\r\n        <div className=\"flex items-center space-x-4 text-sm text-slate-600\">\r\n          <span>\r\n            <span className=\"font-medium\">{events.length}</span> appointments\r\n          </span>\r\n          <span>\r\n            <span className=\"font-medium\">\r\n              {events.reduce((total, event) => total + event.duration, 0)}\r\n            </span> minutes total\r\n          </span>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {[\r\n            { status: 'pending', count: events.filter(e => e.status === 'pending').length },\r\n            { status: 'assigned', count: events.filter(e => e.status === 'assigned').length },\r\n            { status: 'in-progress', count: events.filter(e => e.status === 'in-progress').length },\r\n            { status: 'completed', count: events.filter(e => e.status === 'completed').length },\r\n            { status: 'conflict', count: events.filter(e => e.status === 'conflict').length }\r\n          ].filter(({ count }) => count > 0).map(({ status, count }) => (\r\n            <div key={status} className=\"flex items-center space-x-1\">\r\n              <div className={`w-2 h-2 rounded-full ${getStatusColor(status).split(' ')[0]}`} />\r\n              <span className=\"text-xs\">{count}</span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;AA0BO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,eAAe,EACM;IACrB,MAAM,YAAY,EAAE;IACpB,MAAM,YAAY;IAClB,MAAM,UAAU;IAEhB,wCAAwC;IACxC,IAAK,IAAI,OAAO,WAAW,QAAQ,SAAS,OAAQ;QAClD,UAAU,IAAI,CAAC;YACb,MAAM,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;YAC9C;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,IAAI,YAAY,OAAO,KAAK,YAAY;QAExD,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,cAAc,IAAI,QAAQ;QAChC,MAAM,gBAAgB,IAAI,UAAU;QAEpC,IAAI,cAAc,aAAa,cAAc,SAAS,OAAO;QAE7D,MAAM,WAAW,CAAC,CAAC,cAAc,SAAS,IAAI,KAAK,aAAa,IAAI,CAAC,UAAU,SAAS,IAAI,KAAK;QACjG,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,IAAI;IACzC;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,CAAC,OAAO,QAAQ,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QACxD,MAAM,eAAe,CAAC,QAAQ,SAAS,IAAI,KAAK;QAChD,MAAM,MAAM,AAAC,eAAe,CAAC,CAAC,UAAU,SAAS,IAAI,EAAE,IAAK;QAC5D,MAAM,SAAS,AAAC,MAAM,QAAQ,GAAG,CAAC,CAAC,UAAU,SAAS,IAAI,EAAE,IAAK;QAEjE,OAAO;YACL,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;YAC3B,QAAQ,GAAG,KAAK,GAAG,CAAC,QAAQ,MAAM,KAAK,CAAC,CAAC;QAC3C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,CAAC,OAAO,QAAQ,GAAG,WAAW,KAAK,CAAC,KAAK,GAAG,CAAC;QACnD,MAAM,OAAO,SAAS,KAAK,OAAO;QAClC,MAAM,cAAc,QAAQ,MAAM;QAClC,OAAO,GAAG,YAAY,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,MAAM;IACxE;IAEA,MAAM,sBAAsB;IAE5B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CACX,KAAK,kBAAkB,CAAC,SAAS;oCAChC,SAAS;oCACT,OAAO;oCACP,KAAK;oCACL,MAAM;gCACR;;;;;;0CAEF,8OAAC;gCAAE,WAAU;;oCACV,OAAO,MAAM;oCAAC;oCAAE,OAAO,MAAM,KAAK,IAAI,gBAAgB;oCAAe;;;;;;;;;;;;;kCAG1E,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;wBACV,MAAK;;0CAEL,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,iBACtB,8OAAC;oCAAe,WAAU;8CACxB,cAAA,8OAAC;wCAAK,WAAU;kDACb,WAAW;;;;;;mCAFN;;;;;;;;;;sCASd,8OAAC;4BAAI,WAAU;;gCAEZ,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,iBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,YAAY;4CAAE,iBAAiB;wCAAU;wCACzC,SAAS,IAAM,gBAAgB;kDAG/B,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;uCAZhC;;;;;gCAoBR,OAAO,GAAG,CAAC,CAAC;oCACX,MAAM,WAAW,iBAAiB;oCAElC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAW,CAAC;;oBAEV,EAAE,eAAe,MAAM,MAAM,EAAE;;kBAEjC,CAAC;wCACD,OAAO;wCACP,YAAY;4CAAE,OAAO;4CAAM,QAAQ;wCAAG;wCACtC,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,aAAa;wCAC5B,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,MAAM,KAAK;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;gEACE,WAAW,MAAM,SAAS;gEAAE;gEAAG,MAAM,QAAQ;gEAAC;;;;;;;;;;;;;8DAInD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAY,MAAM,QAAQ;;;;;;;;;;;;gDAG3C,MAAM,SAAS,kBACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,8OAAC,kIAAA,CAAA,cAAW;oEAAC,KAAK,CAAC,SAAS,EAAE,MAAM,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC;;;;;;8EACnF,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;sEAGpD,8OAAC;4DAAK,WAAU;sEAAoB,MAAM,SAAS;;;;;;;;;;;;8DAIvD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAY,MAAM,OAAO;;;;;;;;;;;;;;;;;;uCA7CxC,MAAM,EAAE;;;;;gCAkDnB;gCAGC,wBAAwB,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE,KAAK,GAAG,oBAAoB,CAAC,CAAC;oCAAC;oCACxC,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDAAoE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7F,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAe,OAAO,MAAM;;;;;;oCAAQ;;;;;;;0CAEtD,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDACb,OAAO,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,QAAQ,EAAE;;;;;;oCACpD;;;;;;;;;;;;;kCAIX,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,QAAQ;gCAAW,OAAO,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;4BAAC;4BAC9E;gCAAE,QAAQ;gCAAY,OAAO,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;4BAAC;4BAChF;gCAAE,QAAQ;gCAAe,OAAO,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;4BAAC;4BACtF;gCAAE,QAAQ;gCAAa,OAAO,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;4BAAC;4BAClF;gCAAE,QAAQ;gCAAY,OAAO,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;4BAAC;yBACjF,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,QAAQ,GAAG,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,iBACvD,8OAAC;gCAAiB,WAAU;;kDAC1B,8OAAC;wCAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;;;;;;kDAC9E,8OAAC;wCAAK,WAAU;kDAAW;;;;;;;+BAFnB;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/schedule/calendar-week-view.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport { ChevronLeft, ChevronRight, Plus } from 'lucide-react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Card, CardContent } from '@/components/ui/card'\r\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\r\n\r\ninterface WeekEvent {\r\n  id: string\r\n  title: string\r\n  startTime: string\r\n  duration: number // in minutes\r\n  customer: string\r\n  address: string\r\n  installer: string\r\n  status: 'assigned' | 'pending' | 'in-progress' | 'completed'\r\n}\r\n\r\ninterface CalendarWeekViewProps {\r\n  date: Date // The date to center the week around\r\n  events: WeekEvent[]\r\n  onEventClick: (event: WeekEvent) => void\r\n  onTimeSlotClick: (date: Date, time: string) => void\r\n  onNavigate: (direction: 'prev' | 'next') => void\r\n  onNewAppointment: (date: Date) => void\r\n}\r\n\r\nexport function CalendarWeekView({\r\n  date,\r\n  events,\r\n  onEventClick,\r\n  onTimeSlotClick,\r\n  onNavigate,\r\n  onNewAppointment\r\n}: CalendarWeekViewProps) {\r\n  // Get the start of the week (Sunday)\r\n  const getWeekStart = (date: Date): Date => {\r\n    const d = new Date(date)\r\n    const day = d.getDay()\r\n    const diff = d.getDate() - day\r\n    return new Date(d.setDate(diff))\r\n  }\r\n\r\n  // Generate the 7 days of the week\r\n  const getWeekDays = (startDate: Date): Date[] => {\r\n    const days = []\r\n    for (let i = 0; i < 7; i++) {\r\n      const day = new Date(startDate)\r\n      day.setDate(startDate.getDate() + i)\r\n      days.push(day)\r\n    }\r\n    return days\r\n  }\r\n\r\n  // Generate time slots from 8 AM to 6 PM\r\n  const generateTimeSlots = (): string[] => {\r\n    const slots = []\r\n    for (let hour = 8; hour <= 18; hour++) {\r\n      slots.push(`${hour.toString().padStart(2, '0')}:00`)\r\n      if (hour < 18) {\r\n        slots.push(`${hour.toString().padStart(2, '0')}:30`)\r\n      }\r\n    }\r\n    return slots\r\n  }\r\n\r\n  // Calculate event position and height\r\n  const getEventStyle = (startTime: string, duration: number) => {\r\n    const [hours, minutes] = startTime.split(':').map(Number)\r\n    const startMinutes = (hours - 8) * 60 + minutes\r\n    const topPercent = (startMinutes / (10 * 60)) * 100 // 10 hours (8 AM to 6 PM)\r\n    const heightPercent = (duration / (10 * 60)) * 100\r\n    \r\n    return {\r\n      top: `${topPercent}%`,\r\n      height: `${Math.max(heightPercent, 8)}%`, // Minimum height for visibility\r\n    }\r\n  }\r\n\r\n  // Get status color\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'assigned': return 'bg-blue-500'\r\n      case 'pending': return 'bg-amber-500'\r\n      case 'in-progress': return 'bg-orange-500'\r\n      case 'completed': return 'bg-green-500'\r\n      default: return 'bg-gray-500'\r\n    }\r\n  }\r\n\r\n  // Filter events by date\r\n  const getEventsForDate = (targetDate: Date): WeekEvent[] => {\r\n    const dateStr = targetDate.toISOString().split('T')[0]\r\n    const today = new Date()\r\n    const todayStr = today.toISOString().split('T')[0]\r\n    \r\n    // Show events only on today for demo purposes\r\n    // In a real app, you'd filter events by the specific date\r\n    if (dateStr === todayStr) {\r\n      return events\r\n    }\r\n    return []\r\n  }\r\n\r\n  const weekStart = getWeekStart(date)\r\n  const weekDays = getWeekDays(weekStart)\r\n  const timeSlots = generateTimeSlots()\r\n  const today = new Date()\r\n\r\n  return (\r\n    <Card className=\"bg-white border-gray-200 shadow-sm\">\r\n      <CardContent className=\"p-0\">\r\n        {/* Week Header */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={() => onNavigate('prev')}\r\n          >\r\n            <ChevronLeft className=\"h-4 w-4\" />\r\n          </Button>\r\n          \r\n          <h3 className=\"font-semibold text-slate-700\">\r\n            {weekStart.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })} - {' '}\r\n            {weekDays[6].toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}\r\n          </h3>\r\n          \r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={() => onNavigate('next')}\r\n          >\r\n            <ChevronRight className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Week Grid */}\r\n        <div className=\"grid grid-cols-8 min-h-[600px]\">\r\n          {/* Time Column */}\r\n          <div className=\"border-r border-gray-200 bg-gray-50\">\r\n            <div className=\"h-16 border-b border-gray-200\"></div> {/* Header spacer */}\r\n            {timeSlots.map((time) => (\r\n              <div\r\n                key={time}\r\n                className=\"h-12 px-2 py-1 text-xs text-slate-500 border-b border-gray-100 flex items-center justify-end\"\r\n              >\r\n                {time}\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Day Columns */}\r\n          {weekDays.map((day) => {\r\n            const dayEvents = getEventsForDate(day)\r\n            const isToday = day.toDateString() === today.toDateString()\r\n            const isWeekend = day.getDay() === 0 || day.getDay() === 6\r\n\r\n            return (\r\n              <div\r\n                key={day.toISOString()}\r\n                className={`border-r border-gray-200 relative ${\r\n                  isWeekend ? 'bg-gray-50' : 'bg-white'\r\n                }`}\r\n              >\r\n                {/* Day Header */}\r\n                <div\r\n                  className={`h-16 p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors ${\r\n                    isToday ? 'bg-amber-50' : ''\r\n                  }`}\r\n                  onClick={() => onNewAppointment(day)}\r\n                >\r\n                  <div className=\"text-xs text-slate-500 uppercase font-medium\">\r\n                    {day.toLocaleDateString('en-US', { weekday: 'short' })}\r\n                  </div>\r\n                  <div\r\n                    className={`text-lg font-semibold mt-1 ${\r\n                      isToday ? 'text-amber-600' : 'text-slate-700'\r\n                    }`}\r\n                  >\r\n                    {day.getDate()}\r\n                  </div>\r\n                  {dayEvents.length > 0 && (\r\n                    <div className=\"text-xs text-slate-500 mt-1\">\r\n                      {dayEvents.length} appointment{dayEvents.length > 1 ? 's' : ''}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Time Slots */}\r\n                <div className=\"relative\">\r\n                  {timeSlots.map((time) => (\r\n                    <div\r\n                      key={time}\r\n                      className=\"h-12 border-b border-gray-100 hover:bg-amber-50 cursor-pointer transition-colors group\"\r\n                      onClick={() => onTimeSlotClick(day, time)}\r\n                    >\r\n                      <div className=\"opacity-0 group-hover:opacity-100 transition-opacity p-1\">\r\n                        <Plus className=\"h-3 w-3 text-amber-500\" />\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n\r\n                  {/* Events */}\r\n                  {dayEvents.map((event) => {\r\n                    const style = getEventStyle(event.startTime, event.duration)\r\n                    return (\r\n                      <motion.div\r\n                        key={event.id}\r\n                        initial={{ opacity: 0, scale: 0.9 }}\r\n                        animate={{ opacity: 1, scale: 1 }}\r\n                        transition={{ duration: 0.2 }}\r\n                        className={`absolute left-1 right-1 rounded-lg p-2 cursor-pointer hover:shadow-md transition-shadow z-10 ${getStatusColor(\r\n                          event.status\r\n                        )} text-white text-xs`}\r\n                        style={style}\r\n                        onClick={() => onEventClick(event)}\r\n                      >\r\n                        <div className=\"font-medium truncate\">{event.title}</div>\r\n                        <div className=\"opacity-90 truncate\">{event.customer}</div>\r\n                        <div className=\"flex items-center mt-1\">\r\n                          <Avatar className=\"h-4 w-4 mr-1\">\r\n                            <AvatarFallback className=\"text-[10px] bg-white/20\">\r\n                              {event.installer.split(' ').map(n => n[0]).join('')}\r\n                            </AvatarFallback>\r\n                          </Avatar>\r\n                          <span className=\"text-[10px] opacity-90 truncate\">\r\n                            {event.installer}\r\n                          </span>\r\n                        </div>\r\n                      </motion.div>\r\n                    )\r\n                  })}\r\n                </div>\r\n              </div>\r\n            )\r\n          })}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;AA6BO,SAAS,iBAAiB,EAC/B,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,eAAe,EACf,UAAU,EACV,gBAAgB,EACM;IACtB,qCAAqC;IACrC,MAAM,eAAe,CAAC;QACpB,MAAM,IAAI,IAAI,KAAK;QACnB,MAAM,MAAM,EAAE,MAAM;QACpB,MAAM,OAAO,EAAE,OAAO,KAAK;QAC3B,OAAO,IAAI,KAAK,EAAE,OAAO,CAAC;IAC5B;IAEA,kCAAkC;IAClC,MAAM,cAAc,CAAC;QACnB,MAAM,OAAO,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,MAAM,IAAI,KAAK;YACrB,IAAI,OAAO,CAAC,UAAU,OAAO,KAAK;YAClC,KAAK,IAAI,CAAC;QACZ;QACA,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,oBAAoB;QACxB,MAAM,QAAQ,EAAE;QAChB,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;YACrC,MAAM,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;YACnD,IAAI,OAAO,IAAI;gBACb,MAAM,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;YACrD;QACF;QACA,OAAO;IACT;IAEA,sCAAsC;IACtC,MAAM,gBAAgB,CAAC,WAAmB;QACxC,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC;QAClD,MAAM,eAAe,CAAC,QAAQ,CAAC,IAAI,KAAK;QACxC,MAAM,aAAa,AAAC,eAAe,CAAC,KAAK,EAAE,IAAK,IAAI,0BAA0B;;QAC9E,MAAM,gBAAgB,AAAC,WAAW,CAAC,KAAK,EAAE,IAAK;QAE/C,OAAO;YACL,KAAK,GAAG,WAAW,CAAC,CAAC;YACrB,QAAQ,GAAG,KAAK,GAAG,CAAC,eAAe,GAAG,CAAC,CAAC;QAC1C;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,wBAAwB;IACxB,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAU,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACtD,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAElD,8CAA8C;QAC9C,0DAA0D;QAC1D,IAAI,YAAY,UAAU;YACxB,OAAO;QACT;QACA,OAAO,EAAE;IACX;IAEA,MAAM,YAAY,aAAa;IAC/B,MAAM,WAAW,YAAY;IAC7B,MAAM,YAAY;IAClB,MAAM,QAAQ,IAAI;IAElB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,WAAW;sCAE1B,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAGzB,8OAAC;4BAAG,WAAU;;gCACX,UAAU,kBAAkB,CAAC,SAAS;oCAAE,OAAO;oCAAQ,KAAK;gCAAU;gCAAG;gCAAI;gCAC7E,QAAQ,CAAC,EAAE,CAAC,kBAAkB,CAAC,SAAS;oCAAE,OAAO;oCAAQ,KAAK;oCAAW,MAAM;gCAAU;;;;;;;sCAG5F,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,WAAW;sCAE1B,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;gCAAsC;gCACpD,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;wBASV,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,YAAY,iBAAiB;4BACnC,MAAM,UAAU,IAAI,YAAY,OAAO,MAAM,YAAY;4BACzD,MAAM,YAAY,IAAI,MAAM,OAAO,KAAK,IAAI,MAAM,OAAO;4BAEzD,qBACE,8OAAC;gCAEC,WAAW,CAAC,kCAAkC,EAC5C,YAAY,eAAe,YAC3B;;kDAGF,8OAAC;wCACC,WAAW,CAAC,oFAAoF,EAC9F,UAAU,gBAAgB,IAC1B;wCACF,SAAS,IAAM,iBAAiB;;0DAEhC,8OAAC;gDAAI,WAAU;0DACZ,IAAI,kBAAkB,CAAC,SAAS;oDAAE,SAAS;gDAAQ;;;;;;0DAEtD,8OAAC;gDACC,WAAW,CAAC,2BAA2B,EACrC,UAAU,mBAAmB,kBAC7B;0DAED,IAAI,OAAO;;;;;;4CAEb,UAAU,MAAM,GAAG,mBAClB,8OAAC;gDAAI,WAAU;;oDACZ,UAAU,MAAM;oDAAC;oDAAa,UAAU,MAAM,GAAG,IAAI,MAAM;;;;;;;;;;;;;kDAMlE,8OAAC;wCAAI,WAAU;;4CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,gBAAgB,KAAK;8DAEpC,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;mDALb;;;;;4CAWR,UAAU,GAAG,CAAC,CAAC;gDACd,MAAM,QAAQ,cAAc,MAAM,SAAS,EAAE,MAAM,QAAQ;gDAC3D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,WAAW,CAAC,6FAA6F,EAAE,eACzG,MAAM,MAAM,EACZ,mBAAmB,CAAC;oDACtB,OAAO;oDACP,SAAS,IAAM,aAAa;;sEAE5B,8OAAC;4DAAI,WAAU;sEAAwB,MAAM,KAAK;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAuB,MAAM,QAAQ;;;;;;sEACpD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;8EAGpD,8OAAC;oEAAK,WAAU;8EACb,MAAM,SAAS;;;;;;;;;;;;;mDAnBf,MAAM,EAAE;;;;;4CAwBnB;;;;;;;;+BAxEG,IAAI,WAAW;;;;;wBA4E1B;;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 2491, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/textarea.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nTextarea.displayName = 'Textarea'\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2548, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/switch.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface SwitchProps {\r\n  checked?: boolean\r\n  onCheckedChange?: (checked: boolean) => void\r\n  disabled?: boolean\r\n  className?: string\r\n  id?: string\r\n}\r\n\r\nconst Switch = React.forwardRef<HTMLButtonElement, SwitchProps>(\r\n  ({ className, checked = false, onCheckedChange, disabled = false, id, ...props }, ref) => {\r\n    const handleClick = () => {\r\n      if (!disabled && onCheckedChange) {\r\n        onCheckedChange(!checked)\r\n      }\r\n    }\r\n\r\n    return (\r\n      <button\r\n        ref={ref}\r\n        type=\"button\"\r\n        role=\"switch\"\r\n        aria-checked={checked}\r\n        disabled={disabled}\r\n        onClick={handleClick}\r\n        id={id}\r\n        className={cn(\r\n          'peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-sm border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50',\r\n          checked ? 'bg-primary' : 'bg-input',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <span\r\n          className={cn(\r\n            'pointer-events-none block h-5 w-5 rounded bg-background shadow-lg ring-0 transition-transform',\r\n            checked ? 'translate-x-5' : 'translate-x-0'\r\n          )}\r\n        />\r\n      </button>\r\n    )\r\n  }\r\n)\r\nSwitch.displayName = 'Switch'\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAaA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,eAAe,EAAE,WAAW,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAChF,MAAM,cAAc;QAClB,IAAI,CAAC,YAAY,iBAAiB;YAChC,gBAAgB,CAAC;QACnB;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,MAAK;QACL,MAAK;QACL,gBAAc;QACd,UAAU;QACV,SAAS;QACT,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oTACA,UAAU,eAAe,YACzB;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,UAAU,kBAAkB;;;;;;;;;;;AAKtC;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2595, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2659, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2851, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3025, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/schedule/new-appointment-modal.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport { \r\n  Calendar as CalendarIcon, \r\n  User, \r\n  MapPin, \r\n  Zap,\r\n  Save,\r\n  Users,\r\n  Building,\r\n  FileText,\r\n  Bell,\r\n  Paperclip\r\n} from 'lucide-react'\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Textarea } from '@/components/ui/textarea'\r\nimport { Card, CardContent } from '@/components/ui/card'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport { Switch } from '@/components/ui/switch'\r\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\n\r\ninterface Project {\r\n  id: string\r\n  name: string\r\n  customer: string\r\n  address: string\r\n  productTypes: string[]\r\n  status: string\r\n}\r\n\r\ninterface Installer {\r\n  id: string\r\n  name: string\r\n  avatar: string | null\r\n  availability: 'available' | 'busy' | 'unavailable'\r\n  utilization: number\r\n}\r\n\r\ninterface Customer {\r\n  id: string\r\n  name: string\r\n  email: string\r\n  phone: string\r\n  address: string\r\n}\r\n\r\ninterface AppointmentData {\r\n  id: string\r\n  projectId: string\r\n  customerId: string\r\n  date: string\r\n  startTime: string\r\n  endTime: string\r\n  duration: number\r\n  installerId: string\r\n  location: string\r\n  productTypes: string[]\r\n  notes: string\r\n  estimatedDuration: number\r\n  sendNotification: boolean\r\n  autoReminder: boolean\r\n  smsReminder: boolean\r\n  emailReminder: boolean\r\n}\r\n\r\ninterface NewAppointmentModalProps {\r\n  isOpen: boolean\r\n  onClose: () => void\r\n  onSave: (appointment: AppointmentData) => void\r\n  selectedDate?: Date\r\n  selectedTime?: string\r\n  projects?: Project[]\r\n  installers?: Installer[]\r\n  customers?: Customer[]\r\n}\r\n\r\nexport function NewAppointmentModal({\r\n  isOpen,\r\n  onClose,\r\n  onSave,\r\n  selectedDate,\r\n  selectedTime,\r\n  projects = [],\r\n  installers = [],\r\n  customers = []\r\n}: NewAppointmentModalProps) {\r\n  const [activeTab, setActiveTab] = useState('details')\r\n  const [isOptimizing, setIsOptimizing] = useState(false)\r\n\r\n  const [formData, setFormData] = useState<Partial<AppointmentData>>({\r\n    projectId: '',\r\n    customerId: '',\r\n    date: selectedDate?.toISOString().split('T')[0] || '',\r\n    startTime: selectedTime || '09:00',\r\n    endTime: '12:00',\r\n    duration: 180,\r\n    installerId: '',\r\n    location: '',\r\n    productTypes: [],\r\n    notes: '',\r\n    estimatedDuration: 180,\r\n    sendNotification: true,\r\n    autoReminder: true,\r\n    smsReminder: false,\r\n    emailReminder: true\r\n  })\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({})\r\n\r\n  const validateForm = () => {\r\n    const newErrors: Record<string, string> = {}\r\n    \r\n    if (!formData.projectId) newErrors.projectId = 'Project is required'\r\n    if (!formData.date) newErrors.date = 'Date is required'\r\n    if (!formData.startTime) newErrors.startTime = 'Start time is required'\r\n    if (!formData.endTime) newErrors.endTime = 'End time is required'\r\n    if (!formData.installerId) newErrors.installerId = 'Installer is required'\r\n    \r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }\r\n\r\n  const handleSave = () => {\r\n    if (!validateForm()) {\r\n      setActiveTab('details')\r\n      return\r\n    }\r\n\r\n    const appointment: AppointmentData = {\r\n      ...formData,\r\n      id: `appt_${Date.now()}`,\r\n    } as AppointmentData\r\n\r\n    onSave(appointment)\r\n    onClose()\r\n  }\r\n\r\n  const handleOptimizeSlot = async () => {\r\n    setIsOptimizing(true)\r\n    \r\n    // Simulate AI optimization\r\n    await new Promise(resolve => setTimeout(resolve, 2000))\r\n    \r\n    // Mock optimization result\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      startTime: '14:00',\r\n      endTime: '16:30',\r\n      installerId: installers.find(i => i.availability === 'available')?.id || prev.installerId\r\n    }))\r\n    \r\n    setIsOptimizing(false)\r\n  }\r\n\r\n  const selectedProject = projects.find(p => p.id === formData.projectId)\r\n  const selectedInstaller = installers.find(i => i.id === formData.installerId)\r\n\r\n  const getAvailabilityColor = (availability: string) => {\r\n    switch (availability) {\r\n      case 'available': return 'bg-green-500'\r\n      case 'busy': return 'bg-amber-500'\r\n      case 'unavailable': return 'bg-red-500'\r\n      default: return 'bg-gray-500'\r\n    }\r\n  }\r\n\r\n  const productTypeOptions = [\r\n    'Windows', 'Blinds', 'Shutters', 'Curtains', 'Shades', \r\n    'Awnings', 'Security Screens', 'Repair', 'Maintenance'\r\n  ]\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-hidden p-0\">\r\n        <DialogHeader className=\"px-6 py-4 border-b border-gray-200\">\r\n          <DialogTitle className=\"text-xl font-bold text-slate-700\">\r\n            Schedule New Appointment\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"flex-1 overflow-auto\">\r\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n            <TabsList className=\"grid grid-cols-4 w-full px-6 py-2\">\r\n              <TabsTrigger value=\"details\" className=\"flex items-center space-x-2\">\r\n                <CalendarIcon className=\"h-4 w-4\" />\r\n                <span>Details</span>\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"workflow\" className=\"flex items-center space-x-2\">\r\n                <Users className=\"h-4 w-4\" />\r\n                <span>Team</span>\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"notifications\" className=\"flex items-center space-x-2\">\r\n                <Bell className=\"h-4 w-4\" />\r\n                <span>Notifications</span>\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"attachments\" className=\"flex items-center space-x-2\">\r\n                <Paperclip className=\"h-4 w-4\" />\r\n                <span>Files</span>\r\n              </TabsTrigger>\r\n            </TabsList>\r\n\r\n            <div className=\"p-6\">\r\n              <TabsContent value=\"details\" className=\"space-y-6 mt-0\">\r\n                {/* Project Selection */}\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium\">\r\n                    Project *\r\n                  </Label>\r\n                  <Select\r\n                    value={formData.projectId}\r\n                    onValueChange={(value) => {\r\n                      const project = projects.find(p => p.id === value)\r\n                      setFormData(prev => ({ \r\n                        ...prev, \r\n                        projectId: value,\r\n                        location: project?.address || ''\r\n                      }))\r\n                    }}\r\n                  >\r\n                    <SelectTrigger className={errors.projectId ? 'border-red-500' : ''}>\r\n                      <SelectValue placeholder=\"Select project...\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {projects.map((project) => (\r\n                        <SelectItem key={project.id} value={project.id}>\r\n                          <div className=\"flex flex-col\">\r\n                            <span className=\"font-medium\">{project.name}</span>\r\n                            <span className=\"text-xs text-muted-foreground\">\r\n                              {project.customer} • {project.address}\r\n                            </span>\r\n                          </div>\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                  {errors.projectId && <p className=\"text-sm text-red-500\">{errors.projectId}</p>}\r\n                  \r\n                  {selectedProject && (\r\n                    <Card className=\"mt-3\">\r\n                      <CardContent className=\"pt-4\">\r\n                        <div className=\"space-y-2\">\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <User className=\"h-4 w-4 text-muted-foreground\" />\r\n                            <span className=\"text-sm\">{selectedProject.customer}</span>\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <MapPin className=\"h-4 w-4 text-muted-foreground\" />\r\n                            <span className=\"text-sm\">{selectedProject.address}</span>\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <Building className=\"h-4 w-4 text-muted-foreground\" />\r\n                            <div className=\"flex flex-wrap gap-1\">\r\n                              {selectedProject.productTypes.map((type) => (\r\n                                <Badge key={type} variant=\"secondary\" className=\"text-xs\">\r\n                                  {type}\r\n                                </Badge>\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Date and Time */}\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  <div className=\"space-y-2\">\r\n                    <Label className=\"text-sm font-medium\">\r\n                      Date *\r\n                    </Label>\r\n                    <Input\r\n                      type=\"date\"\r\n                      value={formData.date}\r\n                      onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}\r\n                      className={errors.date ? 'border-red-500' : ''}\r\n                    />\r\n                    {errors.date && <p className=\"text-sm text-red-500\">{errors.date}</p>}\r\n                  </div>\r\n                  <div className=\"space-y-2\">\r\n                    <Label className=\"text-sm font-medium\">\r\n                      Time Range *\r\n                    </Label>\r\n                    <div className=\"grid grid-cols-2 gap-2\">\r\n                      <Input\r\n                        type=\"time\"\r\n                        value={formData.startTime}\r\n                        onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}\r\n                        className={errors.startTime ? 'border-red-500' : ''}\r\n                      />\r\n                      <Input\r\n                        type=\"time\"\r\n                        value={formData.endTime}\r\n                        onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}\r\n                        className={errors.endTime ? 'border-red-500' : ''}\r\n                      />\r\n                    </div>\r\n                    {(errors.startTime || errors.endTime) && (\r\n                      <p className=\"text-sm text-red-500\">Start and end times are required</p>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Estimated Duration */}\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium\">\r\n                    Estimated Duration: {formData.estimatedDuration} minutes\r\n                  </Label>\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => setFormData(prev => ({ \r\n                        ...prev, \r\n                        estimatedDuration: Math.max(30, (prev.estimatedDuration || 180) - 15)\r\n                      }))}\r\n                    >\r\n                      -15m\r\n                    </Button>\r\n                    <Input\r\n                      type=\"number\"\r\n                      min=\"30\"\r\n                      max=\"480\"\r\n                      step=\"15\"\r\n                      value={formData.estimatedDuration}\r\n                      onChange={(e) => setFormData(prev => ({ \r\n                        ...prev, \r\n                        estimatedDuration: parseInt(e.target.value) || 180\r\n                      }))}\r\n                      className=\"text-center\"\r\n                    />\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => setFormData(prev => ({ \r\n                        ...prev, \r\n                        estimatedDuration: Math.min(480, (prev.estimatedDuration || 180) + 15)\r\n                      }))}\r\n                    >\r\n                      +15m\r\n                    </Button>\r\n                  </div>\r\n                  <div className=\"flex justify-between text-xs text-muted-foreground\">\r\n                    <span>30 min minimum</span>\r\n                    <span>8 hours maximum</span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Product Types */}\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium\">Product Types</Label>\r\n                  <div className=\"flex flex-wrap gap-2\">\r\n                    {productTypeOptions.map((type) => (\r\n                      <Button\r\n                        key={type}\r\n                        variant={formData.productTypes?.includes(type) ? \"default\" : \"outline\"}\r\n                        size=\"sm\"\r\n                        onClick={() => {\r\n                          const currentTypes = formData.productTypes || []\r\n                          const newTypes = currentTypes.includes(type)\r\n                            ? currentTypes.filter(t => t !== type)\r\n                            : [...currentTypes, type]\r\n                          setFormData(prev => ({ ...prev, productTypes: newTypes }))\r\n                        }}\r\n                      >\r\n                        {type}\r\n                      </Button>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Location */}\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium\">Location</Label>\r\n                  <Input\r\n                    placeholder=\"Enter appointment location...\"\r\n                    value={formData.location}\r\n                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}\r\n                  />\r\n                </div>\r\n\r\n                {/* AI Optimization */}\r\n                <Card className=\"bg-amber-50 border-amber-200\">\r\n                  <CardContent className=\"p-4\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center space-x-3\">\r\n                        <div className=\"p-2 bg-amber-100 rounded-full\">\r\n                          <Zap className=\"h-4 w-4 text-amber-600\" />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-slate-700\">AI Schedule Optimization</p>\r\n                          <p className=\"text-xs text-slate-600\">\r\n                            Find the optimal time slot based on installer availability and location\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={handleOptimizeSlot}\r\n                        disabled={isOptimizing}\r\n                        className=\"bg-amber-500 text-white hover:bg-amber-600 border-amber-500\"\r\n                      >\r\n                        {isOptimizing ? (\r\n                          <motion.div\r\n                            className=\"h-3 w-3 border-2 border-white border-t-transparent rounded-full\"\r\n                            animate={{ rotate: 360 }}\r\n                            transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\r\n                          />\r\n                        ) : (\r\n                          <>\r\n                            <Zap className=\"h-3 w-3 mr-1\" />\r\n                            Optimize\r\n                          </>\r\n                        )}\r\n                      </Button>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"workflow\" className=\"space-y-6 mt-0\">\r\n                {/* Installer Assignment */}\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium\">\r\n                    Assign Installer *\r\n                  </Label>\r\n                  <Select\r\n                    value={formData.installerId}\r\n                    onValueChange={(value) => setFormData(prev => ({ ...prev, installerId: value }))}\r\n                  >\r\n                    <SelectTrigger className={errors.installerId ? 'border-red-500' : ''}>\r\n                      <SelectValue placeholder=\"Select an installer...\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {installers.map((installer) => (\r\n                        <SelectItem key={installer.id} value={installer.id}>\r\n                          <div className=\"flex items-center space-x-3\">\r\n                            <Avatar className=\"h-6 w-6\">\r\n                              <AvatarImage src={installer.avatar || undefined} />\r\n                              <AvatarFallback className=\"text-xs\">\r\n                                {installer.name.split(' ').map(n => n[0]).join('')}\r\n                              </AvatarFallback>\r\n                            </Avatar>\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <span>{installer.name}</span>\r\n                              <div className={`w-2 h-2 rounded-full ${getAvailabilityColor(installer.availability)}`} />\r\n                              <span className=\"text-xs text-muted-foreground\">\r\n                                {installer.utilization}%\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                  {errors.installerId && <p className=\"text-sm text-red-500\">{errors.installerId}</p>}\r\n\r\n                  {selectedInstaller && (\r\n                    <Card className=\"mt-3\">\r\n                      <CardContent className=\"pt-4\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"flex items-center space-x-3\">\r\n                            <Avatar className=\"h-8 w-8\">\r\n                              <AvatarImage src={selectedInstaller.avatar || undefined} />\r\n                              <AvatarFallback>\r\n                                {selectedInstaller.name.split(' ').map(n => n[0]).join('')}\r\n                              </AvatarFallback>\r\n                            </Avatar>\r\n                            <div>\r\n                              <p className=\"font-medium\">{selectedInstaller.name}</p>\r\n                              <p className=\"text-xs text-muted-foreground capitalize\">\r\n                                {selectedInstaller.availability} • {selectedInstaller.utilization}% utilized\r\n                              </p>\r\n                            </div>\r\n                          </div>\r\n                          <div className={`w-3 h-3 rounded-full ${getAvailabilityColor(selectedInstaller.availability)}`} />\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Customer Information */}\r\n                <div className=\"space-y-2\">\r\n                  <Label className=\"text-sm font-medium\">Customer</Label>\r\n                  <Select\r\n                    value={formData.customerId}\r\n                    onValueChange={(value) => setFormData(prev => ({ ...prev, customerId: value }))}\r\n                  >\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Select customer...\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {customers.map((customer) => (\r\n                        <SelectItem key={customer.id} value={customer.id}>\r\n                          <div className=\"flex flex-col\">\r\n                            <span className=\"font-medium\">{customer.name}</span>\r\n                            <span className=\"text-xs text-muted-foreground\">\r\n                              {customer.email} • {customer.phone}\r\n                            </span>\r\n                          </div>\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"notifications\" className=\"space-y-6 mt-0\">\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <Label className=\"text-sm font-medium\">Send Initial Notification</Label>\r\n                      <p className=\"text-xs text-muted-foreground\">\r\n                        Notify customer and installer about this appointment\r\n                      </p>\r\n                    </div>\r\n                    <Switch\r\n                      checked={formData.sendNotification}\r\n                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, sendNotification: checked }))}\r\n                    />\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <Label className=\"text-sm font-medium\">Auto Reminder</Label>\r\n                      <p className=\"text-xs text-muted-foreground\">\r\n                        Send automatic reminder 24 hours before appointment\r\n                      </p>\r\n                    </div>\r\n                    <Switch\r\n                      checked={formData.autoReminder}\r\n                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, autoReminder: checked }))}\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <Label className=\"text-sm font-medium\">SMS Notifications</Label>\r\n                      <p className=\"text-xs text-muted-foreground\">\r\n                        Send SMS updates to customer and installer\r\n                      </p>\r\n                    </div>\r\n                    <Switch\r\n                      checked={formData.smsReminder}\r\n                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, smsReminder: checked }))}\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <Label className=\"text-sm font-medium\">Email Notifications</Label>\r\n                      <p className=\"text-xs text-muted-foreground\">\r\n                        Send email confirmations and updates\r\n                      </p>\r\n                    </div>\r\n                    <Switch\r\n                      checked={formData.emailReminder}\r\n                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, emailReminder: checked }))}\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"attachments\" className=\"space-y-6 mt-0\">\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"space-y-2\">\r\n                    <Label className=\"text-sm font-medium\">\r\n                      Appointment Notes\r\n                    </Label>\r\n                    <Textarea\r\n                      placeholder=\"Add any special instructions, notes, or requirements for this appointment...\"\r\n                      value={formData.notes}\r\n                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}\r\n                      rows={4}\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"space-y-2\">\r\n                    <Label className=\"text-sm font-medium\">File Attachments</Label>\r\n                    <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-amber-400 transition-colors\">\r\n                      <FileText className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n                      <p className=\"mt-2 text-sm text-gray-600\">\r\n                        Drag and drop files here, or click to browse\r\n                      </p>\r\n                      <p className=\"text-xs text-gray-500 mt-1\">\r\n                        Support for images, PDFs, and documents up to 10MB\r\n                      </p>\r\n                      <Button variant=\"outline\" className=\"mt-4\" size=\"sm\">\r\n                        Choose Files\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n            </div>\r\n          </Tabs>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-gray-50\">\r\n          <div className=\"text-sm text-muted-foreground\">\r\n            * Required fields\r\n          </div>\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Button variant=\"outline\" onClick={onClose}>\r\n              Cancel\r\n            </Button>\r\n            <Button \r\n              onClick={handleSave} \r\n              className=\"bg-amber-500 hover:bg-amber-600 text-white\"\r\n            >\r\n              <Save className=\"h-4 w-4 mr-2\" />\r\n              Schedule Appointment\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAhCA;;;;;;;;;;;;;;;;AA8FO,SAAS,oBAAoB,EAClC,MAAM,EACN,OAAO,EACP,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,WAAW,EAAE,EACb,aAAa,EAAE,EACf,YAAY,EAAE,EACW;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QACjE,WAAW;QACX,YAAY;QACZ,MAAM,cAAc,cAAc,MAAM,IAAI,CAAC,EAAE,IAAI;QACnD,WAAW,gBAAgB;QAC3B,SAAS;QACT,UAAU;QACV,aAAa;QACb,UAAU;QACV,cAAc,EAAE;QAChB,OAAO;QACP,mBAAmB;QACnB,kBAAkB;QAClB,cAAc;QACd,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,SAAS,EAAE,UAAU,SAAS,GAAG;QAC/C,IAAI,CAAC,SAAS,IAAI,EAAE,UAAU,IAAI,GAAG;QACrC,IAAI,CAAC,SAAS,SAAS,EAAE,UAAU,SAAS,GAAG;QAC/C,IAAI,CAAC,SAAS,OAAO,EAAE,UAAU,OAAO,GAAG;QAC3C,IAAI,CAAC,SAAS,WAAW,EAAE,UAAU,WAAW,GAAG;QAEnD,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB,aAAa;YACb;QACF;QAEA,MAAM,cAA+B;YACnC,GAAG,QAAQ;YACX,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;QAC1B;QAEA,OAAO;QACP;IACF;IAEA,MAAM,qBAAqB;QACzB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,2BAA2B;QAC3B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW;gBACX,SAAS;gBACT,aAAa,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,cAAc,MAAM,KAAK,WAAW;YAC3F,CAAC;QAED,gBAAgB;IAClB;IAEA,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,SAAS;IACtE,MAAM,oBAAoB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,WAAW;IAE5E,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB;QACzB;QAAW;QAAU;QAAY;QAAY;QAC7C;QAAW;QAAoB;QAAU;KAC1C;IAED,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAmC;;;;;;;;;;;8BAK5D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;wBAAc,WAAU;;0CAC7D,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;;0DACrC,8OAAC,0MAAA,CAAA,WAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;;0DACtC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAgB,WAAU;;0DAC3C,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAc,WAAU;;0DACzC,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAIV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;;0DAErC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEAGvC,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,SAAS,SAAS;wDACzB,eAAe,CAAC;4DACd,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;4DAC5C,YAAY,CAAA,OAAQ,CAAC;oEACnB,GAAG,IAAI;oEACP,WAAW;oEACX,UAAU,SAAS,WAAW;gEAChC,CAAC;wDACH;;0EAEA,8OAAC,kIAAA,CAAA,gBAAa;gEAAC,WAAW,OAAO,SAAS,GAAG,mBAAmB;0EAC9D,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;0EACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,kIAAA,CAAA,aAAU;wEAAkB,OAAO,QAAQ,EAAE;kFAC5C,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAe,QAAQ,IAAI;;;;;;8FAC3C,8OAAC;oFAAK,WAAU;;wFACb,QAAQ,QAAQ;wFAAC;wFAAI,QAAQ,OAAO;;;;;;;;;;;;;uEAJ1B,QAAQ,EAAE;;;;;;;;;;;;;;;;oDAWhC,OAAO,SAAS,kBAAI,8OAAC;wDAAE,WAAU;kEAAwB,OAAO,SAAS;;;;;;oDAEzE,iCACC,8OAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;kEACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;sEACrB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,8OAAC;gFAAK,WAAU;0FAAW,gBAAgB,QAAQ;;;;;;;;;;;;kFAErD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;gFAAK,WAAU;0FAAW,gBAAgB,OAAO;;;;;;;;;;;;kFAEpD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,8OAAC;gFAAI,WAAU;0FACZ,gBAAgB,YAAY,CAAC,GAAG,CAAC,CAAC,qBACjC,8OAAC,iIAAA,CAAA,QAAK;wFAAY,SAAQ;wFAAY,WAAU;kGAC7C;uFADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAa5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAsB;;;;;;0EAGvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEACvE,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;4DAE7C,OAAO,IAAI,kBAAI,8OAAC;gEAAE,WAAU;0EAAwB,OAAO,IAAI;;;;;;;;;;;;kEAElE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAsB;;;;;;0EAGvC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO,SAAS,SAAS;wEACzB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEAC5E,WAAW,OAAO,SAAS,GAAG,mBAAmB;;;;;;kFAEnD,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO,SAAS,OAAO;wEACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEAC1E,WAAW,OAAO,OAAO,GAAG,mBAAmB;;;;;;;;;;;;4DAGlD,CAAC,OAAO,SAAS,IAAI,OAAO,OAAO,mBAClC,8OAAC;gEAAE,WAAU;0EAAuB;;;;;;;;;;;;;;;;;;0DAM1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;;4DAAsB;4DAChB,SAAS,iBAAiB;4DAAC;;;;;;;kEAElD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAClC,GAAG,IAAI;4EACP,mBAAmB,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,iBAAiB,IAAI,GAAG,IAAI;wEACpE,CAAC;0EACF;;;;;;0EAGD,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,OAAO,SAAS,iBAAiB;gEACjC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EACpC,GAAG,IAAI;4EACP,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wEACjD,CAAC;gEACD,WAAU;;;;;;0EAEZ,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAClC,GAAG,IAAI;4EACP,mBAAmB,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,iBAAiB,IAAI,GAAG,IAAI;wEACrE,CAAC;0EACF;;;;;;;;;;;;kEAIH,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAKV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEACvC,8OAAC;wDAAI,WAAU;kEACZ,mBAAmB,GAAG,CAAC,CAAC,qBACvB,8OAAC,kIAAA,CAAA,SAAM;gEAEL,SAAS,SAAS,YAAY,EAAE,SAAS,QAAQ,YAAY;gEAC7D,MAAK;gEACL,SAAS;oEACP,MAAM,eAAe,SAAS,YAAY,IAAI,EAAE;oEAChD,MAAM,WAAW,aAAa,QAAQ,CAAC,QACnC,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM,QAC/B;2EAAI;wEAAc;qEAAK;oEAC3B,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,cAAc;wEAAS,CAAC;gEAC1D;0EAEC;+DAXI;;;;;;;;;;;;;;;;0DAkBb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;;;;;;;;;;;;0DAK/E,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAqC;;;;;;0FAClD,8OAAC;gFAAE,WAAU;0FAAyB;;;;;;;;;;;;;;;;;;0EAK1C,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS;gEACT,UAAU;gEACV,WAAU;0EAET,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEAAE,QAAQ;oEAAI;oEACvB,YAAY;wEAAE,UAAU;wEAAG,QAAQ;wEAAU,MAAM;oEAAS;;;;;yFAG9D;;sFACE,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAU9C,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;;0DAEtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEAGvC,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,SAAS,WAAW;wDAC3B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,aAAa;gEAAM,CAAC;;0EAE9E,8OAAC,kIAAA,CAAA,gBAAa;gEAAC,WAAW,OAAO,WAAW,GAAG,mBAAmB;0EAChE,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;0EACX,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC,kIAAA,CAAA,aAAU;wEAAoB,OAAO,UAAU,EAAE;kFAChD,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kIAAA,CAAA,SAAM;oFAAC,WAAU;;sGAChB,8OAAC,kIAAA,CAAA,cAAW;4FAAC,KAAK,UAAU,MAAM,IAAI;;;;;;sGACtC,8OAAC,kIAAA,CAAA,iBAAc;4FAAC,WAAU;sGACvB,UAAU,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;8FAGnD,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;sGAAM,UAAU,IAAI;;;;;;sGACrB,8OAAC;4FAAI,WAAW,CAAC,qBAAqB,EAAE,qBAAqB,UAAU,YAAY,GAAG;;;;;;sGACtF,8OAAC;4FAAK,WAAU;;gGACb,UAAU,WAAW;gGAAC;;;;;;;;;;;;;;;;;;;uEAZd,UAAU,EAAE;;;;;;;;;;;;;;;;oDAoBlC,OAAO,WAAW,kBAAI,8OAAC;wDAAE,WAAU;kEAAwB,OAAO,WAAW;;;;;;oDAE7E,mCACC,8OAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;kEACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;sEACrB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kIAAA,CAAA,SAAM;gFAAC,WAAU;;kGAChB,8OAAC,kIAAA,CAAA,cAAW;wFAAC,KAAK,kBAAkB,MAAM,IAAI;;;;;;kGAC9C,8OAAC,kIAAA,CAAA,iBAAc;kGACZ,kBAAkB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;0FAG3D,8OAAC;;kGACC,8OAAC;wFAAE,WAAU;kGAAe,kBAAkB,IAAI;;;;;;kGAClD,8OAAC;wFAAE,WAAU;;4FACV,kBAAkB,YAAY;4FAAC;4FAAI,kBAAkB,WAAW;4FAAC;;;;;;;;;;;;;;;;;;;kFAIxE,8OAAC;wEAAI,WAAW,CAAC,qBAAqB,EAAE,qBAAqB,kBAAkB,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQxG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,SAAS,UAAU;wDAC1B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,YAAY;gEAAM,CAAC;;0EAE7E,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;0EACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;wEAAmB,OAAO,SAAS,EAAE;kFAC9C,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAe,SAAS,IAAI;;;;;;8FAC5C,8OAAC;oFAAK,WAAU;;wFACb,SAAS,KAAK;wFAAC;wFAAI,SAAS,KAAK;;;;;;;;;;;;;uEAJvB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDActC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAgB,WAAU;kDAC3C,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;8EACvC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAI/C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,SAAS,gBAAgB;4DAClC,iBAAiB,CAAC,UAAY,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,kBAAkB;oEAAQ,CAAC;;;;;;;;;;;;8DAI7F,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;8EACvC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAI/C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,SAAS,YAAY;4DAC9B,iBAAiB,CAAC,UAAY,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,cAAc;oEAAQ,CAAC;;;;;;;;;;;;8DAIzF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;8EACvC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAI/C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,SAAS,WAAW;4DAC7B,iBAAiB,CAAC,UAAY,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,aAAa;oEAAQ,CAAC;;;;;;;;;;;;8DAIxF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;8EACvC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;sEAI/C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,SAAS,aAAa;4DAC/B,iBAAiB,CAAC,UAAY,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,eAAe;oEAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;kDAM9F,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAc,WAAU;kDACzC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsB;;;;;;sEAGvC,8OAAC,oIAAA,CAAA,WAAQ;4DACP,aAAY;4DACZ,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACxE,MAAM;;;;;;;;;;;;8DAIV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsB;;;;;;sEACvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,8OAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,8OAAC,kIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,WAAU;oEAAO,MAAK;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAYnE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgC;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAS;;;;;;8CAG5C,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C", "debugId": null}}, {"offset": {"line": 4585, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/schedule/schedule-page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport { \r\n  Calendar as CalendarIcon, \r\n  Clock,\r\n  Search,\r\n  Plus,\r\n  Download,\r\n  Filter,\r\n  MapPin,\r\n  BarChart3,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  Timer\r\n} from 'lucide-react'\r\n\r\nimport { <PERSON><PERSON> } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\r\nimport { CalendarMonthView } from '@/components/schedule/calendar-month-view'\r\nimport { CalendarDayView } from '@/components/schedule/calendar-day-view'\r\nimport { CalendarWeekView } from '@/components/schedule/calendar-week-view'\r\nimport { NewAppointmentModal } from '@/components/schedule/new-appointment-modal'\r\nimport { useTheme } from '@/contexts/theme-context'\r\n\r\n// Types\r\ninterface AppointmentData {\r\n  id: string\r\n  projectId: string\r\n  customerId: string\r\n  date: string\r\n  startTime: string\r\n  endTime: string\r\n  duration: number\r\n  installerId: string\r\n  location: string\r\n  productTypes: string[]\r\n  notes: string\r\n  estimatedDuration: number\r\n  sendNotification: boolean\r\n  autoReminder: boolean\r\n  smsReminder: boolean\r\n  emailReminder: boolean\r\n}\r\n\r\n// Mock data\r\nconst mockProjects = [\r\n  {\r\n    id: 'project-1',\r\n    name: 'Kitchen Window Installation',\r\n    customer: 'Sarah Johnson',\r\n    address: '123 Oak Street, Portland, OR',\r\n    productTypes: ['Windows', 'Blinds'],\r\n    status: 'active'\r\n  },\r\n  {\r\n    id: 'project-2',\r\n    name: 'Bathroom Renovation',\r\n    customer: 'Mike Chen',\r\n    address: '456 Pine Avenue, Seattle, WA',\r\n    productTypes: ['Shutters', 'Blinds'],\r\n    status: 'active'\r\n  },\r\n  {\r\n    id: 'project-3',\r\n    name: 'Living Room Makeover',\r\n    customer: 'Emily Davis',\r\n    address: '789 Maple Drive, Vancouver, WA',\r\n    productTypes: ['Curtains', 'Shades'],\r\n    status: 'active'\r\n  }\r\n]\r\n\r\nconst mockInstallers = [\r\n  {\r\n    id: 'installer-1',\r\n    name: 'Alex Thompson',\r\n    avatar: null,\r\n    availability: 'available' as const,\r\n    utilization: 65\r\n  },\r\n  {\r\n    id: 'installer-2',\r\n    name: 'Jordan Martinez',\r\n    avatar: null,\r\n    availability: 'busy' as const,\r\n    utilization: 85\r\n  },\r\n  {\r\n    id: 'installer-3',\r\n    name: 'Sam Wilson',\r\n    avatar: null,\r\n    availability: 'available' as const,\r\n    utilization: 45\r\n  }\r\n]\r\n\r\nconst mockCustomers = [\r\n  {\r\n    id: 'customer-1',\r\n    name: 'Sarah Johnson',\r\n    email: '<EMAIL>',\r\n    phone: '(*************',\r\n    address: '123 Oak Street, Portland, OR'\r\n  },\r\n  {\r\n    id: 'customer-2',\r\n    name: 'Mike Chen',\r\n    email: '<EMAIL>',\r\n    phone: '(*************',\r\n    address: '456 Pine Avenue, Seattle, WA'\r\n  },\r\n  {\r\n    id: 'customer-3',\r\n    name: 'Emily Davis',\r\n    email: '<EMAIL>',\r\n    phone: '(*************',\r\n    address: '789 Maple Drive, Vancouver, WA'\r\n  }\r\n]\r\n\r\n// Today's date for demo\r\nconst today = new Date('2025-07-12')\r\n\r\nconst mockAppointments = [\r\n  {\r\n    id: '1',\r\n    title: 'Kitchen Window Installation',\r\n    date: '2025-07-12',\r\n    time: '09:00',\r\n    status: 'assigned' as const,\r\n    customer: 'Sarah Johnson',\r\n    installer: 'Alex Thompson'\r\n  },\r\n  {\r\n    id: '2',\r\n    title: 'Bathroom Consultation',\r\n    date: '2025-07-12',\r\n    time: '14:00',\r\n    status: 'pending' as const,\r\n    customer: 'Mike Chen',\r\n    installer: 'Jordan Martinez'\r\n  },\r\n  {\r\n    id: '3',\r\n    title: 'Living Room Curtains',\r\n    date: '2025-07-13',\r\n    time: '10:00',\r\n    status: 'in-progress' as const,\r\n    customer: 'Emily Davis',\r\n    installer: 'Sam Wilson'\r\n  },\r\n  {\r\n    id: '4',\r\n    title: 'Window Maintenance',\r\n    date: '2025-07-14',\r\n    time: '15:00',\r\n    status: 'completed' as const,\r\n    customer: 'Sarah Johnson',\r\n    installer: 'Alex Thompson'\r\n  }\r\n]\r\n\r\nconst mockDayViewEvents = [\r\n  {\r\n    id: '1',\r\n    title: 'Kitchen Window Installation',\r\n    startTime: '09:00',\r\n    duration: 180,\r\n    customer: 'Sarah Johnson',\r\n    address: '123 Oak Street',\r\n    installer: 'Alex Thompson',\r\n    status: 'assigned' as const\r\n  },\r\n  {\r\n    id: '2',\r\n    title: 'Bathroom Consultation',\r\n    startTime: '14:00',\r\n    duration: 120,\r\n    customer: 'Mike Chen',\r\n    address: '456 Pine Avenue',\r\n    installer: 'Jordan Martinez',\r\n    status: 'pending' as const\r\n  }\r\n]\r\n\r\nexport default function SchedulePage() {\r\n  const { accentColor } = useTheme()\r\n  const [currentView, setCurrentView] = useState<'month' | 'week' | 'day'>('month')\r\n  const [selectedDate, setSelectedDate] = useState(today)\r\n  const [showNewAppointment, setShowNewAppointment] = useState(false)\r\n  const [searchQuery, setSearchQuery] = useState('')\r\n\r\n  const handleDateSelect = (date: Date) => {\r\n    setSelectedDate(date)\r\n    setCurrentView('day')\r\n  }\r\n\r\n  const handleNewAppointment = (date?: Date) => {\r\n    if (date) setSelectedDate(date)\r\n    setShowNewAppointment(true)\r\n  }\r\n\r\n  const handleSaveAppointment = (appointment: AppointmentData) => {\r\n    console.log('Saving appointment:', appointment)\r\n    // Here you would typically save to your backend\r\n    setShowNewAppointment(false)\r\n  }\r\n\r\n  const handleNavigate = (direction: 'prev' | 'next') => {\r\n    const newDate = new Date(selectedDate)\r\n    if (currentView === 'month') {\r\n      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1))\r\n    } else if (currentView === 'week') {\r\n      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7))\r\n    } else {\r\n      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1))\r\n    }\r\n    setSelectedDate(newDate)\r\n  }\r\n\r\n  const todaysAppointments = mockAppointments.filter(apt => apt.date === today.toISOString().split('T')[0])\r\n  const upcomingAppointments = mockAppointments.filter(apt => \r\n    new Date(apt.date) > today\r\n  ).slice(0, 5)\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-slate-700\">Schedule</h1>\r\n          <p className=\"text-slate-600 mt-1\">\r\n            Manage installer appointments and project timelines\r\n          </p>\r\n          <p className=\"text-sm font-medium mt-1\" style={{ color: accentColor }}>\r\n            {today.toLocaleDateString('en-US', { \r\n              weekday: 'long', \r\n              month: 'long', \r\n              day: 'numeric', \r\n              year: 'numeric' \r\n            })}\r\n          </p>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-3\">\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <Filter className=\"h-4 w-4 mr-2\" />\r\n            Filter\r\n          </Button>\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <Download className=\"h-4 w-4 mr-2\" />\r\n            Export\r\n          </Button>\r\n          <Button \r\n            onClick={() => handleNewAppointment()}\r\n            className=\"text-white\"\r\n            style={{ \r\n              backgroundColor: accentColor,\r\n              borderColor: accentColor\r\n            }}\r\n          >\r\n            <Plus className=\"h-4 w-4 mr-2\" />\r\n            New Appointment\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Row */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n        <Card className=\"bg-white border-gray-200 shadow-sm\">\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium text-slate-600\">Upcoming Appointments</CardTitle>\r\n            <CalendarIcon className=\"h-4 w-4\" style={{ color: accentColor }} />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold text-slate-700\">15</div>\r\n            <p className=\"text-xs text-green-600 flex items-center mt-1\">\r\n              +10% from last week\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n        \r\n        <Card className=\"bg-white border-gray-200 shadow-sm\">\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium text-slate-600\">Installer Utilization</CardTitle>\r\n            <BarChart3 className=\"h-4 w-4\" style={{ color: accentColor }} />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold text-slate-700\">75%</div>\r\n            <p className=\"text-xs text-green-600 flex items-center mt-1\">\r\n              +5% from last month\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n        \r\n        <Card className=\"bg-white border-gray-200 shadow-sm\">\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium text-slate-600\">Conflicts</CardTitle>\r\n            <AlertTriangle className=\"h-4 w-4 text-red-500\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold text-slate-700\">2</div>\r\n            <p className=\"text-xs text-red-600 flex items-center mt-1\">\r\n              Requires attention\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n        \r\n        <Card className=\"bg-white border-gray-200 shadow-sm\">\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium text-slate-600\">Avg Job Duration</CardTitle>\r\n            <Timer className=\"h-4 w-4\" style={{ color: accentColor }} />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold text-slate-700\">4h</div>\r\n            <p className=\"text-xs text-slate-500 flex items-center mt-1\">\r\n              Per appointment\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-6\">\r\n        {/* Calendar Area */}\r\n        <div className=\"xl:col-span-3 space-y-6\">\r\n          {/* View Controls */}\r\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\r\n            <CardContent className=\"p-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <Button\r\n                    variant={currentView === 'month' ? 'default' : 'outline'}\r\n                    size=\"sm\"\r\n                    onClick={() => setCurrentView('month')}\r\n                    className={currentView === 'month' ? 'text-white' : ''}\r\n                    style={currentView === 'month' ? { \r\n                      backgroundColor: accentColor,\r\n                      borderColor: accentColor\r\n                    } : {}}\r\n                  >\r\n                    <CalendarIcon className=\"h-4 w-4 mr-2\" />\r\n                    Month\r\n                  </Button>\r\n                  <Button\r\n                    variant={currentView === 'week' ? 'default' : 'outline'}\r\n                    size=\"sm\"\r\n                    onClick={() => setCurrentView('week')}\r\n                    className={currentView === 'week' ? 'text-white' : ''}\r\n                    style={currentView === 'week' ? { \r\n                      backgroundColor: accentColor,\r\n                      borderColor: accentColor\r\n                    } : {}}\r\n                  >\r\n                    <CalendarIcon className=\"h-4 w-4 mr-2\" />\r\n                    Week\r\n                  </Button>\r\n                  <Button\r\n                    variant={currentView === 'day' ? 'default' : 'outline'}\r\n                    size=\"sm\"\r\n                    onClick={() => setCurrentView('day')}\r\n                    className={currentView === 'day' ? 'text-white' : ''}\r\n                    style={currentView === 'day' ? { \r\n                      backgroundColor: accentColor,\r\n                      borderColor: accentColor\r\n                    } : {}}\r\n                  >\r\n                    <Clock className=\"h-4 w-4 mr-2\" />\r\n                    Day\r\n                  </Button>\r\n                </div>\r\n\r\n                <div className=\"text-center\">\r\n                  <h2 className=\"font-semibold text-slate-700\">\r\n                    {currentView === 'month' \r\n                      ? selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })\r\n                      : currentView === 'week'\r\n                      ? (() => {\r\n                          const weekStart = new Date(selectedDate)\r\n                          const day = weekStart.getDay()\r\n                          const diff = weekStart.getDate() - day\r\n                          weekStart.setDate(diff)\r\n                          const weekEnd = new Date(weekStart)\r\n                          weekEnd.setDate(weekStart.getDate() + 6)\r\n                          return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`\r\n                        })()\r\n                      : selectedDate.toLocaleDateString('en-US', { \r\n                          weekday: 'long', \r\n                          month: 'long', \r\n                          day: 'numeric',\r\n                          year: 'numeric'\r\n                        })\r\n                    }\r\n                  </h2>\r\n                </div>\r\n\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => setSelectedDate(today)}\r\n                >\r\n                  Today\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Calendar Views */}\r\n          <motion.div\r\n            key={currentView}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n          >\r\n            {currentView === 'month' ? (\r\n              <CalendarMonthView\r\n                selectedDate={selectedDate}\r\n                events={mockAppointments}\r\n                onDateSelect={handleDateSelect}\r\n                onEventClick={(event) => console.log('Event clicked:', event)}\r\n                onNavigate={handleNavigate}\r\n                onNewAppointment={handleNewAppointment}\r\n              />\r\n            ) : currentView === 'week' ? (\r\n              <CalendarWeekView\r\n                date={selectedDate}\r\n                events={mockDayViewEvents}\r\n                onEventClick={(event) => console.log('Event clicked:', event)}\r\n                onTimeSlotClick={(date) => handleNewAppointment(date)}\r\n                onNavigate={handleNavigate}\r\n                onNewAppointment={handleNewAppointment}\r\n              />\r\n            ) : (\r\n              <CalendarDayView\r\n                date={selectedDate}\r\n                events={selectedDate.toDateString() === today.toDateString() ? mockDayViewEvents : []}\r\n                onEventClick={(event) => console.log('Event clicked:', event)}\r\n                onTimeSlotClick={() => handleNewAppointment(selectedDate)}\r\n              />\r\n            )}\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* Sidebar */}\r\n        <div className=\"space-y-6\">\r\n          {/* Quick Search */}\r\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\r\n            <CardHeader className=\"pb-3\">\r\n              <CardTitle className=\"text-lg text-slate-700\">Quick Search</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-3\">\r\n              <div className=\"relative\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\r\n                <Input\r\n                  placeholder=\"Search appointments...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-10 border-gray-200\"\r\n                />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Today's Appointments */}\r\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\r\n            <CardHeader className=\"pb-3\">\r\n              <CardTitle className=\"text-lg text-slate-700\">Today&apos;s Schedule</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-3\">\r\n              {todaysAppointments.length > 0 ? (\r\n                todaysAppointments.map((appointment) => (\r\n                  <div key={appointment.id} className=\"p-3 border border-gray-200 rounded-lg hover:shadow-md transition-shadow\">\r\n                    <div className=\"flex items-start justify-between mb-2\">\r\n                      <h4 className=\"font-medium text-sm text-slate-700\">{appointment.title}</h4>\r\n                      <Badge \r\n                        variant={appointment.status === 'completed' ? 'default' : 'secondary'}\r\n                        className={\r\n                          appointment.status === 'assigned' ? 'bg-blue-100 text-blue-700' :\r\n                          appointment.status === 'pending' ? 'bg-opacity-20 text-slate-700' :\r\n                          appointment.status === 'in-progress' ? 'bg-orange-100 text-orange-700' :\r\n                          appointment.status === 'completed' ? 'bg-green-100 text-green-700' :\r\n                          'bg-gray-100 text-gray-700'\r\n                        }\r\n                        style={appointment.status === 'pending' ? { \r\n                          backgroundColor: `${accentColor}33`,\r\n                          color: accentColor\r\n                        } : {}}\r\n                      >\r\n                        {appointment.status}\r\n                      </Badge>\r\n                    </div>\r\n                    <div className=\"space-y-1 text-xs text-slate-500\">\r\n                      <div className=\"flex items-center\">\r\n                        <Clock className=\"h-3 w-3 mr-1\" />\r\n                        {appointment.time}\r\n                      </div>\r\n                      <div className=\"flex items-center\">\r\n                        <MapPin className=\"h-3 w-3 mr-1\" />\r\n                        {appointment.customer}\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center mt-2\">\r\n                      <Avatar className=\"h-5 w-5 mr-2\">\r\n                        <AvatarFallback \r\n                          className=\"text-xs bg-opacity-20 text-slate-700\"\r\n                          style={{ \r\n                            backgroundColor: `${accentColor}33`,\r\n                            color: accentColor\r\n                          }}\r\n                        >\r\n                          {appointment.installer.split(' ').map(n => n[0]).join('')}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <span className=\"text-xs text-slate-600\">{appointment.installer}</span>\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              ) : (\r\n                <p className=\"text-sm text-slate-500\">No appointments today</p>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Upcoming Appointments */}\r\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\r\n            <CardHeader className=\"pb-3\">\r\n              <CardTitle className=\"text-lg text-slate-700\">Upcoming</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-3\">\r\n              {upcomingAppointments.map((appointment) => (\r\n                <div key={appointment.id} className=\"p-3 border border-gray-200 rounded-lg\">\r\n                  <div className=\"flex items-start justify-between mb-2\">\r\n                    <h4 className=\"font-medium text-sm text-slate-700\">{appointment.title}</h4>\r\n                    <Badge variant=\"secondary\" className=\"bg-gray-100 text-gray-700\">\r\n                      {appointment.status}\r\n                    </Badge>\r\n                  </div>\r\n                  <div className=\"space-y-1 text-xs text-slate-500\">\r\n                    <div className=\"flex items-center\">\r\n                      <CalendarIcon className=\"h-3 w-3 mr-1\" />\r\n                      {new Date(appointment.date).toLocaleDateString()}\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                      <Clock className=\"h-3 w-3 mr-1\" />\r\n                      {appointment.time}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n\r\n      {/* New Appointment Modal */}\r\n      <NewAppointmentModal\r\n        isOpen={showNewAppointment}\r\n        onClose={() => setShowNewAppointment(false)}\r\n        onSave={handleSaveAppointment}\r\n        selectedDate={selectedDate}\r\n        projects={mockProjects}\r\n        installers={mockInstallers}\r\n        customers={mockCustomers}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1BA;;;;;;;;;;;;;;;AAgDA,YAAY;AACZ,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,cAAc;YAAC;YAAW;SAAS;QACnC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,cAAc;YAAC;YAAY;SAAS;QACpC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,cAAc;YAAC;YAAY;SAAS;QACpC,QAAQ;IACV;CACD;AAED,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,cAAc;QACd,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,cAAc;QACd,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,cAAc;QACd,aAAa;IACf;CACD;AAED,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;IACX;CACD;AAED,wBAAwB;AACxB,MAAM,QAAQ,IAAI,KAAK;AAEvB,MAAM,mBAAmB;IACvB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW;IACb;CACD;AAED,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,UAAU;QACV,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,UAAU;QACV,UAAU;QACV,SAAS;QACT,WAAW;QACX,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,MAAM,gBAAgB;QAC1B,sBAAsB;IACxB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,QAAQ,GAAG,CAAC,uBAAuB;QACnC,gDAAgD;QAChD,sBAAsB;IACxB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,IAAI,KAAK;QACzB,IAAI,gBAAgB,SAAS;YAC3B,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,KAAK,CAAC,cAAc,SAAS,IAAI,CAAC,CAAC;QACtE,OAAO,IAAI,gBAAgB,QAAQ;YACjC,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK,CAAC,cAAc,SAAS,IAAI,CAAC,CAAC;QACpE,OAAO;YACL,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK,CAAC,cAAc,SAAS,IAAI,CAAC,CAAC;QACpE;QACA,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACxG,MAAM,uBAAuB,iBAAiB,MAAM,CAAC,CAAA,MACnD,IAAI,KAAK,IAAI,IAAI,IAAI,OACrB,KAAK,CAAC,GAAG;IAEX,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;0CAGnC,8OAAC;gCAAE,WAAU;gCAA2B,OAAO;oCAAE,OAAO;gCAAY;0CACjE,MAAM,kBAAkB,CAAC,SAAS;oCACjC,SAAS;oCACT,OAAO;oCACP,KAAK;oCACL,MAAM;gCACR;;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM;gCACf,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,aAAa;gCACf;;kDAEA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqC;;;;;;kDAC1D,8OAAC,0MAAA,CAAA,WAAY;wCAAC,WAAU;wCAAU,OAAO;4CAAE,OAAO;wCAAY;;;;;;;;;;;;0CAEhE,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;kCAMjE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqC;;;;;;kDAC1D,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;wCAAU,OAAO;4CAAE,OAAO;wCAAY;;;;;;;;;;;;0CAE7D,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;kCAMjE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqC;;;;;;kDAC1D,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;0CAE3B,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAA8C;;;;;;;;;;;;;;;;;;kCAM/D,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqC;;;;;;kDAC1D,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAU,OAAO;4CAAE,OAAO;wCAAY;;;;;;;;;;;;0CAEzD,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;0BAQnE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,gBAAgB,UAAU,YAAY;wDAC/C,MAAK;wDACL,SAAS,IAAM,eAAe;wDAC9B,WAAW,gBAAgB,UAAU,eAAe;wDACpD,OAAO,gBAAgB,UAAU;4DAC/B,iBAAiB;4DACjB,aAAa;wDACf,IAAI,CAAC;;0EAEL,8OAAC,0MAAA,CAAA,WAAY;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAG3C,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,gBAAgB,SAAS,YAAY;wDAC9C,MAAK;wDACL,SAAS,IAAM,eAAe;wDAC9B,WAAW,gBAAgB,SAAS,eAAe;wDACnD,OAAO,gBAAgB,SAAS;4DAC9B,iBAAiB;4DACjB,aAAa;wDACf,IAAI,CAAC;;0EAEL,8OAAC,0MAAA,CAAA,WAAY;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAG3C,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,gBAAgB,QAAQ,YAAY;wDAC7C,MAAK;wDACL,SAAS,IAAM,eAAe;wDAC9B,WAAW,gBAAgB,QAAQ,eAAe;wDAClD,OAAO,gBAAgB,QAAQ;4DAC7B,iBAAiB;4DACjB,aAAa;wDACf,IAAI,CAAC;;0EAEL,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAKtC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;8DACX,gBAAgB,UACb,aAAa,kBAAkB,CAAC,SAAS;wDAAE,OAAO;wDAAQ,MAAM;oDAAU,KAC1E,gBAAgB,SAChB,CAAC;wDACC,MAAM,YAAY,IAAI,KAAK;wDAC3B,MAAM,MAAM,UAAU,MAAM;wDAC5B,MAAM,OAAO,UAAU,OAAO,KAAK;wDACnC,UAAU,OAAO,CAAC;wDAClB,MAAM,UAAU,IAAI,KAAK;wDACzB,QAAQ,OAAO,CAAC,UAAU,OAAO,KAAK;wDACtC,OAAO,GAAG,UAAU,kBAAkB,CAAC,SAAS;4DAAE,OAAO;4DAAS,KAAK;wDAAU,GAAG,GAAG,EAAE,QAAQ,kBAAkB,CAAC,SAAS;4DAAE,OAAO;4DAAS,KAAK;4DAAW,MAAM;wDAAU,IAAI;oDACrL,CAAC,MACD,aAAa,kBAAkB,CAAC,SAAS;wDACvC,SAAS;wDACT,OAAO;wDACP,KAAK;wDACL,MAAM;oDACR;;;;;;;;;;;0DAKR,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,gBAAgB;0DAChC;;;;;;;;;;;;;;;;;;;;;;0CAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;0CAE3B,gBAAgB,wBACf,8OAAC,2JAAA,CAAA,oBAAiB;oCAChB,cAAc;oCACd,QAAQ;oCACR,cAAc;oCACd,cAAc,CAAC,QAAU,QAAQ,GAAG,CAAC,kBAAkB;oCACvD,YAAY;oCACZ,kBAAkB;;;;;2CAElB,gBAAgB,uBAClB,8OAAC,0JAAA,CAAA,mBAAgB;oCACf,MAAM;oCACN,QAAQ;oCACR,cAAc,CAAC,QAAU,QAAQ,GAAG,CAAC,kBAAkB;oCACvD,iBAAiB,CAAC,OAAS,qBAAqB;oCAChD,YAAY;oCACZ,kBAAkB;;;;;yDAGpB,8OAAC,yJAAA,CAAA,kBAAe;oCACd,MAAM;oCACN,QAAQ,aAAa,YAAY,OAAO,MAAM,YAAY,KAAK,oBAAoB,EAAE;oCACrF,cAAc,CAAC,QAAU,QAAQ,GAAG,CAAC,kBAAkB;oCACvD,iBAAiB,IAAM,qBAAqB;;;;;;+BA5B3C;;;;;;;;;;;kCAmCT,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAyB;;;;;;;;;;;kDAEhD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAOlB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAyB;;;;;;;;;;;kDAEhD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB,mBAAmB,MAAM,GAAG,IAC3B,mBAAmB,GAAG,CAAC,CAAC,4BACtB,8OAAC;gDAAyB,WAAU;;kEAClC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC,YAAY,KAAK;;;;;;0EACrE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAS,YAAY,MAAM,KAAK,cAAc,YAAY;gEAC1D,WACE,YAAY,MAAM,KAAK,aAAa,8BACpC,YAAY,MAAM,KAAK,YAAY,iCACnC,YAAY,MAAM,KAAK,gBAAgB,kCACvC,YAAY,MAAM,KAAK,cAAc,gCACrC;gEAEF,OAAO,YAAY,MAAM,KAAK,YAAY;oEACxC,iBAAiB,GAAG,YAAY,EAAE,CAAC;oEACnC,OAAO;gEACT,IAAI,CAAC;0EAEJ,YAAY,MAAM;;;;;;;;;;;;kEAGvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,YAAY,IAAI;;;;;;;0EAEnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEACjB,YAAY,QAAQ;;;;;;;;;;;;;kEAGzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oEACb,WAAU;oEACV,OAAO;wEACL,iBAAiB,GAAG,YAAY,EAAE,CAAC;wEACnC,OAAO;oEACT;8EAEC,YAAY,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;0EAG1D,8OAAC;gEAAK,WAAU;0EAA0B,YAAY,SAAS;;;;;;;;;;;;;+CA1CzD,YAAY,EAAE;;;;sEA+C1B,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;0CAM5C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAyB;;;;;;;;;;;kDAEhD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB,qBAAqB,GAAG,CAAC,CAAC,4BACzB,8OAAC;gDAAyB,WAAU;;kEAClC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC,YAAY,KAAK;;;;;;0EACrE,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;0EAClC,YAAY,MAAM;;;;;;;;;;;;kEAGvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAY;wEAAC,WAAU;;;;;;oEACvB,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB;;;;;;;0EAEhD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,YAAY,IAAI;;;;;;;;;;;;;;+CAdb,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAyBlC,8OAAC,6JAAA,CAAA,sBAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,QAAQ;gBACR,cAAc;gBACd,UAAU;gBACV,YAAY;gBACZ,WAAW;;;;;;;;;;;;AAInB", "debugId": null}}]}