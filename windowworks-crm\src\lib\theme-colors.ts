import { useTheme } from '@/contexts/theme-context'

// Utility function to convert hex color to RGB values
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

// Generate lighter/darker variants of the accent color
export const getColorVariants = (accentColor: string) => {
  const rgb = hexToRgb(accentColor)
  if (!rgb) return null

  const { r, g, b } = rgb

  // Calculate perceived brightness using the relative luminance formula
  const brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255

  return {
    primary: accentColor,
    light: `rgb(${Math.min(255, r + 40)} ${Math.min(255, g + 40)} ${Math.min(255, b + 40)})`,
    lighter: `rgb(${Math.min(255, r + 80)} ${Math.min(255, g + 80)} ${Math.min(255, b + 80)})`,
    dark: `rgb(${Math.max(0, r - 40)} ${Math.max(0, g - 40)} ${Math.max(0, b - 40)})`,
    darker: `rgb(${Math.max(0, r - 80)} ${Math.max(0, g - 80)} ${Math.max(0, b - 80)})`,
    // Alpha variants for backgrounds
    alpha10: `${accentColor}1A`, // 10% opacity
    alpha20: `${accentColor}33`, // 20% opacity
    alpha50: `${accentColor}80`, // 50% opacity
    // For text contrast
    textColor: brightness > 0.5 ? '#1e293b' : '#ffffff' // Dark text for light colors, light text for dark colors
  }
}

// Hook to get theme-based color classes
export const useThemeColors = () => {
  const { accentColor } = useTheme()
  
  const variants = getColorVariants(accentColor)
  
  if (!variants) {
    // Fallback to amber if color parsing fails
    return {
      primary: '#D97706',
      primaryHover: '#B45309',
      primaryLight: '#FEF3C7',
      primaryText: '#92400E',
      primaryBorder: '#F59E0B',
      primaryRing: '#FBBF24',
      primaryAlpha: '#D9770633'
    }
  }

  return {
    primary: variants.primary,
    primaryHover: variants.dark,
    primaryLight: variants.lighter,
    primaryText: variants.darker,
    primaryBorder: variants.light,
    primaryRing: variants.primary,
    primaryAlpha: variants.alpha20
  }
}

// Tailwind-compatible color classes generator
export const getThemeClasses = () => {
  return {
    // Button classes
    buttonPrimary: `text-white border-[var(--color-brand-primary)]`,
    buttonPrimaryHover: `hover:opacity-90`,
    
    // Text classes
    textPrimary: `text-[var(--color-brand-primary)]`,
    
    // Background classes
    bgPrimary: `bg-[var(--color-brand-primary)]`,
    bgPrimaryLight: `bg-[var(--color-brand-primary)]`,
    bgPrimaryAlpha: `bg-[var(--color-brand-primary)]`,
    
    // Border classes
    borderPrimary: `border-[var(--color-brand-primary)]`,
    
    // Ring classes
    ringPrimary: `ring-[var(--color-brand-primary)]`
  }
}
