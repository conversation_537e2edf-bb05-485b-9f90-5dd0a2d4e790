"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[205],{285:(e,t,a)=>{a.d(t,{$:()=>o});var r=a(5155);a(2115);var i=a(9708),s=a(2085),n=a(9434);let d=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:s,asChild:o=!1,...l}=e,c=o?i.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:a,size:s,className:t})),...l})}},871:(e,t,a)=>{a.d(t,{DashboardLayout:()=>C});var r=a(5155),i=a(6408),s=a(6874),n=a.n(s),d=a(5695),o=a(7340),l=a(7580),c=a(4395),u=a(9074),f=a(381),m=a(4835),x=a(4783),v=a(7924),g=a(3861),h=a(285),b=a(2523),p=a(6126),j=a(1394),y=a(4838),N=a(8294),w=a(5453),k=a(6786);let A=(0,w.v)()((0,k.lt)(e=>({notifications:[],unreadCount:0,addNotification:t=>{let a={...t,id:crypto.randomUUID(),createdAt:new Date().toISOString()};e(e=>({notifications:[a,...e.notifications],unreadCount:e.unreadCount+1}),!1,"addNotification")},markAsRead:t=>e(e=>({notifications:e.notifications.map(e=>e.id===t?{...e,read:!0}:e),unreadCount:Math.max(0,e.unreadCount-1)}),!1,"markAsRead"),markAllAsRead:()=>e(e=>({notifications:e.notifications.map(e=>({...e,read:!0})),unreadCount:0}),!1,"markAllAsRead"),removeNotification:t=>e(e=>{let a=e.notifications.find(e=>e.id===t),r=a&&!a.read;return{notifications:e.notifications.filter(e=>e.id!==t),unreadCount:r?Math.max(0,e.unreadCount-1):e.unreadCount}},!1,"removeNotification"),setNotifications:t=>e({notifications:t,unreadCount:t.filter(e=>!e.read).length},!1,"setNotifications")}),{name:"notification-store"})),z=[{icon:o.A,label:"Dashboard",href:"/",badge:null},{icon:l.A,label:"Customers",href:"/customers",badge:null},{icon:c.A,label:"Projects",href:"/projects",badge:null},{icon:u.A,label:"Schedule",href:"/schedule",badge:null},{icon:f.A,label:"Settings",href:"/settings",badge:null}];function C(e){var t,a,s,o,l,c,u;let{children:w}=e,{user:k,clearAuth:C}=(0,N.n)(),{unreadCount:_}=A(),P=(0,d.usePathname)();return(0,r.jsxs)("div",{className:"h-screen bg-background flex overflow-hidden",children:[(0,r.jsxs)(i.P.aside,{initial:{x:-300},animate:{x:0},className:"w-64 bg-card border-r border-border flex flex-col fixed left-0 top-0 h-full z-30",children:[(0,r.jsx)("div",{className:"p-6 border-b border-border flex-shrink-0",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:.2},className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-[var(--color-brand-primary)] rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"W"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-foreground",children:"WindowWorks"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"CRM Platform"})]})]})}),(0,r.jsx)("nav",{className:"flex-1 p-4 space-y-2 overflow-y-auto",children:z.map((e,t)=>{let a=P===e.href;return(0,r.jsx)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*(t+1)},children:(0,r.jsx)(n(),{href:e.href,children:(0,r.jsxs)(h.$,{variant:"ghost",className:"w-full justify-start rounded-md ".concat(a?"bg-primary/10 text-primary border-primary/20":"text-muted-foreground hover:text-foreground hover:bg-accent"),children:[(0,r.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.label,e.badge&&(0,r.jsx)(p.E,{variant:"secondary",className:"ml-auto",children:e.badge})]})})},e.href)})}),(0,r.jsx)("div",{className:"p-4 border-t border-border flex-shrink-0 sticky bottom-0 bg-card",children:(0,r.jsxs)(y.rI,{children:[(0,r.jsx)(y.ty,{asChild:!0,children:(0,r.jsxs)(h.$,{variant:"ghost",className:"w-full justify-start p-3 hover:bg-accent",children:[(0,r.jsxs)(j.eu,{className:"h-8 w-8 mr-3",children:[(0,r.jsx)(j.BK,{src:null==k||null==(t=k.profile)?void 0:t.avatar}),(0,r.jsxs)(j.q5,{className:"bg-primary/10 text-primary",children:[null==k||null==(s=k.profile)||null==(a=s.firstName)?void 0:a[0],null==k||null==(l=k.profile)||null==(o=l.lastName)?void 0:o[0]]})]}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-foreground",children:[null==k||null==(c=k.profile)?void 0:c.firstName," ",null==k||null==(u=k.profile)?void 0:u.lastName]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground capitalize",children:null==k?void 0:k.role})]})]})}),(0,r.jsxs)(y.SQ,{align:"start",className:"w-56",children:[(0,r.jsxs)(y._2,{children:[(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Account Settings"]}),(0,r.jsxs)(y._2,{onClick:()=>{C()},children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Sign Out"]})]})]})})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col bg-background ml-64",children:[(0,r.jsx)(i.P.header,{initial:{y:-50,opacity:0},animate:{y:0,opacity:1},className:"bg-card border-b border-border px-6 py-4 sticky top-0 z-20 flex-shrink-0",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(h.$,{variant:"ghost",size:"sm",className:"md:hidden",children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{className:"relative w-96 hidden md:block",children:[(0,r.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,r.jsx)(b.p,{placeholder:"Search customers, projects...",className:"pl-10 bg-accent/50 border-border focus:bg-background"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(h.$,{variant:"ghost",size:"sm",className:"relative",children:[(0,r.jsx)(g.A,{className:"h-5 w-5"}),_>0&&(0,r.jsx)(i.P.div,{initial:{scale:0},animate:{scale:1},className:"absolute -top-1 -right-1 h-5 w-5 bg-destructive rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs text-destructive-foreground font-medium",children:_>9?"9+":_})})]})})]})}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto bg-background",children:(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:w})})})]})]})}},1394:(e,t,a)=>{a.d(t,{BK:()=>d,eu:()=>n,q5:()=>o});var r=a(5155);a(2115);var i=a(4011),s=a(9434);function n(e){let{className:t,...a}=e;return(0,r.jsx)(i.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)(i._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)(i.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}},2523:(e,t,a)=>{a.d(t,{p:()=>s});var r=a(5155);a(2115);var i=a(9434);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},4838:(e,t,a)=>{a.d(t,{SQ:()=>o,_2:()=>l,mB:()=>c,rI:()=>n,ty:()=>d});var r=a(5155);a(2115);var i=a(8698),s=a(9434);function n(e){let{...t}=e;return(0,r.jsx)(i.bL,{"data-slot":"dropdown-menu",...t})}function d(e){let{...t}=e;return(0,r.jsx)(i.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:a=4,...n}=e;return(0,r.jsx)(i.ZL,{children:(0,r.jsx)(i.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n})})}function l(e){let{className:t,inset:a,variant:n="default",...d}=e;return(0,r.jsx)(i.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(i.wv,{"data-slot":"dropdown-menu-separator",className:(0,s.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},6126:(e,t,a)=>{a.d(t,{E:()=>o});var r=a(5155);a(2115);var i=a(9708),s=a(2085),n=a(9434);let d=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:s=!1,...o}=e,l=s?i.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:a}),t),...o})}},6695:(e,t,a)=>{a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>s,aR:()=>n});var r=a(5155);a(2115);var i=a(9434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm transition-colors","border-border dark:border-muted-foreground/50 dark:hover:border-muted-foreground",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...a})}},8294:(e,t,a)=>{a.d(t,{n:()=>s});var r=a(5453),i=a(6786);let s=(0,r.v)()((0,i.lt)((0,i.Zr)(e=>({user:null,isLoading:!0,isAuthenticated:!1,setUser:t=>e({user:t,isAuthenticated:!!t,isLoading:!1},!1,"setUser"),setLoading:t=>e({isLoading:t},!1,"setLoading"),clearAuth:()=>e({user:null,isAuthenticated:!1,isLoading:!1},!1,"clearAuth")}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}),{name:"auth-store"}))},9434:(e,t,a)=>{a.d(t,{cn:()=>s});var r=a(2596),i=a(9688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,r.$)(t))}}}]);