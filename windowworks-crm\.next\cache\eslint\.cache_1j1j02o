[{"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\customers\\page.tsx": "1", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\layout.tsx": "2", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\page.tsx": "3", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\page_new.tsx": "4", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\projects\\page.tsx": "5", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\schedule\\page.tsx": "6", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\settings\\page.tsx": "7", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\customers\\customers-page.tsx": "8", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\customers\\new-customer-modal.tsx": "9", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\dashboard\\dashboard-layout.tsx": "10", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\dashboard\\dashboard-overview.tsx": "11", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\projects\\new-project-modal.tsx": "12", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\projects\\projects-page.tsx": "13", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\calendar-day-view.tsx": "14", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\calendar-month-view.tsx": "15", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\calendar-week-view.tsx": "16", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\new-appointment-modal.tsx": "17", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\schedule-page.tsx": "18", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\settings\\settings-page.tsx": "19", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\avatar.tsx": "20", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\badge.tsx": "21", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\button.tsx": "22", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\card.tsx": "23", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\checkbox.tsx": "24", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\dialog.tsx": "25", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\dropdown-menu.tsx": "26", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\form.tsx": "27", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\input.tsx": "28", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\label.tsx": "29", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\navigation-menu.tsx": "30", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\progress.tsx": "31", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\select.tsx": "32", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\separator.tsx": "33", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\sheet.tsx": "34", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\sidebar.tsx": "35", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\skeleton.tsx": "36", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\sonner.tsx": "37", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\switch.tsx": "38", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\table.tsx": "39", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\tabs.tsx": "40", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\textarea.tsx": "41", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\tooltip.tsx": "42", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\contexts\\theme-context.tsx": "43", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\hooks\\use-mobile.ts": "44", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\stores\\auth-store.ts": "45", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\stores\\notification-store.ts": "46", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\supabase\\client.ts": "47", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\theme-colors.ts": "48", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\utils.ts": "49", "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\types\\index.ts": "50"}, {"size": 275, "mtime": 1752355505757, "results": "51", "hashOfConfig": "52"}, {"size": 1469, "mtime": 1752358785517, "results": "53", "hashOfConfig": "52"}, {"size": 1018, "mtime": 1752353532089, "results": "54", "hashOfConfig": "52"}, {"size": 1018, "mtime": 1752353612986, "results": "55", "hashOfConfig": "52"}, {"size": 270, "mtime": 1752356076622, "results": "56", "hashOfConfig": "52"}, {"size": 270, "mtime": 1752356451141, "results": "57", "hashOfConfig": "52"}, {"size": 270, "mtime": 1752357436366, "results": "58", "hashOfConfig": "52"}, {"size": 24982, "mtime": 1752367752876, "results": "59", "hashOfConfig": "52"}, {"size": 29280, "mtime": 1752371433476, "results": "60", "hashOfConfig": "52"}, {"size": 7804, "mtime": 1752372797338, "results": "61", "hashOfConfig": "52"}, {"size": 12141, "mtime": 1752358279085, "results": "62", "hashOfConfig": "52"}, {"size": 50959, "mtime": 1752372849919, "results": "63", "hashOfConfig": "52"}, {"size": 34009, "mtime": 1752372870577, "results": "64", "hashOfConfig": "52"}, {"size": 10337, "mtime": 1752366163722, "results": "65", "hashOfConfig": "52"}, {"size": 8187, "mtime": 1752372949206, "results": "66", "hashOfConfig": "52"}, {"size": 8831, "mtime": 1752366163723, "results": "67", "hashOfConfig": "52"}, {"size": 26480, "mtime": 1752366163723, "results": "68", "hashOfConfig": "52"}, {"size": 21087, "mtime": 1752367079378, "results": "69", "hashOfConfig": "52"}, {"size": 50372, "mtime": 1752357904821, "results": "70", "hashOfConfig": "52"}, {"size": 1097, "mtime": 1752353186756, "results": "71", "hashOfConfig": "52"}, {"size": 1631, "mtime": 1752353186758, "results": "72", "hashOfConfig": "52"}, {"size": 2123, "mtime": 1752353186711, "results": "73", "hashOfConfig": "52"}, {"size": 2099, "mtime": 1752364212726, "results": "74", "hashOfConfig": "52"}, {"size": 1100, "mtime": 1752355505755, "results": "75", "hashOfConfig": "52"}, {"size": 3982, "mtime": 1752353186744, "results": "76", "hashOfConfig": "52"}, {"size": 8284, "mtime": 1752353186751, "results": "77", "hashOfConfig": "52"}, {"size": 3759, "mtime": 1752353186738, "results": "78", "hashOfConfig": "52"}, {"size": 967, "mtime": 1752353186723, "results": "79", "hashOfConfig": "52"}, {"size": 611, "mtime": 1752353186725, "results": "80", "hashOfConfig": "52"}, {"size": 6664, "mtime": 1752353198611, "results": "81", "hashOfConfig": "52"}, {"size": 901, "mtime": 1752360114827, "results": "82", "hashOfConfig": "52"}, {"size": 5789, "mtime": 1752355505755, "results": "83", "hashOfConfig": "52"}, {"size": 699, "mtime": 1752353198599, "results": "84", "hashOfConfig": "52"}, {"size": 4090, "mtime": 1752353198602, "results": "85", "hashOfConfig": "52"}, {"size": 21633, "mtime": 1752353198589, "results": "86", "hashOfConfig": "52"}, {"size": 276, "mtime": 1752353198607, "results": "87", "hashOfConfig": "52"}, {"size": 564, "mtime": 1752353198609, "results": "88", "hashOfConfig": "52"}, {"size": 1506, "mtime": 1752357436366, "results": "89", "hashOfConfig": "52"}, {"size": 2896, "mtime": 1752357436366, "results": "90", "hashOfConfig": "52"}, {"size": 1969, "mtime": 1752353186754, "results": "91", "hashOfConfig": "52"}, {"size": 793, "mtime": 1752372963655, "results": "92", "hashOfConfig": "52"}, {"size": 1891, "mtime": 1752353198603, "results": "93", "hashOfConfig": "52"}, {"size": 3296, "mtime": 1752358785517, "results": "94", "hashOfConfig": "52"}, {"size": 565, "mtime": 1752353198606, "results": "95", "hashOfConfig": "52"}, {"size": 1345, "mtime": 1752353612981, "results": "96", "hashOfConfig": "52"}, {"size": 2707, "mtime": 1752353612983, "results": "97", "hashOfConfig": "52"}, {"size": 220, "mtime": 1752353612981, "results": "98", "hashOfConfig": "52"}, {"size": 2957, "mtime": 1752367079378, "results": "99", "hashOfConfig": "52"}, {"size": 166, "mtime": 1752353162579, "results": "100", "hashOfConfig": "52"}, {"size": 5304, "mtime": 1752353612981, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lgvlfp", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\customers\\page.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\layout.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\page.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\page_new.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\projects\\page.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\schedule\\page.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\settings\\page.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\customers\\customers-page.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\customers\\new-customer-modal.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\dashboard\\dashboard-layout.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\dashboard\\dashboard-overview.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\projects\\new-project-modal.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\projects\\projects-page.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\calendar-day-view.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\calendar-month-view.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\calendar-week-view.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\new-appointment-modal.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\schedule\\schedule-page.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\settings\\settings-page.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\avatar.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\badge.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\button.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\card.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\checkbox.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\dialog.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\dropdown-menu.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\form.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\input.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\label.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\navigation-menu.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\progress.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\select.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\separator.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\sheet.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\sidebar.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\skeleton.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\sonner.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\switch.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\table.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\tabs.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\textarea.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\ui\\tooltip.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\contexts\\theme-context.tsx", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\hooks\\use-mobile.ts", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\stores\\auth-store.ts", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\stores\\notification-store.ts", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\supabase\\client.ts", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\theme-colors.ts", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\lib\\utils.ts", [], [], "D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\types\\index.ts", [], []]