(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{333:(e,s,a)=>{"use strict";a.d(s,{d:()=>n});var r=a(5155),t=a(2115),i=a(9434);let n=t.forwardRef((e,s)=>{let{className:a,checked:t=!1,onCheckedChange:n,disabled:l=!1,id:c,...d}=e;return(0,r.jsx)("button",{ref:s,type:"button",role:"switch","aria-checked":t,disabled:l,onClick:()=>{!l&&n&&n(!t)},id:c,className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-sm border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",t?"bg-primary":"bg-input",a),...d,children:(0,r.jsx)("span",{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded bg-background shadow-lg ring-0 transition-transform",t?"translate-x-5":"translate-x-0")})})});n.displayName="Switch"},1029:(e,s,a)=>{Promise.resolve().then(a.bind(a,871)),Promise.resolve().then(a.bind(a,3008))},2905:(e,s,a)=>{"use strict";a.d(s,{D:()=>c,ThemeProvider:()=>l});var r=a(5155),t=a(2115);let i=(0,t.createContext)(void 0),n=()=>{try{let e=localStorage.getItem("windowworks-theme");if(e&&["light","dark"].includes(e))return e;return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}catch(e){return"light"}};function l(e){let{children:s}=e,[a,l]=(0,t.useState)("light"),[c,d]=(0,t.useState)("#D97706"),[o,m]=(0,t.useState)(!1);(0,t.useEffect)(()=>{m(!0);let e=n();l(e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark");let s=localStorage.getItem("windowworks-accent-color");s&&d(s)},[]);let x=(0,t.useCallback)(e=>{l(e);try{localStorage.setItem("windowworks-theme",e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}catch(e){console.error("Failed to set theme in localStorage",e)}},[]),h=(0,t.useCallback)(e=>{d(e);try{localStorage.setItem("windowworks-accent-color",e);let s=document.documentElement,a="oklch(from ".concat(e," l c h)");s.style.setProperty("--primary",a),s.style.setProperty("--color-brand-primary",e)}catch(e){console.error("Failed to set accent color in localStorage",e)}},[]);return(0,r.jsx)(i.Provider,{value:{theme:a,setTheme:x,accentColor:c,setAccentColor:h,mounted:o},children:s})}function c(){let e=(0,t.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},3008:(e,s,a)=>{"use strict";a.d(s,{default:()=>er});var r=a(5155),t=a(2115),i=a(6408),n=a(760),l=a(4516),c=a(8883),d=a(9074),o=a(4229),m=a(1007),x=a(381),h=a(4869),u=a(5525),p=a(2919),j=a(4355),f=a(9869),g=a(8749),v=a(2657),N=a(2098),b=a(3509),y=a(2318),w=a(3717),A=a(1788),k=a(4186),C=a(4835),P=a(6767),R=a(4738),S=a(2525),T=a(9947),E=a(133),Z=a(285),_=a(2523),J=a(5057),L=a(8539),I=a(6695),D=a(6126),F=a(1394),M=a(333),z=a(7313),B=a(9409),U=a(9434);let W=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:s,className:(0,U.cn)("w-full caption-bottom text-sm",a),...t})})});W.displayName="Table";let $=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("thead",{ref:s,className:(0,U.cn)("[&_tr]:border-b",a),...t})});$.displayName="TableHeader";let q=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("tbody",{ref:s,className:(0,U.cn)("[&_tr:last-child]:border-0",a),...t})});q.displayName="TableBody",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("tfoot",{ref:s,className:(0,U.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...t})}).displayName="TableFooter";let G=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("tr",{ref:s,className:(0,U.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...t})});G.displayName="TableRow";let O=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("th",{ref:s,className:(0,U.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...t})});O.displayName="TableHead";let V=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("td",{ref:s,className:(0,U.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...t})});V.displayName="TableCell",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("caption",{ref:s,className:(0,U.cn)("mt-4 text-sm text-muted-foreground",a),...t})}).displayName="TableCaption";var X=a(4838),Q=a(2905);let K={id:"user_001",name:"John Admin",email:"<EMAIL>",phone:"(*************",bio:"Operations manager with 5+ years in window treatment industry.",avatar:null,role:"Admin"},H={darkMode:!1,emailNotifications:!0,smsAlerts:!1,language:"en",timezone:"America/Chicago",customAccentColor:"#D97706"},Y=[{id:"google-maps",name:"Google Maps",description:"Get directions and location data for customer addresses",status:"connected",icon:l.A,connectionType:"oauth"},{id:"email-provider",name:"Email Provider",description:"Send automated emails and notifications",status:"disconnected",icon:c.A,connectionType:"api-key",apiKey:""},{id:"calendar-sync",name:"Calendar Sync",description:"Sync appointments with external calendars",status:"connected",icon:d.A,connectionType:"toggle"}],ee=[{id:"team_001",name:"John Admin",email:"<EMAIL>",role:"Admin",status:"Active",lastActive:"2025-07-12T14:30:00Z"},{id:"team_002",name:"Sarah Manager",email:"<EMAIL>",role:"Manager",status:"Active",lastActive:"2025-07-12T12:15:00Z"},{id:"team_003",name:"Mike Installer",email:"<EMAIL>",role:"Installer",status:"Active",lastActive:"2025-07-12T09:45:00Z"},{id:"team_004",name:"Lisa Anderson",email:"<EMAIL>",role:"Installer",status:"Inactive",lastActive:"2025-07-10T16:20:00Z"}],es=[{id:"audit_001",action:"Project Created",user:"John Admin",timestamp:"2025-07-12T14:30:00Z",details:'Created project "Living Room Shutters" for Sarah Johnson'},{id:"audit_002",action:"User Invited",user:"John Admin",timestamp:"2025-07-12T13:15:00Z",details:"Invited new installer: <EMAIL>"},{id:"audit_003",action:"Settings Updated",user:"Sarah Manager",timestamp:"2025-07-12T11:20:00Z",details:"Updated notification preferences"}],ea=[{id:"session_001",device:"Chrome on Windows",location:"Chicago, IL",lastActive:"2025-07-12T14:30:00Z",current:!0},{id:"session_002",device:"Safari on iPhone",location:"Chicago, IL",lastActive:"2025-07-12T09:15:00Z",current:!1}];function er(){let{theme:e,setTheme:s,accentColor:a,setAccentColor:l}=(0,Q.D)(),[c,d]=(0,t.useState)("profile"),[U,er]=(0,t.useState)(K),[et,ei]=(0,t.useState)({...H,darkMode:!1,customAccentColor:"#D97706"}),[en,el]=(0,t.useState)(Y),[ec,ed]=(0,t.useState)(!1),[eo,em]=(0,t.useState)(!1),[ex,eh]=(0,t.useState)(!1),[eu,ep]=(0,t.useState)(!1),ej=(0,t.useRef)(null);(0,t.useEffect)(()=>{ei(s=>({...s,darkMode:"dark"===e,customAccentColor:a}))},[e,a]);let ef=(0,t.useId)(),eg=(0,t.useId)(),ev=(0,t.useId)(),eN=(0,t.useId)(),eb=(e,s)=>{er(a=>({...a,[e]:s})),ed(!0)},ey=(e,a)=>{"darkMode"===e?s(a?"dark":"light"):"customAccentColor"===e&&l(a),ei(s=>({...s,[e]:a})),ed(!0)},ew=e=>{if(e&&e[0]){let s=e[0],a=new FileReader;a.onload=e=>{var s;eb("avatar",null==(s=e.target)?void 0:s.result)},a.readAsDataURL(s)}},eA=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?eh(!0):"dragleave"===e.type&&eh(!1)},ek=e=>{switch(e.toLowerCase()){case"active":case"connected":return(0,r.jsx)(D.E,{className:"bg-green-500/10 text-green-600 border-green-500/20",children:"Active"});case"inactive":case"disconnected":return(0,r.jsx)(D.E,{variant:"secondary",children:"Inactive"});case"pending":return(0,r.jsx)(D.E,{className:"bg-yellow-500/10 text-yellow-600 border-yellow-500/20",children:"Pending"});case"error":return(0,r.jsx)(D.E,{variant:"destructive",children:"Error"});default:return(0,r.jsx)(D.E,{variant:"outline",children:e})}},eC=e=>{switch(e.toLowerCase()){case"admin":return"bg-primary/10 text-primary border-primary/20";case"manager":return"bg-blue-500/10 text-blue-600 border-blue-500/20";case"installer":return"bg-green-500/10 text-green-600 border-green-500/20";default:return"bg-gray-500/10 text-gray-600 border-gray-500/20"}},eP=e=>new Date(e).toLocaleString();return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Settings"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Customize your profile, preferences, and platform integrations"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(D.E,{variant:"secondary",className:"text-xs",children:"Saturday, July 12, 2025"}),(0,r.jsxs)(Z.$,{className:"bg-primary hover:bg-primary/90",disabled:!ec,onClick:()=>{console.log("Saving changes...",{profile:U,preferences:et}),ed(!1)},children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]})]})]}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,r.jsxs)(z.tU,{value:c,onValueChange:d,className:"w-full",children:[(0,r.jsxs)(z.j7,{className:"grid grid-cols-5 w-full max-w-2xl",children:[(0,r.jsxs)(z.Xi,{value:"profile",className:"flex items-center space-x-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Profile"})]}),(0,r.jsxs)(z.Xi,{value:"preferences",className:"flex items-center space-x-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Preferences"})]}),(0,r.jsxs)(z.Xi,{value:"integrations",className:"flex items-center space-x-2",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Integrations"})]}),(0,r.jsxs)(z.Xi,{value:"admin",className:"flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Admin"})]}),(0,r.jsxs)(z.Xi,{value:"security",className:"flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Security"})]})]}),(0,r.jsxs)("div",{className:"mt-6 grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsxs)(n.N,{mode:"wait",children:[(0,r.jsx)(z.av,{value:"profile",className:"mt-0",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[(0,r.jsxs)(I.Zp,{children:[(0,r.jsx)(I.aR,{children:(0,r.jsx)(I.ZB,{children:"Profile Information"})}),(0,r.jsxs)(I.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(F.eu,{className:"h-24 w-24",children:[(0,r.jsx)(F.BK,{src:U.avatar||void 0}),(0,r.jsx)(F.q5,{className:"bg-primary/10 text-primary text-xl",children:U.name.split(" ").map(e=>e[0]).join("")})]}),(0,r.jsx)("button",{onClick:()=>{var e;return null==(e=ej.current)?void 0:e.click()},className:"absolute -bottom-2 -right-2 bg-primary text-white rounded-full p-2 hover:bg-primary/90 transition-colors",children:(0,r.jsx)(j.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-4 text-center transition-colors ".concat(ex?"border-primary bg-primary/5":"border-border"),onDragEnter:eA,onDragLeave:eA,onDragOver:eA,onDrop:e=>{e.preventDefault(),e.stopPropagation(),eh(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&ew(e.dataTransfer.files)},children:[(0,r.jsx)(f.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Drag and drop an image, or"," ",(0,r.jsx)("button",{onClick:()=>{var e;return null==(e=ej.current)?void 0:e.click()},className:"text-primary hover:underline",children:"browse"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"JPG, PNG up to 2MB"})]}),(0,r.jsx)("input",{ref:ej,type:"file",accept:"image/*",onChange:e=>ew(e.target.files),className:"hidden"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{htmlFor:"name",children:"Full Name"}),(0,r.jsx)(_.p,{id:"name",value:U.name,onChange:e=>eb("name",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{htmlFor:"email",children:"Email Address"}),(0,r.jsx)(_.p,{id:"email",type:"email",value:U.email,onChange:e=>eb("email",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{htmlFor:"phone",children:"Phone Number"}),(0,r.jsx)(_.p,{id:"phone",value:U.phone,onChange:e=>eb("phone",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{htmlFor:"role",children:"Role"}),(0,r.jsx)("div",{className:"pt-2",children:(0,r.jsx)(D.E,{className:eC(U.role),children:U.role})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{htmlFor:"bio",children:"Bio"}),(0,r.jsx)(L.T,{id:"bio",value:U.bio,onChange:e=>eb("bio",e.target.value),rows:3,placeholder:"Tell us about yourself..."})]})]})]}),(0,r.jsxs)(I.Zp,{children:[(0,r.jsx)(I.aR,{children:(0,r.jsx)(I.ZB,{children:"Change Password"})}),(0,r.jsxs)(I.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{htmlFor:"current-password",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(_.p,{id:"current-password",type:eo?"text":"password",placeholder:"Enter current password"}),(0,r.jsx)("button",{type:"button",onClick:()=>em(!eo),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground",children:eo?(0,r.jsx)(g.A,{className:"h-4 w-4"}):(0,r.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{htmlFor:"new-password",children:"New Password"}),(0,r.jsx)(_.p,{id:"new-password",type:"password",placeholder:"Enter new password"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{htmlFor:"confirm-password",children:"Confirm Password"}),(0,r.jsx)(_.p,{id:"confirm-password",type:"password",placeholder:"Confirm new password"})]})]}),(0,r.jsx)(Z.$,{variant:"outline",children:"Update Password"})]})]})]})},"profile"),(0,r.jsx)(z.av,{value:"preferences",className:"mt-0",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[(0,r.jsxs)(I.Zp,{children:[(0,r.jsx)(I.aR,{children:(0,r.jsx)(I.ZB,{children:"Appearance"})}),(0,r.jsxs)(I.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(J.J,{htmlFor:ef,className:"text-base",children:"Dark Mode"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Toggle between light and dark themes"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)(M.d,{id:ef,checked:et.darkMode,onCheckedChange:e=>ey("darkMode",e),className:"rounded-sm [&_span]:rounded"}),(0,r.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{children:"Custom Accent Color"}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("input",{type:"color",value:et.customAccentColor,onChange:e=>ey("customAccentColor",e.target.value),className:"w-12 h-12 rounded border border-border cursor-pointer"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Preview"}),(0,r.jsx)("div",{className:"w-24 h-8 rounded border border-border",style:{backgroundColor:et.customAccentColor}})]})]})]})]})]}),(0,r.jsxs)(I.Zp,{children:[(0,r.jsx)(I.aR,{children:(0,r.jsx)(I.ZB,{children:"Notifications"})}),(0,r.jsxs)(I.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(J.J,{htmlFor:eg,className:"text-base",children:"Email Notifications"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive updates about projects and appointments"})]}),(0,r.jsx)(M.d,{id:eg,checked:et.emailNotifications,onCheckedChange:e=>ey("emailNotifications",e),className:"rounded-sm [&_span]:rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(J.J,{htmlFor:ev,className:"text-base",children:"SMS Alerts"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Get text messages for urgent updates"})]}),(0,r.jsx)(M.d,{id:ev,checked:et.smsAlerts,onCheckedChange:e=>ey("smsAlerts",e),className:"rounded-sm [&_span]:rounded"})]})]})]}),(0,r.jsxs)(I.Zp,{children:[(0,r.jsx)(I.aR,{children:(0,r.jsx)(I.ZB,{children:"Localization"})}),(0,r.jsx)(I.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{children:"Language"}),(0,r.jsxs)(B.l6,{value:et.language,onValueChange:e=>ey("language",e),children:[(0,r.jsx)(B.bq,{children:(0,r.jsx)(B.yv,{})}),(0,r.jsxs)(B.gC,{children:[(0,r.jsx)(B.eb,{value:"en",children:"English"}),(0,r.jsx)(B.eb,{value:"es",children:"Espa\xf1ol"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{children:"Timezone"}),(0,r.jsxs)(B.l6,{value:et.timezone,onValueChange:e=>ey("timezone",e),children:[(0,r.jsx)(B.bq,{children:(0,r.jsx)(B.yv,{})}),(0,r.jsxs)(B.gC,{children:[(0,r.jsx)(B.eb,{value:"America/Chicago",children:"Central Time"}),(0,r.jsx)(B.eb,{value:"America/New_York",children:"Eastern Time"}),(0,r.jsx)(B.eb,{value:"America/Los_Angeles",children:"Pacific Time"}),(0,r.jsx)(B.eb,{value:"America/Denver",children:"Mountain Time"})]})]})]})]})})]})]})},"preferences"),(0,r.jsx)(z.av,{value:"integrations",className:"mt-0",children:(0,r.jsx)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:en.map(e=>{let s=e.icon;return(0,r.jsxs)(I.Zp,{children:[(0,r.jsx)(I.aR,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-md bg-primary/10",children:(0,r.jsx)(s,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(I.ZB,{className:"text-lg",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]})]}),ek(e.status)]})}),(0,r.jsxs)(I.Wu,{children:["oauth"===e.connectionType&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(Z.$,{variant:"connected"===e.status?"outline":"default",className:"connected"===e.status?"":"bg-primary hover:bg-primary/90",children:"connected"===e.status?"Disconnect":"Connect with OAuth"}),"connected"===e.status&&(0,r.jsx)(Z.$,{variant:"outline",size:"sm",children:"Test Connection"})]}),"api-key"===e.connectionType&&(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(_.p,{placeholder:"Enter API key",type:"password",value:e.apiKey||"",onChange:s=>{el(en.map(a=>a.id===e.id?{...a,apiKey:s.target.value}:a)),ed(!0)}}),(0,r.jsx)(Z.$,{variant:"outline",children:"Test"})]})}),"toggle"===e.connectionType&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(J.J,{htmlFor:"".concat(e.name.toLowerCase().replace(" ","-"),"-sync"),className:"text-sm text-muted-foreground",children:["Enable ",e.name," synchronization"]}),(0,r.jsx)(M.d,{id:"".concat(e.name.toLowerCase().replace(" ","-"),"-sync"),checked:"connected"===e.status,className:"rounded-sm [&_span]:rounded"})]})]})]},e.id)})})},"integrations"),(0,r.jsx)(z.av,{value:"admin",className:"mt-0",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[(0,r.jsxs)(I.Zp,{children:[(0,r.jsxs)(I.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsx)(I.ZB,{children:"Team Management"}),(0,r.jsxs)(Z.$,{className:"bg-primary hover:bg-primary/90",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Invite User"]})]}),(0,r.jsx)(I.Wu,{children:(0,r.jsxs)(W,{children:[(0,r.jsx)($,{children:(0,r.jsxs)(G,{children:[(0,r.jsx)(O,{children:"User"}),(0,r.jsx)(O,{children:"Role"}),(0,r.jsx)(O,{children:"Status"}),(0,r.jsx)(O,{children:"Last Active"}),(0,r.jsx)(O,{children:"Actions"})]})}),(0,r.jsx)(q,{children:ee.map(e=>(0,r.jsxs)(G,{children:[(0,r.jsx)(V,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(F.eu,{className:"h-8 w-8",children:(0,r.jsx)(F.q5,{className:"bg-primary/10 text-primary text-xs",children:e.name.split(" ").map(e=>e[0]).join("")})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email})]})]})}),(0,r.jsx)(V,{children:(0,r.jsx)(D.E,{className:eC(e.role),children:e.role})}),(0,r.jsx)(V,{children:ek(e.status)}),(0,r.jsx)(V,{className:"text-sm text-muted-foreground",children:eP(e.lastActive)}),(0,r.jsx)(V,{children:(0,r.jsxs)(X.rI,{children:[(0,r.jsx)(X.ty,{asChild:!0,children:(0,r.jsx)(Z.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(X.SQ,{children:[(0,r.jsx)(X._2,{children:"Edit User"}),(0,r.jsx)(X._2,{children:"Change Role"}),(0,r.jsx)(X._2,{className:"text-destructive",children:"Remove User"})]})]})})]},e.id))})]})})]}),(0,r.jsxs)(I.Zp,{children:[(0,r.jsxs)(I.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsx)(I.ZB,{children:"Audit Logs"}),(0,r.jsxs)(Z.$,{variant:"outline",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Export Logs"]})]}),(0,r.jsx)(I.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:es.map(e=>(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-border",children:[(0,r.jsx)("div",{className:"p-1 rounded-full bg-primary/10 mt-1",children:(0,r.jsx)(k.A,{className:"h-3 w-3 text-primary"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-medium",children:e.action}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:eP(e.timestamp)})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.details}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["by ",e.user]})]})]},e.id))})})]}),(0,r.jsxs)(I.Zp,{children:[(0,r.jsx)(I.aR,{children:(0,r.jsx)(I.ZB,{children:"Data Export"})}),(0,r.jsx)(I.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(Z.$,{variant:"outline",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Export as CSV"]}),(0,r.jsxs)(Z.$,{variant:"outline",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Export as JSON"]})]})})]})]})},"admin"),(0,r.jsx)(z.av,{value:"security",className:"mt-0",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[(0,r.jsxs)(I.Zp,{children:[(0,r.jsx)(I.aR,{children:(0,r.jsx)(I.ZB,{children:"Two-Factor Authentication"})}),(0,r.jsxs)(I.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(J.J,{htmlFor:eN,className:"text-base",children:"Enable 2FA"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add an extra layer of security to your account"})]}),(0,r.jsx)(M.d,{id:eN,checked:eu,onCheckedChange:ep,className:"rounded-sm [&_span]:rounded"})]}),eu&&(0,r.jsxs)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"border border-border rounded-lg p-4 space-y-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Scan QR Code"}),(0,r.jsx)("div",{className:"w-32 h-32 bg-muted rounded-lg flex items-center justify-center",children:(0,r.jsxs)("p",{className:"text-xs text-muted-foreground text-center",children:["QR Code",(0,r.jsx)("br",{}),"Placeholder"]})}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Scan this QR code with your authenticator app"})]})]})]}),(0,r.jsxs)(I.Zp,{children:[(0,r.jsxs)(I.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsx)(I.ZB,{children:"Active Sessions"}),(0,r.jsxs)(Z.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Logout All"]})]}),(0,r.jsx)(I.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:ea.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border border-border",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-md bg-primary/10",children:e.device.includes("iPhone")?(0,r.jsx)(P.A,{className:"h-4 w-4 text-primary"}):(0,r.jsx)(R.A,{className:"h-4 w-4 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.device}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.location," • ",eP(e.lastActive)]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.current&&(0,r.jsx)(D.E,{className:"bg-green-500/10 text-green-600 border-green-500/20",children:"Current"}),!e.current&&(0,r.jsx)(Z.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(C.A,{className:"h-4 w-4"})})]})]},e.id))})})]}),(0,r.jsxs)(I.Zp,{children:[(0,r.jsx)(I.aR,{children:(0,r.jsx)(I.ZB,{children:"Privacy Settings"})}),(0,r.jsxs)(I.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(J.J,{children:"Data Retention Period"}),(0,r.jsxs)(B.l6,{defaultValue:"12",children:[(0,r.jsx)(B.bq,{children:(0,r.jsx)(B.yv,{})}),(0,r.jsxs)(B.gC,{children:[(0,r.jsx)(B.eb,{value:"6",children:"6 months"}),(0,r.jsx)(B.eb,{value:"12",children:"12 months"}),(0,r.jsx)(B.eb,{value:"24",children:"24 months"}),(0,r.jsx)(B.eb,{value:"60",children:"5 years"})]})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"How long to keep deleted data before permanent removal"})]}),(0,r.jsxs)(Z.$,{variant:"outline",className:"text-destructive border-destructive hover:bg-destructive/10",children:[(0,r.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Delete All My Data"]})]})]})]})},"security")]})}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)(I.Zp,{className:"sticky top-6",children:[(0,r.jsx)(I.aR,{children:(0,r.jsxs)(I.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(T.A,{className:"h-5 w-5 text-primary"}),(0,r.jsx)("span",{children:"Quick Tips"})]})}),(0,r.jsxs)(I.Wu,{className:"space-y-4",children:["profile"===c&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-primary/5 border border-primary/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Profile Photo"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"A professional photo helps build trust with customers"})]}),(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-blue-500/5 border border-blue-500/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Strong Password"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Use at least 12 characters with mixed case and symbols"})]})]},"profile-tips"),"preferences"===c&&(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-primary/5 border border-primary/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Notifications"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Enable SMS for urgent project updates"})]})},"preferences-tips"),"integrations"===c&&(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-primary/5 border border-primary/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Google Maps"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Connect to get automatic driving directions to appointments"})]})},"integrations-tips"),"security"===c&&(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-primary/5 border border-primary/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"2FA Required"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Admin accounts must have two-factor authentication enabled"})]})},"security-tips"),(0,r.jsx)("div",{className:"pt-4 border-t border-border",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Need help with integrations?"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(_.p,{placeholder:"Ask here...",className:"text-sm"}),(0,r.jsx)(Z.$,{size:"sm",variant:"outline",children:"Ask"})]})]})})]})]})})]})]})}),(0,r.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"flex items-center justify-between pt-6 border-t border-border",children:[(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"WindowWorks CRM v2.1.0"}),(0,r.jsxs)(Z.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Reset to Defaults"]})]})]})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>n});var r=a(5155);a(2115);var t=a(968),i=a(9434);function n(e){let{className:s,...a}=e;return(0,r.jsx)(t.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},7313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>c,av:()=>d,j7:()=>l,tU:()=>n});var r=a(5155);a(2115);var t=a(704),i=a(9434);function n(e){let{className:s,...a}=e;return(0,r.jsx)(t.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",s),...a})}function l(e){let{className:s,...a}=e;return(0,r.jsx)(t.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...a})}function c(e){let{className:s,...a}=e;return(0,r.jsx)(t.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...a})}function d(e){let{className:s,...a}=e;return(0,r.jsx)(t.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",s),...a})}},8539:(e,s,a)=>{"use strict";a.d(s,{T:()=>n});var r=a(5155),t=a(2115),i=a(9434);let n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...t})});n.displayName="Textarea"},9409:(e,s,a)=>{"use strict";a.d(s,{bq:()=>x,eb:()=>j,gC:()=>p,l6:()=>o,yv:()=>m});var r=a(5155),t=a(2115),i=a(8715),n=a(6474),l=a(7863),c=a(5196),d=a(9434);let o=i.bL;i.YJ;let m=i.WT,x=t.forwardRef((e,s)=>{let{className:a,children:t,...l}=e;return(0,r.jsxs)(i.l9,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...l,children:[t,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=i.l9.displayName;let h=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})});h.displayName=i.PP.displayName;let u=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});u.displayName=i.wn.displayName;let p=t.forwardRef((e,s)=>{let{className:a,children:t,position:n="popper",...l}=e;return(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{ref:s,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...l,children:[(0,r.jsx)(h,{}),(0,r.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(u,{})]})})});p.displayName=i.UC.displayName,t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...t})}).displayName=i.JU.displayName;let j=t.forwardRef((e,s)=>{let{className:a,children:t,...n}=e;return(0,r.jsxs)(i.q7,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})}),(0,r.jsx)(i.p4,{children:t})]})});j.displayName=i.q7.displayName,t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",a),...t})}).displayName=i.wv.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[418,0,426,205,441,684,358],()=>s(1029)),_N_E=e.O()}]);