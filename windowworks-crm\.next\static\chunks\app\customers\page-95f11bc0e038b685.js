(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[812],{2905:(e,s,a)=>{"use strict";a.d(s,{D:()=>d,ThemeProvider:()=>i});var t=a(5155),r=a(2115);let l=(0,r.createContext)(void 0),n=()=>{try{let e=localStorage.getItem("windowworks-theme");if(e&&["light","dark"].includes(e))return e;return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}catch(e){return"light"}};function i(e){let{children:s}=e,[a,i]=(0,r.useState)("light"),[d,c]=(0,r.useState)("#D97706"),[o,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{m(!0);let e=n();i(e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark");let s=localStorage.getItem("windowworks-accent-color");s&&c(s)},[]);let x=(0,r.useCallback)(e=>{i(e);try{localStorage.setItem("windowworks-theme",e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}catch(e){console.error("Failed to set theme in localStorage",e)}},[]),h=(0,r.useCallback)(e=>{c(e);try{localStorage.setItem("windowworks-accent-color",e);let s=document.documentElement,a="oklch(from ".concat(e," l c h)");s.style.setProperty("--primary",a),s.style.setProperty("--color-brand-primary",e)}catch(e){console.error("Failed to set accent color in localStorage",e)}},[]);return(0,t.jsx)(l.Provider,{value:{theme:a,setTheme:x,accentColor:d,setAccentColor:h,mounted:o},children:s})}function d(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},4165:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>o,L3:()=>x,c7:()=>m,lG:()=>i});var t=a(5155);a(2115);var r=a(5452),l=a(4416),n=a(9434);function i(e){let{...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"dialog",...s})}function d(e){let{...s}=e;return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...s})}function c(e){let{className:s,...a}=e;return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...a})}function o(e){let{className:s,children:a,showCloseButton:i=!0,...o}=e;return(0,t.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,t.jsx)(c,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...o,children:[a,i&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(l.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",s),...a})}function x(e){let{className:s,...a}=e;return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",s),...a})}},4272:(e,s,a)=>{"use strict";a.d(s,{default:()=>Y});var t=a(5155),r=a(2115),l=a(6408),n=a(7580),i=a(9946);let d=(0,i.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),c=(0,i.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var o=a(5868),m=a(646),x=a(4186),h=a(4416),u=a(4616),p=a(7924),j=a(1788),f=a(8883);let g=(0,i.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var v=a(4516),N=a(9074),b=a(5623),y=a(2657),w=a(3717);let C=(0,i.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var k=a(2525),A=a(285),S=a(2523),P=a(6695),L=a(6126),R=a(1394),z=a(4838),M=a(9409),q=a(7262),F=a(1007);let E=(0,i.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var Z=a(5339),I=a(1154);let B=(0,i.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),T=(0,i.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var $=a(4229),J=a(5057),_=a(8539),D=a(7313),V=a(4165),W=a(2905);let U=[{id:"blinds",label:"Blinds",popular:!0},{id:"shutters",label:"Shutters",popular:!0},{id:"shades",label:"Shades",popular:!0},{id:"curtains",label:"Curtains",popular:!1},{id:"drapes",label:"Drapes",popular:!1},{id:"valances",label:"Valances",popular:!1}],O=[{id:"white",label:"White",hex:"#FFFFFF"},{id:"cream",label:"Cream",hex:"#F5F5DC"},{id:"beige",label:"Beige",hex:"#F5F5DC"},{id:"gray",label:"Gray",hex:"#808080"},{id:"brown",label:"Brown",hex:"#A52A2A"},{id:"black",label:"Black",hex:"#000000"}],H=[{value:"under-500",label:"Under $500"},{value:"500-1000",label:"$500 - $1,000"},{value:"1000-2500",label:"$1,000 - $2,500"},{value:"2500-5000",label:"$2,500 - $5,000"},{value:"over-5000",label:"Over $5,000"}],G=[{value:"email",label:"Email"},{value:"phone",label:"Phone"},{value:"text",label:"Text/SMS"},{value:"app",label:"In-App Notifications"}];function X(e){let{isOpen:s,onClose:a,onSave:l}=e,{accentColor:n}=(0,W.D)(),[i,d]=(0,r.useState)("details"),[c,m]=(0,r.useState)(!1),[x,h]=(0,r.useState)({}),[u,p]=(0,r.useState)(!1),[j,N]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:"",address:{street:"",city:"",state:"",zipCode:"",country:"United States"},preferences:{windowTypes:[],preferredColors:[],budgetRange:"",communicationMethod:"email"},notes:""}),b=(0,r.useCallback)(()=>{let e={};return j.firstName.trim()||(e.firstName="First name is required"),j.lastName.trim()||(e.lastName="Last name is required"),j.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(j.email)||(e.email="Please enter a valid email address"):e.email="Email is required",j.phone.trim()||(e.phone="Phone number is required"),j.address.street.trim()||(e.street="Street address is required"),j.address.city.trim()||(e.city="City is required"),j.address.state.trim()||(e.state="State is required"),j.address.zipCode.trim()||(e.zipCode="ZIP code is required"),h(e),0===Object.keys(e).length},[j]),y=(0,r.useCallback)(async()=>{p(!0),await new Promise(e=>setTimeout(e,1500));let e={windowTypes:["blinds","shutters"],preferredColors:["white","gray"],budgetRange:"1000-2500",communicationMethod:"email"};N(s=>({...s,preferences:{...s.preferences,...e}})),p(!1)},[]),w=async()=>{if(!b())return void(x.firstName||x.lastName||x.email||x.phone?d("details"):(x.street||x.city||x.state||x.zipCode)&&d("address"));m(!0);try{await l(j),a(),N({firstName:"",lastName:"",email:"",phone:"",address:{street:"",city:"",state:"",zipCode:"",country:"United States"},preferences:{windowTypes:[],preferredColors:[],budgetRange:"",communicationMethod:"email"},notes:""})}catch(e){console.error("Error saving customer:",e)}finally{m(!1)}},C=(e,s)=>{N(a=>{let t=e.split("."),r={...a},l=r;for(let e=0;e<t.length-1;e++)void 0===l[t[e]]&&(l[t[e]]={}),l=l[t[e]];return l[t[t.length-1]]=s,r}),x[e]&&h(s=>({...s,[e]:""}))},k=e=>{let s=j.preferences.windowTypes;C("preferences.windowTypes",s.includes(e)?s.filter(s=>s!==e):[...s,e])},R=e=>{let s=j.preferences.preferredColors;C("preferences.preferredColors",s.includes(e)?s.filter(s=>s!==e):[...s,e])};return(0,t.jsx)(V.lG,{open:s,onOpenChange:a,children:(0,t.jsx)(V.Cf,{className:"max-w-6xl sm:max-w-6xl max-h-[70vh] overflow-hidden p-0",children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsx)(V.c7,{className:"px-6 py-4 border-b border-gray-200 flex-shrink-0",children:(0,t.jsx)(V.L3,{className:"text-xl font-semibold text-slate-700",children:"Add New Customer"})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,t.jsxs)(D.tU,{value:i,onValueChange:d,className:"space-y-6",children:[(0,t.jsxs)(D.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsxs)(D.Xi,{value:"details",className:"flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"h-4 w-4"}),"Details & Address"]}),(0,t.jsxs)(D.Xi,{value:"preferences",className:"flex items-center gap-2",children:[(0,t.jsx)(E,{className:"h-4 w-4"}),"Preferences & Notes"]})]}),(0,t.jsx)(D.av,{value:"details",className:"space-y-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"h-5 w-5"}),"Personal Information"]})}),(0,t.jsxs)(P.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(J.J,{htmlFor:"firstName",className:"text-slate-600",children:"First Name *"}),(0,t.jsx)(S.p,{id:"firstName",value:j.firstName,onChange:e=>C("firstName",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(x.firstName?"border-red-500":""),placeholder:"Enter first name"}),x.firstName&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),x.firstName]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(J.J,{htmlFor:"lastName",className:"text-slate-600",children:"Last Name *"}),(0,t.jsx)(S.p,{id:"lastName",value:j.lastName,onChange:e=>C("lastName",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(x.lastName?"border-red-500":""),placeholder:"Enter last name"}),x.lastName&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),x.lastName]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(J.J,{htmlFor:"email",className:"text-slate-600",children:"Email Address *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(f.A,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),(0,t.jsx)(S.p,{id:"email",type:"email",value:j.email,onChange:e=>C("email",e.target.value),className:"pl-10 border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(x.email?"border-red-500":""),placeholder:"<EMAIL>"})]}),x.email&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),x.email]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(J.J,{htmlFor:"phone",className:"text-slate-600",children:"Phone Number *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),(0,t.jsx)(S.p,{id:"phone",type:"tel",value:j.phone,onChange:e=>C("phone",e.target.value),className:"pl-10 border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(x.phone?"border-red-500":""),placeholder:"(*************"})]}),x.phone&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),x.phone]})]})]})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5"}),"Address Information"]})}),(0,t.jsxs)(P.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(J.J,{htmlFor:"street",className:"text-slate-600",children:"Street Address *"}),(0,t.jsx)(S.p,{id:"street",value:j.address.street,onChange:e=>C("address.street",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(x.street?"border-red-500":""),placeholder:"123 Main Street"}),x.street&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),x.street]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(J.J,{htmlFor:"city",className:"text-slate-600",children:"City *"}),(0,t.jsx)(S.p,{id:"city",value:j.address.city,onChange:e=>C("address.city",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(x.city?"border-red-500":""),placeholder:"Springfield"}),x.city&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),x.city]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(J.J,{htmlFor:"state",className:"text-slate-600",children:"State *"}),(0,t.jsx)(S.p,{id:"state",value:j.address.state,onChange:e=>C("address.state",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(x.state?"border-red-500":""),placeholder:"IL"}),x.state&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),x.state]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(J.J,{htmlFor:"zipCode",className:"text-slate-600",children:"ZIP Code *"}),(0,t.jsx)(S.p,{id:"zipCode",value:j.address.zipCode,onChange:e=>C("address.zipCode",e.target.value),className:"border-gray-200 focus:border-[var(--color-brand-primary)] ".concat(x.zipCode?"border-red-500":""),placeholder:"62704"}),x.zipCode&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),x.zipCode]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(J.J,{htmlFor:"country",className:"text-slate-600",children:"Country"}),(0,t.jsxs)(M.l6,{value:j.address.country,onValueChange:e=>C("address.country",e),children:[(0,t.jsx)(M.bq,{className:"border-gray-200",children:(0,t.jsx)(M.yv,{})}),(0,t.jsxs)(M.gC,{children:[(0,t.jsx)(M.eb,{value:"United States",children:"United States"}),(0,t.jsx)(M.eb,{value:"Canada",children:"Canada"}),(0,t.jsx)(M.eb,{value:"Mexico",children:"Mexico"})]})]})]})]})]})]})]})}),(0,t.jsxs)(D.av,{value:"preferences",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-700",children:"Customer Preferences & Notes"}),(0,t.jsxs)(A.$,{variant:"outline",onClick:y,disabled:u,className:"border-gray-200",style:{borderColor:n,color:n},children:[u?(0,t.jsx)(I.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(B,{className:"h-4 w-4 mr-2"}),u?"Analyzing...":"AI Suggest"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsx)(P.ZB,{className:"text-base text-slate-700",children:"Window Types"}),(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Select preferred window treatment types"})]}),(0,t.jsx)(P.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 gap-3",children:U.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(q.S,{id:e.id,checked:j.preferences.windowTypes.includes(e.id),onCheckedChange:()=>k(e.id),className:"border-gray-300"}),(0,t.jsxs)(J.J,{htmlFor:e.id,className:"text-sm font-normal text-slate-600 flex items-center gap-2",children:[e.label,e.popular&&(0,t.jsx)(L.E,{variant:"secondary",className:"text-xs px-1.5 py-0.5",children:"Popular"})]})]},e.id))})})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsx)(P.ZB,{className:"text-base text-slate-700",children:"Preferred Colors"}),(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Select preferred color schemes"})]}),(0,t.jsx)(P.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-2 gap-3",children:O.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(q.S,{id:e.id,checked:j.preferences.preferredColors.includes(e.id),onCheckedChange:()=>R(e.id),className:"border-gray-300"}),(0,t.jsxs)(J.J,{htmlFor:e.id,className:"text-sm font-normal text-slate-600 flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border border-gray-300",style:{backgroundColor:e.hex}}),e.label]})]},e.id))})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsxs)(P.ZB,{className:"text-base text-slate-700 flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),"Budget Range"]})}),(0,t.jsx)(P.Wu,{children:(0,t.jsxs)(M.l6,{value:j.preferences.budgetRange,onValueChange:e=>C("preferences.budgetRange",e),children:[(0,t.jsx)(M.bq,{className:"border-gray-200",children:(0,t.jsx)(M.yv,{placeholder:"Select budget range"})}),(0,t.jsx)(M.gC,{children:H.map(e=>(0,t.jsx)(M.eb,{value:e.value,children:e.label},e.value))})]})})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsx)(P.ZB,{className:"text-base text-slate-700",children:"Communication"})}),(0,t.jsx)(P.Wu,{children:(0,t.jsxs)(M.l6,{value:j.preferences.communicationMethod,onValueChange:e=>C("preferences.communicationMethod",e),children:[(0,t.jsx)(M.bq,{className:"border-gray-200",children:(0,t.jsx)(M.yv,{})}),(0,t.jsx)(M.gC,{children:G.map(e=>(0,t.jsx)(M.eb,{value:e.value,children:e.label},e.value))})]})})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsxs)(P.ZB,{className:"text-lg text-slate-700 flex items-center gap-2",children:[(0,t.jsx)(T,{className:"h-5 w-5"}),"Additional Notes"]}),(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Add any additional information about the customer"})]}),(0,t.jsx)(P.Wu,{children:(0,t.jsx)(_.T,{value:j.notes,onChange:e=>C("notes",e.target.value),className:"min-h-[200px] border-gray-200 focus:border-[var(--color-brand-primary)]",placeholder:"Special requirements, previous interactions, design preferences, installation considerations..."})})]})]})]})]})]})}),(0,t.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 flex-shrink-0",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(A.$,{variant:"outline",onClick:a,disabled:c,className:"border-gray-200",children:"Cancel"}),(0,t.jsx)(A.$,{onClick:w,disabled:c,className:"text-white min-w-[120px]",style:{backgroundColor:n,borderColor:n},children:c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(I.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)($.A,{className:"h-4 w-4 mr-2"}),"Save Customer"]})})]})})]})})})}let K=[{id:"1",name:"Sarah Johnson",email:"<EMAIL>",phone:"(*************",address:"123 Maple Street, Springfield, IL 62704",status:"Active",lastProject:"2024-12-15",totalRevenue:2840,projectCount:3,preferences:["Blinds","Shutters"],avatar:null},{id:"2",name:"Robert Chen",email:"<EMAIL>",phone:"(*************",address:"456 Oak Avenue, Springfield, IL 62701",status:"Active",lastProject:"2025-01-08",totalRevenue:1560,projectCount:2,preferences:["Shades"],avatar:null},{id:"3",name:"Emma Davis",email:"<EMAIL>",phone:"(*************",address:"789 Pine Road, Springfield, IL 62702",status:"Pending",lastProject:null,totalRevenue:0,projectCount:0,preferences:["Shutters","Shades"],avatar:null},{id:"4",name:"Michael Williams",email:"<EMAIL>",phone:"(*************",address:"321 Elm Street, Springfield, IL 62703",status:"Active",lastProject:"2024-11-22",totalRevenue:4250,projectCount:5,preferences:["Blinds","Shutters","Shades"],avatar:null},{id:"5",name:"Lisa Anderson",email:"<EMAIL>",phone:"(*************",address:"654 Cedar Lane, Springfield, IL 62705",status:"Inactive",lastProject:"2024-08-14",totalRevenue:890,projectCount:1,preferences:["Blinds"],avatar:null}],Q=[{title:"Total Customers",value:"89",change:"-2%",icon:n.A,trend:"down"},{title:"New Customers",value:"12",change:"+5%",icon:d,trend:"up"},{title:"High-Value Clients",value:"25",change:"",icon:c,trend:"neutral"},{title:"Avg Revenue/Customer",value:"$520",change:"+8%",icon:o.A,trend:"up"}];function Y(){let[e,s]=(0,r.useState)(""),[a,n]=(0,r.useState)("all"),[i,d]=(0,r.useState)("all"),[c,o]=(0,r.useState)("name"),[F,E]=(0,r.useState)([]),[Z,I]=(0,r.useState)(!1),[B,T]=(0,r.useState)(1),$=K.filter(s=>{let t=s.name.toLowerCase().includes(e.toLowerCase())||s.email.toLowerCase().includes(e.toLowerCase()),r="all"===a||s.status.toLowerCase()===a.toLowerCase(),l="all"===i||s.preferences.some(e=>e.toLowerCase()===i.toLowerCase());return t&&r&&l}).sort((e,s)=>{switch(c){case"name":return e.name.localeCompare(s.name);case"date":return new Date(s.lastProject||0).getTime()-new Date(e.lastProject||0).getTime();case"revenue":return s.totalRevenue-e.totalRevenue;default:return 0}}),J=Math.ceil($.length/10),_=$.slice((B-1)*10,10*B),D=e=>{E(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},V=e=>{switch(e.toLowerCase()){case"active":return"default";case"pending":return"secondary";default:return"outline"}},W=e=>{switch(e.toLowerCase()){case"active":return m.A;case"pending":return x.A;default:return h.A}},U=async e=>{try{console.log("Saving customer:",e),await new Promise(e=>setTimeout(e,1e3)),console.log("Customer saved successfully")}catch(e){throw console.error("Error saving customer:",e),e}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Customers"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage your client base and preferences"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(L.E,{variant:"secondary",className:"text-xs",children:"Saturday, July 12, 2025"}),(0,t.jsxs)(A.$,{className:"bg-primary hover:bg-primary/90",onClick:()=>I(!0),children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"New Customer"]})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Q.map((e,s)=>(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:(0,t.jsxs)(P.Zp,{className:"hover:border-border/80 transition-colors",children:[(0,t.jsx)(P.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"p-2 rounded-md bg-primary/10",children:(0,t.jsx)(e.icon,{className:"h-5 w-5 text-primary"})}),e.change&&(0,t.jsx)(L.E,{variant:"up"===e.trend?"default":"down"===e.trend?"destructive":"secondary",className:"text-xs",children:e.change})]})}),(0,t.jsx)(P.Wu,{children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-foreground",children:e.value}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title})]})})]})},e.title))}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,t.jsx)(P.Zp,{children:(0,t.jsx)(P.aR,{className:"pb-4",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex flex-1 items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,t.jsx)(S.p,{placeholder:"Search customers...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(M.l6,{value:a,onValueChange:n,children:[(0,t.jsx)(M.bq,{className:"w-32",children:(0,t.jsx)(M.yv,{placeholder:"Status"})}),(0,t.jsxs)(M.gC,{children:[(0,t.jsx)(M.eb,{value:"all",children:"All Status"}),(0,t.jsx)(M.eb,{value:"active",children:"Active"}),(0,t.jsx)(M.eb,{value:"pending",children:"Pending"}),(0,t.jsx)(M.eb,{value:"inactive",children:"Inactive"})]})]}),(0,t.jsxs)(M.l6,{value:i,onValueChange:d,children:[(0,t.jsx)(M.bq,{className:"w-36",children:(0,t.jsx)(M.yv,{placeholder:"Preference"})}),(0,t.jsxs)(M.gC,{children:[(0,t.jsx)(M.eb,{value:"all",children:"All Products"}),(0,t.jsx)(M.eb,{value:"blinds",children:"Blinds"}),(0,t.jsx)(M.eb,{value:"shutters",children:"Shutters"}),(0,t.jsx)(M.eb,{value:"shades",children:"Shades"})]})]}),(0,t.jsxs)(M.l6,{value:c,onValueChange:o,children:[(0,t.jsx)(M.bq,{className:"w-32",children:(0,t.jsx)(M.yv,{placeholder:"Sort by"})}),(0,t.jsxs)(M.gC,{children:[(0,t.jsx)(M.eb,{value:"name",children:"Name"}),(0,t.jsx)(M.eb,{value:"date",children:"Date Added"}),(0,t.jsx)(M.eb,{value:"revenue",children:"Revenue"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(A.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Export"]}),F.length>0&&(0,t.jsxs)(L.E,{variant:"secondary",children:[F.length," selected"]})]})]})})})}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,t.jsx)(P.Zp,{children:(0,t.jsxs)(P.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"border-b border-border",children:(0,t.jsxs)("tr",{className:"bg-accent/20",children:[(0,t.jsx)("th",{className:"text-left p-4 w-12",children:(0,t.jsx)(q.S,{checked:F.length===_.length&&_.length>0,onCheckedChange:()=>{F.length===_.length?E([]):E(_.map(e=>e.id))}})}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Customer"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Contact"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Address"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Last Project"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Status"}),(0,t.jsx)("th",{className:"text-left p-4 font-medium text-foreground",children:"Revenue"}),(0,t.jsx)("th",{className:"text-right p-4 w-16"})]})}),(0,t.jsx)("tbody",{children:_.map((e,s)=>{let a=W(e.status);return(0,t.jsxs)(l.P.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},className:"border-b border-border hover:bg-accent/30 transition-colors",children:[(0,t.jsxs)("td",{className:"p-4",children:["                            ",(0,t.jsx)(q.S,{checked:F.includes(e.id),onCheckedChange:()=>D(e.id),onClick:e=>e.stopPropagation()})]}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(R.eu,{className:"h-8 w-8",children:[(0,t.jsx)(R.BK,{src:e.avatar||void 0}),(0,t.jsx)(R.q5,{className:"bg-primary/10 text-primary",children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.projectCount," project",1!==e.projectCount?"s":""]})]})]})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(f.A,{className:"h-3 w-3 mr-2"}),e.email]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(g,{className:"h-3 w-3 mr-2"}),e.phone]})]})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(v.A,{className:"h-3 w-3 mr-2 flex-shrink-0"}),(0,t.jsx)("span",{className:"truncate max-w-48",children:e.address})]})}),(0,t.jsx)("td",{className:"p-4",children:e.lastProject?(0,t.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(N.A,{className:"h-3 w-3 mr-2"}),new Date(e.lastProject).toLocaleDateString()]}):(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"No projects"})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)(L.E,{variant:V(e.status),className:"text-xs",children:[(0,t.jsx)(a,{className:"h-3 w-3 mr-1"}),e.status]})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)("span",{className:"font-medium text-foreground",children:["$",e.totalRevenue.toLocaleString()]})}),(0,t.jsx)("td",{className:"p-4",children:(0,t.jsxs)(z.rI,{children:[(0,t.jsx)(z.ty,{asChild:!0,onClick:e=>e.stopPropagation(),children:(0,t.jsx)(A.$,{variant:"ghost",size:"sm",children:(0,t.jsx)(b.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(z.SQ,{align:"end",children:[(0,t.jsxs)(z._2,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),(0,t.jsxs)(z._2,{children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Edit Customer"]}),(0,t.jsxs)(z._2,{children:[(0,t.jsx)(C,{className:"h-4 w-4 mr-2"}),"View Projects"]}),(0,t.jsx)(z.mB,{}),(0,t.jsxs)(z._2,{className:"text-destructive",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})})]},e.id)})})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-t border-border",children:[(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",(B-1)*10+1," to ",Math.min(10*B,$.length)," of ",$.length," customers"]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(A.$,{variant:"outline",size:"sm",onClick:()=>T(e=>Math.max(1,e-1)),disabled:1===B,children:"Previous"}),(0,t.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,J)},(e,s)=>{let a=s+1;return(0,t.jsx)(A.$,{variant:B===a?"default":"outline",size:"sm",onClick:()=>T(a),className:"w-8 h-8 p-0",children:a},a)})}),(0,t.jsx)(A.$,{variant:"outline",size:"sm",onClick:()=>T(e=>Math.min(J,e+1)),disabled:B===J,children:"Next"})]})]})]})})}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(P.Zp,{className:"lg:col-span-2",children:[(0,t.jsx)(P.aR,{children:(0,t.jsx)(P.ZB,{children:"Top Customers by Revenue"})}),(0,t.jsx)(P.Wu,{children:(0,t.jsx)("div",{className:"space-y-3",children:K.sort((e,s)=>s.totalRevenue-e.totalRevenue).slice(0,5).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-md bg-accent/20",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("span",{className:"text-sm font-medium text-muted-foreground w-4",children:["#",s+1]}),(0,t.jsx)(R.eu,{className:"h-6 w-6",children:(0,t.jsx)(R.q5,{className:"bg-primary/10 text-primary text-xs",children:e.name.split(" ").map(e=>e[0]).join("")})}),(0,t.jsx)("span",{className:"text-sm font-medium text-foreground",children:e.name})]}),(0,t.jsxs)("span",{className:"text-sm font-medium text-foreground",children:["$",e.totalRevenue.toLocaleString()]})]},e.id))})})]}),(0,t.jsxs)(P.Zp,{children:[(0,t.jsx)(P.aR,{children:(0,t.jsx)(P.ZB,{children:"AI Insights"})}),(0,t.jsx)(P.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-3 rounded-md bg-primary/5 border border-primary/20",children:[(0,t.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Outreach Recommendation"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"5 inactive clients haven't been contacted in 90+ days. Consider a follow-up campaign."})]}),(0,t.jsxs)("div",{className:"p-3 rounded-md bg-accent/20",children:[(0,t.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Seasonal Opportunity"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"12 customers have shown interest in shutters. Summer promotion recommended."})]})]})})]})]}),(0,t.jsx)(X,{isOpen:Z,onClose:()=>I(!1),onSave:U})]})}},4516:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4727:(e,s,a)=>{Promise.resolve().then(a.bind(a,4272)),Promise.resolve().then(a.bind(a,871))},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>n});var t=a(5155);a(2115);var r=a(968),l=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},7262:(e,s,a)=>{"use strict";a.d(s,{S:()=>d});var t=a(5155),r=a(2115),l=a(6981),n=a(5196),i=a(9434);let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.bL,{ref:s,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...r,children:(0,t.jsx)(l.C1,{className:(0,i.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})})});d.displayName=l.bL.displayName},7313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>c,j7:()=>i,tU:()=>n});var t=a(5155);a(2115);var r=a(704),l=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",s),...a})}},8539:(e,s,a)=>{"use strict";a.d(s,{T:()=>n});var t=a(5155),r=a(2115),l=a(9434);let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...r})});n.displayName="Textarea"},8883:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9409:(e,s,a)=>{"use strict";a.d(s,{bq:()=>x,eb:()=>j,gC:()=>p,l6:()=>o,yv:()=>m});var t=a(5155),r=a(2115),l=a(8715),n=a(6474),i=a(7863),d=a(5196),c=a(9434);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef((e,s)=>{let{className:a,children:r,...i}=e;return(0,t.jsxs)(l.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...i,children:[r,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.l9.displayName;let h=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=l.PP.displayName;let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})});u.displayName=l.wn.displayName;let p=r.forwardRef((e,s)=>{let{className:a,children:r,position:n="popper",...i}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...i,children:[(0,t.jsx)(h,{}),(0,t.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,t.jsx)(u,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...r})}).displayName=l.JU.displayName;let j=r.forwardRef((e,s)=>{let{className:a,children:r,...n}=e;return(0,t.jsxs)(l.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:r})]})});j.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...r})}).displayName=l.wv.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[418,0,663,205,441,684,358],()=>s(4727)),_N_E=e.O()}]);