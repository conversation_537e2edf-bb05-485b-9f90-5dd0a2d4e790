# New Project Modal - WindowWorks CRM

## Overview

A comprehensive modal component for creating new window treatment installation projects in the WindowWorks CRM system. The modal provides a multi-tab interface for capturing all project details, from basic information to detailed room specifications.

## Features

### 🎯 **Core Functionality**
- **Multi-tab Interface**: Organized into Overview, Rooms, Workflow, and Notes sections
- **Dynamic Room Management**: Add/remove rooms with window specifications
- **Real-time Cost Calculation**: Auto-calculates project costs based on window specs
- **AI Suggestions**: Mock AI feature to auto-fill project details based on customer preferences
- **Form Validation**: Comprehensive validation with error highlighting

### 🏠 **Room & Window Management**
- **Room Templates**: Pre-configured room types (Living Room, Bedroom, etc.)
- **Dynamic Window Addition**: Add/remove windows per room
- **Detailed Specifications**: Width, height, treatment type, material, installation type
- **Feature Selection**: Motorized, blackout, UV protection, and more
- **Cost Calculation**: Automatic cost estimation per window and total project

### 🎨 **Design & UX**
- **Consistent Styling**: Matches WindowWorks brand (amber accents, slate text, white background)
- **Responsive Design**: Works on desktop and mobile devices
- **Smooth Animations**: Framer Motion animations for enhanced UX
- **Accessibility**: ARIA-compliant with keyboard navigation support

### 📊 **Data Management**
- **TypeScript Types**: Fully typed with existing CRM type definitions
- **Form State Management**: Centralized state with validation
- **Integration Ready**: Designed to integrate with Supabase API

## Component Structure

```
NewProjectModal/
├── Overview Tab
│   ├── Basic Information (Title, Description, Priority)
│   ├── Customer Selection (Searchable dropdown)
│   ├── Installer Assignment
│   ├── Timeline (Duration, Start Date)
│   └── Cost Summary (Auto-calculated)
├── Rooms Tab
│   ├── Room Management (Add/Remove)
│   ├── Room Templates
│   └── Window Specifications
│       ├── Dimensions (Width/Height)
│       ├── Treatment Type (Blinds, Shutters, etc.)
│       ├── Materials & Colors
│       ├── Installation Type
│       ├── Features (Checkboxes)
│       └── Individual Cost Calculation
├── Workflow Tab
│   ├── Installer Assignment
│   ├── Timeline Planning
│   └── Project Summary
└── Notes Tab
    └── Free-form Project Notes
```

## Usage

```tsx
import { NewProjectModal } from '@/components/projects/new-project-modal'

// In your component
const [isModalOpen, setIsModalOpen] = useState(false)

const handleCreateProject = async (projectData: ProjectFormData) => {
  // Handle project creation
  await createProject(projectData)
}

// JSX
<NewProjectModal
  isOpen={isModalOpen}
  onClose={() => setIsModalOpen(false)}
  onSave={handleCreateProject}
  customers={customers}
  installers={installers}
/>
```

## Data Flow

1. **Modal Opens**: User clicks "New Project" button
2. **Form Interaction**: User fills out project details across tabs
3. **Real-time Updates**: Costs calculate automatically as windows are added/modified
4. **AI Suggestions**: Optional AI auto-fill based on customer preferences
5. **Validation**: Form validates required fields and data integrity
6. **Submission**: Project data formatted and passed to parent component
7. **API Integration**: Ready for Supabase project creation endpoint

## Key Technical Decisions

### **State Management**
- Single form state object with nested structure matching API expectations
- Centralized validation with field-specific error tracking
- Optimized re-renders with useCallback hooks

### **Cost Calculation Algorithm**
```typescript
// Base cost per square foot by treatment type
const baseCosts = {
  blinds: $15/sqft,
  shutters: $35/sqft,
  shades: $25/sqft,
  curtains: $20/sqft,
  drapes: $30/sqft
}

// Material multipliers
const materialMultipliers = {
  Wood: 1.3x,
  Vinyl: 0.8x,
  Aluminum: 0.9x,
  // etc.
}

// Feature upcharges
$50 per selected feature

Total = (width × height × baseCost × materialMultiplier) + (features × $50)
```

### **Form Validation Strategy**
- Progressive validation (validates as user types)
- Tab-specific error routing (switches to tab with errors)
- Visual error indicators (red borders, error messages)
- Required field enforcement

## Integration Points

### **With Existing CRM**
- Uses existing type definitions from `/types/index.ts`
- Integrates with customer and installer data
- Follows established UI patterns and theme

### **API Ready**
- Form data maps directly to `ProjectFormData` type
- Ready for POST to `/api/projects` endpoint
- Handles async operations with loading states

### **Future Enhancements**
- Real-time customer search via Supabase
- Photo upload for room references
- Material cost database integration
- Installation scheduling integration
- PDF quote generation

## File Structure

```
src/components/projects/
├── new-project-modal.tsx     # Main modal component (1,000+ lines)
├── projects-page.tsx         # Updated with modal integration
└── README.md                # This documentation
```

## Dependencies

- **React**: Component framework
- **Framer Motion**: Animations
- **Lucide React**: Icons
- **Tailwind CSS**: Styling
- **TypeScript**: Type safety
- **Custom UI Components**: Button, Input, Dialog, etc.

## Browser Support

- **Modern browsers**: Chrome, Firefox, Safari, Edge
- **Mobile responsive**: iOS Safari, Chrome Mobile
- **Accessibility**: Screen readers, keyboard navigation

---

*Built for WindowWorks CRM - Window Treatment Installation Management System*
