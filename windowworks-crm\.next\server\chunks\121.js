"use strict";exports.id=121,exports.ids=[121],exports.modules={211:(e,t,r)=>{r.d(t,{C1:()=>C,bL:()=>b});var n=r(3210),o=r(8599),a=r(1273),l=r(569),i=r(5551),s=r(3721),d=r(8853),c=r(6059),u=r(4163),p=r(687),f="Checkbox",[y,h]=(0,a.A)(f),[g,m]=y(f);function x(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:l,form:s,name:d,onCheckedChange:c,required:u,value:y="on",internal_do_not_use_render:h}=e,[m,x]=(0,i.i)({prop:r,defaultProp:a??!1,onChange:c,caller:f}),[v,k]=n.useState(null),[b,j]=n.useState(null),C=n.useRef(!1),D=!v||!!s||!!v.closest("form"),A={checked:m,disabled:l,setChecked:x,control:v,setControl:k,name:d,form:s,value:y,hasConsumerStoppedPropagationRef:C,required:u,defaultChecked:!R(a)&&a,isFormControl:D,bubbleInput:b,setBubbleInput:j};return(0,p.jsx)(g,{scope:t,...A,children:"function"==typeof h?h(A):o})}var v="CheckboxTrigger",k=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...a},i)=>{let{control:s,value:d,disabled:c,checked:f,required:y,setControl:h,setChecked:g,hasConsumerStoppedPropagationRef:x,isFormControl:k,bubbleInput:b}=m(v,e),j=(0,o.s)(i,h),C=n.useRef(f);return n.useEffect(()=>{let e=s?.form;if(e){let t=()=>g(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,g]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":R(f)?"mixed":f,"aria-required":y,"data-state":w(f),"data-disabled":c?"":void 0,disabled:c,value:d,...a,ref:j,onKeyDown:(0,l.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(r,e=>{g(e=>!!R(e)||!e),b&&k&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});k.displayName=v;var b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:l,disabled:i,value:s,onCheckedChange:d,form:c,...u}=e;return(0,p.jsx)(x,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:i,required:l,onCheckedChange:d,name:n,form:c,value:s,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(k,{...u,ref:t,__scopeCheckbox:r}),e&&(0,p.jsx)(A,{__scopeCheckbox:r})]})})});b.displayName=f;var j="CheckboxIndicator",C=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=m(j,r);return(0,p.jsx)(c.C,{present:n||R(a.checked)||!0===a.checked,children:(0,p.jsx)(u.sG.span,{"data-state":w(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=j;var D="CheckboxBubbleInput",A=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:a,hasConsumerStoppedPropagationRef:l,checked:i,defaultChecked:c,required:f,disabled:y,name:h,value:g,form:x,bubbleInput:v,setBubbleInput:k}=m(D,e),b=(0,o.s)(r,k),j=(0,s.Z)(i),C=(0,d.X)(a);n.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(j!==i&&e){let r=new Event("click",{bubbles:t});v.indeterminate=R(i),e.call(v,!R(i)&&i),v.dispatchEvent(r)}},[v,j,i,l]);let A=n.useRef(!R(i)&&i);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??A.current,required:f,disabled:y,name:h,value:g,form:x,...t,tabIndex:-1,ref:b,style:{...t.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function w(e){return R(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=D},1860:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},1862:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3143:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3613:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3661:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},3861:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3928:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},5336:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6134:(e,t,r)=>{r.d(t,{UC:()=>Y,ZL:()=>J,bL:()=>X,bm:()=>et,hE:()=>ee,hJ:()=>Q});var n=r(3210),o=r(569),a=r(8599),l=r(1273),i=r(6963),s=r(5551),d=r(1355),c=r(2547),u=r(5028),p=r(6059),f=r(4163),y=r(1359),h=r(2247),g=r(3376),m=r(8730),x=r(687),v="Dialog",[k,b]=(0,l.A)(v),[j,C]=k(v),D=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:a??!1,onChange:l,caller:v});return(0,x.jsx)(j,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};D.displayName=v;var A="DialogTrigger";n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=C(A,r),i=(0,a.s)(t,l.triggerRef);return(0,x.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":$(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})}).displayName=A;var R="DialogPortal",[w,E]=k(R,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=C(R,t);return(0,x.jsx)(w,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,x.jsx)(p.C,{present:r||l.open,children:(0,x.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};I.displayName=R;var M="DialogOverlay",N=n.forwardRef((e,t)=>{let r=E(M,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(M,e.__scopeDialog);return a.modal?(0,x.jsx)(p.C,{present:n||a.open,children:(0,x.jsx)(_,{...o,ref:t})}):null});N.displayName=M;var O=(0,m.TL)("DialogOverlay.RemoveScroll"),_=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(M,r);return(0,x.jsx)(h.A,{as:O,allowPinchZoom:!0,shards:[o.contentRef],children:(0,x.jsx)(f.sG.div,{"data-state":$(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),F="DialogContent",P=n.forwardRef((e,t)=>{let r=E(F,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(F,e.__scopeDialog);return(0,x.jsx)(p.C,{present:n||a.open,children:a.modal?(0,x.jsx)(q,{...o,ref:t}):(0,x.jsx)(G,{...o,ref:t})})});P.displayName=F;var q=n.forwardRef((e,t)=>{let r=C(F,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,x.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=n.forwardRef((e,t)=>{let r=C(F,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,x.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=C(F,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,y.Oh)(),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,x.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":$(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(K,{titleId:u.titleId}),(0,x.jsx)(U,{contentRef:p,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(T,r);return(0,x.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});B.displayName=T;var H="DialogDescription";n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(H,r);return(0,x.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})}).displayName=H;var S="DialogClose",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C(S,r);return(0,x.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function $(e){return e?"open":"closed"}z.displayName=S;var W="DialogTitleWarning",[Z,V]=(0,l.q)(W,{contentName:F,titleName:T,docsSlug:"dialog"}),K=({titleId:e})=>{let t=V(W),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},U=({contentRef:e,descriptionId:t})=>{let r=V("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},X=D,J=I,Q=N,Y=P,ee=B,et=z},6474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},8233:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};