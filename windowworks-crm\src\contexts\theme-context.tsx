'use client'

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'

type Theme = 'light' | 'dark'

interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  accentColor: string
  setAccentColor: (color: string) => void
  mounted: boolean
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// This function now safely runs on the client and defaults for the server
const getInitialTheme = (): Theme => {
  if (typeof window === 'undefined') {
    return 'light' // Always default to light on the server
  }
  try {
    const savedTheme = localStorage.getItem('windowworks-theme') as Theme
    if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
      return savedTheme
    }
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  } catch {
    return 'light' // Default to light in case of any errors
  }
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Initialize state with a value that is consistent on both server and client
  const [theme, setThemeState] = useState<Theme>('light')
  const [accentColor, setAccentColorState] = useState('#D97706')
  const [mounted, setMounted] = useState(false)

  // Effect to run on client mount
  useEffect(() => {
    setMounted(true)
    // Set the theme from localStorage or system preference
    const initialTheme = getInitialTheme()
    setThemeState(initialTheme)
    
    if (initialTheme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }

    // Set the accent color from localStorage
    const savedAccentColor = localStorage.getItem('windowworks-accent-color')
    if (savedAccentColor) {
      setAccentColorState(savedAccentColor)
    }
  }, [])

  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme)
    try {
      localStorage.setItem('windowworks-theme', newTheme)
      if (newTheme === 'dark') {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    } catch (e) {
      console.error("Failed to set theme in localStorage", e)
    }
  }, [])

  const setAccentColor = useCallback((color: string) => {
    setAccentColorState(color)
    try {
      localStorage.setItem('windowworks-accent-color', color)
      const root = document.documentElement
      // This is a placeholder for a real hex to oklch conversion
      const oklchColor = `oklch(from ${color} l c h)`
      root.style.setProperty('--primary', oklchColor)
      root.style.setProperty('--color-brand-primary', color)
    } catch (e) {
      console.error("Failed to set accent color in localStorage", e)
    }
  }, [])

  const value = { theme, setTheme, accentColor, setAccentColor, mounted }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
