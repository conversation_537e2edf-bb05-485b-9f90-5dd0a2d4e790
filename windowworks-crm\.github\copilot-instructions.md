# WindowWorks CRM - Copilot Instructions

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Overview
WindowWorks CRM is a modern SaaS platform for window treatment installation companies, built with Next.js 14, TypeScript, Tailwind CSS, and Supabase.

## Architecture & Stack
- **Framework**: Next.js 14+ with App Router and Turbopack
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS v4 with Slate color scheme
- **UI Components**: Shadcn/UI with Radix UI primitives
- **Authentication**: Supabase Auth with SSR support
- **Database**: Supabase PostgreSQL (when implemented)
- **State Management**: Zustand for global state
- **Animations**: Framer Motion for micro-interactions
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React

## Code Standards & Patterns

### TypeScript
- Use strict TypeScript with proper types
- Define interfaces for all data structures
- Use proper generics and utility types
- Avoid `any` types - use `unknown` or proper typing

### React Components
- Use functional components with hooks
- Implement proper error boundaries
- Use React.memo for performance optimization
- Follow the composition over inheritance pattern

### Next.js Patterns
- Use App Router exclusively (no Pages Router)
- Implement proper server/client component separation
- Use Server Actions for form submissions
- Implement proper SEO with metadata API

### Styling Guidelines
- Use Tailwind CSS utility classes
- Implement responsive design (mobile-first)
- Use CSS custom properties for dynamic theming
- Follow the established color palette: Slate grays with Amber accents (#D97706)

### File Organization
```
src/
├── app/                    # Next.js App Router pages
├── components/
│   ├── ui/                # Shadcn/UI components
│   ├── auth/              # Authentication components
│   ├── dashboard/         # Dashboard-specific components
│   ├── customers/         # Customer management components
│   └── projects/          # Project management components
├── lib/
│   ├── supabase/          # Supabase client configuration
│   ├── auth/              # Authentication utilities
│   ├── stores/            # Zustand stores
│   └── utils.ts           # Utility functions
├── types/                 # TypeScript type definitions
└── hooks/                 # Custom React hooks
```

## Authentication & Security
- Implement role-based access control (admin, installer, customer)
- Use Supabase RLS (Row Level Security) policies
- Secure API routes with proper authentication checks
- Implement proper session management

## UI/UX Guidelines
- Design for mobile-first, responsive layouts
- Use consistent spacing and typography
- Implement loading states and error handling
- Add smooth micro-interactions with Framer Motion
- Ensure WCAG accessibility compliance
- Use semantic HTML elements

## Performance Optimization
- Implement proper code splitting
- Use Next.js Image component for optimized images
- Implement proper caching strategies
- Use React.lazy for dynamic imports
- Optimize bundle size with tree shaking

## Business Logic
- **Customer Management**: CRUD operations for customer profiles with contact info and preferences
- **Project Management**: Job tracking with statuses, timelines, and installer assignments
- **Installer Workflows**: Mobile-optimized interfaces for job management and status updates
- **Real-time Notifications**: In-app alerts for job assignments and updates

## Component Naming Conventions
- Use PascalCase for component names
- Use descriptive, business-domain names (e.g., `CustomerCard`, `ProjectStatus`, `InstallerDashboard`)
- Prefix UI components with the domain (e.g., `DashboardHeader`, `AuthForm`)

## Error Handling
- Implement proper error boundaries
- Use try-catch blocks for async operations
- Provide user-friendly error messages
- Log errors appropriately for debugging

## Testing Strategy
- Write unit tests for utility functions
- Implement integration tests for critical user flows
- Use accessibility testing tools
- Test responsive design across devices

When generating code, always consider:
1. Type safety and proper TypeScript usage
2. Accessibility and semantic HTML
3. Performance and optimization
4. Mobile-first responsive design
5. Business domain context (window treatment installation)
6. Security best practices
7. Modern React and Next.js patterns
