(()=>{var e={};e.id=662,e.ids=[662],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1550:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3143:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(2688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3955:(e,s,a)=>{Promise.resolve().then(a.bind(a,8054)),Promise.resolve().then(a.bind(a,8805))},4198:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var r=a(7413),t=a(8054),i=a(8805);function n(){return(0,r.jsx)(t.DashboardLayout,{children:(0,r.jsx)(i.default,{})})}},4987:(e,s,a)=>{"use strict";a.d(s,{d:()=>n});var r=a(687),t=a(3210),i=a(4780);let n=t.forwardRef(({className:e,checked:s=!1,onCheckedChange:a,disabled:t=!1,id:n,...l},c)=>(0,r.jsx)("button",{ref:c,type:"button",role:"switch","aria-checked":s,disabled:t,onClick:()=>{!t&&a&&a(!s)},id:n,className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-sm border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",s?"bg-primary":"bg-input",e),...l,children:(0,r.jsx)("span",{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded bg-background shadow-lg ring-0 transition-transform",s?"translate-x-5":"translate-x-0")})}));n.displayName="Switch"},5946:(e,s,a)=>{"use strict";a.d(s,{default:()=>et});var r=a(687),t=a(3210),i=a(6001),n=a(8920),l=a(7992),c=a(1550),d=a(228),o=a(8819),m=a(8869),x=a(4027),h=a(2688);let p=(0,h.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),u=(0,h.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),j=(0,h.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),y=(0,h.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),g=(0,h.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),v=(0,h.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var f=a(3861);let N=(0,h.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),b=(0,h.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);var w=a(3026),k=a(3143),A=a(1158),C=a(6349),P=a(83);let M=(0,h.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),R=(0,h.A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);var S=a(8233);let T=(0,h.A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),_=(0,h.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var Z=a(9523),E=a(9667),D=a(13),q=a(4729),z=a(4493),J=a(6834),G=a(2584),L=a(4987),F=a(5763),I=a(5079),$=a(4780);let B=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:(0,$.cn)("w-full caption-bottom text-sm",e),...s})}));B.displayName="Table";let W=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("thead",{ref:a,className:(0,$.cn)("[&_tr]:border-b",e),...s}));W.displayName="TableHeader";let U=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("tbody",{ref:a,className:(0,$.cn)("[&_tr:last-child]:border-0",e),...s}));U.displayName="TableBody",t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("tfoot",{ref:a,className:(0,$.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let V=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("tr",{ref:a,className:(0,$.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));V.displayName="TableRow";let H=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("th",{ref:a,className:(0,$.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));H.displayName="TableHead";let X=t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("td",{ref:a,className:(0,$.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));X.displayName="TableCell",t.forwardRef(({className:e,...s},a)=>(0,r.jsx)("caption",{ref:a,className:(0,$.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption";var K=a(1342),O=a(2587);let Q={id:"user_001",name:"John Admin",email:"<EMAIL>",phone:"(*************",bio:"Operations manager with 5+ years in window treatment industry.",avatar:null,role:"Admin"},Y={darkMode:!1,emailNotifications:!0,smsAlerts:!1,language:"en",timezone:"America/Chicago",customAccentColor:"#D97706"},ee=[{id:"google-maps",name:"Google Maps",description:"Get directions and location data for customer addresses",status:"connected",icon:l.A,connectionType:"oauth"},{id:"email-provider",name:"Email Provider",description:"Send automated emails and notifications",status:"disconnected",icon:c.A,connectionType:"api-key",apiKey:""},{id:"calendar-sync",name:"Calendar Sync",description:"Sync appointments with external calendars",status:"connected",icon:d.A,connectionType:"toggle"}],es=[{id:"team_001",name:"John Admin",email:"<EMAIL>",role:"Admin",status:"Active",lastActive:"2025-07-12T14:30:00Z"},{id:"team_002",name:"Sarah Manager",email:"<EMAIL>",role:"Manager",status:"Active",lastActive:"2025-07-12T12:15:00Z"},{id:"team_003",name:"Mike Installer",email:"<EMAIL>",role:"Installer",status:"Active",lastActive:"2025-07-12T09:45:00Z"},{id:"team_004",name:"Lisa Anderson",email:"<EMAIL>",role:"Installer",status:"Inactive",lastActive:"2025-07-10T16:20:00Z"}],ea=[{id:"audit_001",action:"Project Created",user:"John Admin",timestamp:"2025-07-12T14:30:00Z",details:'Created project "Living Room Shutters" for Sarah Johnson'},{id:"audit_002",action:"User Invited",user:"John Admin",timestamp:"2025-07-12T13:15:00Z",details:"Invited new installer: <EMAIL>"},{id:"audit_003",action:"Settings Updated",user:"Sarah Manager",timestamp:"2025-07-12T11:20:00Z",details:"Updated notification preferences"}],er=[{id:"session_001",device:"Chrome on Windows",location:"Chicago, IL",lastActive:"2025-07-12T14:30:00Z",current:!0},{id:"session_002",device:"Safari on iPhone",location:"Chicago, IL",lastActive:"2025-07-12T09:15:00Z",current:!1}];function et(){let{theme:e,setTheme:s,accentColor:a,setAccentColor:l}=(0,O.D)(),[c,d]=(0,t.useState)("profile"),[h,$]=(0,t.useState)(Q),[et,ei]=(0,t.useState)({...Y,darkMode:!1,customAccentColor:"#D97706"}),[en,el]=(0,t.useState)(ee),[ec,ed]=(0,t.useState)(!1),[eo,em]=(0,t.useState)(!1),[ex,eh]=(0,t.useState)(!1),[ep,eu]=(0,t.useState)(!1),ej=(0,t.useRef)(null),ey=(0,t.useId)(),eg=(0,t.useId)(),ev=(0,t.useId)(),ef=(0,t.useId)(),eN=(e,s)=>{$(a=>({...a,[e]:s})),ed(!0)},eb=(e,a)=>{"darkMode"===e?s(a?"dark":"light"):"customAccentColor"===e&&l(a),ei(s=>({...s,[e]:a})),ed(!0)},ew=e=>{if(e&&e[0]){let s=e[0],a=new FileReader;a.onload=e=>{eN("avatar",e.target?.result)},a.readAsDataURL(s)}},ek=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?eh(!0):"dragleave"===e.type&&eh(!1)},eA=e=>{switch(e.toLowerCase()){case"active":case"connected":return(0,r.jsx)(J.E,{className:"bg-green-500/10 text-green-600 border-green-500/20",children:"Active"});case"inactive":case"disconnected":return(0,r.jsx)(J.E,{variant:"secondary",children:"Inactive"});case"pending":return(0,r.jsx)(J.E,{className:"bg-yellow-500/10 text-yellow-600 border-yellow-500/20",children:"Pending"});case"error":return(0,r.jsx)(J.E,{variant:"destructive",children:"Error"});default:return(0,r.jsx)(J.E,{variant:"outline",children:e})}},eC=e=>{switch(e.toLowerCase()){case"admin":return"bg-primary/10 text-primary border-primary/20";case"manager":return"bg-blue-500/10 text-blue-600 border-blue-500/20";case"installer":return"bg-green-500/10 text-green-600 border-green-500/20";default:return"bg-gray-500/10 text-gray-600 border-gray-500/20"}},eP=e=>new Date(e).toLocaleString();return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Settings"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Customize your profile, preferences, and platform integrations"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(J.E,{variant:"secondary",className:"text-xs",children:"Saturday, July 12, 2025"}),(0,r.jsxs)(Z.$,{className:"bg-primary hover:bg-primary/90",disabled:!ec,onClick:()=>{console.log("Saving changes...",{profile:h,preferences:et}),ed(!1)},children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]})]})]}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,r.jsxs)(F.tU,{value:c,onValueChange:d,className:"w-full",children:[(0,r.jsxs)(F.j7,{className:"grid grid-cols-5 w-full max-w-2xl",children:[(0,r.jsxs)(F.Xi,{value:"profile",className:"flex items-center space-x-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Profile"})]}),(0,r.jsxs)(F.Xi,{value:"preferences",className:"flex items-center space-x-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Preferences"})]}),(0,r.jsxs)(F.Xi,{value:"integrations",className:"flex items-center space-x-2",children:[(0,r.jsx)(p,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Integrations"})]}),(0,r.jsxs)(F.Xi,{value:"admin",className:"flex items-center space-x-2",children:[(0,r.jsx)(u,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Admin"})]}),(0,r.jsxs)(F.Xi,{value:"security",className:"flex items-center space-x-2",children:[(0,r.jsx)(j,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Security"})]})]}),(0,r.jsxs)("div",{className:"mt-6 grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsxs)(n.N,{mode:"wait",children:[(0,r.jsx)(F.av,{value:"profile",className:"mt-0",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[(0,r.jsxs)(z.Zp,{children:[(0,r.jsx)(z.aR,{children:(0,r.jsx)(z.ZB,{children:"Profile Information"})}),(0,r.jsxs)(z.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(G.eu,{className:"h-24 w-24",children:[(0,r.jsx)(G.BK,{src:h.avatar||void 0}),(0,r.jsx)(G.q5,{className:"bg-primary/10 text-primary text-xl",children:h.name.split(" ").map(e=>e[0]).join("")})]}),(0,r.jsx)("button",{onClick:()=>ej.current?.click(),className:"absolute -bottom-2 -right-2 bg-primary text-white rounded-full p-2 hover:bg-primary/90 transition-colors",children:(0,r.jsx)(y,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${ex?"border-primary bg-primary/5":"border-border"}`,onDragEnter:ek,onDragLeave:ek,onDragOver:ek,onDrop:e=>{e.preventDefault(),e.stopPropagation(),eh(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&ew(e.dataTransfer.files)},children:[(0,r.jsx)(g,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Drag and drop an image, or"," ",(0,r.jsx)("button",{onClick:()=>ej.current?.click(),className:"text-primary hover:underline",children:"browse"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"JPG, PNG up to 2MB"})]}),(0,r.jsx)("input",{ref:ej,type:"file",accept:"image/*",onChange:e=>ew(e.target.files),className:"hidden"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"name",children:"Full Name"}),(0,r.jsx)(E.p,{id:"name",value:h.name,onChange:e=>eN("name",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"email",children:"Email Address"}),(0,r.jsx)(E.p,{id:"email",type:"email",value:h.email,onChange:e=>eN("email",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"phone",children:"Phone Number"}),(0,r.jsx)(E.p,{id:"phone",value:h.phone,onChange:e=>eN("phone",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"role",children:"Role"}),(0,r.jsx)("div",{className:"pt-2",children:(0,r.jsx)(J.E,{className:eC(h.role),children:h.role})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"bio",children:"Bio"}),(0,r.jsx)(q.T,{id:"bio",value:h.bio,onChange:e=>eN("bio",e.target.value),rows:3,placeholder:"Tell us about yourself..."})]})]})]}),(0,r.jsxs)(z.Zp,{children:[(0,r.jsx)(z.aR,{children:(0,r.jsx)(z.ZB,{children:"Change Password"})}),(0,r.jsxs)(z.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"current-password",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(E.p,{id:"current-password",type:eo?"text":"password",placeholder:"Enter current password"}),(0,r.jsx)("button",{type:"button",onClick:()=>em(!eo),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground",children:eo?(0,r.jsx)(v,{className:"h-4 w-4"}):(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"new-password",children:"New Password"}),(0,r.jsx)(E.p,{id:"new-password",type:"password",placeholder:"Enter new password"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{htmlFor:"confirm-password",children:"Confirm Password"}),(0,r.jsx)(E.p,{id:"confirm-password",type:"password",placeholder:"Confirm new password"})]})]}),(0,r.jsx)(Z.$,{variant:"outline",children:"Update Password"})]})]})]})},"profile"),(0,r.jsx)(F.av,{value:"preferences",className:"mt-0",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[(0,r.jsxs)(z.Zp,{children:[(0,r.jsx)(z.aR,{children:(0,r.jsx)(z.ZB,{children:"Appearance"})}),(0,r.jsxs)(z.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(D.J,{htmlFor:ey,className:"text-base",children:"Dark Mode"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Toggle between light and dark themes"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(N,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)(L.d,{id:ey,checked:et.darkMode,onCheckedChange:e=>eb("darkMode",e),className:"rounded-sm [&_span]:rounded"}),(0,r.jsx)(b,{className:"h-4 w-4 text-muted-foreground"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{children:"Custom Accent Color"}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("input",{type:"color",value:et.customAccentColor,onChange:e=>eb("customAccentColor",e.target.value),className:"w-12 h-12 rounded border border-border cursor-pointer"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Preview"}),(0,r.jsx)("div",{className:"w-24 h-8 rounded border border-border",style:{backgroundColor:et.customAccentColor}})]})]})]})]})]}),(0,r.jsxs)(z.Zp,{children:[(0,r.jsx)(z.aR,{children:(0,r.jsx)(z.ZB,{children:"Notifications"})}),(0,r.jsxs)(z.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(D.J,{htmlFor:eg,className:"text-base",children:"Email Notifications"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive updates about projects and appointments"})]}),(0,r.jsx)(L.d,{id:eg,checked:et.emailNotifications,onCheckedChange:e=>eb("emailNotifications",e),className:"rounded-sm [&_span]:rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(D.J,{htmlFor:ev,className:"text-base",children:"SMS Alerts"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Get text messages for urgent updates"})]}),(0,r.jsx)(L.d,{id:ev,checked:et.smsAlerts,onCheckedChange:e=>eb("smsAlerts",e),className:"rounded-sm [&_span]:rounded"})]})]})]}),(0,r.jsxs)(z.Zp,{children:[(0,r.jsx)(z.aR,{children:(0,r.jsx)(z.ZB,{children:"Localization"})}),(0,r.jsx)(z.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{children:"Language"}),(0,r.jsxs)(I.l6,{value:et.language,onValueChange:e=>eb("language",e),children:[(0,r.jsx)(I.bq,{children:(0,r.jsx)(I.yv,{})}),(0,r.jsxs)(I.gC,{children:[(0,r.jsx)(I.eb,{value:"en",children:"English"}),(0,r.jsx)(I.eb,{value:"es",children:"Espa\xf1ol"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{children:"Timezone"}),(0,r.jsxs)(I.l6,{value:et.timezone,onValueChange:e=>eb("timezone",e),children:[(0,r.jsx)(I.bq,{children:(0,r.jsx)(I.yv,{})}),(0,r.jsxs)(I.gC,{children:[(0,r.jsx)(I.eb,{value:"America/Chicago",children:"Central Time"}),(0,r.jsx)(I.eb,{value:"America/New_York",children:"Eastern Time"}),(0,r.jsx)(I.eb,{value:"America/Los_Angeles",children:"Pacific Time"}),(0,r.jsx)(I.eb,{value:"America/Denver",children:"Mountain Time"})]})]})]})]})})]})]})},"preferences"),(0,r.jsx)(F.av,{value:"integrations",className:"mt-0",children:(0,r.jsx)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:en.map(e=>{let s=e.icon;return(0,r.jsxs)(z.Zp,{children:[(0,r.jsx)(z.aR,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-md bg-primary/10",children:(0,r.jsx)(s,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(z.ZB,{className:"text-lg",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]})]}),eA(e.status)]})}),(0,r.jsxs)(z.Wu,{children:["oauth"===e.connectionType&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(Z.$,{variant:"connected"===e.status?"outline":"default",className:"connected"===e.status?"":"bg-primary hover:bg-primary/90",children:"connected"===e.status?"Disconnect":"Connect with OAuth"}),"connected"===e.status&&(0,r.jsx)(Z.$,{variant:"outline",size:"sm",children:"Test Connection"})]}),"api-key"===e.connectionType&&(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(E.p,{placeholder:"Enter API key",type:"password",value:e.apiKey||"",onChange:s=>{el(en.map(a=>a.id===e.id?{...a,apiKey:s.target.value}:a)),ed(!0)}}),(0,r.jsx)(Z.$,{variant:"outline",children:"Test"})]})}),"toggle"===e.connectionType&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(D.J,{htmlFor:`${e.name.toLowerCase().replace(" ","-")}-sync`,className:"text-sm text-muted-foreground",children:["Enable ",e.name," synchronization"]}),(0,r.jsx)(L.d,{id:`${e.name.toLowerCase().replace(" ","-")}-sync`,checked:"connected"===e.status,className:"rounded-sm [&_span]:rounded"})]})]})]},e.id)})})},"integrations"),(0,r.jsx)(F.av,{value:"admin",className:"mt-0",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[(0,r.jsxs)(z.Zp,{children:[(0,r.jsxs)(z.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsx)(z.ZB,{children:"Team Management"}),(0,r.jsxs)(Z.$,{className:"bg-primary hover:bg-primary/90",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Invite User"]})]}),(0,r.jsx)(z.Wu,{children:(0,r.jsxs)(B,{children:[(0,r.jsx)(W,{children:(0,r.jsxs)(V,{children:[(0,r.jsx)(H,{children:"User"}),(0,r.jsx)(H,{children:"Role"}),(0,r.jsx)(H,{children:"Status"}),(0,r.jsx)(H,{children:"Last Active"}),(0,r.jsx)(H,{children:"Actions"})]})}),(0,r.jsx)(U,{children:es.map(e=>(0,r.jsxs)(V,{children:[(0,r.jsx)(X,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(G.eu,{className:"h-8 w-8",children:(0,r.jsx)(G.q5,{className:"bg-primary/10 text-primary text-xs",children:e.name.split(" ").map(e=>e[0]).join("")})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email})]})]})}),(0,r.jsx)(X,{children:(0,r.jsx)(J.E,{className:eC(e.role),children:e.role})}),(0,r.jsx)(X,{children:eA(e.status)}),(0,r.jsx)(X,{className:"text-sm text-muted-foreground",children:eP(e.lastActive)}),(0,r.jsx)(X,{children:(0,r.jsxs)(K.rI,{children:[(0,r.jsx)(K.ty,{asChild:!0,children:(0,r.jsx)(Z.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(k.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(K.SQ,{children:[(0,r.jsx)(K._2,{children:"Edit User"}),(0,r.jsx)(K._2,{children:"Change Role"}),(0,r.jsx)(K._2,{className:"text-destructive",children:"Remove User"})]})]})})]},e.id))})]})})]}),(0,r.jsxs)(z.Zp,{children:[(0,r.jsxs)(z.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsx)(z.ZB,{children:"Audit Logs"}),(0,r.jsxs)(Z.$,{variant:"outline",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Export Logs"]})]}),(0,r.jsx)(z.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:ea.map(e=>(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-border",children:[(0,r.jsx)("div",{className:"p-1 rounded-full bg-primary/10 mt-1",children:(0,r.jsx)(C.A,{className:"h-3 w-3 text-primary"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-medium",children:e.action}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:eP(e.timestamp)})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.details}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["by ",e.user]})]})]},e.id))})})]}),(0,r.jsxs)(z.Zp,{children:[(0,r.jsx)(z.aR,{children:(0,r.jsx)(z.ZB,{children:"Data Export"})}),(0,r.jsx)(z.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(Z.$,{variant:"outline",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Export as CSV"]}),(0,r.jsxs)(Z.$,{variant:"outline",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Export as JSON"]})]})})]})]})},"admin"),(0,r.jsx)(F.av,{value:"security",className:"mt-0",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[(0,r.jsxs)(z.Zp,{children:[(0,r.jsx)(z.aR,{children:(0,r.jsx)(z.ZB,{children:"Two-Factor Authentication"})}),(0,r.jsxs)(z.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(D.J,{htmlFor:ef,className:"text-base",children:"Enable 2FA"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add an extra layer of security to your account"})]}),(0,r.jsx)(L.d,{id:ef,checked:ep,onCheckedChange:eu,className:"rounded-sm [&_span]:rounded"})]}),ep&&(0,r.jsxs)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"border border-border rounded-lg p-4 space-y-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Scan QR Code"}),(0,r.jsx)("div",{className:"w-32 h-32 bg-muted rounded-lg flex items-center justify-center",children:(0,r.jsxs)("p",{className:"text-xs text-muted-foreground text-center",children:["QR Code",(0,r.jsx)("br",{}),"Placeholder"]})}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Scan this QR code with your authenticator app"})]})]})]}),(0,r.jsxs)(z.Zp,{children:[(0,r.jsxs)(z.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsx)(z.ZB,{children:"Active Sessions"}),(0,r.jsxs)(Z.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Logout All"]})]}),(0,r.jsx)(z.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:er.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border border-border",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-md bg-primary/10",children:e.device.includes("iPhone")?(0,r.jsx)(M,{className:"h-4 w-4 text-primary"}):(0,r.jsx)(R,{className:"h-4 w-4 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.device}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.location," • ",eP(e.lastActive)]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.current&&(0,r.jsx)(J.E,{className:"bg-green-500/10 text-green-600 border-green-500/20",children:"Current"}),!e.current&&(0,r.jsx)(Z.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(P.A,{className:"h-4 w-4"})})]})]},e.id))})})]}),(0,r.jsxs)(z.Zp,{children:[(0,r.jsx)(z.aR,{children:(0,r.jsx)(z.ZB,{children:"Privacy Settings"})}),(0,r.jsxs)(z.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(D.J,{children:"Data Retention Period"}),(0,r.jsxs)(I.l6,{defaultValue:"12",children:[(0,r.jsx)(I.bq,{children:(0,r.jsx)(I.yv,{})}),(0,r.jsxs)(I.gC,{children:[(0,r.jsx)(I.eb,{value:"6",children:"6 months"}),(0,r.jsx)(I.eb,{value:"12",children:"12 months"}),(0,r.jsx)(I.eb,{value:"24",children:"24 months"}),(0,r.jsx)(I.eb,{value:"60",children:"5 years"})]})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"How long to keep deleted data before permanent removal"})]}),(0,r.jsxs)(Z.$,{variant:"outline",className:"text-destructive border-destructive hover:bg-destructive/10",children:[(0,r.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Delete All My Data"]})]})]})]})},"security")]})}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)(z.Zp,{className:"sticky top-6",children:[(0,r.jsx)(z.aR,{children:(0,r.jsxs)(z.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(T,{className:"h-5 w-5 text-primary"}),(0,r.jsx)("span",{children:"Quick Tips"})]})}),(0,r.jsxs)(z.Wu,{className:"space-y-4",children:["profile"===c&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-primary/5 border border-primary/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Profile Photo"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"A professional photo helps build trust with customers"})]}),(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-blue-500/5 border border-blue-500/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Strong Password"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Use at least 12 characters with mixed case and symbols"})]})]},"profile-tips"),"preferences"===c&&(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-primary/5 border border-primary/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Notifications"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Enable SMS for urgent project updates"})]})},"preferences-tips"),"integrations"===c&&(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-primary/5 border border-primary/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"Google Maps"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Connect to get automatic driving directions to appointments"})]})},"integrations-tips"),"security"===c&&(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-primary/5 border border-primary/20",children:[(0,r.jsx)("p",{className:"text-sm text-foreground font-medium mb-1",children:"2FA Required"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Admin accounts must have two-factor authentication enabled"})]})},"security-tips"),(0,r.jsx)("div",{className:"pt-4 border-t border-border",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Need help with integrations?"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(E.p,{placeholder:"Ask here...",className:"text-sm"}),(0,r.jsx)(Z.$,{size:"sm",variant:"outline",children:"Ask"})]})]})})]})]})})]})]})}),(0,r.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"flex items-center justify-between pt-6 border-t border-border",children:[(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"WindowWorks CRM v2.1.0"}),(0,r.jsxs)(Z.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(_,{className:"h-4 w-4 mr-2"}),"Reset to Defaults"]})]})]})}},7992:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8233:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8795:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var r=a(5239),t=a(8088),i=a(8170),n=a.n(i),l=a(893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(s,c);let d={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4198)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\settings\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\app\\settings\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8805:(e,s,a)=>{"use strict";a.d(s,{default:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Source Code\\\\GoogleGemini\\\\windowworks-crm\\\\src\\\\components\\\\settings\\\\settings-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\components\\settings\\settings-page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9611:(e,s,a)=>{Promise.resolve().then(a.bind(a,7634)),Promise.resolve().then(a.bind(a,5946))}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[447,145,861,979,171,556],()=>a(8795));module.exports=r})();