'use client'

import { ReactNode } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Bell, 
  Home, 
  Users, 
  FolderOpen, 
  Calendar,
  Settings,
  LogOut,
  Menu,
  Search
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useNotificationStore } from '@/lib/stores/notification-store'

interface DashboardLayoutProps {
  children: ReactNode
}

const navigationItems = [
  { icon: Home, label: 'Dashboard', href: '/', badge: null },
  { icon: Users, label: 'Customers', href: '/customers', badge: null },
  { icon: FolderOpen, label: 'Projects', href: '/projects', badge: null },
  { icon: Calendar, label: 'Schedule', href: '/schedule', badge: null },
  { icon: Settings, label: 'Settings', href: '/settings', badge: null },
]

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user, clearAuth } = useAuthStore()
  const { unreadCount } = useNotificationStore()
  const pathname = usePathname()

  const handleLogout = () => {
    clearAuth()
    // In a real app, you'd also call Supabase signOut here
  }

  return (
    <div className="h-screen bg-background flex overflow-hidden">
      {/* Sidebar */}
      <motion.aside
        initial={{ x: -300 }}
        animate={{ x: 0 }}
        className="w-64 bg-card border-r border-border flex flex-col fixed left-0 top-0 h-full z-30"
      >
        {/* Logo */}
        <div className="p-6 border-b border-border flex-shrink-0">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="flex items-center space-x-3"
          >
            <div className="w-8 h-8 bg-[var(--color-brand-primary)] rounded-md flex items-center justify-center">
              <span className="text-white font-bold text-sm">W</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-foreground">WindowWorks</h1>
              <p className="text-xs text-muted-foreground">CRM Platform</p>
            </div>
          </motion.div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {navigationItems.map((item, index) => {
            const isActive = pathname === item.href
            return (
              <motion.div
                key={item.href}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 * (index + 1) }}
              >
                <Link href={item.href}>
                  <Button
                    variant="ghost"
                    className={`w-full justify-start rounded-md ${
                      isActive 
                        ? 'bg-primary/10 text-primary border-primary/20' 
                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                    }`}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    {item.label}
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto">
                        {item.badge}
                      </Badge>
                    )}
                  </Button>
                </Link>
              </motion.div>
            )
          })}
        </nav>

        {/* User Profile - Sticky at bottom */}
        <div className="p-4 border-t border-border flex-shrink-0 sticky bottom-0 bg-card">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="w-full justify-start p-3 hover:bg-accent">
                <Avatar className="h-8 w-8 mr-3">
                  <AvatarImage src={user?.profile?.avatar} />
                  <AvatarFallback className="bg-primary/10 text-primary">
                    {user?.profile?.firstName?.[0]}{user?.profile?.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
                <div className="text-left">
                  <p className="text-sm font-medium text-foreground">
                    {user?.profile?.firstName} {user?.profile?.lastName}
                  </p>
                  <p className="text-xs text-muted-foreground capitalize">{user?.role}</p>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                Account Settings
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </motion.aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-background ml-64">
        {/* Top Header - Sticky */}
        <motion.header
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="bg-card border-b border-border px-6 py-4 sticky top-0 z-20 flex-shrink-0"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" className="md:hidden">
                <Menu className="h-5 w-5" />
              </Button>
              
              {/* Search */}
              <div className="relative w-96 hidden md:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search customers, projects..."
                  className="pl-10 bg-accent/50 border-border focus:bg-background"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 h-5 w-5 bg-destructive rounded-full flex items-center justify-center"
                  >
                    <span className="text-xs text-destructive-foreground font-medium">
                      {unreadCount > 9 ? '9+' : unreadCount}
                    </span>
                  </motion.div>
                )}
              </Button>


            </div>
          </div>
        </motion.header>

        {/* Page Content - Scrollable */}
        <main className="flex-1 overflow-y-auto bg-background">
          <div className="p-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {children}
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  )
}
