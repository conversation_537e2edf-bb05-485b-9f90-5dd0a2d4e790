'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Calendar as CalendarIcon, 
  User, 
  MapPin, 
  Zap,
  Save,
  Users,
  Building,
  FileText,
  Bell,
  Paperclip
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

interface Project {
  id: string
  name: string
  customer: string
  address: string
  productTypes: string[]
  status: string
}

interface Installer {
  id: string
  name: string
  avatar: string | null
  availability: 'available' | 'busy' | 'unavailable'
  utilization: number
}

interface Customer {
  id: string
  name: string
  email: string
  phone: string
  address: string
}

interface AppointmentData {
  id: string
  projectId: string
  customerId: string
  date: string
  startTime: string
  endTime: string
  duration: number
  installerId: string
  location: string
  productTypes: string[]
  notes: string
  estimatedDuration: number
  sendNotification: boolean
  autoReminder: boolean
  smsReminder: boolean
  emailReminder: boolean
}

interface NewAppointmentModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (appointment: AppointmentData) => void
  selectedDate?: Date
  selectedTime?: string
  projects?: Project[]
  installers?: Installer[]
  customers?: Customer[]
}

export function NewAppointmentModal({
  isOpen,
  onClose,
  onSave,
  selectedDate,
  selectedTime,
  projects = [],
  installers = [],
  customers = []
}: NewAppointmentModalProps) {
  const [activeTab, setActiveTab] = useState('details')
  const [isOptimizing, setIsOptimizing] = useState(false)

  const [formData, setFormData] = useState<Partial<AppointmentData>>({
    projectId: '',
    customerId: '',
    date: selectedDate?.toISOString().split('T')[0] || '',
    startTime: selectedTime || '09:00',
    endTime: '12:00',
    duration: 180,
    installerId: '',
    location: '',
    productTypes: [],
    notes: '',
    estimatedDuration: 180,
    sendNotification: true,
    autoReminder: true,
    smsReminder: false,
    emailReminder: true
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.projectId) newErrors.projectId = 'Project is required'
    if (!formData.date) newErrors.date = 'Date is required'
    if (!formData.startTime) newErrors.startTime = 'Start time is required'
    if (!formData.endTime) newErrors.endTime = 'End time is required'
    if (!formData.installerId) newErrors.installerId = 'Installer is required'
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (!validateForm()) {
      setActiveTab('details')
      return
    }

    const appointment: AppointmentData = {
      ...formData,
      id: `appt_${Date.now()}`,
    } as AppointmentData

    onSave(appointment)
    onClose()
  }

  const handleOptimizeSlot = async () => {
    setIsOptimizing(true)
    
    // Simulate AI optimization
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mock optimization result
    setFormData(prev => ({
      ...prev,
      startTime: '14:00',
      endTime: '16:30',
      installerId: installers.find(i => i.availability === 'available')?.id || prev.installerId
    }))
    
    setIsOptimizing(false)
  }

  const selectedProject = projects.find(p => p.id === formData.projectId)
  const selectedInstaller = installers.find(i => i.id === formData.installerId)

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'available': return 'bg-green-500'
      case 'busy': return 'bg-amber-500'
      case 'unavailable': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const productTypeOptions = [
    'Windows', 'Blinds', 'Shutters', 'Curtains', 'Shades', 
    'Awnings', 'Security Screens', 'Repair', 'Maintenance'
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="px-6 py-4 border-b border-gray-200">
          <DialogTitle className="text-xl font-bold text-slate-700">
            Schedule New Appointment
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-4 w-full px-6 py-2">
              <TabsTrigger value="details" className="flex items-center space-x-2">
                <CalendarIcon className="h-4 w-4" />
                <span>Details</span>
              </TabsTrigger>
              <TabsTrigger value="workflow" className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Team</span>
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center space-x-2">
                <Bell className="h-4 w-4" />
                <span>Notifications</span>
              </TabsTrigger>
              <TabsTrigger value="attachments" className="flex items-center space-x-2">
                <Paperclip className="h-4 w-4" />
                <span>Files</span>
              </TabsTrigger>
            </TabsList>

            <div className="p-6">
              <TabsContent value="details" className="space-y-6 mt-0">
                {/* Project Selection */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Project *
                  </Label>
                  <Select
                    value={formData.projectId}
                    onValueChange={(value) => {
                      const project = projects.find(p => p.id === value)
                      setFormData(prev => ({ 
                        ...prev, 
                        projectId: value,
                        location: project?.address || ''
                      }))
                    }}
                  >
                    <SelectTrigger className={errors.projectId ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select project..." />
                    </SelectTrigger>
                    <SelectContent>
                      {projects.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{project.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {project.customer} • {project.address}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.projectId && <p className="text-sm text-red-500">{errors.projectId}</p>}
                  
                  {selectedProject && (
                    <Card className="mt-3">
                      <CardContent className="pt-4">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{selectedProject.customer}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{selectedProject.address}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <div className="flex flex-wrap gap-1">
                              {selectedProject.productTypes.map((type) => (
                                <Badge key={type} variant="secondary" className="text-xs">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* Date and Time */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      Date *
                    </Label>
                    <Input
                      type="date"
                      value={formData.date}
                      onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                      className={errors.date ? 'border-red-500' : ''}
                    />
                    {errors.date && <p className="text-sm text-red-500">{errors.date}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      Time Range *
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="time"
                        value={formData.startTime}
                        onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                        className={errors.startTime ? 'border-red-500' : ''}
                      />
                      <Input
                        type="time"
                        value={formData.endTime}
                        onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                        className={errors.endTime ? 'border-red-500' : ''}
                      />
                    </div>
                    {(errors.startTime || errors.endTime) && (
                      <p className="text-sm text-red-500">Start and end times are required</p>
                    )}
                  </div>
                </div>

                {/* Estimated Duration */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Estimated Duration: {formData.estimatedDuration} minutes
                  </Label>
                  <div className="flex items-center space-x-4">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setFormData(prev => ({ 
                        ...prev, 
                        estimatedDuration: Math.max(30, (prev.estimatedDuration || 180) - 15)
                      }))}
                    >
                      -15m
                    </Button>
                    <Input
                      type="number"
                      min="30"
                      max="480"
                      step="15"
                      value={formData.estimatedDuration}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        estimatedDuration: parseInt(e.target.value) || 180
                      }))}
                      className="text-center"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setFormData(prev => ({ 
                        ...prev, 
                        estimatedDuration: Math.min(480, (prev.estimatedDuration || 180) + 15)
                      }))}
                    >
                      +15m
                    </Button>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>30 min minimum</span>
                    <span>8 hours maximum</span>
                  </div>
                </div>

                {/* Product Types */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Product Types</Label>
                  <div className="flex flex-wrap gap-2">
                    {productTypeOptions.map((type) => (
                      <Button
                        key={type}
                        variant={formData.productTypes?.includes(type) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          const currentTypes = formData.productTypes || []
                          const newTypes = currentTypes.includes(type)
                            ? currentTypes.filter(t => t !== type)
                            : [...currentTypes, type]
                          setFormData(prev => ({ ...prev, productTypes: newTypes }))
                        }}
                      >
                        {type}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Location */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Location</Label>
                  <Input
                    placeholder="Enter appointment location..."
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  />
                </div>

                {/* AI Optimization */}
                <Card className="bg-amber-50 border-amber-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-amber-100 rounded-full">
                          <Zap className="h-4 w-4 text-amber-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-slate-700">AI Schedule Optimization</p>
                          <p className="text-xs text-slate-600">
                            Find the optimal time slot based on installer availability and location
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleOptimizeSlot}
                        disabled={isOptimizing}
                        className="bg-amber-500 text-white hover:bg-amber-600 border-amber-500"
                      >
                        {isOptimizing ? (
                          <motion.div
                            className="h-3 w-3 border-2 border-white border-t-transparent rounded-full"
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          />
                        ) : (
                          <>
                            <Zap className="h-3 w-3 mr-1" />
                            Optimize
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="workflow" className="space-y-6 mt-0">
                {/* Installer Assignment */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Assign Installer *
                  </Label>
                  <Select
                    value={formData.installerId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, installerId: value }))}
                  >
                    <SelectTrigger className={errors.installerId ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select an installer..." />
                    </SelectTrigger>
                    <SelectContent>
                      {installers.map((installer) => (
                        <SelectItem key={installer.id} value={installer.id}>
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={installer.avatar || undefined} />
                              <AvatarFallback className="text-xs">
                                {installer.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex items-center space-x-2">
                              <span>{installer.name}</span>
                              <div className={`w-2 h-2 rounded-full ${getAvailabilityColor(installer.availability)}`} />
                              <span className="text-xs text-muted-foreground">
                                {installer.utilization}%
                              </span>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.installerId && <p className="text-sm text-red-500">{errors.installerId}</p>}

                  {selectedInstaller && (
                    <Card className="mt-3">
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={selectedInstaller.avatar || undefined} />
                              <AvatarFallback>
                                {selectedInstaller.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{selectedInstaller.name}</p>
                              <p className="text-xs text-muted-foreground capitalize">
                                {selectedInstaller.availability} • {selectedInstaller.utilization}% utilized
                              </p>
                            </div>
                          </div>
                          <div className={`w-3 h-3 rounded-full ${getAvailabilityColor(selectedInstaller.availability)}`} />
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* Customer Information */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Customer</Label>
                  <Select
                    value={formData.customerId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, customerId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select customer..." />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{customer.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {customer.email} • {customer.phone}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>

              <TabsContent value="notifications" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Send Initial Notification</Label>
                      <p className="text-xs text-muted-foreground">
                        Notify customer and installer about this appointment
                      </p>
                    </div>
                    <Switch
                      checked={formData.sendNotification}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, sendNotification: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Auto Reminder</Label>
                      <p className="text-xs text-muted-foreground">
                        Send automatic reminder 24 hours before appointment
                      </p>
                    </div>
                    <Switch
                      checked={formData.autoReminder}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, autoReminder: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">SMS Notifications</Label>
                      <p className="text-xs text-muted-foreground">
                        Send SMS updates to customer and installer
                      </p>
                    </div>
                    <Switch
                      checked={formData.smsReminder}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, smsReminder: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Email Notifications</Label>
                      <p className="text-xs text-muted-foreground">
                        Send email confirmations and updates
                      </p>
                    </div>
                    <Switch
                      checked={formData.emailReminder}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, emailReminder: checked }))}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="attachments" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      Appointment Notes
                    </Label>
                    <Textarea
                      placeholder="Add any special instructions, notes, or requirements for this appointment..."
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      rows={4}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">File Attachments</Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-amber-400 transition-colors">
                      <FileText className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-sm text-gray-600">
                        Drag and drop files here, or click to browse
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Support for images, PDFs, and documents up to 10MB
                      </p>
                      <Button variant="outline" className="mt-4" size="sm">
                        Choose Files
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-muted-foreground">
            * Required fields
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSave} 
              className="bg-amber-500 hover:bg-amber-600 text-white"
            >
              <Save className="h-4 w-4 mr-2" />
              Schedule Appointment
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
