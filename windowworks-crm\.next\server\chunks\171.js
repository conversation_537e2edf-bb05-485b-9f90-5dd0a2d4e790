exports.id=171,exports.ids=[171],exports.modules={165:(e,t,r)=>{Promise.resolve().then(r.bind(r,6457))},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},1342:(e,t,r)=>{"use strict";r.d(t,{SQ:()=>d,_2:()=>l,mB:()=>c,rI:()=>n,ty:()=>o});var a=r(687);r(3210);var s=r(6312),i=r(4780);function n({...e}){return(0,a.jsx)(s.bL,{"data-slot":"dropdown-menu",...e})}function o({...e}){return(0,a.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...e})}function d({className:e,sideOffset:t=4,...r}){return(0,a.jsx)(s.ZL,{children:(0,a.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function l({className:e,inset:t,variant:r="default",...n}){return(0,a.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function c({className:e,...t}){return(0,a.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},2021:(e,t,r)=>{Promise.resolve().then(r.bind(r,2587))},2584:(e,t,r)=>{"use strict";r.d(t,{BK:()=>o,eu:()=>n,q5:()=>d});var a=r(687);r(3210);var s=r(1096),i=r(4780);function n({className:e,...t}){return(0,a.jsx)(s.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function o({className:e,...t}){return(0,a.jsx)(s._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full",e),...t})}function d({className:e,...t}){return(0,a.jsx)(s.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},2587:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,ThemeProvider:()=>n});var a=r(687),s=r(3210);let i=(0,s.createContext)(void 0);function n({children:e}){let[t,r]=(0,s.useState)("light"),[n,o]=(0,s.useState)("#D97706"),[d,l]=(0,s.useState)(!1),c=(0,s.useCallback)(e=>{r(e);try{localStorage.setItem("windowworks-theme",e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}catch(e){console.error("Failed to set theme in localStorage",e)}},[]),u=(0,s.useCallback)(e=>{o(e);try{localStorage.setItem("windowworks-accent-color",e);let t=document.documentElement,r=`oklch(from ${e} l c h)`;t.style.setProperty("--primary",r),t.style.setProperty("--color-brand-primary",e)}catch(e){console.error("Failed to set accent color in localStorage",e)}},[]);return(0,a.jsx)(i.Provider,{value:{theme:t,setTheme:c,accentColor:n,setAccentColor:u,mounted:d},children:e})}function o(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},2912:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});var a=r(6787),s=r(9350);let i=(0,a.v)()((0,s.lt)((0,s.Zr)(e=>({user:null,isLoading:!0,isAuthenticated:!1,setUser:t=>e({user:t,isAuthenticated:!!t,isLoading:!1},!1,"setUser"),setLoading:t=>e({isLoading:t},!1,"setLoading"),clearAuth:()=>e({user:null,isAuthenticated:!1,isLoading:!1},!1,"clearAuth")}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}),{name:"auth-store"}))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var a=r(7413),s=r(2376),i=r.n(s),n=r(8726),o=r.n(n);r(1135);var d=r(6457);let l={title:"WindowWorks CRM",description:"Customer and project management with installer workflows"};function c({children:e}){return(0,a.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,a.jsx)("head",{children:(0,a.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                try {
                  const theme = localStorage.getItem('windowworks-theme');
                  if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                  } else {
                    document.documentElement.classList.remove('dark');
                  }
                } catch (_) {}
              })();
            `}})}),(0,a.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:(0,a.jsx)(d.ThemeProvider,{children:e})})]})}},4493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n});var a=r(687);r(3210);var s=r(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm transition-colors","border-border dark:border-muted-foreground/50 dark:hover:border-muted-foreground",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},4685:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(9384),s=r(2348);function i(...e){return(0,s.QP)((0,a.$)(e))}},6125:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6457:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});var a=r(2907);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\contexts\\theme-context.tsx","ThemeProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Source Code\\GoogleGemini\\windowworks-crm\\src\\contexts\\theme-context.tsx","useTheme")},6834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var a=r(687);r(3210);var s=r(8730),i=r(4224),n=r(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...i}){let d=r?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i})}},7634:(e,t,r)=>{"use strict";r.d(t,{DashboardLayout:()=>A});var a=r(687),s=r(6001),i=r(5814),n=r.n(i),o=r(6189),d=r(2192),l=r(1312),c=r(8179),u=r(228),m=r(4027),f=r(83),h=r(2941),v=r(9270),x=r(7051),b=r(9523),g=r(9667),p=r(6834),y=r(2584),j=r(1342),w=r(2912),N=r(6787),k=r(9350);let P=(0,N.v)()((0,k.lt)(e=>({notifications:[],unreadCount:0,addNotification:t=>{let r={...t,id:crypto.randomUUID(),createdAt:new Date().toISOString()};e(e=>({notifications:[r,...e.notifications],unreadCount:e.unreadCount+1}),!1,"addNotification")},markAsRead:t=>e(e=>({notifications:e.notifications.map(e=>e.id===t?{...e,read:!0}:e),unreadCount:Math.max(0,e.unreadCount-1)}),!1,"markAsRead"),markAllAsRead:()=>e(e=>({notifications:e.notifications.map(e=>({...e,read:!0})),unreadCount:0}),!1,"markAllAsRead"),removeNotification:t=>e(e=>{let r=e.notifications.find(e=>e.id===t),a=r&&!r.read;return{notifications:e.notifications.filter(e=>e.id!==t),unreadCount:a?Math.max(0,e.unreadCount-1):e.unreadCount}},!1,"removeNotification"),setNotifications:t=>e({notifications:t,unreadCount:t.filter(e=>!e.read).length},!1,"setNotifications")}),{name:"notification-store"})),C=[{icon:d.A,label:"Dashboard",href:"/",badge:null},{icon:l.A,label:"Customers",href:"/customers",badge:null},{icon:c.A,label:"Projects",href:"/projects",badge:null},{icon:u.A,label:"Schedule",href:"/schedule",badge:null},{icon:m.A,label:"Settings",href:"/settings",badge:null}];function A({children:e}){let{user:t,clearAuth:r}=(0,w.n)(),{unreadCount:i}=P(),d=(0,o.usePathname)();return(0,a.jsxs)("div",{className:"h-screen bg-background flex overflow-hidden",children:[(0,a.jsxs)(s.P.aside,{initial:{x:-300},animate:{x:0},className:"w-64 bg-card border-r border-border flex flex-col fixed left-0 top-0 h-full z-30",children:[(0,a.jsx)("div",{className:"p-6 border-b border-border flex-shrink-0",children:(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:.2},className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[var(--color-brand-primary)] rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"W"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-foreground",children:"WindowWorks"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"CRM Platform"})]})]})}),(0,a.jsx)("nav",{className:"flex-1 p-4 space-y-2 overflow-y-auto",children:C.map((e,t)=>{let r=d===e.href;return(0,a.jsx)(s.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*(t+1)},children:(0,a.jsx)(n(),{href:e.href,children:(0,a.jsxs)(b.$,{variant:"ghost",className:`w-full justify-start rounded-md ${r?"bg-primary/10 text-primary border-primary/20":"text-muted-foreground hover:text-foreground hover:bg-accent"}`,children:[(0,a.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.label,e.badge&&(0,a.jsx)(p.E,{variant:"secondary",className:"ml-auto",children:e.badge})]})})},e.href)})}),(0,a.jsx)("div",{className:"p-4 border-t border-border flex-shrink-0 sticky bottom-0 bg-card",children:(0,a.jsxs)(j.rI,{children:[(0,a.jsx)(j.ty,{asChild:!0,children:(0,a.jsxs)(b.$,{variant:"ghost",className:"w-full justify-start p-3 hover:bg-accent",children:[(0,a.jsxs)(y.eu,{className:"h-8 w-8 mr-3",children:[(0,a.jsx)(y.BK,{src:t?.profile?.avatar}),(0,a.jsxs)(y.q5,{className:"bg-primary/10 text-primary",children:[t?.profile?.firstName?.[0],t?.profile?.lastName?.[0]]})]}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-foreground",children:[t?.profile?.firstName," ",t?.profile?.lastName]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground capitalize",children:t?.role})]})]})}),(0,a.jsxs)(j.SQ,{align:"start",className:"w-56",children:[(0,a.jsxs)(j._2,{children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Account Settings"]}),(0,a.jsxs)(j._2,{onClick:()=>{r()},children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Sign Out"]})]})]})})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col bg-background ml-64",children:[(0,a.jsx)(s.P.header,{initial:{y:-50,opacity:0},animate:{y:0,opacity:1},className:"bg-card border-b border-border px-6 py-4 sticky top-0 z-20 flex-shrink-0",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(b.$,{variant:"ghost",size:"sm",className:"md:hidden",children:(0,a.jsx)(h.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{className:"relative w-96 hidden md:block",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,a.jsx)(g.p,{placeholder:"Search customers, projects...",className:"pl-10 bg-accent/50 border-border focus:bg-background"})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)(b.$,{variant:"ghost",size:"sm",className:"relative",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),i>0&&(0,a.jsx)(s.P.div,{initial:{scale:0},animate:{scale:1},className:"absolute -top-1 -right-1 h-5 w-5 bg-destructive rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs text-destructive-foreground font-medium",children:i>9?"9+":i})})]})})]})}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto bg-background",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:e})})})]})]})}},9523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(687);r(3210);var s=r(8730),i=r(4224),n=r(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:i=!1,...d}){let l=i?s.DX:"button";return(0,a.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...d})}},9667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(687);r(3210);var s=r(4780);function i({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}}};