'use client'

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  User,
  Calendar,
  DollarSign,
  Clock,
  Plus,
  Trash2,
  Save,
  Loader2,
  AlertCircle,
  Sparkles,
  Home,
  Settings,
  FileText
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTit<PERSON>,
} from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { useTheme } from '@/contexts/theme-context'
import { ProjectFormData, ProjectPriority, WindowTreatmentType, Customer, User as UserType } from '@/types'

// Types for the modal
interface WindowFormData {
  id: string
  width: number | ''
  height: number | ''
  treatmentType: WindowTreatmentType | ''
  color: string
  material: string
  installationType: 'inside-mount' | 'outside-mount'
  features: string[]
  cost: number
}

interface RoomFormData {
  id: string
  name: string
  windows: WindowFormData[]
}

interface ProjectModalFormData {
  customerId: string
  title: string
  description: string
  priority: ProjectPriority
  estimatedCost: number
  estimatedDuration: number
  startDate: string
  installerId: string
  rooms: RoomFormData[]
  notes: string
}

interface NewProjectModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (project: ProjectFormData) => Promise<void>
  customers: Customer[]
  installers: UserType[]
}

// Constants
const priorityOptions = [
  { value: 'low' as ProjectPriority, label: 'Low', color: 'bg-gray-100 text-gray-700' },
  { value: 'medium' as ProjectPriority, label: 'Medium', color: 'bg-blue-100 text-blue-700' },
  { value: 'high' as ProjectPriority, label: 'High', color: 'bg-orange-100 text-orange-700' },
  { value: 'urgent' as ProjectPriority, label: 'Urgent', color: 'bg-red-100 text-red-700' }
]

const treatmentTypes = [
  { value: 'blinds' as WindowTreatmentType, label: 'Blinds' },
  { value: 'shutters' as WindowTreatmentType, label: 'Shutters' },
  { value: 'shades' as WindowTreatmentType, label: 'Shades' },
  { value: 'curtains' as WindowTreatmentType, label: 'Curtains' },
  { value: 'drapes' as WindowTreatmentType, label: 'Drapes' }
]

const materialOptions = [
  'Wood', 'Vinyl', 'Aluminum', 'Fabric', 'Bamboo', 'Faux Wood', 'Composite'
]

const featureOptions = [
  'Motorized', 'Blackout', 'Light Filtering', 'UV Protection', 
  'Energy Efficient', 'Cordless', 'Top Down Bottom Up'
]

const roomTemplates = [
  { name: 'Living Room', windows: 2 },
  { name: 'Bedroom', windows: 1 },
  { name: 'Kitchen', windows: 1 },
  { name: 'Dining Room', windows: 2 },
  { name: 'Office', windows: 1 },
  { name: 'Bathroom', windows: 1 }
]

export function NewProjectModal({ isOpen, onClose, onSave, customers, installers }: NewProjectModalProps) {
  const { accentColor } = useTheme()
  const [activeTab, setActiveTab] = useState('overview')
  const [isLoading, setIsLoading] = useState(false)
  const [isSuggesting, setIsSuggesting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [formData, setFormData] = useState<ProjectModalFormData>({
    customerId: '',
    title: '',
    description: '',
    priority: 'medium',
    estimatedCost: 0,
    estimatedDuration: 5,
    startDate: '',
    installerId: '',
    rooms: [],
    notes: ''
  })

  // Generate unique IDs
  const generateId = () => `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  // Cost calculation
  const calculateWindowCost = useCallback((window: WindowFormData): number => {
    if (!window.width || !window.height || !window.treatmentType) return 0
    
    const area = Number(window.width) * Number(window.height)
    const baseCostPerSqFt = {
      blinds: 15,
      shutters: 35,
      shades: 25,
      curtains: 20,
      drapes: 30
    }
    
    const materialMultiplier = {
      'Wood': 1.3,
      'Vinyl': 0.8,
      'Aluminum': 0.9,
      'Fabric': 1.1,
      'Bamboo': 1.2,
      'Faux Wood': 1.0,
      'Composite': 1.1
    }
    
    let cost = area * baseCostPerSqFt[window.treatmentType as WindowTreatmentType]
    cost *= materialMultiplier[window.material as keyof typeof materialMultiplier] || 1
    cost += window.features.length * 50 // Feature upcharge
    
    return Math.round(cost)
  }, [])

  // Update total cost when rooms/windows change
  const calculateTotalCost = useCallback(() => {
    const total = formData.rooms.reduce((roomTotal, room) => {
      return roomTotal + room.windows.reduce((windowTotal, window) => {
        return windowTotal + calculateWindowCost(window)
      }, 0)
    }, 0)
    
    setFormData(prev => ({ ...prev, estimatedCost: total }))
  }, [formData.rooms, calculateWindowCost])

  // Update form data
  const updateFormData = useCallback((path: string, value: unknown) => {
    setFormData(prev => {
      const keys = path.split('.')
      const newData = { ...prev }
      let current: Record<string, unknown> = newData
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (current[keys[i]] === undefined) current[keys[i]] = {}
        current = current[keys[i]] as Record<string, unknown>
      }
      
      current[keys[keys.length - 1]] = value
      return newData
    })
    
    // Clear error for this field
    setErrors(prev => {
      if (prev[path]) {
        const newErrors = { ...prev }
        delete newErrors[path]
        return newErrors
      }
      return prev
    })
  }, [])

  // Add new room
  const addRoom = (templateName?: string) => {
    const newRoom: RoomFormData = {
      id: generateId(),
      name: templateName || `Room ${formData.rooms.length + 1}`,
      windows: []
    }
    
    // Add default window if using template
    if (templateName) {
      const template = roomTemplates.find(t => t.name === templateName)
      if (template) {
        for (let i = 0; i < template.windows; i++) {
          newRoom.windows.push({
            id: generateId(),
            width: '',
            height: '',
            treatmentType: '',
            color: 'White',
            material: 'Vinyl',
            installationType: 'inside-mount',
            features: [],
            cost: 0
          })
        }
      }
    }
    
    setFormData(prev => ({
      ...prev,
      rooms: [...prev.rooms, newRoom]
    }))
  }

  // Remove room
  const removeRoom = (roomId: string) => {
    setFormData(prev => ({
      ...prev,
      rooms: prev.rooms.filter(room => room.id !== roomId)
    }))
  }

  // Add window to room
  const addWindow = (roomId: string) => {
    const newWindow: WindowFormData = {
      id: generateId(),
      width: '',
      height: '',
      treatmentType: '',
      color: 'White',
      material: 'Vinyl',
      installationType: 'inside-mount',
      features: [],
      cost: 0
    }
    
    setFormData(prev => ({
      ...prev,
      rooms: prev.rooms.map(room => 
        room.id === roomId 
          ? { ...room, windows: [...room.windows, newWindow] }
          : room
      )
    }))
  }

  // Remove window from room
  const removeWindow = (roomId: string, windowId: string) => {
    setFormData(prev => ({
      ...prev,
      rooms: prev.rooms.map(room => 
        room.id === roomId 
          ? { ...room, windows: room.windows.filter(window => window.id !== windowId) }
          : room
      )
    }))
  }

  // Update window data
  const updateWindow = (roomId: string, windowId: string, field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      rooms: prev.rooms.map(room => 
        room.id === roomId 
          ? {
              ...room,
              windows: room.windows.map(window => 
                window.id === windowId 
                  ? { ...window, [field]: value }
                  : window
              )
            }
          : room
      )
    }))
  }

  // AI Suggestion (mock)
  const suggestProject = useCallback(async () => {
    if (!formData.customerId) {
      setErrors({ customerId: 'Please select a customer first' })
      return
    }
    
    setIsSuggesting(true)
    
    // Mock AI processing
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const customer = customers.find(c => c.id === formData.customerId)
    if (customer) {
      // Mock suggestions based on customer preferences
      const suggestions = {
        title: `${customer.preferences.windowTreatmentTypes[0]?.charAt(0).toUpperCase() + customer.preferences.windowTreatmentTypes[0]?.slice(1) || 'Window'} Installation - ${customer.firstName} ${customer.lastName}`,
        description: `Professional installation of ${customer.preferences.windowTreatmentTypes.join(', ')} for ${customer.firstName} ${customer.lastName}`,
        priority: 'medium' as ProjectPriority,
        estimatedDuration: 3
      }
      
      Object.entries(suggestions).forEach(([key, value]) => {
        updateFormData(key, value)
      })
    }
    
    setIsSuggesting(false)
  }, [formData.customerId, customers, updateFormData])

  // Validation
  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.title.trim()) newErrors.title = 'Project title is required'
    if (!formData.customerId) newErrors.customerId = 'Customer selection is required'
    if (!formData.description.trim()) newErrors.description = 'Project description is required'
    if (formData.rooms.length === 0) newErrors.rooms = 'At least one room is required'
    
    // Validate rooms and windows
    formData.rooms.forEach((room, roomIndex) => {
      if (!room.name.trim()) {
        newErrors[`rooms.${roomIndex}.name`] = 'Room name is required'
      }
      if (room.windows.length === 0) {
        newErrors[`rooms.${roomIndex}.windows`] = 'At least one window is required per room'
      }
      
      room.windows.forEach((window, windowIndex) => {
        if (!window.width || window.width <= 0) {
          newErrors[`rooms.${roomIndex}.windows.${windowIndex}.width`] = 'Valid width is required'
        }
        if (!window.height || window.height <= 0) {
          newErrors[`rooms.${roomIndex}.windows.${windowIndex}.height`] = 'Valid height is required'
        }
        if (!window.treatmentType) {
          newErrors[`rooms.${roomIndex}.windows.${windowIndex}.treatmentType`] = 'Treatment type is required'
        }
      })
    })
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [formData])

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      // Switch to appropriate tab with errors
      if (errors.title || errors.customerId || errors.description) {
        setActiveTab('overview')
      } else if (Object.keys(errors).some(key => key.startsWith('rooms'))) {
        setActiveTab('rooms')
      }
      return
    }
    
    setIsLoading(true)
    try {
      // Calculate final costs
      const roomsWithCalculatedCosts = formData.rooms.map(room => ({
        name: room.name,
        windows: room.windows
          .filter(window => window.treatmentType !== '') // Filter out incomplete windows
          .map(window => ({
            id: window.id,
            width: Number(window.width),
            height: Number(window.height),
            treatmentType: window.treatmentType as WindowTreatmentType,
            color: window.color,
            material: window.material,
            installationType: window.installationType,
            features: window.features,
            cost: calculateWindowCost(window)
          }))
      }))
      
      const projectData: ProjectFormData = {
        customerId: formData.customerId,
        title: formData.title,
        description: formData.description,
        priority: formData.priority,
        estimatedCost: formData.estimatedCost,
        estimatedDuration: formData.estimatedDuration,
        startDate: formData.startDate || undefined,
        installerId: formData.installerId || undefined,
        rooms: roomsWithCalculatedCosts,
        notes: formData.notes || undefined
      }
      
      await onSave(projectData)
      onClose()
      
      // Reset form
      setFormData({
        customerId: '',
        title: '',
        description: '',
        priority: 'medium',
        estimatedCost: 0,
        estimatedDuration: 5,
        startDate: '',
        installerId: '',
        rooms: [],
        notes: ''
      })
      setActiveTab('overview')
    } catch (error) {
      console.error('Error saving project:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Calculate costs when rooms change
  React.useEffect(() => {
    calculateTotalCost()
  }, [calculateTotalCost])

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl w-[95vw] max-h-[90vh] overflow-hidden p-0">
        <motion.div 
          className="flex flex-col h-full"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
        >
          {/* Header */}
          <DialogHeader className="px-6 py-4 border-b border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-xl font-semibold text-slate-700">
                Create New Project
              </DialogTitle>
              <Button
                variant="outline"
                onClick={suggestProject}
                disabled={isSuggesting || !formData.customerId}
                className="border-gray-200"
                style={{ borderColor: accentColor, color: accentColor }}
              >
                {isSuggesting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Sparkles className="h-4 w-4 mr-2" />
                )}
                {isSuggesting ? 'Analyzing...' : 'AI Suggest'}
              </Button>
            </div>
          </DialogHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="rooms" className="flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  Rooms
                </TabsTrigger>
                <TabsTrigger value="workflow" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Workflow
                </TabsTrigger>
                <TabsTrigger value="notes" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Notes
                </TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Basic Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Basic Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="title" className="text-slate-600">
                          Project Title *
                        </Label>
                        <Input
                          id="title"
                          value={formData.title}
                          onChange={(e) => updateFormData('title', e.target.value)}
                          className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${
                            errors.title ? 'border-red-500' : ''
                          }`}
                          placeholder="Enter project title"
                        />
                        {errors.title && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.title}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="description" className="text-slate-600">
                          Description *
                        </Label>
                        <Textarea
                          id="description"
                          value={formData.description}
                          onChange={(e) => updateFormData('description', e.target.value)}
                          className={`min-h-[100px] border-gray-200 focus:border-[var(--color-brand-primary)] ${
                            errors.description ? 'border-red-500' : ''
                          }`}
                          placeholder="Describe the project details..."
                        />
                        {errors.description && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.description}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label className="text-slate-600">Priority</Label>
                        <div className="flex gap-2">
                          {priorityOptions.map((option) => (
                            <Badge
                              key={option.value}
                              variant={formData.priority === option.value ? "default" : "outline"}
                              className={`cursor-pointer transition-colors ${
                                formData.priority === option.value 
                                  ? option.color 
                                  : 'border-gray-300 text-gray-600 hover:bg-gray-50'
                              }`}
                              onClick={() => updateFormData('priority', option.value)}
                            >
                              {option.label}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Customer & Assignment */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                        <User className="h-5 w-5" />
                        Customer & Assignment
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label className="text-slate-600">Customer *</Label>
                        <Select 
                          value={formData.customerId} 
                          onValueChange={(value) => updateFormData('customerId', value)}
                        >
                          <SelectTrigger className={`border-gray-200 ${errors.customerId ? 'border-red-500' : ''}`}>
                            <SelectValue placeholder="Select customer" />
                          </SelectTrigger>
                          <SelectContent>
                            {customers.map((customer) => (
                              <SelectItem key={customer.id} value={customer.id}>
                                <div className="flex items-center gap-2">
                                  <Avatar className="h-6 w-6">
                                    <AvatarFallback className="text-xs">
                                      {customer.firstName[0]}{customer.lastName[0]}
                                    </AvatarFallback>
                                  </Avatar>
                                  {customer.firstName} {customer.lastName}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.customerId && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.customerId}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label className="text-slate-600">Installer</Label>
                        <Select 
                          value={formData.installerId} 
                          onValueChange={(value) => updateFormData('installerId', value)}
                        >
                          <SelectTrigger className="border-gray-200">
                            <SelectValue placeholder="Select installer (optional)" />
                          </SelectTrigger>
                          <SelectContent>
                            {installers.map((installer) => (
                              <SelectItem key={installer.id} value={installer.id}>
                                <div className="flex items-center gap-2">
                                  <Avatar className="h-6 w-6">
                                    <AvatarImage src={installer.profile?.avatar} />
                                    <AvatarFallback className="text-xs">
                                      {installer.profile.firstName[0]}{installer.profile.lastName[0]}
                                    </AvatarFallback>
                                  </Avatar>
                                  {installer.profile.firstName} {installer.profile.lastName}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="estimatedDuration" className="text-slate-600 flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            Duration (days)
                          </Label>
                          <Input
                            id="estimatedDuration"
                            type="number"
                            min="1"
                            value={formData.estimatedDuration}
                            onChange={(e) => updateFormData('estimatedDuration', Number(e.target.value))}
                            className="border-gray-200 focus:border-[var(--color-brand-primary)]"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="startDate" className="text-slate-600 flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            Start Date
                          </Label>
                          <Input
                            id="startDate"
                            type="date"
                            value={formData.startDate}
                            onChange={(e) => updateFormData('startDate', e.target.value)}
                            className="border-gray-200 focus:border-[var(--color-brand-primary)]"
                          />
                        </div>
                      </div>

                      <div className="p-4 bg-amber-50 rounded-lg border border-amber-200">
                        <div className="flex items-center gap-2 text-amber-800">
                          <DollarSign className="h-5 w-5" />
                          <span className="font-medium">Estimated Cost</span>
                        </div>
                        <div className="text-2xl font-bold text-amber-900 mt-1">
                          ${formData.estimatedCost.toLocaleString()}
                        </div>
                        <p className="text-sm text-amber-700 mt-1">
                          Auto-calculated from room specifications
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Rooms Tab */}
              <TabsContent value="rooms" className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-slate-700">Room Specifications</h3>
                  <div className="flex gap-2">
                    <Select onValueChange={addRoom}>
                      <SelectTrigger className="w-48 border-gray-200">
                        <SelectValue placeholder="Add room template" />
                      </SelectTrigger>
                      <SelectContent>
                        {roomTemplates.map((template) => (
                          <SelectItem key={template.name} value={template.name}>
                            {template.name} ({template.windows} window{template.windows !== 1 ? 's' : ''})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={() => addRoom()}
                      className="text-white"
                      style={{ backgroundColor: accentColor, borderColor: accentColor }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Room
                    </Button>
                  </div>
                </div>

                {errors.rooms && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.rooms}
                    </p>
                  </div>
                )}

                <div className="space-y-4">
                  <AnimatePresence>
                    {formData.rooms.map((room, roomIndex) => (
                      <motion.div
                        key={room.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Card>
                          <CardHeader>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <Home className="h-5 w-5 text-slate-600" />
                                <Input
                                  value={room.name}
                                  onChange={(e) => updateFormData(`rooms.${roomIndex}.name`, e.target.value)}
                                  className={`font-medium border-none focus:border-[var(--color-brand-primary)] bg-transparent p-0 text-lg ${
                                    errors[`rooms.${roomIndex}.name`] ? 'border-red-500' : ''
                                  }`}
                                  placeholder="Room name"
                                />
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeRoom(room.id)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                            {errors[`rooms.${roomIndex}.name`] && (
                              <p className="text-sm text-red-600 flex items-center gap-1">
                                <AlertCircle className="h-3 w-3" />
                                {errors[`rooms.${roomIndex}.name`]}
                              </p>
                            )}
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-slate-600">Windows</span>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => addWindow(room.id)}
                                  className="border-gray-200"
                                >
                                  <Plus className="h-4 w-4 mr-1" />
                                  Add Window
                                </Button>
                              </div>

                              {errors[`rooms.${roomIndex}.windows`] && (
                                <div className="p-2 bg-red-50 border border-red-200 rounded">
                                  <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors[`rooms.${roomIndex}.windows`]}
                                  </p>
                                </div>
                              )}

                              <div className="space-y-3">
                                <AnimatePresence>
                                  {room.windows.map((window, windowIndex) => (
                                    <motion.div
                                      key={window.id}
                                      initial={{ opacity: 0, height: 0 }}
                                      animate={{ opacity: 1, height: 'auto' }}
                                      exit={{ opacity: 0, height: 0 }}
                                      transition={{ duration: 0.2 }}
                                      className="p-4 border border-gray-200 rounded-lg space-y-4"
                                    >
                                      <div className="flex items-center justify-between">
                                        <span className="font-medium text-slate-700">
                                          Window {windowIndex + 1}
                                        </span>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => removeWindow(room.id, window.id)}
                                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                        >
                                          <Trash2 className="h-3 w-3" />
                                        </Button>
                                      </div>

                                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                                        <div className="space-y-1">
                                          <Label className="text-xs text-slate-600">Width (inches) *</Label>
                                          <Input
                                            type="number"
                                            min="1"
                                            step="0.25"
                                            value={window.width}
                                            onChange={(e) => updateWindow(room.id, window.id, 'width', Number(e.target.value))}
                                            className={`text-sm border-gray-200 ${
                                              errors[`rooms.${roomIndex}.windows.${windowIndex}.width`] ? 'border-red-500' : ''
                                            }`}
                                            placeholder="Width"
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs text-slate-600">Height (inches) *</Label>
                                          <Input
                                            type="number"
                                            min="1"
                                            step="0.25"
                                            value={window.height}
                                            onChange={(e) => updateWindow(room.id, window.id, 'height', Number(e.target.value))}
                                            className={`text-sm border-gray-200 ${
                                              errors[`rooms.${roomIndex}.windows.${windowIndex}.height`] ? 'border-red-500' : ''
                                            }`}
                                            placeholder="Height"
                                          />
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs text-slate-600">Treatment Type *</Label>
                                          <Select 
                                            value={window.treatmentType} 
                                            onValueChange={(value) => updateWindow(room.id, window.id, 'treatmentType', value)}
                                          >
                                            <SelectTrigger className={`text-sm border-gray-200 ${
                                              errors[`rooms.${roomIndex}.windows.${windowIndex}.treatmentType`] ? 'border-red-500' : ''
                                            }`}>
                                              <SelectValue placeholder="Type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                              {treatmentTypes.map((type) => (
                                                <SelectItem key={type.value} value={type.value}>
                                                  {type.label}
                                                </SelectItem>
                                              ))}
                                            </SelectContent>
                                          </Select>
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs text-slate-600">Color</Label>
                                          <Input
                                            value={window.color}
                                            onChange={(e) => updateWindow(room.id, window.id, 'color', e.target.value)}
                                            className="text-sm border-gray-200"
                                            placeholder="Color"
                                          />
                                        </div>
                                      </div>

                                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
                                        <div className="space-y-1">
                                          <Label className="text-xs text-slate-600">Material</Label>
                                          <Select 
                                            value={window.material} 
                                            onValueChange={(value) => updateWindow(room.id, window.id, 'material', value)}
                                          >
                                            <SelectTrigger className="text-sm border-gray-200">
                                              <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                              {materialOptions.map((material) => (
                                                <SelectItem key={material} value={material}>
                                                  {material}
                                                </SelectItem>
                                              ))}
                                            </SelectContent>
                                          </Select>
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs text-slate-600">Installation</Label>
                                          <Select 
                                            value={window.installationType} 
                                            onValueChange={(value) => updateWindow(room.id, window.id, 'installationType', value)}
                                          >
                                            <SelectTrigger className="text-sm border-gray-200">
                                              <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                              <SelectItem value="inside-mount">Inside Mount</SelectItem>
                                              <SelectItem value="outside-mount">Outside Mount</SelectItem>
                                            </SelectContent>
                                          </Select>
                                        </div>
                                        <div className="space-y-1">
                                          <Label className="text-xs text-slate-600">Estimated Cost</Label>
                                          <div className="text-sm font-medium text-green-600 bg-green-50 px-3 py-2 rounded border">
                                            ${calculateWindowCost(window).toLocaleString()}
                                          </div>
                                        </div>
                                      </div>

                                      <div className="space-y-2">
                                        <Label className="text-xs text-slate-600">Features</Label>
                                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
                                          {featureOptions.map((feature) => (
                                            <div key={feature} className="flex items-center space-x-2">
                                              <Checkbox
                                                id={`${window.id}-${feature}`}
                                                checked={window.features.includes(feature)}
                                                onCheckedChange={(checked) => {
                                                  const newFeatures = checked
                                                    ? [...window.features, feature]
                                                    : window.features.filter(f => f !== feature)
                                                  updateWindow(room.id, window.id, 'features', newFeatures)
                                                }}
                                                className="border-gray-300"
                                              />
                                              <Label htmlFor={`${window.id}-${feature}`} className="text-xs text-slate-600">
                                                {feature}
                                              </Label>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    </motion.div>
                                  ))}
                                </AnimatePresence>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>

                {formData.rooms.length === 0 && (
                  <div className="text-center py-12 text-slate-500">
                    <Home className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                    <p className="text-lg font-medium mb-2">No rooms added yet</p>
                    <p className="text-sm">Add rooms to start defining your project specifications</p>
                  </div>
                )}
              </TabsContent>

              {/* Workflow Tab */}
              <TabsContent value="workflow" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      Project Workflow
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label className="text-slate-600">Assigned Installer</Label>
                          <Select 
                            value={formData.installerId} 
                            onValueChange={(value) => updateFormData('installerId', value)}
                          >
                            <SelectTrigger className="border-gray-200">
                              <SelectValue placeholder="Select installer" />
                            </SelectTrigger>
                            <SelectContent>
                              {installers.map((installer) => (
                                <SelectItem key={installer.id} value={installer.id}>
                                  <div className="flex items-center gap-3">
                                    <Avatar className="h-8 w-8">
                                      <AvatarImage src={installer.profile?.avatar} />
                                      <AvatarFallback>
                                        {installer.profile.firstName[0]}{installer.profile.lastName[0]}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div>
                                      <div className="font-medium">
                                        {installer.profile.firstName} {installer.profile.lastName}
                                      </div>
                                      <div className="text-sm text-slate-500">
                                        {installer.email}
                                      </div>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="startDate" className="text-slate-600 flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            Planned Start Date
                          </Label>
                          <Input
                            id="startDate"
                            type="date"
                            value={formData.startDate}
                            onChange={(e) => updateFormData('startDate', e.target.value)}
                            className="border-gray-200 focus:border-[var(--color-brand-primary)]"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="estimatedDuration" className="text-slate-600 flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            Estimated Duration (days)
                          </Label>
                          <Input
                            id="estimatedDuration"
                            type="number"
                            min="1"
                            value={formData.estimatedDuration}
                            onChange={(e) => updateFormData('estimatedDuration', Number(e.target.value))}
                            className="border-gray-200 focus:border-[var(--color-brand-primary)]"
                          />
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <h4 className="font-medium text-blue-800 mb-2">Project Timeline</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-blue-700">Start Date:</span>
                              <span className="text-blue-900">
                                {formData.startDate || 'Not set'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-blue-700">Duration:</span>
                              <span className="text-blue-900">
                                {formData.estimatedDuration} day{formData.estimatedDuration !== 1 ? 's' : ''}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-blue-700">Estimated Completion:</span>
                              <span className="text-blue-900">
                                {formData.startDate 
                                  ? new Date(
                                      new Date(formData.startDate).getTime() + 
                                      formData.estimatedDuration * 24 * 60 * 60 * 1000
                                    ).toLocaleDateString()
                                  : 'Not calculated'
                                }
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                          <h4 className="font-medium text-green-800 mb-2">Cost Summary</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-green-700">Total Rooms:</span>
                              <span className="text-green-900">{formData.rooms.length}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-green-700">Total Windows:</span>
                              <span className="text-green-900">
                                {formData.rooms.reduce((total, room) => total + room.windows.length, 0)}
                              </span>
                            </div>
                            <div className="flex justify-between font-medium">
                              <span className="text-green-700">Estimated Cost:</span>
                              <span className="text-green-900 text-lg">
                                ${formData.estimatedCost.toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Notes Tab */}
              <TabsContent value="notes" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Project Notes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Textarea
                      value={formData.notes}
                      onChange={(e) => updateFormData('notes', e.target.value)}
                      className="min-h-[300px] border-gray-200 focus:border-[var(--color-brand-primary)]"
                      placeholder="Add any additional notes, special requirements, customer preferences, installation considerations, or other relevant information for this project..."
                    />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="border-gray-200"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isLoading}
                className="text-white min-w-[140px]"
                style={{ 
                  backgroundColor: accentColor,
                  borderColor: accentColor
                }}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Create Project
                  </>
                )}
              </Button>
            </div>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  )
}
