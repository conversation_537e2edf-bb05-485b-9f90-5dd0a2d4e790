"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[663],{646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3717:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5452:(e,t,r)=>{r.d(t,{UC:()=>$,ZL:()=>Q,bL:()=>J,bm:()=>et,hE:()=>ee,hJ:()=>Y});var n=r(2115),a=r(5185),l=r(6101),o=r(6081),i=r(1285),s=r(5845),c=r(9178),d=r(7900),u=r(4378),p=r(8905),f=r(3655),y=r(2293),h=r(3795),g=r(8168),m=r(9708),v=r(5155),x="Dialog",[k,b]=(0,o.A)(x),[j,C]=k(x),D=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:l,onOpenChange:o,modal:c=!0}=e,d=n.useRef(null),u=n.useRef(null),[p,f]=(0,s.i)({prop:a,defaultProp:null!=l&&l,onChange:o,caller:x});return(0,v.jsx)(j,{scope:t,triggerRef:d,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:c,children:r})};D.displayName=x;var w="DialogTrigger";n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(w,r),i=(0,l.s)(t,o.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":W(o.open),...n,ref:i,onClick:(0,a.m)(e.onClick,o.onOpenToggle)})}).displayName=w;var A="DialogPortal",[R,E]=k(A,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:l}=e,o=C(A,t);return(0,v.jsx)(R,{scope:t,forceMount:r,children:n.Children.map(a,e=>(0,v.jsx)(p.C,{present:r||o.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:l,children:e})}))})};I.displayName=A;var N="DialogOverlay",_=n.forwardRef((e,t)=>{let r=E(N,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,l=C(N,e.__scopeDialog);return l.modal?(0,v.jsx)(p.C,{present:n||l.open,children:(0,v.jsx)(O,{...a,ref:t})}):null});_.displayName=N;var M=(0,m.TL)("DialogOverlay.RemoveScroll"),O=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C(N,r);return(0,v.jsx)(h.A,{as:M,allowPinchZoom:!0,shards:[a.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":W(a.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),F="DialogContent",P=n.forwardRef((e,t)=>{let r=E(F,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,l=C(F,e.__scopeDialog);return(0,v.jsx)(p.C,{present:n||l.open,children:l.modal?(0,v.jsx)(q,{...a,ref:t}):(0,v.jsx)(G,{...a,ref:t})})});P.displayName=F;var q=n.forwardRef((e,t)=>{let r=C(F,e.__scopeDialog),o=n.useRef(null),i=(0,l.s)(t,r.contentRef,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,g.Eq)(e)},[]),(0,v.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=n.forwardRef((e,t)=>{let r=C(F,e.__scopeDialog),a=n.useRef(!1),l=n.useRef(!1);return(0,v.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,o;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(a.current||null==(o=r.triggerRef.current)||o.focus(),t.preventDefault()),a.current=!1,l.current=!1},onInteractOutside:t=>{var n,o;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let i=t.target;(null==(o=r.triggerRef.current)?void 0:o.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,u=C(F,r),p=n.useRef(null),f=(0,l.s)(t,p);return(0,y.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(d.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,v.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":W(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(U,{titleId:u.titleId}),(0,v.jsx)(X,{contentRef:p,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C(T,r);return(0,v.jsx)(f.sG.h2,{id:a.titleId,...n,ref:t})});B.displayName=T;var H="DialogDescription";n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C(H,r);return(0,v.jsx)(f.sG.p,{id:a.descriptionId,...n,ref:t})}).displayName=H;var S="DialogClose",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=C(S,r);return(0,v.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,a.m)(e.onClick,()=>l.onOpenChange(!1))})});function W(e){return e?"open":"closed"}z.displayName=S;var Z="DialogTitleWarning",[V,K]=(0,o.q)(Z,{contentName:F,titleName:T,docsSlug:"dialog"}),U=e=>{let{titleId:t}=e,r=K(Z),a="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(a))},[a,t]),null},X=e=>{let{contentRef:t,descriptionId:r}=e,a=K("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(l))},[l,t,r]),null},J=D,Q=I,Y=_,$=P,ee=B,et=z},5623:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5868:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6981:(e,t,r)=>{r.d(t,{C1:()=>C,bL:()=>b});var n=r(2115),a=r(6101),l=r(6081),o=r(5185),i=r(5845),s=r(5503),c=r(1275),d=r(8905),u=r(3655),p=r(5155),f="Checkbox",[y,h]=(0,l.A)(f),[g,m]=y(f);function v(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:l,disabled:o,form:s,name:c,onCheckedChange:d,required:u,value:y="on",internal_do_not_use_render:h}=e,[m,v]=(0,i.i)({prop:r,defaultProp:null!=l&&l,onChange:d,caller:f}),[x,k]=n.useState(null),[b,j]=n.useState(null),C=n.useRef(!1),D=!x||!!s||!!x.closest("form"),w={checked:m,disabled:o,setChecked:v,control:x,setControl:k,name:c,form:s,value:y,hasConsumerStoppedPropagationRef:C,required:u,defaultChecked:!A(l)&&l,isFormControl:D,bubbleInput:b,setBubbleInput:j};return(0,p.jsx)(g,{scope:t,...w,children:"function"==typeof h?h(w):a})}var x="CheckboxTrigger",k=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:l,onClick:i,...s}=e,{control:c,value:d,disabled:f,checked:y,required:h,setControl:g,setChecked:v,hasConsumerStoppedPropagationRef:k,isFormControl:b,bubbleInput:j}=m(x,r),C=(0,a.s)(t,g),D=n.useRef(y);return n.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>v(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,v]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":A(y)?"mixed":y,"aria-required":h,"data-state":R(y),"data-disabled":f?"":void 0,disabled:f,value:d,...s,ref:C,onKeyDown:(0,o.m)(l,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.m)(i,e=>{v(e=>!!A(e)||!e),j&&b&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})})});k.displayName=x;var b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:a,defaultChecked:l,required:o,disabled:i,value:s,onCheckedChange:c,form:d,...u}=e;return(0,p.jsx)(v,{__scopeCheckbox:r,checked:a,defaultChecked:l,disabled:i,required:o,onCheckedChange:c,name:n,form:d,value:s,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(k,{...u,ref:t,__scopeCheckbox:r}),n&&(0,p.jsx)(w,{__scopeCheckbox:r})]})}})});b.displayName=f;var j="CheckboxIndicator",C=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...a}=e,l=m(j,r);return(0,p.jsx)(d.C,{present:n||A(l.checked)||!0===l.checked,children:(0,p.jsx)(u.sG.span,{"data-state":R(l.checked),"data-disabled":l.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=j;var D="CheckboxBubbleInput",w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...l}=e,{control:o,hasConsumerStoppedPropagationRef:i,checked:d,defaultChecked:f,required:y,disabled:h,name:g,value:v,form:x,bubbleInput:k,setBubbleInput:b}=m(D,r),j=(0,a.s)(t,b),C=(0,s.Z)(d),w=(0,c.X)(o);n.useEffect(()=>{if(!k)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(C!==d&&e){let r=new Event("click",{bubbles:t});k.indeterminate=A(d),e.call(k,!A(d)&&d),k.dispatchEvent(r)}},[k,C,d,i]);let R=n.useRef(!A(d)&&d);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=f?f:R.current,required:y,disabled:h,name:g,value:v,form:x,...l,tabIndex:-1,ref:j,style:{...l.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"indeterminate"===e}function R(e){return A(e)?"indeterminate":e?"checked":"unchecked"}w.displayName=D}}]);