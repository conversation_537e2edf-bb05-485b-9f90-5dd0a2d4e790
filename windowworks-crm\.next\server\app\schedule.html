<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/565c5cdb6422d85d.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-7830a1e56b896d24.js"/><script src="/_next/static/chunks/4bd1b696-4c55c06374f17186.js" async=""></script><script src="/_next/static/chunks/684-59965ae66533eeba.js" async=""></script><script src="/_next/static/chunks/main-app-192255ccf50adb76.js" async=""></script><script src="/_next/static/chunks/app/layout-a5e0196e67753112.js" async=""></script><script src="/_next/static/chunks/418-df76ad183e9997a3.js" async=""></script><script src="/_next/static/chunks/0-b81ccb400d47eae3.js" async=""></script><script src="/_next/static/chunks/376-b00716b5fdd8b73c.js" async=""></script><script src="/_next/static/chunks/205-f8008496adcb6314.js" async=""></script><script src="/_next/static/chunks/app/schedule/page-1bb0cb84adb0a5c5.js" async=""></script><script>
              (function() {
                try {
                  const theme = localStorage.getItem('windowworks-theme');
                  if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                  } else {
                    document.documentElement.classList.remove('dark');
                  }
                } catch (_) {}
              })();
            </script><title>WindowWorks CRM</title><meta name="description" content="Customer and project management with installer workflows"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div hidden=""><!--$--><!--/$--></div><div class="h-screen bg-background flex overflow-hidden"><aside class="w-64 bg-card border-r border-border flex flex-col fixed left-0 top-0 h-full z-30" style="transform:translateX(-300px)"><div class="p-6 border-b border-border flex-shrink-0"><div class="flex items-center space-x-3" style="opacity:0;transform:translateY(-20px)"><div class="w-8 h-8 bg-[var(--color-brand-primary)] rounded-md flex items-center justify-center"><span class="text-white font-bold text-sm">W</span></div><div><h1 class="text-xl font-bold text-foreground">WindowWorks</h1><p class="text-xs text-muted-foreground">CRM Platform</p></div></div></div><nav class="flex-1 p-4 space-y-2 overflow-y-auto"><div style="opacity:0;transform:translateX(-20px)"><a href="/"><button data-slot="button" class="inline-flex items-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house mr-3 h-5 w-5" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Dashboard</button></a></div><div style="opacity:0;transform:translateX(-20px)"><a href="/customers"><button data-slot="button" class="inline-flex items-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users mr-3 h-5 w-5" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg>Customers</button></a></div><div style="opacity:0;transform:translateX(-20px)"><a href="/projects"><button data-slot="button" class="inline-flex items-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open mr-3 h-5 w-5" aria-hidden="true"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg>Projects</button></a></div><div style="opacity:0;transform:translateX(-20px)"><a href="/schedule"><button data-slot="button" class="inline-flex items-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-9 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start rounded-md bg-primary/10 text-primary border-primary/20"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar mr-3 h-5 w-5" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg>Schedule</button></a></div><div style="opacity:0;transform:translateX(-20px)"><a href="/settings"><button data-slot="button" class="inline-flex items-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings mr-3 h-5 w-5" aria-hidden="true"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>Settings</button></a></div></nav><div class="p-4 border-t border-border flex-shrink-0 sticky bottom-0 bg-card"><button data-slot="dropdown-menu-trigger" class="inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:text-accent-foreground dark:hover:bg-accent/50 h-9 has-[&gt;svg]:px-3 w-full justify-start p-3 hover:bg-accent" type="button" id="radix-«R3iutb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><span data-slot="avatar" class="relative flex size-8 shrink-0 overflow-hidden rounded-full h-8 w-8 mr-3"><span data-slot="avatar-fallback" class="flex size-full items-center justify-center rounded-full bg-primary/10 text-primary"></span></span><div class="text-left"><p class="text-sm font-medium text-foreground"> </p><p class="text-xs text-muted-foreground capitalize"></p></div></button></div></aside><div class="flex-1 flex flex-col bg-background ml-64"><header class="bg-card border-b border-border px-6 py-4 sticky top-0 z-20 flex-shrink-0" style="opacity:0;transform:translateY(-50px)"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 md:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu h-5 w-5" aria-hidden="true"><path d="M4 12h16"></path><path d="M4 18h16"></path><path d="M4 6h16"></path></svg></button><div class="relative w-96 hidden md:block"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" aria-hidden="true"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg><input data-slot="input" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 flex h-9 w-full min-w-0 rounded-md border px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive pl-10 bg-accent/50 border-border focus:bg-background" placeholder="Search customers, projects..."/></div></div><div class="flex items-center space-x-4"><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-5 w-5" aria-hidden="true"><path d="M10.268 21a2 2 0 0 0 3.464 0"></path><path d="M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"></path></svg></button></div></div></header><main class="flex-1 overflow-y-auto bg-background"><div class="p-6"><div style="opacity:0;transform:translateY(20px)"><div class="space-y-6"><div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4"><div><h1 class="text-3xl font-bold text-slate-700">Schedule</h1><p class="text-slate-600 mt-1">Manage installer appointments and project timelines</p><p class="text-sm font-medium mt-1" style="color:#D97706">Friday, July 11, 2025</p></div><div class="flex items-center gap-3"><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-funnel h-4 w-4 mr-2" aria-hidden="true"><path d="M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z"></path></svg>Filter</button><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download h-4 w-4 mr-2" aria-hidden="true"><path d="M12 15V3"></path><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><path d="m7 10 5 5 5-5"></path></svg>Export</button><button data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[&gt;svg]:px-3 text-white" style="background-color:#D97706;border-color:#D97706"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-4 w-4 mr-2" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg>New Appointment</button></div></div><div class="grid grid-cols-1 md:grid-cols-4 gap-6"><div data-slot="card" class="text-card-foreground flex flex-col gap-6 rounded-xl border py-6 transition-colors dark:border-muted-foreground/50 dark:hover:border-muted-foreground bg-white border-gray-200 shadow-sm"><div data-slot="card-header" class="@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2"><div data-slot="card-title" class="text-sm font-medium text-slate-600">Upcoming Appointments</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4" aria-hidden="true" style="color:#D97706"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></div><div data-slot="card-content" class="px-6"><div class="text-2xl font-bold text-slate-700">15</div><p class="text-xs text-green-600 flex items-center mt-1">+10% from last week</p></div></div><div data-slot="card" class="text-card-foreground flex flex-col gap-6 rounded-xl border py-6 transition-colors dark:border-muted-foreground/50 dark:hover:border-muted-foreground bg-white border-gray-200 shadow-sm"><div data-slot="card-header" class="@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2"><div data-slot="card-title" class="text-sm font-medium text-slate-600">Installer Utilization</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-column h-4 w-4" aria-hidden="true" style="color:#D97706"><path d="M3 3v16a2 2 0 0 0 2 2h16"></path><path d="M18 17V9"></path><path d="M13 17V5"></path><path d="M8 17v-3"></path></svg></div><div data-slot="card-content" class="px-6"><div class="text-2xl font-bold text-slate-700">75%</div><p class="text-xs text-green-600 flex items-center mt-1">+5% from last month</p></div></div><div data-slot="card" class="text-card-foreground flex flex-col gap-6 rounded-xl border py-6 transition-colors dark:border-muted-foreground/50 dark:hover:border-muted-foreground bg-white border-gray-200 shadow-sm"><div data-slot="card-header" class="@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2"><div data-slot="card-title" class="text-sm font-medium text-slate-600">Conflicts</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert h-4 w-4 text-red-500" aria-hidden="true"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg></div><div data-slot="card-content" class="px-6"><div class="text-2xl font-bold text-slate-700">2</div><p class="text-xs text-red-600 flex items-center mt-1">Requires attention</p></div></div><div data-slot="card" class="text-card-foreground flex flex-col gap-6 rounded-xl border py-6 transition-colors dark:border-muted-foreground/50 dark:hover:border-muted-foreground bg-white border-gray-200 shadow-sm"><div data-slot="card-header" class="@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2"><div data-slot="card-title" class="text-sm font-medium text-slate-600">Avg Job Duration</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-timer h-4 w-4" aria-hidden="true" style="color:#D97706"><line x1="10" x2="14" y1="2" y2="2"></line><line x1="12" x2="15" y1="14" y2="11"></line><circle cx="12" cy="14" r="8"></circle></svg></div><div data-slot="card-content" class="px-6"><div class="text-2xl font-bold text-slate-700">4h</div><p class="text-xs text-slate-500 flex items-center mt-1">Per appointment</p></div></div></div><div class="grid grid-cols-1 xl:grid-cols-4 gap-6"><div class="xl:col-span-3 space-y-6"><div data-slot="card" class="text-card-foreground flex flex-col gap-6 rounded-xl border py-6 transition-colors dark:border-muted-foreground/50 dark:hover:border-muted-foreground bg-white border-gray-200 shadow-sm"><div data-slot="card-content" class="p-4"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary shadow-xs hover:bg-primary/90 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5 text-white" style="background-color:#D97706;border-color:#D97706"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4 mr-2" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg>Month</button><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4 mr-2" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg>Week</button><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-4 w-4 mr-2" aria-hidden="true"><path d="M12 6v6l4 2"></path><circle cx="12" cy="12" r="10"></circle></svg>Day</button></div><div class="text-center"><h2 class="font-semibold text-slate-700">July 2025</h2></div><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-8 rounded-md gap-1.5 px-3 has-[&gt;svg]:px-2.5">Today</button></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="bg-white rounded-lg border border-gray-200 shadow-sm"><div class="flex items-center justify-between p-4 border-b border-gray-200"><h2 class="text-lg font-semibold text-slate-700">July<!-- --> <!-- -->2025</h2><div class="flex items-center space-x-2"><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 rounded-md gap-1.5 has-[&gt;svg]:px-2.5 h-8 w-8 p-0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left h-4 w-4" aria-hidden="true"><path d="m15 18-6-6 6-6"></path></svg></button><button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 rounded-md gap-1.5 has-[&gt;svg]:px-2.5 h-8 w-8 p-0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4" aria-hidden="true"><path d="m9 18 6-6-6-6"></path></svg></button></div></div><div class="p-4"><div class="grid grid-cols-7 mb-2"><div class="text-center text-sm font-medium text-slate-600 py-2">Sun</div><div class="text-center text-sm font-medium text-slate-600 py-2">Mon</div><div class="text-center text-sm font-medium text-slate-600 py-2">Tue</div><div class="text-center text-sm font-medium text-slate-600 py-2">Wed</div><div class="text-center text-sm font-medium text-slate-600 py-2">Thu</div><div class="text-center text-sm font-medium text-slate-600 py-2">Fri</div><div class="text-center text-sm font-medium text-slate-600 py-2">Sat</div></div><div class="grid grid-cols-7 gap-1"><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">30</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">31</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">1</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">2</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">3</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">4</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">5</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">6</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">7</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">8</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">9</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">10</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  ring-2 ring-blue-400 bg-blue-50
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">11</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  ring-2 ring-amber-400 bg-amber-50
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-amber-700
                ">12</div><div class="space-y-1"><div class="
                        text-xs p-1 rounded text-white truncate cursor-pointer
                        bg-blue-500
                      " title="09:00 - Kitchen Window Installation">09:00<!-- --> <!-- -->Kitchen Window Installation</div><div class="
                        text-xs p-1 rounded text-white truncate cursor-pointer
                        bg-amber-500
                      " title="14:00 - Bathroom Consultation">14:00<!-- --> <!-- -->Bathroom Consultation</div></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">13</div><div class="space-y-1"><div class="
                        text-xs p-1 rounded text-white truncate cursor-pointer
                        bg-orange-500
                      " title="10:00 - Living Room Curtains">10:00<!-- --> <!-- -->Living Room Curtains</div></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">14</div><div class="space-y-1"><div class="
                        text-xs p-1 rounded text-white truncate cursor-pointer
                        bg-green-500
                      " title="15:00 - Window Maintenance">15:00<!-- --> <!-- -->Window Maintenance</div></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">15</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">16</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">17</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">18</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">19</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">20</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">21</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">22</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">23</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">24</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">25</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">26</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">27</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">28</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">29</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">30</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-700
                ">31</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">1</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">2</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">3</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">4</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">5</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">6</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">7</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">8</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="
                  relative min-h-[120px] p-2 border border-gray-100 rounded-md cursor-pointer
                  transition-all duration-200 hover:bg-gray-50 group
                  opacity-40
                  
                  
                " tabindex="0"><div class="
                  text-sm font-medium mb-1
                  text-slate-400
                ">9</div><div class="space-y-1"></div><button class="absolute top-1 right-1 opacity-0 group-hover:opacity-100    w-5 h-5 bg-amber-500 rounded text-white flex items-center justify-center   hover:bg-amber-600 transition-all duration-200" title="Add appointment" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3 w-3" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div></div></div><div class="flex items-center justify-center space-x-4 p-4 border-t border-gray-200 bg-gray-50"><div class="flex items-center space-x-1"><div class="w-3 h-3 rounded bg-amber-500"></div><span class="text-xs text-slate-600">Pending</span></div><div class="flex items-center space-x-1"><div class="w-3 h-3 rounded bg-blue-500"></div><span class="text-xs text-slate-600">Assigned</span></div><div class="flex items-center space-x-1"><div class="w-3 h-3 rounded bg-orange-500"></div><span class="text-xs text-slate-600">In Progress</span></div><div class="flex items-center space-x-1"><div class="w-3 h-3 rounded bg-green-500"></div><span class="text-xs text-slate-600">Completed</span></div><div class="flex items-center space-x-1"><div class="w-3 h-3 rounded bg-red-500"></div><span class="text-xs text-slate-600">Conflict</span></div></div></div></div></div><div class="space-y-6"><div data-slot="card" class="text-card-foreground flex flex-col gap-6 rounded-xl border py-6 transition-colors dark:border-muted-foreground/50 dark:hover:border-muted-foreground bg-white border-gray-200 shadow-sm"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-3"><div data-slot="card-title" class="font-semibold text-lg text-slate-700">Quick Search</div></div><div data-slot="card-content" class="px-6 space-y-3"><div class="relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" aria-hidden="true"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg><input data-slot="input" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive pl-10 border-gray-200" placeholder="Search appointments..." value=""/></div></div></div><div data-slot="card" class="text-card-foreground flex flex-col gap-6 rounded-xl border py-6 transition-colors dark:border-muted-foreground/50 dark:hover:border-muted-foreground bg-white border-gray-200 shadow-sm"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-3"><div data-slot="card-title" class="font-semibold text-lg text-slate-700">Today&#x27;s Schedule</div></div><div data-slot="card-content" class="px-6 space-y-3"><div class="p-3 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"><div class="flex items-start justify-between mb-2"><h4 class="font-medium text-sm text-slate-700">Kitchen Window Installation</h4><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&amp;]:hover:bg-secondary/90 bg-blue-100 text-blue-700">assigned</span></div><div class="space-y-1 text-xs text-slate-500"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-3 w-3 mr-1" aria-hidden="true"><path d="M12 6v6l4 2"></path><circle cx="12" cy="12" r="10"></circle></svg>09:00</div><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-3 w-3 mr-1" aria-hidden="true"><path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path><circle cx="12" cy="10" r="3"></circle></svg>Sarah Johnson</div></div><div class="flex items-center mt-2"><span data-slot="avatar" class="relative flex size-8 shrink-0 overflow-hidden rounded-full h-5 w-5 mr-2"><span data-slot="avatar-fallback" class="flex size-full items-center justify-center rounded-full text-xs bg-opacity-20 text-slate-700" style="background-color:#D9770633;color:#D97706">AT</span></span><span class="text-xs text-slate-600">Alex Thompson</span></div></div><div class="p-3 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"><div class="flex items-start justify-between mb-2"><h4 class="font-medium text-sm text-slate-700">Bathroom Consultation</h4><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&amp;]:hover:bg-secondary/90 bg-opacity-20 text-slate-700" style="background-color:#D9770633;color:#D97706">pending</span></div><div class="space-y-1 text-xs text-slate-500"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-3 w-3 mr-1" aria-hidden="true"><path d="M12 6v6l4 2"></path><circle cx="12" cy="12" r="10"></circle></svg>14:00</div><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-3 w-3 mr-1" aria-hidden="true"><path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path><circle cx="12" cy="10" r="3"></circle></svg>Mike Chen</div></div><div class="flex items-center mt-2"><span data-slot="avatar" class="relative flex size-8 shrink-0 overflow-hidden rounded-full h-5 w-5 mr-2"><span data-slot="avatar-fallback" class="flex size-full items-center justify-center rounded-full text-xs bg-opacity-20 text-slate-700" style="background-color:#D9770633;color:#D97706">JM</span></span><span class="text-xs text-slate-600">Jordan Martinez</span></div></div></div></div><div data-slot="card" class="text-card-foreground flex flex-col gap-6 rounded-xl border py-6 transition-colors dark:border-muted-foreground/50 dark:hover:border-muted-foreground bg-white border-gray-200 shadow-sm"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-3"><div data-slot="card-title" class="font-semibold text-lg text-slate-700">Upcoming</div></div><div data-slot="card-content" class="px-6 space-y-3"><div class="p-3 border border-gray-200 rounded-lg"><div class="flex items-start justify-between mb-2"><h4 class="font-medium text-sm text-slate-700">Living Room Curtains</h4><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&amp;]:hover:bg-secondary/90 bg-gray-100 text-gray-700">in-progress</span></div><div class="space-y-1 text-xs text-slate-500"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-3 w-3 mr-1" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg>7/12/2025</div><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-3 w-3 mr-1" aria-hidden="true"><path d="M12 6v6l4 2"></path><circle cx="12" cy="12" r="10"></circle></svg>10:00</div></div></div><div class="p-3 border border-gray-200 rounded-lg"><div class="flex items-start justify-between mb-2"><h4 class="font-medium text-sm text-slate-700">Window Maintenance</h4><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&amp;]:hover:bg-secondary/90 bg-gray-100 text-gray-700">completed</span></div><div class="space-y-1 text-xs text-slate-500"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-3 w-3 mr-1" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg>7/13/2025</div><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-3 w-3 mr-1" aria-hidden="true"><path d="M12 6v6l4 2"></path><circle cx="12" cy="12" r="10"></circle></svg>15:00</div></div></div></div></div></div></div></div></div></div></main></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-7830a1e56b896d24.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[2905,[\"177\",\"static/chunks/app/layout-a5e0196e67753112.js\"],\"ThemeProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[871,[\"418\",\"static/chunks/418-df76ad183e9997a3.js\",\"0\",\"static/chunks/0-b81ccb400d47eae3.js\",\"376\",\"static/chunks/376-b00716b5fdd8b73c.js\",\"205\",\"static/chunks/205-f8008496adcb6314.js\",\"826\",\"static/chunks/app/schedule/page-1bb0cb84adb0a5c5.js\"],\"DashboardLayout\"]\n6:I[3972,[\"418\",\"static/chunks/418-df76ad183e9997a3.js\",\"0\",\"static/chunks/0-b81ccb400d47eae3.js\",\"376\",\"static/chunks/376-b00716b5fdd8b73c.js\",\"205\",\"static/chunks/205-f8008496adcb6314.js\",\"826\",\"static/chunks/app/schedule/page-1bb0cb84adb0a5c5.js\"],\"default\"]\n7:I[9665,[],\"OutletBoundary\"]\na:I[4911,[],\"AsyncMetadataOutlet\"]\nc:I[9665,[],\"ViewportBoundary\"]\ne:I[9665,[],\"MetadataBoundary\"]\n10:I[6614,[],\"\"]\n:HL[\"/_next/static/css/565c5cdb6422d85d.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"_Z2cIrbfG8K2YjdWsuK6p\",\"p\":\"\",\"c\":[\"\",\"schedule\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"schedule\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/565c5cdb6422d85d.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n              (function() {\\n                try {\\n                  const theme = localStorage.getItem('windowworks-theme');\\n                  if (theme === 'dark' || (!theme \u0026\u0026 window.matchMedia('(prefers-color-scheme: dark)').matches)) {\\n                    document.documentElement.classList.add('dark');\\n                  } else {\\n                    document.documentElement.classList.remove('dark');\\n                  }\\n                } catch (_) {}\\n              })();\\n            \"}}]}],[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}]]}],{\"children\":[\"schedule\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"children\":[\"$\",\"$L6\",null,{}]}],null,[\"$\",\"$L7\",null,{\"children\":[\"$L8\",\"$L9\",[\"$\",\"$La\",null,{\"promise\":\"$@b\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"bdR-PQuSn0cCC8_K3L6RPv\",{\"children\":[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],null]}],[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$10\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"11:\"$Sreact.suspense\"\n12:I[4911,[],\"AsyncMetadata\"]\nf:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$11\",null,{\"fallback\":null,\"children\":[\"$\",\"$L12\",null,{\"promise\":\"$@13\"}]}]}]\n9:null\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n8:null\n"])</script><script>self.__next_f.push([1,"b:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"WindowWorks CRM\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Customer and project management with installer workflows\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n13:{\"metadata\":\"$b:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>