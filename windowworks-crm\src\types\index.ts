// User types
export interface User {
  id: string
  email: string
  role: UserRole
  profile: UserProfile
  createdAt: string
  updatedAt: string
}

export type UserRole = 'admin' | 'installer' | 'customer'

export interface UserProfile {
  firstName: string
  lastName: string
  phone?: string
  avatar?: string
}

// Customer types
export interface Customer {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  address: Address
  preferences: CustomerPreferences
  notes?: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface Address {
  street: string
  city: string
  state: string
  zipCode: string
  country: string
}

export interface CustomerPreferences {
  windowTreatmentTypes: WindowTreatmentType[]
  preferredColors: string[]
  budget?: BudgetRange
  communication: CommunicationPreference
}

export type WindowTreatmentType = 'blinds' | 'shutters' | 'shades' | 'curtains' | 'drapes'
export type BudgetRange = 'under-500' | '500-1000' | '1000-2500' | '2500-5000' | 'over-5000'
export type CommunicationPreference = 'email' | 'phone' | 'text' | 'app'

// Project types
export interface Project {
  id: string
  customerId: string
  customer?: Customer
  title: string
  description: string
  status: ProjectStatus
  priority: ProjectPriority
  estimatedCost: number
  actualCost?: number
  estimatedDuration: number // in days
  startDate?: string
  completionDate?: string
  installerId?: string
  installer?: User
  rooms: ProjectRoom[]
  notes?: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

export type ProjectStatus = 
  | 'draft'
  | 'quoted'
  | 'approved'
  | 'scheduled'
  | 'in-progress'
  | 'completed'
  | 'cancelled'
  | 'on-hold'

export type ProjectPriority = 'low' | 'medium' | 'high' | 'urgent'

export interface ProjectRoom {
  id: string
  name: string
  windows: WindowSpec[]
  measurements?: RoomMeasurements
  notes?: string
}

export interface WindowSpec {
  id: string
  width: number
  height: number
  treatmentType: WindowTreatmentType
  color: string
  material: string
  installationType: 'inside-mount' | 'outside-mount'
  features: string[]
  cost: number
}

export interface RoomMeasurements {
  length: number
  width: number
  height: number
  notes?: string
}

// Installation workflow types
export interface InstallationTask {
  id: string
  projectId: string
  title: string
  description: string
  status: TaskStatus
  assignedTo?: string
  estimatedDuration: number // in minutes
  actualDuration?: number
  startTime?: string
  endTime?: string
  location: Address
  photos: InstallationPhoto[]
  notes?: string
  createdAt: string
  updatedAt: string
}

export type TaskStatus = 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'rescheduled'

export interface InstallationPhoto {
  id: string
  url: string
  caption?: string
  timestamp: string
  type: 'before' | 'during' | 'after' | 'issue'
}

// Notification types
export interface Notification {
  id: string
  userId: string
  type: NotificationType
  title: string
  message: string
  data?: Record<string, unknown>
  read: boolean
  createdAt: string
}

export type NotificationType = 
  | 'project-assigned'
  | 'project-updated'
  | 'task-assigned'
  | 'task-completed'
  | 'customer-message'
  | 'schedule-reminder'
  | 'system-alert'

// API Response types
export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form types
export interface CustomerFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  address: Address
  preferences: Partial<CustomerPreferences>
  notes?: string
}

export interface ProjectFormData {
  customerId: string
  title: string
  description: string
  priority: ProjectPriority
  estimatedCost: number
  estimatedDuration: number
  startDate?: string
  installerId?: string
  rooms: Omit<ProjectRoom, 'id'>[]
  notes?: string
}

// Dashboard types
export interface DashboardStats {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  totalCustomers: number
  revenueThisMonth: number
  revenueLastMonth: number
  averageProjectValue: number
  upcomingTasks: number
}

export interface RecentActivity {
  id: string
  type: 'project-created' | 'project-updated' | 'task-completed' | 'customer-added'
  title: string
  description: string
  timestamp: string
  user: string
  entityId?: string
}

// Search and filter types
export interface SearchFilters {
  query?: string
  status?: string[]
  dateRange?: {
    start: string
    end: string
  }
  assignedTo?: string[]
  priority?: ProjectPriority[]
}

export interface SortOption {
  field: string
  direction: 'asc' | 'desc'
}

// Component prop types
export interface TableColumn<T> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: unknown, row: T) => React.ReactNode
}
