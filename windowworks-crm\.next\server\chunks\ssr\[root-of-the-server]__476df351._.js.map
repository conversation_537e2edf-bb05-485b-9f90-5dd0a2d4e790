{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/stores/auth-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, persist } from 'zustand/middleware'\r\nimport type { User } from '@/types'\r\n\r\ninterface AuthState {\r\n  user: User | null\r\n  isLoading: boolean\r\n  isAuthenticated: boolean\r\n}\r\n\r\ninterface AuthActions {\r\n  setUser: (user: User | null) => void\r\n  setLoading: (loading: boolean) => void\r\n  clearAuth: () => void\r\n}\r\n\r\nexport const useAuthStore = create<AuthState & AuthActions>()(\r\n  devtools(\r\n    persist(\r\n      (set) => ({\r\n        user: null,\r\n        isLoading: true,\r\n        isAuthenticated: false,\r\n        setUser: (user) =>\r\n          set(\r\n            {\r\n              user,\r\n              isAuthenticated: !!user,\r\n              isLoading: false,\r\n            },\r\n            false,\r\n            'setUser'\r\n          ),\r\n        setLoading: (isLoading) =>\r\n          set({ isLoading }, false, 'setLoading'),\r\n        clearAuth: () =>\r\n          set(\r\n            {\r\n              user: null,\r\n              isAuthenticated: false,\r\n              isLoading: false,\r\n            },\r\n            false,\r\n            'clearAuth'\r\n          ),\r\n      }),\r\n      {\r\n        name: 'auth-storage',\r\n        partialize: (state) => ({\r\n          user: state.user,\r\n          isAuthenticated: state.isAuthenticated,\r\n        }),\r\n      }\r\n    ),\r\n    {\r\n      name: 'auth-store',\r\n    }\r\n  )\r\n)\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAeO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,WAAW;QACX,iBAAiB;QACjB,SAAS,CAAC,OACR,IACE;gBACE;gBACA,iBAAiB,CAAC,CAAC;gBACnB,WAAW;YACb,GACA,OACA;QAEJ,YAAY,CAAC,YACX,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B,WAAW,IACT,IACE;gBACE,MAAM;gBACN,iBAAiB;gBACjB,WAAW;YACb,GACA,OACA;IAEN,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH,IAEF;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/lib/stores/notification-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools } from 'zustand/middleware'\r\nimport type { Notification } from '@/types'\r\n\r\ninterface NotificationState {\r\n  notifications: Notification[]\r\n  unreadCount: number\r\n}\r\n\r\ninterface NotificationActions {\r\n  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void\r\n  markAsRead: (id: string) => void\r\n  markAllAsRead: () => void\r\n  removeNotification: (id: string) => void\r\n  setNotifications: (notifications: Notification[]) => void\r\n}\r\n\r\nexport const useNotificationStore = create<NotificationState & NotificationActions>()(\r\n  devtools(\r\n    (set) => ({\r\n      notifications: [],\r\n      unreadCount: 0,\r\n      addNotification: (notification) => {\r\n        const newNotification: Notification = {\r\n          ...notification,\r\n          id: crypto.randomUUID(),\r\n          createdAt: new Date().toISOString(),\r\n        }\r\n        set(\r\n          (state) => ({\r\n            notifications: [newNotification, ...state.notifications],\r\n            unreadCount: state.unreadCount + 1,\r\n          }),\r\n          false,\r\n          'addNotification'\r\n        )\r\n      },\r\n      markAsRead: (id) =>\r\n        set(\r\n          (state) => ({\r\n            notifications: state.notifications.map((notification) =>\r\n              notification.id === id\r\n                ? { ...notification, read: true }\r\n                : notification\r\n            ),\r\n            unreadCount: Math.max(0, state.unreadCount - 1),\r\n          }),\r\n          false,\r\n          'markAsRead'\r\n        ),\r\n      markAllAsRead: () =>\r\n        set(\r\n          (state) => ({\r\n            notifications: state.notifications.map((notification) => ({\r\n              ...notification,\r\n              read: true,\r\n            })),\r\n            unreadCount: 0,\r\n          }),\r\n          false,\r\n          'markAllAsRead'\r\n        ),\r\n      removeNotification: (id) =>\r\n        set(\r\n          (state) => {\r\n            const notification = state.notifications.find((n) => n.id === id)\r\n            const wasUnread = notification && !notification.read\r\n            return {\r\n              notifications: state.notifications.filter((n) => n.id !== id),\r\n              unreadCount: wasUnread\r\n                ? Math.max(0, state.unreadCount - 1)\r\n                : state.unreadCount,\r\n            }\r\n          },\r\n          false,\r\n          'removeNotification'\r\n        ),\r\n      setNotifications: (notifications) =>\r\n        set(\r\n          {\r\n            notifications,\r\n            unreadCount: notifications.filter((n) => !n.read).length,\r\n          },\r\n          false,\r\n          'setNotifications'\r\n        ),\r\n    }),\r\n    {\r\n      name: 'notification-store',\r\n    }\r\n  )\r\n)\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBO,MAAM,uBAAuB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACvC,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAC,MAAQ,CAAC;QACR,eAAe,EAAE;QACjB,aAAa;QACb,iBAAiB,CAAC;YAChB,MAAM,kBAAgC;gBACpC,GAAG,YAAY;gBACf,IAAI,OAAO,UAAU;gBACrB,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,IACE,CAAC,QAAU,CAAC;oBACV,eAAe;wBAAC;2BAAoB,MAAM,aAAa;qBAAC;oBACxD,aAAa,MAAM,WAAW,GAAG;gBACnC,CAAC,GACD,OACA;QAEJ;QACA,YAAY,CAAC,KACX,IACE,CAAC,QAAU,CAAC;oBACV,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eACtC,aAAa,EAAE,KAAK,KAChB;4BAAE,GAAG,YAAY;4BAAE,MAAM;wBAAK,IAC9B;oBAEN,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;gBAC/C,CAAC,GACD,OACA;QAEJ,eAAe,IACb,IACE,CAAC,QAAU,CAAC;oBACV,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,eAAiB,CAAC;4BACxD,GAAG,YAAY;4BACf,MAAM;wBACR,CAAC;oBACD,aAAa;gBACf,CAAC,GACD,OACA;QAEJ,oBAAoB,CAAC,KACnB,IACE,CAAC;gBACC,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAC9D,MAAM,YAAY,gBAAgB,CAAC,aAAa,IAAI;gBACpD,OAAO;oBACL,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAC1D,aAAa,YACT,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG,KAChC,MAAM,WAAW;gBACvB;YACF,GACA,OACA;QAEJ,kBAAkB,CAAC,gBACjB,IACE;gBACE;gBACA,aAAa,cAAc,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,IAAI,EAAE,MAAM;YAC1D,GACA,OACA;IAEN,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/dashboard/dashboard-layout.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ReactNode } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { \r\n  Bell, \r\n  Home, \r\n  Users, \r\n  FolderOpen, \r\n  Calendar,\r\n  Settings,\r\n  LogOut,\r\n  Menu,\r\n  Search\r\n} from 'lucide-react'\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu'\r\nimport { useAuthStore } from '@/lib/stores/auth-store'\r\nimport { useNotificationStore } from '@/lib/stores/notification-store'\r\n\r\ninterface DashboardLayoutProps {\r\n  children: ReactNode\r\n}\r\n\r\nconst navigationItems = [\r\n  { icon: Home, label: 'Dashboard', href: '/', badge: null },\r\n  { icon: Users, label: 'Customers', href: '/customers', badge: null },\r\n  { icon: FolderOpen, label: 'Projects', href: '/projects', badge: null },\r\n  { icon: Calendar, label: 'Schedule', href: '/schedule', badge: null },\r\n  { icon: Settings, label: 'Settings', href: '/settings', badge: null },\r\n]\r\n\r\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\r\n  const { user, clearAuth } = useAuthStore()\r\n  const { unreadCount } = useNotificationStore()\r\n  const pathname = usePathname()\r\n\r\n  const handleLogout = () => {\r\n    clearAuth()\r\n    // In a real app, you'd also call Supabase signOut here\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-screen bg-background flex overflow-hidden\">\r\n      {/* Sidebar */}\r\n      <motion.aside\r\n        initial={{ x: -300 }}\r\n        animate={{ x: 0 }}\r\n        className=\"w-64 bg-card border-r border-border flex flex-col fixed left-0 top-0 h-full z-30\"\r\n      >\r\n        {/* Logo */}\r\n        <div className=\"p-6 border-b border-border flex-shrink-0\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.2 }}\r\n            className=\"flex items-center space-x-3\"\r\n          >\r\n            <div className=\"w-8 h-8 bg-[var(--color-brand-primary)] rounded-md flex items-center justify-center\">\r\n              <span className=\"text-white font-bold text-sm\">W</span>\r\n            </div>\r\n            <div>\r\n              <h1 className=\"text-xl font-bold text-foreground\">WindowWorks</h1>\r\n              <p className=\"text-xs text-muted-foreground\">CRM Platform</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <nav className=\"flex-1 p-4 space-y-2 overflow-y-auto\">\r\n          {navigationItems.map((item, index) => {\r\n            const isActive = pathname === item.href\r\n            return (\r\n              <motion.div\r\n                key={item.href}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                transition={{ delay: 0.1 * (index + 1) }}\r\n              >\r\n                <Link href={item.href}>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className={`w-full justify-start rounded-md ${\r\n                      isActive \r\n                        ? 'bg-primary/10 text-primary border-primary/20' \r\n                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'\r\n                    }`}\r\n                  >\r\n                    <item.icon className=\"mr-3 h-5 w-5\" />\r\n                    {item.label}\r\n                    {item.badge && (\r\n                      <Badge variant=\"secondary\" className=\"ml-auto\">\r\n                        {item.badge}\r\n                      </Badge>\r\n                    )}\r\n                  </Button>\r\n                </Link>\r\n              </motion.div>\r\n            )\r\n          })}\r\n        </nav>\r\n\r\n        {/* User Profile - Sticky at bottom */}\r\n        <div className=\"p-4 border-t border-border flex-shrink-0 sticky bottom-0 bg-card\">\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" className=\"w-full justify-start p-3 hover:bg-accent\">\r\n                <Avatar className=\"h-8 w-8 mr-3\">\r\n                  <AvatarImage src={user?.profile?.avatar} />\r\n                  <AvatarFallback className=\"bg-primary/10 text-primary\">\r\n                    {user?.profile?.firstName?.[0]}{user?.profile?.lastName?.[0]}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"text-left\">\r\n                  <p className=\"text-sm font-medium text-foreground\">\r\n                    {user?.profile?.firstName} {user?.profile?.lastName}\r\n                  </p>\r\n                  <p className=\"text-xs text-muted-foreground capitalize\">{user?.role}</p>\r\n                </div>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"start\" className=\"w-56\">\r\n              <DropdownMenuItem>\r\n                <Settings className=\"mr-2 h-4 w-4\" />\r\n                Account Settings\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                Sign Out\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </motion.aside>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 flex flex-col bg-background ml-64\">\r\n        {/* Top Header - Sticky */}\r\n        <motion.header\r\n          initial={{ y: -50, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"bg-card border-b border-border px-6 py-4 sticky top-0 z-20 flex-shrink-0\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\r\n                <Menu className=\"h-5 w-5\" />\r\n              </Button>\r\n              \r\n              {/* Search */}\r\n              <div className=\"relative w-96 hidden md:block\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\r\n                <Input\r\n                  placeholder=\"Search customers, projects...\"\r\n                  className=\"pl-10 bg-accent/50 border-border focus:bg-background\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Notifications */}\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\r\n                <Bell className=\"h-5 w-5\" />\r\n                {unreadCount > 0 && (\r\n                  <motion.div\r\n                    initial={{ scale: 0 }}\r\n                    animate={{ scale: 1 }}\r\n                    className=\"absolute -top-1 -right-1 h-5 w-5 bg-destructive rounded-full flex items-center justify-center\"\r\n                  >\r\n                    <span className=\"text-xs text-destructive-foreground font-medium\">\r\n                      {unreadCount > 9 ? '9+' : unreadCount}\r\n                    </span>\r\n                  </motion.div>\r\n                )}\r\n              </Button>\r\n\r\n              {/* Quick Actions */}\r\n              <Button size=\"sm\" className=\"bg-primary hover:bg-primary/90\">\r\n                + New Project\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content - Scrollable */}\r\n        <main className=\"flex-1 overflow-y-auto bg-background\">\r\n          <div className=\"p-6\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.3 }}\r\n            >\r\n              {children}\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AAMA;AACA;AA5BA;;;;;;;;;;;;;AAkCA,MAAM,kBAAkB;IACtB;QAAE,MAAM,mMAAA,CAAA,OAAI;QAAE,OAAO;QAAa,MAAM;QAAK,OAAO;IAAK;IACzD;QAAE,MAAM,oMAAA,CAAA,QAAK;QAAE,OAAO;QAAa,MAAM;QAAc,OAAO;IAAK;IACnE;QAAE,MAAM,kNAAA,CAAA,aAAU;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;IACtE;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;IACpE;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,OAAO;QAAY,MAAM;QAAa,OAAO;IAAK;CACrE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IACvC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,uBAAoB,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB;IACA,uDAAuD;IACzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;kCAMnD,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM;4BAC1B,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,MAAM,CAAC,QAAQ,CAAC;gCAAE;0CAEvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,IAAI;8CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAW,CAAC,gCAAgC,EAC1C,WACI,iDACA,+DACJ;;0DAEF,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,KAAK;4CACV,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,KAAK,KAAK;;;;;;;;;;;;;;;;;+BAlBd,KAAK,IAAI;;;;;wBAyBpB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;;0DAChC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDAAC,KAAK,MAAM,SAAS;;;;;;kEACjC,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;;4DACvB,MAAM,SAAS,WAAW,CAAC,EAAE;4DAAE,MAAM,SAAS,UAAU,CAAC,EAAE;;;;;;;;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DACV,MAAM,SAAS;4DAAU;4DAAE,MAAM,SAAS;;;;;;;kEAE7C,8OAAC;wDAAE,WAAU;kEAA4C,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAIrE,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAQ,WAAU;;sDAC3C,8OAAC,4IAAA,CAAA,mBAAgB;;8DACf,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BAAE,GAAG,CAAC;4BAAI,SAAS;wBAAE;wBAC9B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;;8DAC1C,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,cAAc,mBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO;oDAAE;oDACpB,WAAU;8DAEV,cAAA,8OAAC;wDAAK,WAAU;kEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;sDAOlC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;;;;;;kCAQnE,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAExB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm transition-colors\",\n        \"border-border dark:border-muted-foreground/50 dark:hover:border-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,oFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { Check } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n))\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1458, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/textarea.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nTextarea.displayName = 'Textarea'\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1579, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1753, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/projects/new-project-modal.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState, useCallback } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { \r\n  User,\r\n  Calendar,\r\n  DollarSign,\r\n  Clock,\r\n  Plus,\r\n  Trash2,\r\n  Save,\r\n  Loader2,\r\n  AlertCircle,\r\n  Home,\r\n  Settings,\r\n  FileText\r\n} from 'lucide-react'\r\n\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Textarea } from '@/components/ui/textarea'\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { useTheme } from '@/contexts/theme-context'\r\nimport { ProjectFormData, ProjectPriority, WindowTreatmentType, Customer, User as UserType } from '@/types'\r\n\r\n// Types for the modal\r\ninterface WindowFormData {\r\n  id: string\r\n  width: number | ''\r\n  height: number | ''\r\n  treatmentType: WindowTreatmentType | ''\r\n  color: string\r\n  material: string\r\n  installationType: 'inside-mount' | 'outside-mount'\r\n  features: string[]\r\n  cost: number\r\n}\r\n\r\ninterface RoomFormData {\r\n  id: string\r\n  name: string\r\n  windows: WindowFormData[]\r\n}\r\n\r\ninterface ProjectModalFormData {\r\n  customerId: string\r\n  title: string\r\n  description: string\r\n  priority: ProjectPriority\r\n  estimatedCost: number\r\n  estimatedDuration: number\r\n  startDate: string\r\n  installerId: string\r\n  rooms: RoomFormData[]\r\n  notes: string\r\n}\r\n\r\ninterface NewProjectModalProps {\r\n  isOpen: boolean\r\n  onClose: () => void\r\n  onSave: (project: ProjectFormData) => Promise<void>\r\n  customers: Customer[]\r\n  installers: UserType[]\r\n}\r\n\r\n// Constants\r\nconst priorityOptions = [\r\n  { value: 'low' as ProjectPriority, label: 'Low', color: 'bg-gray-100 text-gray-700' },\r\n  { value: 'medium' as ProjectPriority, label: 'Medium', color: 'bg-blue-100 text-blue-700' },\r\n  { value: 'high' as ProjectPriority, label: 'High', color: 'bg-orange-100 text-orange-700' },\r\n  { value: 'urgent' as ProjectPriority, label: 'Urgent', color: 'bg-red-100 text-red-700' }\r\n]\r\n\r\nconst treatmentTypes = [\r\n  { value: 'blinds' as WindowTreatmentType, label: 'Blinds' },\r\n  { value: 'shutters' as WindowTreatmentType, label: 'Shutters' },\r\n  { value: 'shades' as WindowTreatmentType, label: 'Shades' },\r\n  { value: 'curtains' as WindowTreatmentType, label: 'Curtains' },\r\n  { value: 'drapes' as WindowTreatmentType, label: 'Drapes' }\r\n]\r\n\r\nconst materialOptions = [\r\n  'Wood', 'Vinyl', 'Aluminum', 'Fabric', 'Bamboo', 'Faux Wood', 'Composite'\r\n]\r\n\r\nconst featureOptions = [\r\n  'Motorized', 'Blackout', 'Light Filtering', 'UV Protection', \r\n  'Energy Efficient', 'Cordless', 'Top Down Bottom Up'\r\n]\r\n\r\nconst roomTemplates = [\r\n  { name: 'Living Room', windows: 2 },\r\n  { name: 'Bedroom', windows: 1 },\r\n  { name: 'Kitchen', windows: 1 },\r\n  { name: 'Dining Room', windows: 2 },\r\n  { name: 'Office', windows: 1 },\r\n  { name: 'Bathroom', windows: 1 }\r\n]\r\n\r\nexport function NewProjectModal({ isOpen, onClose, onSave, customers, installers }: NewProjectModalProps) {\r\n  const { accentColor } = useTheme()\r\n  const [activeTab, setActiveTab] = useState('overview')\r\n  const [isLoading, setIsLoading] = useState(false)\r\n  const [errors, setErrors] = useState<Record<string, string>>({})\r\n\r\n  const [formData, setFormData] = useState<ProjectModalFormData>({\r\n    customerId: '',\r\n    title: '',\r\n    description: '',\r\n    priority: 'medium',\r\n    estimatedCost: 0,\r\n    estimatedDuration: 5,\r\n    startDate: '',\r\n    installerId: '',\r\n    rooms: [],\r\n    notes: ''\r\n  })\r\n\r\n  // Generate unique IDs\r\n  const generateId = () => `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\r\n\r\n  // Cost calculation\r\n  const calculateWindowCost = useCallback((window: WindowFormData): number => {\r\n    if (!window.width || !window.height || !window.treatmentType) return 0\r\n    \r\n    const area = Number(window.width) * Number(window.height)\r\n    const baseCostPerSqFt = {\r\n      blinds: 15,\r\n      shutters: 35,\r\n      shades: 25,\r\n      curtains: 20,\r\n      drapes: 30\r\n    }\r\n    \r\n    const materialMultiplier = {\r\n      'Wood': 1.3,\r\n      'Vinyl': 0.8,\r\n      'Aluminum': 0.9,\r\n      'Fabric': 1.1,\r\n      'Bamboo': 1.2,\r\n      'Faux Wood': 1.0,\r\n      'Composite': 1.1\r\n    }\r\n    \r\n    let cost = area * baseCostPerSqFt[window.treatmentType as WindowTreatmentType]\r\n    cost *= materialMultiplier[window.material as keyof typeof materialMultiplier] || 1\r\n    cost += window.features.length * 50 // Feature upcharge\r\n    \r\n    return Math.round(cost)\r\n  }, [])\r\n\r\n  // Update total cost when rooms/windows change\r\n  const calculateTotalCost = useCallback(() => {\r\n    const total = formData.rooms.reduce((roomTotal, room) => {\r\n      return roomTotal + room.windows.reduce((windowTotal, window) => {\r\n        return windowTotal + calculateWindowCost(window)\r\n      }, 0)\r\n    }, 0)\r\n    \r\n    setFormData(prev => ({ ...prev, estimatedCost: total }))\r\n  }, [formData.rooms, calculateWindowCost])\r\n\r\n  // Update form data\r\n  const updateFormData = useCallback((path: string, value: unknown) => {\r\n    setFormData(prev => {\r\n      const keys = path.split('.')\r\n      const newData = { ...prev }\r\n      let current: Record<string, unknown> = newData\r\n      \r\n      for (let i = 0; i < keys.length - 1; i++) {\r\n        if (current[keys[i]] === undefined) current[keys[i]] = {}\r\n        current = current[keys[i]] as Record<string, unknown>\r\n      }\r\n      \r\n      current[keys[keys.length - 1]] = value\r\n      return newData\r\n    })\r\n    \r\n    // Clear error for this field\r\n    setErrors(prev => {\r\n      if (prev[path]) {\r\n        const newErrors = { ...prev }\r\n        delete newErrors[path]\r\n        return newErrors\r\n      }\r\n      return prev\r\n    })\r\n  }, [])\r\n\r\n  // Add new room\r\n  const addRoom = (templateName?: string) => {\r\n    const newRoom: RoomFormData = {\r\n      id: generateId(),\r\n      name: templateName || `Room ${formData.rooms.length + 1}`,\r\n      windows: []\r\n    }\r\n    \r\n    // Add default window if using template\r\n    if (templateName) {\r\n      const template = roomTemplates.find(t => t.name === templateName)\r\n      if (template) {\r\n        for (let i = 0; i < template.windows; i++) {\r\n          newRoom.windows.push({\r\n            id: generateId(),\r\n            width: '',\r\n            height: '',\r\n            treatmentType: '',\r\n            color: 'White',\r\n            material: 'Vinyl',\r\n            installationType: 'inside-mount',\r\n            features: [],\r\n            cost: 0\r\n          })\r\n        }\r\n      }\r\n    }\r\n    \r\n    setFormData(prev => ({\r\n      ...prev,\r\n      rooms: [...prev.rooms, newRoom]\r\n    }))\r\n  }\r\n\r\n  // Remove room\r\n  const removeRoom = (roomId: string) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      rooms: prev.rooms.filter(room => room.id !== roomId)\r\n    }))\r\n  }\r\n\r\n  // Add window to room\r\n  const addWindow = (roomId: string) => {\r\n    const newWindow: WindowFormData = {\r\n      id: generateId(),\r\n      width: '',\r\n      height: '',\r\n      treatmentType: '',\r\n      color: 'White',\r\n      material: 'Vinyl',\r\n      installationType: 'inside-mount',\r\n      features: [],\r\n      cost: 0\r\n    }\r\n    \r\n    setFormData(prev => ({\r\n      ...prev,\r\n      rooms: prev.rooms.map(room => \r\n        room.id === roomId \r\n          ? { ...room, windows: [...room.windows, newWindow] }\r\n          : room\r\n      )\r\n    }))\r\n  }\r\n\r\n  // Remove window from room\r\n  const removeWindow = (roomId: string, windowId: string) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      rooms: prev.rooms.map(room => \r\n        room.id === roomId \r\n          ? { ...room, windows: room.windows.filter(window => window.id !== windowId) }\r\n          : room\r\n      )\r\n    }))\r\n  }\r\n\r\n  // Update window data\r\n  const updateWindow = (roomId: string, windowId: string, field: string, value: unknown) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      rooms: prev.rooms.map(room => \r\n        room.id === roomId \r\n          ? {\r\n              ...room,\r\n              windows: room.windows.map(window => \r\n                window.id === windowId \r\n                  ? { ...window, [field]: value }\r\n                  : window\r\n              )\r\n            }\r\n          : room\r\n      )\r\n    }))\r\n  }\r\n\r\n  // Validation\r\n  const validateForm = useCallback(() => {\r\n    const newErrors: Record<string, string> = {}\r\n    \r\n    if (!formData.title.trim()) newErrors.title = 'Project title is required'\r\n    if (!formData.customerId) newErrors.customerId = 'Customer selection is required'\r\n    if (!formData.description.trim()) newErrors.description = 'Project description is required'\r\n    if (formData.rooms.length === 0) newErrors.rooms = 'At least one room is required'\r\n    \r\n    // Validate rooms and windows\r\n    formData.rooms.forEach((room, roomIndex) => {\r\n      if (!room.name.trim()) {\r\n        newErrors[`rooms.${roomIndex}.name`] = 'Room name is required'\r\n      }\r\n      if (room.windows.length === 0) {\r\n        newErrors[`rooms.${roomIndex}.windows`] = 'At least one window is required per room'\r\n      }\r\n      \r\n      room.windows.forEach((window, windowIndex) => {\r\n        if (!window.width || window.width <= 0) {\r\n          newErrors[`rooms.${roomIndex}.windows.${windowIndex}.width`] = 'Valid width is required'\r\n        }\r\n        if (!window.height || window.height <= 0) {\r\n          newErrors[`rooms.${roomIndex}.windows.${windowIndex}.height`] = 'Valid height is required'\r\n        }\r\n        if (!window.treatmentType) {\r\n          newErrors[`rooms.${roomIndex}.windows.${windowIndex}.treatmentType`] = 'Treatment type is required'\r\n        }\r\n      })\r\n    })\r\n    \r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }, [formData])\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async () => {\r\n    if (!validateForm()) {\r\n      // Switch to appropriate tab with errors\r\n      if (errors.title || errors.customerId || errors.description) {\r\n        setActiveTab('overview')\r\n      } else if (Object.keys(errors).some(key => key.startsWith('rooms'))) {\r\n        setActiveTab('rooms')\r\n      }\r\n      return\r\n    }\r\n    \r\n    setIsLoading(true)\r\n    try {\r\n      // Calculate final costs\r\n      const roomsWithCalculatedCosts = formData.rooms.map(room => ({\r\n        name: room.name,\r\n        windows: room.windows\r\n          .filter(window => window.treatmentType !== '') // Filter out incomplete windows\r\n          .map(window => ({\r\n            id: window.id,\r\n            width: Number(window.width),\r\n            height: Number(window.height),\r\n            treatmentType: window.treatmentType as WindowTreatmentType,\r\n            color: window.color,\r\n            material: window.material,\r\n            installationType: window.installationType,\r\n            features: window.features,\r\n            cost: calculateWindowCost(window)\r\n          }))\r\n      }))\r\n      \r\n      const projectData: ProjectFormData = {\r\n        customerId: formData.customerId,\r\n        title: formData.title,\r\n        description: formData.description,\r\n        priority: formData.priority,\r\n        estimatedCost: formData.estimatedCost,\r\n        estimatedDuration: formData.estimatedDuration,\r\n        startDate: formData.startDate || undefined,\r\n        installerId: formData.installerId || undefined,\r\n        rooms: roomsWithCalculatedCosts,\r\n        notes: formData.notes || undefined\r\n      }\r\n      \r\n      await onSave(projectData)\r\n      onClose()\r\n      \r\n      // Reset form\r\n      setFormData({\r\n        customerId: '',\r\n        title: '',\r\n        description: '',\r\n        priority: 'medium',\r\n        estimatedCost: 0,\r\n        estimatedDuration: 5,\r\n        startDate: '',\r\n        installerId: '',\r\n        rooms: [],\r\n        notes: ''\r\n      })\r\n      setActiveTab('overview')\r\n    } catch (error) {\r\n      console.error('Error saving project:', error)\r\n    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }\r\n\r\n  // Calculate costs when rooms change\r\n  React.useEffect(() => {\r\n    calculateTotalCost()\r\n  }, [calculateTotalCost])\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-6xl sm:max-w-6xl w-[95vw] max-h-[70vh] overflow-hidden p-0\">\r\n        <motion.div \r\n          className=\"flex flex-col h-full\"\r\n          initial={{ opacity: 0, scale: 0.95 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          exit={{ opacity: 0, scale: 0.95 }}\r\n          transition={{ duration: 0.2 }}\r\n        >\r\n          {/* Header */}\r\n          <DialogHeader className=\"px-6 py-4 border-b border-gray-200 flex-shrink-0\">\r\n            <DialogTitle className=\"text-xl font-semibold text-slate-700\">\r\n              Create New Project\r\n            </DialogTitle>\r\n          </DialogHeader>\r\n\r\n          {/* Content */}\r\n          <div className=\"flex-1 overflow-y-auto p-6\">\r\n            <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\r\n              <TabsList className=\"grid w-full grid-cols-4\">\r\n                <TabsTrigger value=\"overview\" className=\"flex items-center gap-2\">\r\n                  <FileText className=\"h-4 w-4\" />\r\n                  Overview\r\n                </TabsTrigger>\r\n                <TabsTrigger value=\"rooms\" className=\"flex items-center gap-2\">\r\n                  <Home className=\"h-4 w-4\" />\r\n                  Rooms\r\n                </TabsTrigger>\r\n                <TabsTrigger value=\"workflow\" className=\"flex items-center gap-2\">\r\n                  <Settings className=\"h-4 w-4\" />\r\n                  Workflow\r\n                </TabsTrigger>\r\n                <TabsTrigger value=\"notes\" className=\"flex items-center gap-2\">\r\n                  <FileText className=\"h-4 w-4\" />\r\n                  Notes\r\n                </TabsTrigger>\r\n              </TabsList>\r\n\r\n              {/* Overview Tab */}\r\n              <TabsContent value=\"overview\" className=\"space-y-6\">\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                  {/* Basic Information */}\r\n                  <Card>\r\n                    <CardHeader>\r\n                      <CardTitle className=\"text-lg text-slate-700 flex items-center gap-2\">\r\n                        <FileText className=\"h-5 w-5\" />\r\n                        Basic Information\r\n                      </CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent className=\"space-y-4\">\r\n                      <div className=\"space-y-2\">\r\n                        <Label htmlFor=\"title\" className=\"text-slate-600\">\r\n                          Project Title *\r\n                        </Label>\r\n                        <Input\r\n                          id=\"title\"\r\n                          value={formData.title}\r\n                          onChange={(e) => updateFormData('title', e.target.value)}\r\n                          className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                            errors.title ? 'border-red-500' : ''\r\n                          }`}\r\n                          placeholder=\"Enter project title\"\r\n                        />\r\n                        {errors.title && (\r\n                          <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                            <AlertCircle className=\"h-3 w-3\" />\r\n                            {errors.title}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <Label htmlFor=\"description\" className=\"text-slate-600\">\r\n                          Description *\r\n                        </Label>\r\n                        <Textarea\r\n                          id=\"description\"\r\n                          value={formData.description}\r\n                          onChange={(e) => updateFormData('description', e.target.value)}\r\n                          className={`min-h-[100px] border-gray-200 focus:border-[var(--color-brand-primary)] ${\r\n                            errors.description ? 'border-red-500' : ''\r\n                          }`}\r\n                          placeholder=\"Describe the project details...\"\r\n                        />\r\n                        {errors.description && (\r\n                          <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                            <AlertCircle className=\"h-3 w-3\" />\r\n                            {errors.description}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <Label className=\"text-slate-600\">Priority</Label>\r\n                        <div className=\"flex gap-2\">\r\n                          {priorityOptions.map((option) => (\r\n                            <Badge\r\n                              key={option.value}\r\n                              variant={formData.priority === option.value ? \"default\" : \"outline\"}\r\n                              className={`cursor-pointer transition-colors ${\r\n                                formData.priority === option.value \r\n                                  ? option.color \r\n                                  : 'border-gray-300 text-gray-600 hover:bg-gray-50'\r\n                              }`}\r\n                              onClick={() => updateFormData('priority', option.value)}\r\n                            >\r\n                              {option.label}\r\n                            </Badge>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n\r\n                  {/* Customer & Assignment */}\r\n                  <Card>\r\n                    <CardHeader>\r\n                      <CardTitle className=\"text-lg text-slate-700 flex items-center gap-2\">\r\n                        <User className=\"h-5 w-5\" />\r\n                        Customer & Assignment\r\n                      </CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent className=\"space-y-4\">\r\n                      <div className=\"space-y-2\">\r\n                        <Label className=\"text-slate-600\">Customer *</Label>\r\n                        <Select \r\n                          value={formData.customerId} \r\n                          onValueChange={(value) => updateFormData('customerId', value)}\r\n                        >\r\n                          <SelectTrigger className={`border-gray-200 ${errors.customerId ? 'border-red-500' : ''}`}>\r\n                            <SelectValue placeholder=\"Select customer\" />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            {customers.map((customer) => (\r\n                              <SelectItem key={customer.id} value={customer.id}>\r\n                                <div className=\"flex items-center gap-2\">\r\n                                  <Avatar className=\"h-6 w-6\">\r\n                                    <AvatarFallback className=\"text-xs\">\r\n                                      {customer.firstName[0]}{customer.lastName[0]}\r\n                                    </AvatarFallback>\r\n                                  </Avatar>\r\n                                  {customer.firstName} {customer.lastName}\r\n                                </div>\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectContent>\r\n                        </Select>\r\n                        {errors.customerId && (\r\n                          <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                            <AlertCircle className=\"h-3 w-3\" />\r\n                            {errors.customerId}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <Label className=\"text-slate-600\">Installer</Label>\r\n                        <Select \r\n                          value={formData.installerId} \r\n                          onValueChange={(value) => updateFormData('installerId', value)}\r\n                        >\r\n                          <SelectTrigger className=\"border-gray-200\">\r\n                            <SelectValue placeholder=\"Select installer (optional)\" />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            {installers.map((installer) => (\r\n                              <SelectItem key={installer.id} value={installer.id}>\r\n                                <div className=\"flex items-center gap-2\">\r\n                                  <Avatar className=\"h-6 w-6\">\r\n                                    <AvatarImage src={installer.profile?.avatar} />\r\n                                    <AvatarFallback className=\"text-xs\">\r\n                                      {installer.profile.firstName[0]}{installer.profile.lastName[0]}\r\n                                    </AvatarFallback>\r\n                                  </Avatar>\r\n                                  {installer.profile.firstName} {installer.profile.lastName}\r\n                                </div>\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectContent>\r\n                        </Select>\r\n                      </div>\r\n\r\n                      <div className=\"grid grid-cols-2 gap-4\">\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"estimatedDuration\" className=\"text-slate-600 flex items-center gap-2\">\r\n                            <Clock className=\"h-4 w-4\" />\r\n                            Duration (days)\r\n                          </Label>\r\n                          <Input\r\n                            id=\"estimatedDuration\"\r\n                            type=\"number\"\r\n                            min=\"1\"\r\n                            value={formData.estimatedDuration}\r\n                            onChange={(e) => updateFormData('estimatedDuration', Number(e.target.value))}\r\n                            className=\"border-gray-200 focus:border-[var(--color-brand-primary)]\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"startDate\" className=\"text-slate-600 flex items-center gap-2\">\r\n                            <Calendar className=\"h-4 w-4\" />\r\n                            Start Date\r\n                          </Label>\r\n                          <Input\r\n                            id=\"startDate\"\r\n                            type=\"date\"\r\n                            value={formData.startDate}\r\n                            onChange={(e) => updateFormData('startDate', e.target.value)}\r\n                            className=\"border-gray-200 focus:border-[var(--color-brand-primary)]\"\r\n                          />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"p-4 bg-amber-50 rounded-lg border border-amber-200\">\r\n                        <div className=\"flex items-center gap-2 text-amber-800\">\r\n                          <DollarSign className=\"h-5 w-5\" />\r\n                          <span className=\"font-medium\">Estimated Cost</span>\r\n                        </div>\r\n                        <div className=\"text-2xl font-bold text-amber-900 mt-1\">\r\n                          ${formData.estimatedCost.toLocaleString()}\r\n                        </div>\r\n                        <p className=\"text-sm text-amber-700 mt-1\">\r\n                          Auto-calculated from room specifications\r\n                        </p>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              {/* Rooms Tab */}\r\n              <TabsContent value=\"rooms\" className=\"space-y-6\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <h3 className=\"text-lg font-semibold text-slate-700\">Room Specifications</h3>\r\n                  <div className=\"flex gap-2\">\r\n                    <Select onValueChange={addRoom}>\r\n                      <SelectTrigger className=\"w-48 border-gray-200\">\r\n                        <SelectValue placeholder=\"Add room template\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {roomTemplates.map((template) => (\r\n                          <SelectItem key={template.name} value={template.name}>\r\n                            {template.name} ({template.windows} window{template.windows !== 1 ? 's' : ''})\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                    <Button\r\n                      onClick={() => addRoom()}\r\n                      className=\"text-white\"\r\n                      style={{ backgroundColor: accentColor, borderColor: accentColor }}\r\n                    >\r\n                      <Plus className=\"h-4 w-4 mr-2\" />\r\n                      Add Room\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n\r\n                {errors.rooms && (\r\n                  <div className=\"p-3 bg-red-50 border border-red-200 rounded-lg\">\r\n                    <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                      <AlertCircle className=\"h-4 w-4\" />\r\n                      {errors.rooms}\r\n                    </p>\r\n                  </div>\r\n                )}\r\n\r\n                <div className=\"space-y-4\">\r\n                  <AnimatePresence>\r\n                    {formData.rooms.map((room, roomIndex) => (\r\n                      <motion.div\r\n                        key={room.id}\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        exit={{ opacity: 0, y: -20 }}\r\n                        transition={{ duration: 0.2 }}\r\n                      >\r\n                        <Card>\r\n                          <CardHeader>\r\n                            <div className=\"flex items-center justify-between\">\r\n                              <div className=\"flex items-center gap-3\">\r\n                                <Home className=\"h-5 w-5 text-slate-600\" />\r\n                                <Input\r\n                                  value={room.name}\r\n                                  onChange={(e) => updateFormData(`rooms.${roomIndex}.name`, e.target.value)}\r\n                                  className={`font-medium border-none focus:border-[var(--color-brand-primary)] bg-transparent p-0 text-lg ${\r\n                                    errors[`rooms.${roomIndex}.name`] ? 'border-red-500' : ''\r\n                                  }`}\r\n                                  placeholder=\"Room name\"\r\n                                />\r\n                              </div>\r\n                              <Button\r\n                                variant=\"ghost\"\r\n                                size=\"sm\"\r\n                                onClick={() => removeRoom(room.id)}\r\n                                className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                              >\r\n                                <Trash2 className=\"h-4 w-4\" />\r\n                              </Button>\r\n                            </div>\r\n                            {errors[`rooms.${roomIndex}.name`] && (\r\n                              <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                                <AlertCircle className=\"h-3 w-3\" />\r\n                                {errors[`rooms.${roomIndex}.name`]}\r\n                              </p>\r\n                            )}\r\n                          </CardHeader>\r\n                          <CardContent>\r\n                            <div className=\"space-y-4\">\r\n                              <div className=\"flex items-center justify-between\">\r\n                                <span className=\"text-sm font-medium text-slate-600\">Windows</span>\r\n                                <Button\r\n                                  variant=\"outline\"\r\n                                  size=\"sm\"\r\n                                  onClick={() => addWindow(room.id)}\r\n                                  className=\"border-gray-200\"\r\n                                >\r\n                                  <Plus className=\"h-4 w-4 mr-1\" />\r\n                                  Add Window\r\n                                </Button>\r\n                              </div>\r\n\r\n                              {errors[`rooms.${roomIndex}.windows`] && (\r\n                                <div className=\"p-2 bg-red-50 border border-red-200 rounded\">\r\n                                  <p className=\"text-sm text-red-600 flex items-center gap-1\">\r\n                                    <AlertCircle className=\"h-3 w-3\" />\r\n                                    {errors[`rooms.${roomIndex}.windows`]}\r\n                                  </p>\r\n                                </div>\r\n                              )}\r\n\r\n                              <div className=\"space-y-3\">\r\n                                <AnimatePresence>\r\n                                  {room.windows.map((window, windowIndex) => (\r\n                                    <motion.div\r\n                                      key={window.id}\r\n                                      initial={{ opacity: 0, height: 0 }}\r\n                                      animate={{ opacity: 1, height: 'auto' }}\r\n                                      exit={{ opacity: 0, height: 0 }}\r\n                                      transition={{ duration: 0.2 }}\r\n                                      className=\"p-4 border border-gray-200 rounded-lg space-y-4\"\r\n                                    >\r\n                                      <div className=\"flex items-center justify-between\">\r\n                                        <span className=\"font-medium text-slate-700\">\r\n                                          Window {windowIndex + 1}\r\n                                        </span>\r\n                                        <Button\r\n                                          variant=\"ghost\"\r\n                                          size=\"sm\"\r\n                                          onClick={() => removeWindow(room.id, window.id)}\r\n                                          className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                                        >\r\n                                          <Trash2 className=\"h-3 w-3\" />\r\n                                        </Button>\r\n                                      </div>\r\n\r\n                                      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3\">\r\n                                        <div className=\"space-y-1\">\r\n                                          <Label className=\"text-xs text-slate-600\">Width (inches) *</Label>\r\n                                          <Input\r\n                                            type=\"number\"\r\n                                            min=\"1\"\r\n                                            step=\"0.25\"\r\n                                            value={window.width}\r\n                                            onChange={(e) => updateWindow(room.id, window.id, 'width', Number(e.target.value))}\r\n                                            className={`text-sm border-gray-200 ${\r\n                                              errors[`rooms.${roomIndex}.windows.${windowIndex}.width`] ? 'border-red-500' : ''\r\n                                            }`}\r\n                                            placeholder=\"Width\"\r\n                                          />\r\n                                        </div>\r\n                                        <div className=\"space-y-1\">\r\n                                          <Label className=\"text-xs text-slate-600\">Height (inches) *</Label>\r\n                                          <Input\r\n                                            type=\"number\"\r\n                                            min=\"1\"\r\n                                            step=\"0.25\"\r\n                                            value={window.height}\r\n                                            onChange={(e) => updateWindow(room.id, window.id, 'height', Number(e.target.value))}\r\n                                            className={`text-sm border-gray-200 ${\r\n                                              errors[`rooms.${roomIndex}.windows.${windowIndex}.height`] ? 'border-red-500' : ''\r\n                                            }`}\r\n                                            placeholder=\"Height\"\r\n                                          />\r\n                                        </div>\r\n                                        <div className=\"space-y-1\">\r\n                                          <Label className=\"text-xs text-slate-600\">Treatment Type *</Label>\r\n                                          <Select \r\n                                            value={window.treatmentType} \r\n                                            onValueChange={(value) => updateWindow(room.id, window.id, 'treatmentType', value)}\r\n                                          >\r\n                                            <SelectTrigger className={`text-sm border-gray-200 ${\r\n                                              errors[`rooms.${roomIndex}.windows.${windowIndex}.treatmentType`] ? 'border-red-500' : ''\r\n                                            }`}>\r\n                                              <SelectValue placeholder=\"Type\" />\r\n                                            </SelectTrigger>\r\n                                            <SelectContent>\r\n                                              {treatmentTypes.map((type) => (\r\n                                                <SelectItem key={type.value} value={type.value}>\r\n                                                  {type.label}\r\n                                                </SelectItem>\r\n                                              ))}\r\n                                            </SelectContent>\r\n                                          </Select>\r\n                                        </div>\r\n                                        <div className=\"space-y-1\">\r\n                                          <Label className=\"text-xs text-slate-600\">Color</Label>\r\n                                          <Input\r\n                                            value={window.color}\r\n                                            onChange={(e) => updateWindow(room.id, window.id, 'color', e.target.value)}\r\n                                            className=\"text-sm border-gray-200\"\r\n                                            placeholder=\"Color\"\r\n                                          />\r\n                                        </div>\r\n                                      </div>\r\n\r\n                                      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-3\">\r\n                                        <div className=\"space-y-1\">\r\n                                          <Label className=\"text-xs text-slate-600\">Material</Label>\r\n                                          <Select \r\n                                            value={window.material} \r\n                                            onValueChange={(value) => updateWindow(room.id, window.id, 'material', value)}\r\n                                          >\r\n                                            <SelectTrigger className=\"text-sm border-gray-200\">\r\n                                              <SelectValue />\r\n                                            </SelectTrigger>\r\n                                            <SelectContent>\r\n                                              {materialOptions.map((material) => (\r\n                                                <SelectItem key={material} value={material}>\r\n                                                  {material}\r\n                                                </SelectItem>\r\n                                              ))}\r\n                                            </SelectContent>\r\n                                          </Select>\r\n                                        </div>\r\n                                        <div className=\"space-y-1\">\r\n                                          <Label className=\"text-xs text-slate-600\">Installation</Label>\r\n                                          <Select \r\n                                            value={window.installationType} \r\n                                            onValueChange={(value) => updateWindow(room.id, window.id, 'installationType', value)}\r\n                                          >\r\n                                            <SelectTrigger className=\"text-sm border-gray-200\">\r\n                                              <SelectValue />\r\n                                            </SelectTrigger>\r\n                                            <SelectContent>\r\n                                              <SelectItem value=\"inside-mount\">Inside Mount</SelectItem>\r\n                                              <SelectItem value=\"outside-mount\">Outside Mount</SelectItem>\r\n                                            </SelectContent>\r\n                                          </Select>\r\n                                        </div>\r\n                                        <div className=\"space-y-1\">\r\n                                          <Label className=\"text-xs text-slate-600\">Estimated Cost</Label>\r\n                                          <div className=\"text-sm font-medium text-green-600 bg-green-50 px-3 py-2 rounded border\">\r\n                                            ${calculateWindowCost(window).toLocaleString()}\r\n                                          </div>\r\n                                        </div>\r\n                                      </div>\r\n\r\n                                      <div className=\"space-y-2\">\r\n                                        <Label className=\"text-xs text-slate-600\">Features</Label>\r\n                                        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-2\">\r\n                                          {featureOptions.map((feature) => (\r\n                                            <div key={feature} className=\"flex items-center space-x-2\">\r\n                                              <Checkbox\r\n                                                id={`${window.id}-${feature}`}\r\n                                                checked={window.features.includes(feature)}\r\n                                                onCheckedChange={(checked) => {\r\n                                                  const newFeatures = checked\r\n                                                    ? [...window.features, feature]\r\n                                                    : window.features.filter(f => f !== feature)\r\n                                                  updateWindow(room.id, window.id, 'features', newFeatures)\r\n                                                }}\r\n                                                className=\"border-gray-300\"\r\n                                              />\r\n                                              <Label htmlFor={`${window.id}-${feature}`} className=\"text-xs text-slate-600\">\r\n                                                {feature}\r\n                                              </Label>\r\n                                            </div>\r\n                                          ))}\r\n                                        </div>\r\n                                      </div>\r\n                                    </motion.div>\r\n                                  ))}\r\n                                </AnimatePresence>\r\n                              </div>\r\n                            </div>\r\n                          </CardContent>\r\n                        </Card>\r\n                      </motion.div>\r\n                    ))}\r\n                  </AnimatePresence>\r\n                </div>\r\n\r\n                {formData.rooms.length === 0 && (\r\n                  <div className=\"text-center py-12 text-slate-500\">\r\n                    <Home className=\"h-12 w-12 mx-auto mb-4 text-slate-300\" />\r\n                    <p className=\"text-lg font-medium mb-2\">No rooms added yet</p>\r\n                    <p className=\"text-sm\">Add rooms to start defining your project specifications</p>\r\n                  </div>\r\n                )}\r\n              </TabsContent>\r\n\r\n              {/* Workflow Tab */}\r\n              <TabsContent value=\"workflow\" className=\"space-y-6\">\r\n                <Card>\r\n                  <CardHeader>\r\n                    <CardTitle className=\"text-lg text-slate-700 flex items-center gap-2\">\r\n                      <Settings className=\"h-5 w-5\" />\r\n                      Project Workflow\r\n                    </CardTitle>\r\n                  </CardHeader>\r\n                  <CardContent className=\"space-y-6\">\r\n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                      <div className=\"space-y-4\">\r\n                        <div className=\"space-y-2\">\r\n                          <Label className=\"text-slate-600\">Assigned Installer</Label>\r\n                          <Select \r\n                            value={formData.installerId} \r\n                            onValueChange={(value) => updateFormData('installerId', value)}\r\n                          >\r\n                            <SelectTrigger className=\"border-gray-200\">\r\n                              <SelectValue placeholder=\"Select installer\" />\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                              {installers.map((installer) => (\r\n                                <SelectItem key={installer.id} value={installer.id}>\r\n                                  <div className=\"flex items-center gap-3\">\r\n                                    <Avatar className=\"h-8 w-8\">\r\n                                      <AvatarImage src={installer.profile?.avatar} />\r\n                                      <AvatarFallback>\r\n                                        {installer.profile.firstName[0]}{installer.profile.lastName[0]}\r\n                                      </AvatarFallback>\r\n                                    </Avatar>\r\n                                    <div>\r\n                                      <div className=\"font-medium\">\r\n                                        {installer.profile.firstName} {installer.profile.lastName}\r\n                                      </div>\r\n                                      <div className=\"text-sm text-slate-500\">\r\n                                        {installer.email}\r\n                                      </div>\r\n                                    </div>\r\n                                  </div>\r\n                                </SelectItem>\r\n                              ))}\r\n                            </SelectContent>\r\n                          </Select>\r\n                        </div>\r\n\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"startDate\" className=\"text-slate-600 flex items-center gap-2\">\r\n                            <Calendar className=\"h-4 w-4\" />\r\n                            Planned Start Date\r\n                          </Label>\r\n                          <Input\r\n                            id=\"startDate\"\r\n                            type=\"date\"\r\n                            value={formData.startDate}\r\n                            onChange={(e) => updateFormData('startDate', e.target.value)}\r\n                            className=\"border-gray-200 focus:border-[var(--color-brand-primary)]\"\r\n                          />\r\n                        </div>\r\n\r\n                        <div className=\"space-y-2\">\r\n                          <Label htmlFor=\"estimatedDuration\" className=\"text-slate-600 flex items-center gap-2\">\r\n                            <Clock className=\"h-4 w-4\" />\r\n                            Estimated Duration (days)\r\n                          </Label>\r\n                          <Input\r\n                            id=\"estimatedDuration\"\r\n                            type=\"number\"\r\n                            min=\"1\"\r\n                            value={formData.estimatedDuration}\r\n                            onChange={(e) => updateFormData('estimatedDuration', Number(e.target.value))}\r\n                            className=\"border-gray-200 focus:border-[var(--color-brand-primary)]\"\r\n                          />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"space-y-4\">\r\n                        <div className=\"p-4 bg-blue-50 rounded-lg border border-blue-200\">\r\n                          <h4 className=\"font-medium text-blue-800 mb-2\">Project Timeline</h4>\r\n                          <div className=\"space-y-2 text-sm\">\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-blue-700\">Start Date:</span>\r\n                              <span className=\"text-blue-900\">\r\n                                {formData.startDate || 'Not set'}\r\n                              </span>\r\n                            </div>\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-blue-700\">Duration:</span>\r\n                              <span className=\"text-blue-900\">\r\n                                {formData.estimatedDuration} day{formData.estimatedDuration !== 1 ? 's' : ''}\r\n                              </span>\r\n                            </div>\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-blue-700\">Estimated Completion:</span>\r\n                              <span className=\"text-blue-900\">\r\n                                {formData.startDate \r\n                                  ? new Date(\r\n                                      new Date(formData.startDate).getTime() + \r\n                                      formData.estimatedDuration * 24 * 60 * 60 * 1000\r\n                                    ).toLocaleDateString()\r\n                                  : 'Not calculated'\r\n                                }\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"p-4 bg-green-50 rounded-lg border border-green-200\">\r\n                          <h4 className=\"font-medium text-green-800 mb-2\">Cost Summary</h4>\r\n                          <div className=\"space-y-2 text-sm\">\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-green-700\">Total Rooms:</span>\r\n                              <span className=\"text-green-900\">{formData.rooms.length}</span>\r\n                            </div>\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-green-700\">Total Windows:</span>\r\n                              <span className=\"text-green-900\">\r\n                                {formData.rooms.reduce((total, room) => total + room.windows.length, 0)}\r\n                              </span>\r\n                            </div>\r\n                            <div className=\"flex justify-between font-medium\">\r\n                              <span className=\"text-green-700\">Estimated Cost:</span>\r\n                              <span className=\"text-green-900 text-lg\">\r\n                                ${formData.estimatedCost.toLocaleString()}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              </TabsContent>\r\n\r\n              {/* Notes Tab */}\r\n              <TabsContent value=\"notes\" className=\"space-y-6\">\r\n                <Card>\r\n                  <CardHeader>\r\n                    <CardTitle className=\"text-lg text-slate-700 flex items-center gap-2\">\r\n                      <FileText className=\"h-5 w-5\" />\r\n                      Project Notes\r\n                    </CardTitle>\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <Textarea\r\n                      value={formData.notes}\r\n                      onChange={(e) => updateFormData('notes', e.target.value)}\r\n                      className=\"min-h-[300px] border-gray-200 focus:border-[var(--color-brand-primary)]\"\r\n                      placeholder=\"Add any additional notes, special requirements, customer preferences, installation considerations, or other relevant information for this project...\"\r\n                    />\r\n                  </CardContent>\r\n                </Card>\r\n              </TabsContent>\r\n            </Tabs>\r\n          </div>\r\n\r\n          {/* Footer */}\r\n          <div className=\"px-6 py-4 border-t border-gray-200 flex-shrink-0\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={onClose}\r\n                disabled={isLoading}\r\n                className=\"border-gray-200\"\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                onClick={handleSubmit}\r\n                disabled={isLoading}\r\n                className=\"text-white min-w-[140px]\"\r\n                style={{ \r\n                  backgroundColor: accentColor,\r\n                  borderColor: accentColor\r\n                }}\r\n              >\r\n                {isLoading ? (\r\n                  <>\r\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                    Creating...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Save className=\"h-4 w-4 mr-2\" />\r\n                    Create Project\r\n                  </>\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAMA;AACA;AAzCA;;;;;;;;;;;;;;;;;AAoFA,YAAY;AACZ,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAA0B,OAAO;QAAO,OAAO;IAA4B;IACpF;QAAE,OAAO;QAA6B,OAAO;QAAU,OAAO;IAA4B;IAC1F;QAAE,OAAO;QAA2B,OAAO;QAAQ,OAAO;IAAgC;IAC1F;QAAE,OAAO;QAA6B,OAAO;QAAU,OAAO;IAA0B;CACzF;AAED,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAiC,OAAO;IAAS;IAC1D;QAAE,OAAO;QAAmC,OAAO;IAAW;IAC9D;QAAE,OAAO;QAAiC,OAAO;IAAS;IAC1D;QAAE,OAAO;QAAmC,OAAO;IAAW;IAC9D;QAAE,OAAO;QAAiC,OAAO;IAAS;CAC3D;AAED,MAAM,kBAAkB;IACtB;IAAQ;IAAS;IAAY;IAAU;IAAU;IAAa;CAC/D;AAED,MAAM,iBAAiB;IACrB;IAAa;IAAY;IAAmB;IAC5C;IAAoB;IAAY;CACjC;AAED,MAAM,gBAAgB;IACpB;QAAE,MAAM;QAAe,SAAS;IAAE;IAClC;QAAE,MAAM;QAAW,SAAS;IAAE;IAC9B;QAAE,MAAM;QAAW,SAAS;IAAE;IAC9B;QAAE,MAAM;QAAe,SAAS;IAAE;IAClC;QAAE,MAAM;QAAU,SAAS;IAAE;IAC7B;QAAE,MAAM;QAAY,SAAS;IAAE;CAChC;AAEM,SAAS,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAwB;IACtG,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;QAC7D,YAAY;QACZ,OAAO;QACP,aAAa;QACb,UAAU;QACV,eAAe;QACf,mBAAmB;QACnB,WAAW;QACX,aAAa;QACb,OAAO,EAAE;QACT,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,aAAa,IAAM,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAExF,mBAAmB;IACnB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,OAAO,aAAa,EAAE,OAAO;QAErE,MAAM,OAAO,OAAO,OAAO,KAAK,IAAI,OAAO,OAAO,MAAM;QACxD,MAAM,kBAAkB;YACtB,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,UAAU;YACV,QAAQ;QACV;QAEA,MAAM,qBAAqB;YACzB,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,UAAU;YACV,UAAU;YACV,aAAa;YACb,aAAa;QACf;QAEA,IAAI,OAAO,OAAO,eAAe,CAAC,OAAO,aAAa,CAAwB;QAC9E,QAAQ,kBAAkB,CAAC,OAAO,QAAQ,CAAoC,IAAI;QAClF,QAAQ,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG,mBAAmB;;QAEvD,OAAO,KAAK,KAAK,CAAC;IACpB,GAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,MAAM,QAAQ,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,WAAW;YAC9C,OAAO,YAAY,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa;gBACnD,OAAO,cAAc,oBAAoB;YAC3C,GAAG;QACL,GAAG;QAEH,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,eAAe;YAAM,CAAC;IACxD,GAAG;QAAC,SAAS,KAAK;QAAE;KAAoB;IAExC,mBAAmB;IACnB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,YAAY,CAAA;YACV,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,MAAM,UAAU;gBAAE,GAAG,IAAI;YAAC;YAC1B,IAAI,UAAmC;YAEvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;gBACxC,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,WAAW,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC;gBACxD,UAAU,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B;YAEA,OAAO,CAAC,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG;YACjC,OAAO;QACT;QAEA,6BAA6B;QAC7B,UAAU,CAAA;YACR,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,KAAK;gBACtB,OAAO;YACT;YACA,OAAO;QACT;IACF,GAAG,EAAE;IAEL,eAAe;IACf,MAAM,UAAU,CAAC;QACf,MAAM,UAAwB;YAC5B,IAAI;YACJ,MAAM,gBAAgB,CAAC,KAAK,EAAE,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG;YACzD,SAAS,EAAE;QACb;QAEA,uCAAuC;QACvC,IAAI,cAAc;YAChB,MAAM,WAAW,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;YACpD,IAAI,UAAU;gBACZ,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,OAAO,EAAE,IAAK;oBACzC,QAAQ,OAAO,CAAC,IAAI,CAAC;wBACnB,IAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,eAAe;wBACf,OAAO;wBACP,UAAU;wBACV,kBAAkB;wBAClB,UAAU,EAAE;wBACZ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO;uBAAI,KAAK,KAAK;oBAAE;iBAAQ;YACjC,CAAC;IACH;IAEA,cAAc;IACd,MAAM,aAAa,CAAC;QAClB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC/C,CAAC;IACH;IAEA,qBAAqB;IACrB,MAAM,YAAY,CAAC;QACjB,MAAM,YAA4B;YAChC,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,UAAU;YACV,kBAAkB;YAClB,UAAU,EAAE;YACZ,MAAM;QACR;QAEA,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;wBAAE,GAAG,IAAI;wBAAE,SAAS;+BAAI,KAAK,OAAO;4BAAE;yBAAU;oBAAC,IACjD;YAER,CAAC;IACH;IAEA,0BAA0B;IAC1B,MAAM,eAAe,CAAC,QAAgB;QACpC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;wBAAE,GAAG,IAAI;wBAAE,SAAS,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;oBAAU,IAC1E;YAER,CAAC;IACH;IAEA,qBAAqB;IACrB,MAAM,eAAe,CAAC,QAAgB,UAAkB,OAAe;QACrE,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;wBACE,GAAG,IAAI;wBACP,SAAS,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA,SACxB,OAAO,EAAE,KAAK,WACV;gCAAE,GAAG,MAAM;gCAAE,CAAC,MAAM,EAAE;4BAAM,IAC5B;oBAER,IACA;YAER,CAAC;IACH;IAEA,aAAa;IACb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;QAC9C,IAAI,CAAC,SAAS,UAAU,EAAE,UAAU,UAAU,GAAG;QACjD,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG;QAC1D,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAAG,UAAU,KAAK,GAAG;QAEnD,6BAA6B;QAC7B,SAAS,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM;YAC5B,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI;gBACrB,SAAS,CAAC,CAAC,MAAM,EAAE,UAAU,KAAK,CAAC,CAAC,GAAG;YACzC;YACA,IAAI,KAAK,OAAO,CAAC,MAAM,KAAK,GAAG;gBAC7B,SAAS,CAAC,CAAC,MAAM,EAAE,UAAU,QAAQ,CAAC,CAAC,GAAG;YAC5C;YAEA,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ;gBAC5B,IAAI,CAAC,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,GAAG;oBACtC,SAAS,CAAC,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE,YAAY,MAAM,CAAC,CAAC,GAAG;gBACjE;gBACA,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI,GAAG;oBACxC,SAAS,CAAC,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE,YAAY,OAAO,CAAC,CAAC,GAAG;gBAClE;gBACA,IAAI,CAAC,OAAO,aAAa,EAAE;oBACzB,SAAS,CAAC,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE,YAAY,cAAc,CAAC,CAAC,GAAG;gBACzE;YACF;QACF;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C,GAAG;QAAC;KAAS;IAEb,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI,CAAC,gBAAgB;YACnB,wCAAwC;YACxC,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU,IAAI,OAAO,WAAW,EAAE;gBAC3D,aAAa;YACf,OAAO,IAAI,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,WAAW;gBACnE,aAAa;YACf;YACA;QACF;QAEA,aAAa;QACb,IAAI;YACF,wBAAwB;YACxB,MAAM,2BAA2B,SAAS,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC3D,MAAM,KAAK,IAAI;oBACf,SAAS,KAAK,OAAO,CAClB,MAAM,CAAC,CAAA,SAAU,OAAO,aAAa,KAAK,IAAI,gCAAgC;qBAC9E,GAAG,CAAC,CAAA,SAAU,CAAC;4BACd,IAAI,OAAO,EAAE;4BACb,OAAO,OAAO,OAAO,KAAK;4BAC1B,QAAQ,OAAO,OAAO,MAAM;4BAC5B,eAAe,OAAO,aAAa;4BACnC,OAAO,OAAO,KAAK;4BACnB,UAAU,OAAO,QAAQ;4BACzB,kBAAkB,OAAO,gBAAgB;4BACzC,UAAU,OAAO,QAAQ;4BACzB,MAAM,oBAAoB;wBAC5B,CAAC;gBACL,CAAC;YAED,MAAM,cAA+B;gBACnC,YAAY,SAAS,UAAU;gBAC/B,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,QAAQ;gBAC3B,eAAe,SAAS,aAAa;gBACrC,mBAAmB,SAAS,iBAAiB;gBAC7C,WAAW,SAAS,SAAS,IAAI;gBACjC,aAAa,SAAS,WAAW,IAAI;gBACrC,OAAO;gBACP,OAAO,SAAS,KAAK,IAAI;YAC3B;YAEA,MAAM,OAAO;YACb;YAEA,aAAa;YACb,YAAY;gBACV,YAAY;gBACZ,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,eAAe;gBACf,mBAAmB;gBACnB,WAAW;gBACX,aAAa;gBACb,OAAO,EAAE;gBACT,OAAO;YACT;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,oCAAoC;IACpC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd;IACF,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;sBACvB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAK;gBACnC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,MAAM;oBAAE,SAAS;oBAAG,OAAO;gBAAK;gBAChC,YAAY;oBAAE,UAAU;gBAAI;;kCAG5B,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;kCACtB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAuC;;;;;;;;;;;kCAMhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAW,WAAU;;8DACtC,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGlC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAQ,WAAU;;8DACnC,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG9B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAW,WAAU;;8DACtC,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGlC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAQ,WAAU;;8DACnC,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;8CAMpC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIpC,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAQ,WAAU;kFAAiB;;;;;;kFAGlD,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,OAAO,SAAS,KAAK;wEACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;wEACvD,WAAW,CAAC,0DAA0D,EACpE,OAAO,KAAK,GAAG,mBAAmB,IAClC;wEACF,aAAY;;;;;;oEAEb,OAAO,KAAK,kBACX,8OAAC;wEAAE,WAAU;;0FACX,8OAAC,oNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EACtB,OAAO,KAAK;;;;;;;;;;;;;0EAKnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAc,WAAU;kFAAiB;;;;;;kFAGxD,8OAAC,oIAAA,CAAA,WAAQ;wEACP,IAAG;wEACH,OAAO,SAAS,WAAW;wEAC3B,UAAU,CAAC,IAAM,eAAe,eAAe,EAAE,MAAM,CAAC,KAAK;wEAC7D,WAAW,CAAC,wEAAwE,EAClF,OAAO,WAAW,GAAG,mBAAmB,IACxC;wEACF,aAAY;;;;;;oEAEb,OAAO,WAAW,kBACjB,8OAAC;wEAAE,WAAU;;0FACX,8OAAC,oNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EACtB,OAAO,WAAW;;;;;;;;;;;;;0EAKzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAiB;;;;;;kFAClC,8OAAC;wEAAI,WAAU;kFACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,iIAAA,CAAA,QAAK;gFAEJ,SAAS,SAAS,QAAQ,KAAK,OAAO,KAAK,GAAG,YAAY;gFAC1D,WAAW,CAAC,iCAAiC,EAC3C,SAAS,QAAQ,KAAK,OAAO,KAAK,GAC9B,OAAO,KAAK,GACZ,kDACJ;gFACF,SAAS,IAAM,eAAe,YAAY,OAAO,KAAK;0FAErD,OAAO,KAAK;+EATR,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAkB7B,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIhC,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAiB;;;;;;kFAClC,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,SAAS,UAAU;wEAC1B,eAAe,CAAC,QAAU,eAAe,cAAc;;0FAEvD,8OAAC,kIAAA,CAAA,gBAAa;gFAAC,WAAW,CAAC,gBAAgB,EAAE,OAAO,UAAU,GAAG,mBAAmB,IAAI;0FACtF,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;0FACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;wFAAmB,OAAO,SAAS,EAAE;kGAC9C,cAAA,8OAAC;4FAAI,WAAU;;8GACb,8OAAC,kIAAA,CAAA,SAAM;oGAAC,WAAU;8GAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wGAAC,WAAU;;4GACvB,SAAS,SAAS,CAAC,EAAE;4GAAE,SAAS,QAAQ,CAAC,EAAE;;;;;;;;;;;;gGAG/C,SAAS,SAAS;gGAAC;gGAAE,SAAS,QAAQ;;;;;;;uFAP1B,SAAS,EAAE;;;;;;;;;;;;;;;;oEAajC,OAAO,UAAU,kBAChB,8OAAC;wEAAE,WAAU;;0FACX,8OAAC,oNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EACtB,OAAO,UAAU;;;;;;;;;;;;;0EAKxB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAiB;;;;;;kFAClC,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO,SAAS,WAAW;wEAC3B,eAAe,CAAC,QAAU,eAAe,eAAe;;0FAExD,8OAAC,kIAAA,CAAA,gBAAa;gFAAC,WAAU;0FACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,8OAAC,kIAAA,CAAA,gBAAa;0FACX,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC,kIAAA,CAAA,aAAU;wFAAoB,OAAO,UAAU,EAAE;kGAChD,cAAA,8OAAC;4FAAI,WAAU;;8GACb,8OAAC,kIAAA,CAAA,SAAM;oGAAC,WAAU;;sHAChB,8OAAC,kIAAA,CAAA,cAAW;4GAAC,KAAK,UAAU,OAAO,EAAE;;;;;;sHACrC,8OAAC,kIAAA,CAAA,iBAAc;4GAAC,WAAU;;gHACvB,UAAU,OAAO,CAAC,SAAS,CAAC,EAAE;gHAAE,UAAU,OAAO,CAAC,QAAQ,CAAC,EAAE;;;;;;;;;;;;;gGAGjE,UAAU,OAAO,CAAC,SAAS;gGAAC;gGAAE,UAAU,OAAO,CAAC,QAAQ;;;;;;;uFAR5C,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;0EAgBrC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAoB,WAAU;;kGAC3C,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAY;;;;;;;0FAG/B,8OAAC,iIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,MAAK;gFACL,KAAI;gFACJ,OAAO,SAAS,iBAAiB;gFACjC,UAAU,CAAC,IAAM,eAAe,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;gFAC1E,WAAU;;;;;;;;;;;;kFAGd,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAY,WAAU;;kGACnC,8OAAC,0MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;oFAAY;;;;;;;0FAGlC,8OAAC,iIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,MAAK;gFACL,OAAO,SAAS,SAAS;gFACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;gFAC3D,WAAU;;;;;;;;;;;;;;;;;;0EAKhB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,8OAAC;gFAAK,WAAU;0FAAc;;;;;;;;;;;;kFAEhC,8OAAC;wEAAI,WAAU;;4EAAyC;4EACpD,SAAS,aAAa,CAAC,cAAc;;;;;;;kFAEzC,8OAAC;wEAAE,WAAU;kFAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUrD,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,eAAe;;8EACrB,8OAAC,kIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,8OAAC,kIAAA,CAAA,gBAAa;8EACX,cAAc,GAAG,CAAC,CAAC,yBAClB,8OAAC,kIAAA,CAAA,aAAU;4EAAqB,OAAO,SAAS,IAAI;;gFACjD,SAAS,IAAI;gFAAC;gFAAG,SAAS,OAAO;gFAAC;gFAAQ,SAAS,OAAO,KAAK,IAAI,MAAM;gFAAG;;2EAD9D,SAAS,IAAI;;;;;;;;;;;;;;;;sEAMpC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,IAAM;4DACf,WAAU;4DACV,OAAO;gEAAE,iBAAiB;gEAAa,aAAa;4DAAY;;8EAEhE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;wCAMtC,OAAO,KAAK,kBACX,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;kEACX,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,KAAK;;;;;;;;;;;;sDAKnB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;0DACb,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,MAAM;4DAAE,SAAS;4DAAG,GAAG,CAAC;wDAAG;wDAC3B,YAAY;4DAAE,UAAU;wDAAI;kEAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8EACH,8OAAC,gIAAA,CAAA,aAAU;;sFACT,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,mMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;sGAChB,8OAAC,iIAAA,CAAA,QAAK;4FACJ,OAAO,KAAK,IAAI;4FAChB,UAAU,CAAC,IAAM,eAAe,CAAC,MAAM,EAAE,UAAU,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;4FACzE,WAAW,CAAC,6FAA6F,EACvG,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,KAAK,CAAC,CAAC,GAAG,mBAAmB,IACvD;4FACF,aAAY;;;;;;;;;;;;8FAGhB,8OAAC,kIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS,IAAM,WAAW,KAAK,EAAE;oFACjC,WAAU;8FAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;;wEAGrB,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,KAAK,CAAC,CAAC,kBAChC,8OAAC;4EAAE,WAAU;;8FACX,8OAAC,oNAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;gFACtB,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,KAAK,CAAC,CAAC;;;;;;;;;;;;;8EAIxC,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAqC;;;;;;kGACrD,8OAAC,kIAAA,CAAA,SAAM;wFACL,SAAQ;wFACR,MAAK;wFACL,SAAS,IAAM,UAAU,KAAK,EAAE;wFAChC,WAAU;;0GAEV,8OAAC,kMAAA,CAAA,OAAI;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;;;;;;;4EAKpC,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,QAAQ,CAAC,CAAC,kBACnC,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAE,WAAU;;sGACX,8OAAC,oNAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;wFACtB,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,QAAQ,CAAC,CAAC;;;;;;;;;;;;0FAK3C,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;8FACb,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4FAET,SAAS;gGAAE,SAAS;gGAAG,QAAQ;4FAAE;4FACjC,SAAS;gGAAE,SAAS;gGAAG,QAAQ;4FAAO;4FACtC,MAAM;gGAAE,SAAS;gGAAG,QAAQ;4FAAE;4FAC9B,YAAY;gGAAE,UAAU;4FAAI;4FAC5B,WAAU;;8GAEV,8OAAC;oGAAI,WAAU;;sHACb,8OAAC;4GAAK,WAAU;;gHAA6B;gHACnC,cAAc;;;;;;;sHAExB,8OAAC,kIAAA,CAAA,SAAM;4GACL,SAAQ;4GACR,MAAK;4GACL,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE,OAAO,EAAE;4GAC9C,WAAU;sHAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;gHAAC,WAAU;;;;;;;;;;;;;;;;;8GAItB,8OAAC;oGAAI,WAAU;;sHACb,8OAAC;4GAAI,WAAU;;8HACb,8OAAC,iIAAA,CAAA,QAAK;oHAAC,WAAU;8HAAyB;;;;;;8HAC1C,8OAAC,iIAAA,CAAA,QAAK;oHACJ,MAAK;oHACL,KAAI;oHACJ,MAAK;oHACL,OAAO,OAAO,KAAK;oHACnB,UAAU,CAAC,IAAM,aAAa,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,SAAS,OAAO,EAAE,MAAM,CAAC,KAAK;oHAChF,WAAW,CAAC,wBAAwB,EAClC,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE,YAAY,MAAM,CAAC,CAAC,GAAG,mBAAmB,IAC/E;oHACF,aAAY;;;;;;;;;;;;sHAGhB,8OAAC;4GAAI,WAAU;;8HACb,8OAAC,iIAAA,CAAA,QAAK;oHAAC,WAAU;8HAAyB;;;;;;8HAC1C,8OAAC,iIAAA,CAAA,QAAK;oHACJ,MAAK;oHACL,KAAI;oHACJ,MAAK;oHACL,OAAO,OAAO,MAAM;oHACpB,UAAU,CAAC,IAAM,aAAa,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK;oHACjF,WAAW,CAAC,wBAAwB,EAClC,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE,YAAY,OAAO,CAAC,CAAC,GAAG,mBAAmB,IAChF;oHACF,aAAY;;;;;;;;;;;;sHAGhB,8OAAC;4GAAI,WAAU;;8HACb,8OAAC,iIAAA,CAAA,QAAK;oHAAC,WAAU;8HAAyB;;;;;;8HAC1C,8OAAC,kIAAA,CAAA,SAAM;oHACL,OAAO,OAAO,aAAa;oHAC3B,eAAe,CAAC,QAAU,aAAa,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,iBAAiB;;sIAE5E,8OAAC,kIAAA,CAAA,gBAAa;4HAAC,WAAW,CAAC,wBAAwB,EACjD,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,SAAS,EAAE,YAAY,cAAc,CAAC,CAAC,GAAG,mBAAmB,IACvF;sIACA,cAAA,8OAAC,kIAAA,CAAA,cAAW;gIAAC,aAAY;;;;;;;;;;;sIAE3B,8OAAC,kIAAA,CAAA,gBAAa;sIACX,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,kIAAA,CAAA,aAAU;oIAAkB,OAAO,KAAK,KAAK;8IAC3C,KAAK,KAAK;mIADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;sHAOnC,8OAAC;4GAAI,WAAU;;8HACb,8OAAC,iIAAA,CAAA,QAAK;oHAAC,WAAU;8HAAyB;;;;;;8HAC1C,8OAAC,iIAAA,CAAA,QAAK;oHACJ,OAAO,OAAO,KAAK;oHACnB,UAAU,CAAC,IAAM,aAAa,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oHACzE,WAAU;oHACV,aAAY;;;;;;;;;;;;;;;;;;8GAKlB,8OAAC;oGAAI,WAAU;;sHACb,8OAAC;4GAAI,WAAU;;8HACb,8OAAC,iIAAA,CAAA,QAAK;oHAAC,WAAU;8HAAyB;;;;;;8HAC1C,8OAAC,kIAAA,CAAA,SAAM;oHACL,OAAO,OAAO,QAAQ;oHACtB,eAAe,CAAC,QAAU,aAAa,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,YAAY;;sIAEvE,8OAAC,kIAAA,CAAA,gBAAa;4HAAC,WAAU;sIACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sIAEd,8OAAC,kIAAA,CAAA,gBAAa;sIACX,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC,kIAAA,CAAA,aAAU;oIAAgB,OAAO;8IAC/B;mIADc;;;;;;;;;;;;;;;;;;;;;;sHAOzB,8OAAC;4GAAI,WAAU;;8HACb,8OAAC,iIAAA,CAAA,QAAK;oHAAC,WAAU;8HAAyB;;;;;;8HAC1C,8OAAC,kIAAA,CAAA,SAAM;oHACL,OAAO,OAAO,gBAAgB;oHAC9B,eAAe,CAAC,QAAU,aAAa,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,oBAAoB;;sIAE/E,8OAAC,kIAAA,CAAA,gBAAa;4HAAC,WAAU;sIACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sIAEd,8OAAC,kIAAA,CAAA,gBAAa;;8IACZ,8OAAC,kIAAA,CAAA,aAAU;oIAAC,OAAM;8IAAe;;;;;;8IACjC,8OAAC,kIAAA,CAAA,aAAU;oIAAC,OAAM;8IAAgB;;;;;;;;;;;;;;;;;;;;;;;;sHAIxC,8OAAC;4GAAI,WAAU;;8HACb,8OAAC,iIAAA,CAAA,QAAK;oHAAC,WAAU;8HAAyB;;;;;;8HAC1C,8OAAC;oHAAI,WAAU;;wHAA0E;wHACrF,oBAAoB,QAAQ,cAAc;;;;;;;;;;;;;;;;;;;8GAKlD,8OAAC;oGAAI,WAAU;;sHACb,8OAAC,iIAAA,CAAA,QAAK;4GAAC,WAAU;sHAAyB;;;;;;sHAC1C,8OAAC;4GAAI,WAAU;sHACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;oHAAkB,WAAU;;sIAC3B,8OAAC,oIAAA,CAAA,WAAQ;4HACP,IAAI,GAAG,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;4HAC7B,SAAS,OAAO,QAAQ,CAAC,QAAQ,CAAC;4HAClC,iBAAiB,CAAC;gIAChB,MAAM,cAAc,UAChB;uIAAI,OAAO,QAAQ;oIAAE;iIAAQ,GAC7B,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;gIACtC,aAAa,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,YAAY;4HAC/C;4HACA,WAAU;;;;;;sIAEZ,8OAAC,iIAAA,CAAA,QAAK;4HAAC,SAAS,GAAG,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;4HAAE,WAAU;sIAClD;;;;;;;mHAbK;;;;;;;;;;;;;;;;;2FA/HX,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDAhEvB,KAAK,EAAE;;;;;;;;;;;;;;;wCA8NnB,SAAS,KAAK,CAAC,MAAM,KAAK,mBACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAM7B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAiB;;;;;;sFAClC,8OAAC,kIAAA,CAAA,SAAM;4EACL,OAAO,SAAS,WAAW;4EAC3B,eAAe,CAAC,QAAU,eAAe,eAAe;;8FAExD,8OAAC,kIAAA,CAAA,gBAAa;oFAAC,WAAU;8FACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wFAAC,aAAY;;;;;;;;;;;8FAE3B,8OAAC,kIAAA,CAAA,gBAAa;8FACX,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC,kIAAA,CAAA,aAAU;4FAAoB,OAAO,UAAU,EAAE;sGAChD,cAAA,8OAAC;gGAAI,WAAU;;kHACb,8OAAC,kIAAA,CAAA,SAAM;wGAAC,WAAU;;0HAChB,8OAAC,kIAAA,CAAA,cAAW;gHAAC,KAAK,UAAU,OAAO,EAAE;;;;;;0HACrC,8OAAC,kIAAA,CAAA,iBAAc;;oHACZ,UAAU,OAAO,CAAC,SAAS,CAAC,EAAE;oHAAE,UAAU,OAAO,CAAC,QAAQ,CAAC,EAAE;;;;;;;;;;;;;kHAGlE,8OAAC;;0HACC,8OAAC;gHAAI,WAAU;;oHACZ,UAAU,OAAO,CAAC,SAAS;oHAAC;oHAAE,UAAU,OAAO,CAAC,QAAQ;;;;;;;0HAE3D,8OAAC;gHAAI,WAAU;0HACZ,UAAU,KAAK;;;;;;;;;;;;;;;;;;2FAbP,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;8EAuBrC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAY,WAAU;;8FACnC,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFAAY;;;;;;;sFAGlC,8OAAC,iIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,OAAO,SAAS,SAAS;4EACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;4EAC3D,WAAU;;;;;;;;;;;;8EAId,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAoB,WAAU;;8FAC3C,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAAY;;;;;;;sFAG/B,8OAAC,iIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,KAAI;4EACJ,OAAO,SAAS,iBAAiB;4EACjC,UAAU,CAAC,IAAM,eAAe,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;4EAC1E,WAAU;;;;;;;;;;;;;;;;;;sEAKhB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAiC;;;;;;sFAC/C,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGAAgB;;;;;;sGAChC,8OAAC;4FAAK,WAAU;sGACb,SAAS,SAAS,IAAI;;;;;;;;;;;;8FAG3B,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGAAgB;;;;;;sGAChC,8OAAC;4FAAK,WAAU;;gGACb,SAAS,iBAAiB;gGAAC;gGAAK,SAAS,iBAAiB,KAAK,IAAI,MAAM;;;;;;;;;;;;;8FAG9E,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGAAgB;;;;;;sGAChC,8OAAC;4FAAK,WAAU;sGACb,SAAS,SAAS,GACf,IAAI,KACF,IAAI,KAAK,SAAS,SAAS,EAAE,OAAO,KACpC,SAAS,iBAAiB,GAAG,KAAK,KAAK,KAAK,MAC5C,kBAAkB,KACpB;;;;;;;;;;;;;;;;;;;;;;;;8EAOZ,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAkC;;;;;;sFAChD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGAAiB;;;;;;sGACjC,8OAAC;4FAAK,WAAU;sGAAkB,SAAS,KAAK,CAAC,MAAM;;;;;;;;;;;;8FAEzD,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGAAiB;;;;;;sGACjC,8OAAC;4FAAK,WAAU;sGACb,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,OAAO,CAAC,MAAM,EAAE;;;;;;;;;;;;8FAGzE,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGAAiB;;;;;;sGACjC,8OAAC;4FAAK,WAAU;;gGAAyB;gGACrC,SAAS,aAAa,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAYzD,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;8CACnC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oDACP,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;oDACvD,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,aAAa;oCACf;8CAEC,0BACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD", "debugId": null}}, {"offset": {"line": 4264, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Source%20Code/GoogleGemini/windowworks-crm/src/components/projects/projects-page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport Link from 'next/link'\r\nimport { \r\n  Clipboard, \r\n  PlayCircle, \r\n  Clock, \r\n  Calendar,\r\n  Search,\r\n  Grid3X3,\r\n  List,\r\n  Plus,\r\n  Eye,\r\n  Edit,\r\n  UserPlus,\r\n  Trash2,\r\n  MoreHorizontal,\r\n  Download,\r\n  User,\r\n  CheckCircle,\r\n  AlertCircle,\r\n  Timer,\r\n  ArrowRight\r\n} from 'lucide-react'\r\n\r\nimport { Button } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuSeparator\r\n} from '@/components/ui/dropdown-menu'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport { Checkbox } from '@/components/ui/checkbox'\r\nimport { NewProjectModal } from './new-project-modal'\r\nimport { ProjectFormData, Customer, User as UserType } from '@/types'\r\n\r\n// Types\r\ninterface Project {\r\n  id: string\r\n  name: string\r\n  customer: {\r\n    id: string\r\n    name: string\r\n    email: string\r\n    phone: string\r\n  }\r\n  status: 'Pending' | 'Assigned' | 'In-Progress' | 'Completed'\r\n  installer: {\r\n    id: string\r\n    name: string\r\n    avatar: string | null\r\n  } | null\r\n  dueDate: string\r\n  productType: string[]\r\n  description: string\r\n  address: string\r\n  totalValue: number\r\n  createdAt: string\r\n  completionTime?: number\r\n}\r\n\r\n// Mock data for projects\r\nconst mockProjects: Project[] = [\r\n  {\r\n    id: 'proj_001',\r\n    name: 'Living Room Shutters Installation',\r\n    customer: {\r\n      id: 'cust_001',\r\n      name: 'Sarah Johnson',\r\n      email: '<EMAIL>',\r\n      phone: '(*************'\r\n    },\r\n    status: 'In-Progress',\r\n    installer: {\r\n      id: 'inst_001',\r\n      name: 'Mike Rodriguez',\r\n      avatar: null\r\n    },\r\n    dueDate: '2025-07-15',\r\n    productType: ['Shutters'],\r\n    description: 'Custom wood shutters for living room bay windows',\r\n    address: '123 Maple Street, Springfield, IL 62704',\r\n    totalValue: 2840,\r\n    createdAt: '2025-07-01',\r\n    completionTime: 3\r\n  },\r\n  {\r\n    id: 'proj_002',\r\n    name: 'Office Blinds Setup',\r\n    customer: {\r\n      id: 'cust_002',\r\n      name: 'Robert Chen',\r\n      email: '<EMAIL>',\r\n      phone: '(*************'\r\n    },\r\n    status: 'Assigned',\r\n    installer: {\r\n      id: 'inst_002',\r\n      name: 'Lisa Anderson',\r\n      avatar: null\r\n    },\r\n    dueDate: '2025-07-18',\r\n    productType: ['Blinds'],\r\n    description: 'Motorized blinds for home office windows',\r\n    address: '456 Oak Avenue, Springfield, IL 62701',\r\n    totalValue: 1560,\r\n    createdAt: '2025-07-03'\r\n  },\r\n  {\r\n    id: 'proj_003',\r\n    name: 'Bedroom Shades Installation',\r\n    customer: {\r\n      id: 'cust_003',\r\n      name: 'Emma Davis',\r\n      email: '<EMAIL>',\r\n      phone: '(*************'\r\n    },\r\n    status: 'Pending',\r\n    installer: null,\r\n    dueDate: '2025-07-12',\r\n    productType: ['Shades'],\r\n    description: 'Blackout shades for master bedroom',\r\n    address: '789 Pine Road, Springfield, IL 62702',\r\n    totalValue: 890,\r\n    createdAt: '2025-07-08'\r\n  },\r\n  {\r\n    id: 'proj_004',\r\n    name: 'Kitchen Window Treatments',\r\n    customer: {\r\n      id: 'cust_004',\r\n      name: 'Michael Williams',\r\n      email: '<EMAIL>',\r\n      phone: '(*************'\r\n    },\r\n    status: 'Completed',\r\n    installer: {\r\n      id: 'inst_001',\r\n      name: 'Mike Rodriguez',\r\n      avatar: null\r\n    },\r\n    dueDate: '2025-07-10',\r\n    productType: ['Blinds', 'Shades'],\r\n    description: 'Water-resistant blinds and café shades',\r\n    address: '321 Elm Street, Springfield, IL 62703',\r\n    totalValue: 1250,\r\n    createdAt: '2025-06-28',\r\n    completionTime: 4\r\n  },\r\n  {\r\n    id: 'proj_005',\r\n    name: 'Sunroom Shutters',\r\n    customer: {\r\n      id: 'cust_005',\r\n      name: 'David Thompson',\r\n      email: '<EMAIL>',\r\n      phone: '(*************'\r\n    },\r\n    status: 'In-Progress',\r\n    installer: {\r\n      id: 'inst_003',\r\n      name: 'Carlos Martinez',\r\n      avatar: null\r\n    },\r\n    dueDate: '2025-07-20',\r\n    productType: ['Shutters'],\r\n    description: 'Full-height shutters for sunroom',\r\n    address: '654 Cedar Lane, Springfield, IL 62705',\r\n    totalValue: 3200,\r\n    createdAt: '2025-07-05',\r\n    completionTime: 6\r\n  }\r\n]\r\n\r\nconst statsData = [\r\n  {\r\n    title: 'Total Projects',\r\n    value: '156',\r\n    change: '-1%',\r\n    icon: Clipboard,\r\n    trend: 'down'\r\n  },\r\n  {\r\n    title: 'Active Projects',\r\n    value: '24',\r\n    change: '+5%',\r\n    icon: PlayCircle,\r\n    trend: 'up'\r\n  },\r\n  {\r\n    title: 'Overdue',\r\n    value: '3',\r\n    change: '',\r\n    icon: AlertCircle,\r\n    trend: 'alert'\r\n  },\r\n  {\r\n    title: 'Avg Completion Time',\r\n    value: '5 days',\r\n    change: '+2%',\r\n    icon: Timer,\r\n    trend: 'up'\r\n  }\r\n]\r\n\r\nexport default function ProjectsPage() {\r\n  const [searchQuery, setSearchQuery] = useState('')\r\n  const [statusFilter, setStatusFilter] = useState('all')\r\n  const [productFilter, setProductFilter] = useState('all')\r\n  const [sortBy, setSortBy] = useState('dueDate')\r\n  const [selectedProjects, setSelectedProjects] = useState<string[]>([])\r\n  const [viewMode, setViewMode] = useState<'table' | 'kanban'>('table')\r\n  const [currentPage, setCurrentPage] = useState(1)\r\n  const [isNewProjectModalOpen, setIsNewProjectModalOpen] = useState(false)\r\n  const itemsPerPage = 10\r\n\r\n  // Mock data for customers and installers\r\n  const mockCustomers: Customer[] = [\r\n    {\r\n      id: 'cust_001',\r\n      firstName: 'Sarah',\r\n      lastName: 'Johnson',\r\n      email: '<EMAIL>',\r\n      phone: '(*************',\r\n      address: {\r\n        street: '123 Maple Street',\r\n        city: 'Springfield',\r\n        state: 'IL',\r\n        zipCode: '62704',\r\n        country: 'United States'\r\n      },\r\n      preferences: {\r\n        windowTreatmentTypes: ['shutters', 'blinds'],\r\n        preferredColors: ['white', 'beige'],\r\n        budget: '2500-5000',\r\n        communication: 'email'\r\n      },\r\n      createdAt: '2025-07-01T00:00:00Z',\r\n      updatedAt: '2025-07-01T00:00:00Z',\r\n      createdBy: 'admin'\r\n    }\r\n  ]\r\n\r\n  const mockInstallers: UserType[] = [\r\n    {\r\n      id: 'inst_001',\r\n      email: '<EMAIL>',\r\n      role: 'installer',\r\n      profile: {\r\n        firstName: 'Mike',\r\n        lastName: 'Rodriguez',\r\n        phone: '(*************'\r\n      },\r\n      createdAt: '2025-07-01T00:00:00Z',\r\n      updatedAt: '2025-07-01T00:00:00Z'\r\n    }\r\n  ]\r\n\r\n  // Handle new project creation\r\n  const handleCreateProject = async (projectData: ProjectFormData) => {\r\n    try {\r\n      console.log('Creating new project:', projectData)\r\n      // Here you would typically make an API call to create the project\r\n      // await createProject(projectData)\r\n      \r\n      // For now, just log and close modal\r\n      setIsNewProjectModalOpen(false)\r\n      \r\n      // You might want to refresh the projects list here\r\n      // await refreshProjects()\r\n    } catch (error) {\r\n      console.error('Error creating project:', error)\r\n      throw error\r\n    }\r\n  }\r\n\r\n  // Filter and sort projects\r\n  const filteredProjects = mockProjects.filter(project => {\r\n    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n                         project.customer.name.toLowerCase().includes(searchQuery.toLowerCase())\r\n    \r\n    const matchesStatus = statusFilter === 'all' || project.status.toLowerCase() === statusFilter.toLowerCase()\r\n    \r\n    const matchesProduct = productFilter === 'all' || \r\n                          project.productType.some(type => type.toLowerCase() === productFilter.toLowerCase())\r\n    \r\n    return matchesSearch && matchesStatus && matchesProduct\r\n  }).sort((a, b) => {\r\n    switch (sortBy) {\r\n      case 'dueDate':\r\n        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()\r\n      case 'name':\r\n        return a.name.localeCompare(b.name)\r\n      case 'customer':\r\n        return a.customer.name.localeCompare(b.customer.name)\r\n      default:\r\n        return 0\r\n    }\r\n  })\r\n\r\n  const totalPages = Math.ceil(filteredProjects.length / itemsPerPage)\r\n  const paginatedProjects = filteredProjects.slice(\r\n    (currentPage - 1) * itemsPerPage,\r\n    currentPage * itemsPerPage\r\n  )\r\n\r\n  const handleSelectProject = (projectId: string) => {\r\n    setSelectedProjects(prev => \r\n      prev.includes(projectId)\r\n        ? prev.filter(id => id !== projectId)\r\n        : [...prev, projectId]\r\n    )\r\n  }\r\n\r\n  const handleSelectAll = () => {\r\n    if (selectedProjects.length === paginatedProjects.length) {\r\n      setSelectedProjects([])\r\n    } else {\r\n      setSelectedProjects(paginatedProjects.map(project => project.id))\r\n    }\r\n  }\r\n\r\n  const getStatusBadgeVariant = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'completed':\r\n        return 'default'\r\n      case 'in-progress':\r\n        return 'secondary'\r\n      case 'assigned':\r\n        return 'outline'\r\n      case 'pending':\r\n        return 'destructive'\r\n      default:\r\n        return 'outline'\r\n    }\r\n  }\r\n\r\n  const getStatusIcon = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'completed':\r\n        return CheckCircle\r\n      case 'in-progress':\r\n        return PlayCircle\r\n      case 'assigned':\r\n        return User\r\n      case 'pending':\r\n        return Clock\r\n      default:\r\n        return Clock\r\n    }\r\n  }\r\n\r\n  const isOverdue = (dueDate: string) => {\r\n    return new Date(dueDate) < new Date() && !mockProjects.find(p => p.dueDate === dueDate && p.status === 'Completed')\r\n  }\r\n\r\n  // Kanban view organization\r\n  const kanbanColumns = {\r\n    'Pending': filteredProjects.filter(p => p.status === 'Pending'),\r\n    'Assigned': filteredProjects.filter(p => p.status === 'Assigned'),\r\n    'In-Progress': filteredProjects.filter(p => p.status === 'In-Progress'),\r\n    'Completed': filteredProjects.filter(p => p.status === 'Completed')\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex items-center justify-between\"\r\n      >\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-foreground\">Projects</h1>\r\n          <p className=\"text-muted-foreground\">Track installations, workflows, and statuses</p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-3\">\r\n          <Badge variant=\"secondary\" className=\"text-xs\">\r\n            Saturday, July 12, 2025\r\n          </Badge>\r\n          <Button \r\n            className=\"bg-primary hover:bg-primary/90\"\r\n            onClick={() => setIsNewProjectModalOpen(true)}\r\n          >\r\n            <Plus className=\"h-4 w-4 mr-2\" />\r\n            New Project\r\n          </Button>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n        {statsData.map((stat, index) => (\r\n          <motion.div\r\n            key={stat.title}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: index * 0.1 }}\r\n          >\r\n            <Card className=\"hover:border-border/80 transition-colors\">\r\n              <CardHeader className=\"pb-3\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className={`p-2 rounded-md ${\r\n                    stat.trend === 'alert' ? 'bg-destructive/10' : 'bg-primary/10'\r\n                  }`}>\r\n                    <stat.icon className={`h-5 w-5 ${\r\n                      stat.trend === 'alert' ? 'text-destructive' : 'text-primary'\r\n                    }`} />\r\n                  </div>\r\n                  {stat.change && (\r\n                    <Badge \r\n                      variant={\r\n                        stat.trend === 'up' ? 'default' : \r\n                        stat.trend === 'down' ? 'destructive' : \r\n                        stat.trend === 'alert' ? 'destructive' : 'secondary'\r\n                      } \r\n                      className=\"text-xs\"\r\n                    >\r\n                      {stat.change}\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"space-y-1\">\r\n                  <h3 className=\"text-2xl font-bold text-foreground\">{stat.value}</h3>\r\n                  <p className=\"text-sm text-muted-foreground\">{stat.title}</p>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </motion.div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Filters and View Toggle */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.4 }}\r\n      >\r\n        <Card>\r\n          <CardHeader className=\"pb-4\">\r\n            <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\r\n              <div className=\"flex flex-1 items-center space-x-4\">\r\n                <div className=\"relative flex-1 max-w-md\">\r\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\r\n                  <Input\r\n                    placeholder=\"Search projects, customers...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    className=\"pl-10\"\r\n                  />\r\n                </div>\r\n                \r\n                <Select value={statusFilter} onValueChange={setStatusFilter}>\r\n                  <SelectTrigger className=\"w-32\">\r\n                    <SelectValue placeholder=\"Status\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"all\">All Status</SelectItem>\r\n                    <SelectItem value=\"pending\">Pending</SelectItem>\r\n                    <SelectItem value=\"assigned\">Assigned</SelectItem>\r\n                    <SelectItem value=\"in-progress\">In-Progress</SelectItem>\r\n                    <SelectItem value=\"completed\">Completed</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n\r\n                <Select value={productFilter} onValueChange={setProductFilter}>\r\n                  <SelectTrigger className=\"w-36\">\r\n                    <SelectValue placeholder=\"Product\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"all\">All Products</SelectItem>\r\n                    <SelectItem value=\"blinds\">Blinds</SelectItem>\r\n                    <SelectItem value=\"shutters\">Shutters</SelectItem>\r\n                    <SelectItem value=\"shades\">Shades</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n\r\n                <Select value={sortBy} onValueChange={setSortBy}>\r\n                  <SelectTrigger className=\"w-32\">\r\n                    <SelectValue placeholder=\"Sort by\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"dueDate\">Due Date</SelectItem>\r\n                    <SelectItem value=\"name\">Name</SelectItem>\r\n                    <SelectItem value=\"customer\">Customer</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"flex items-center bg-accent rounded-md p-1\">\r\n                  <Button\r\n                    variant={viewMode === 'table' ? 'default' : 'ghost'}\r\n                    size=\"sm\"\r\n                    onClick={() => setViewMode('table')}\r\n                    className=\"h-8 px-3\"\r\n                  >\r\n                    <List className=\"h-4 w-4\" />\r\n                  </Button>\r\n                  <Button\r\n                    variant={viewMode === 'kanban' ? 'default' : 'ghost'}\r\n                    size=\"sm\"\r\n                    onClick={() => setViewMode('kanban')}\r\n                    className=\"h-8 px-3\"\r\n                  >\r\n                    <Grid3X3 className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </div>\r\n                \r\n                <Button variant=\"outline\" size=\"sm\">\r\n                  <Download className=\"h-4 w-4 mr-2\" />\r\n                  Export\r\n                </Button>\r\n                \r\n                {selectedProjects.length > 0 && (\r\n                  <Badge variant=\"secondary\">\r\n                    {selectedProjects.length} selected\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </CardHeader>\r\n        </Card>\r\n      </motion.div>\r\n\r\n      {/* Main Content - Table or Kanban View */}\r\n      {viewMode === 'table' ? (\r\n        // Table View\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5 }}\r\n        >\r\n          <Card>\r\n            <CardContent className=\"p-0\">\r\n              <div className=\"overflow-x-auto\">\r\n                <table className=\"w-full\">\r\n                  <thead className=\"border-b border-border\">\r\n                    <tr className=\"bg-accent/20\">\r\n                      <th className=\"text-left p-4 w-12\">\r\n                        <Checkbox\r\n                          checked={selectedProjects.length === paginatedProjects.length && paginatedProjects.length > 0}\r\n                          onCheckedChange={handleSelectAll}\r\n                        />\r\n                      </th>\r\n                      <th className=\"text-left p-4 font-medium text-foreground\">Project</th>\r\n                      <th className=\"text-left p-4 font-medium text-foreground\">Customer</th>\r\n                      <th className=\"text-left p-4 font-medium text-foreground\">Status</th>\r\n                      <th className=\"text-left p-4 font-medium text-foreground\">Installer</th>\r\n                      <th className=\"text-left p-4 font-medium text-foreground\">Due Date</th>\r\n                      <th className=\"text-left p-4 font-medium text-foreground\">Value</th>\r\n                      <th className=\"text-right p-4 w-16\"></th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {paginatedProjects.map((project, index) => {\r\n                      const StatusIcon = getStatusIcon(project.status)\r\n                      const overdue = isOverdue(project.dueDate)\r\n                      return (\r\n                        <motion.tr\r\n                          key={project.id}\r\n                          initial={{ opacity: 0, y: 20 }}\r\n                          animate={{ opacity: 1, y: 0 }}\r\n                          transition={{ delay: index * 0.05 }}\r\n                          className=\"border-b border-border hover:bg-accent/30 transition-colors cursor-pointer\"\r\n                        >\r\n                          <td className=\"p-4\">\r\n                            <Checkbox\r\n                              checked={selectedProjects.includes(project.id)}\r\n                              onCheckedChange={() => handleSelectProject(project.id)}\r\n                              onClick={(e: React.MouseEvent) => e.stopPropagation()}\r\n                            />\r\n                          </td>\r\n                          <td className=\"p-4\">\r\n                            <div>\r\n                              <Link \r\n                                href={`/projects/${project.id}`}\r\n                                className=\"font-medium text-foreground hover:text-primary transition-colors\"\r\n                              >\r\n                                {project.name}\r\n                              </Link>\r\n                              <p className=\"text-xs text-muted-foreground mt-1\">\r\n                                {project.productType.join(', ')}\r\n                              </p>\r\n                            </div>\r\n                          </td>\r\n                          <td className=\"p-4\">\r\n                            <div>\r\n                              <Link \r\n                                href={`/customers/${project.customer.id}`}\r\n                                className=\"font-medium text-foreground hover:text-primary transition-colors\"\r\n                              >\r\n                                {project.customer.name}\r\n                              </Link>\r\n                              <p className=\"text-xs text-muted-foreground\">{project.customer.phone}</p>\r\n                            </div>\r\n                          </td>\r\n                          <td className=\"p-4\">\r\n                            <Badge variant={getStatusBadgeVariant(project.status)} className=\"text-xs\">\r\n                              <StatusIcon className=\"h-3 w-3 mr-1\" />\r\n                              {project.status}\r\n                            </Badge>\r\n                          </td>\r\n                          <td className=\"p-4\">\r\n                            {project.installer ? (\r\n                              <div className=\"flex items-center space-x-2\">\r\n                                <Avatar className=\"h-6 w-6\">\r\n                                  <AvatarImage src={project.installer.avatar || undefined} />\r\n                                  <AvatarFallback className=\"bg-primary/10 text-primary text-xs\">\r\n                                    {project.installer.name.split(' ').map(n => n[0]).join('')}\r\n                                  </AvatarFallback>\r\n                                </Avatar>\r\n                                <span className=\"text-sm text-foreground\">{project.installer.name}</span>\r\n                              </div>\r\n                            ) : (\r\n                              <span className=\"text-sm text-muted-foreground\">Unassigned</span>\r\n                            )}\r\n                          </td>\r\n                          <td className=\"p-4\">\r\n                            <div className={`flex items-center text-sm ${overdue ? 'text-destructive' : 'text-muted-foreground'}`}>\r\n                              <Calendar className=\"h-3 w-3 mr-2\" />\r\n                              {new Date(project.dueDate).toLocaleDateString()}\r\n                              {overdue && <AlertCircle className=\"h-3 w-3 ml-1 text-destructive\" />}\r\n                            </div>\r\n                          </td>\r\n                          <td className=\"p-4\">\r\n                            <span className=\"font-medium text-foreground\">\r\n                              ${project.totalValue.toLocaleString()}\r\n                            </span>\r\n                          </td>\r\n                          <td className=\"p-4\">\r\n                            <DropdownMenu>\r\n                              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>\r\n                                <Button variant=\"ghost\" size=\"sm\">\r\n                                  <MoreHorizontal className=\"h-4 w-4\" />\r\n                                </Button>\r\n                              </DropdownMenuTrigger>\r\n                              <DropdownMenuContent align=\"end\">\r\n                                <DropdownMenuItem>\r\n                                  <Eye className=\"h-4 w-4 mr-2\" />\r\n                                  View Details\r\n                                </DropdownMenuItem>\r\n                                <DropdownMenuItem>\r\n                                  <Edit className=\"h-4 w-4 mr-2\" />\r\n                                  Edit Project\r\n                                </DropdownMenuItem>\r\n                                <DropdownMenuItem>\r\n                                  <UserPlus className=\"h-4 w-4 mr-2\" />\r\n                                  Assign Installer\r\n                                </DropdownMenuItem>\r\n                                <DropdownMenuSeparator />\r\n                                <DropdownMenuItem className=\"text-destructive\">\r\n                                  <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                                  Delete\r\n                                </DropdownMenuItem>\r\n                              </DropdownMenuContent>\r\n                            </DropdownMenu>\r\n                          </td>\r\n                        </motion.tr>\r\n                      )\r\n                    })}\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              {/* Pagination */}\r\n              <div className=\"flex items-center justify-between p-4 border-t border-border\">\r\n                <div className=\"text-sm text-muted-foreground\">\r\n                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredProjects.length)} of {filteredProjects.length} projects\r\n                </div>\r\n                \r\n                <div className=\"flex items-center space-x-2\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\r\n                    disabled={currentPage === 1}\r\n                  >\r\n                    Previous\r\n                  </Button>\r\n                  \r\n                  <div className=\"flex items-center space-x-1\">\r\n                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n                      const page = i + 1\r\n                      return (\r\n                        <Button\r\n                          key={page}\r\n                          variant={currentPage === page ? \"default\" : \"outline\"}\r\n                          size=\"sm\"\r\n                          onClick={() => setCurrentPage(page)}\r\n                          className=\"w-8 h-8 p-0\"\r\n                        >\r\n                          {page}\r\n                        </Button>\r\n                      )\r\n                    })}\r\n                  </div>\r\n                  \r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}\r\n                    disabled={currentPage === totalPages}\r\n                  >\r\n                    Next\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </motion.div>\r\n      ) : (\r\n        // Kanban View\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5 }}\r\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\r\n        >\r\n          {Object.entries(kanbanColumns).map(([status, projects]) => (\r\n            <Card key={status} className=\"h-fit\">\r\n              <CardHeader className=\"pb-3\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <h3 className=\"font-semibold text-foreground\">{status}</h3>\r\n                  <Badge variant=\"secondary\" className=\"text-xs\">\r\n                    {projects.length}\r\n                  </Badge>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-3\">\r\n                {projects.map(project => {\r\n                  const StatusIcon = getStatusIcon(project.status)\r\n                  const overdue = isOverdue(project.dueDate)\r\n                  return (\r\n                    <motion.div\r\n                      key={project.id}\r\n                      initial={{ opacity: 0, scale: 0.95 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      whileHover={{ scale: 1.02 }}\r\n                      className=\"p-3 bg-background border border-border rounded-md cursor-pointer hover:border-primary/50 transition-colors\"\r\n                    >\r\n                      <div className=\"space-y-2\">\r\n                        <div className=\"flex items-start justify-between\">\r\n                          <h4 className=\"font-medium text-sm text-foreground leading-tight\">\r\n                            {project.name}\r\n                          </h4>\r\n                          <Badge variant={getStatusBadgeVariant(project.status)} className=\"text-xs\">\r\n                            <StatusIcon className=\"h-3 w-3\" />\r\n                          </Badge>\r\n                        </div>\r\n                        \r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          {project.customer.name}\r\n                        </p>\r\n                        \r\n                        <div className=\"flex items-center justify-between text-xs\">\r\n                          <div className={`flex items-center ${overdue ? 'text-destructive' : 'text-muted-foreground'}`}>\r\n                            <Calendar className=\"h-3 w-3 mr-1\" />\r\n                            {new Date(project.dueDate).toLocaleDateString()}\r\n                          </div>\r\n                          <span className=\"font-medium text-foreground\">\r\n                            ${project.totalValue.toLocaleString()}\r\n                          </span>\r\n                        </div>\r\n                        \r\n                        {project.installer && (\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <Avatar className=\"h-5 w-5\">\r\n                              <AvatarImage src={project.installer.avatar || undefined} />\r\n                              <AvatarFallback className=\"bg-primary/10 text-primary text-xs\">\r\n                                {project.installer.name.split(' ').map(n => n[0]).join('')}\r\n                              </AvatarFallback>\r\n                            </Avatar>\r\n                            <span className=\"text-xs text-muted-foreground\">\r\n                              {project.installer.name}\r\n                            </span>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n                  )\r\n                })}\r\n              </CardContent>\r\n            </Card>\r\n          ))}\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Quick Insights */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.6 }}\r\n        className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\"\r\n      >\r\n        <Card className=\"lg:col-span-2\">\r\n          <CardHeader>\r\n            <CardTitle>Upcoming Schedules</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"space-y-3\">\r\n              {mockProjects\r\n                .filter(p => p.status !== 'Completed')\r\n                .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())\r\n                .slice(0, 5)\r\n                .map((project) => {\r\n                  const overdue = isOverdue(project.dueDate)\r\n                  return (\r\n                    <div key={project.id} className=\"flex items-center justify-between p-3 rounded-md bg-accent/20\">\r\n                      <div className=\"flex items-center space-x-3\">\r\n                        <div className={`w-2 h-2 rounded-full ${\r\n                          overdue ? 'bg-destructive' : 'bg-primary'\r\n                        }`}></div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-foreground\">{project.name}</p>\r\n                          <p className=\"text-xs text-muted-foreground\">{project.customer.name}</p>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <p className={`text-sm font-medium ${overdue ? 'text-destructive' : 'text-foreground'}`}>\r\n                          {new Date(project.dueDate).toLocaleDateString()}\r\n                        </p>\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          {project.installer ? project.installer.name : 'Unassigned'}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  )\r\n                })}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>AI Insights</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"space-y-4\">\r\n              <div className=\"p-3 rounded-md bg-destructive/5 border border-destructive/20\">\r\n                <p className=\"text-sm text-foreground font-medium mb-1\">Assignment Alert</p>\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  2 overdue projects need immediate installer assignment\r\n                </p>\r\n                <Button size=\"sm\" variant=\"outline\" className=\"mt-2 h-7 text-xs\">\r\n                  Assign Now <ArrowRight className=\"h-3 w-3 ml-1\" />\r\n                </Button>\r\n              </div>\r\n              \r\n              <div className=\"p-3 rounded-md bg-primary/5 border border-primary/20\">\r\n                <p className=\"text-sm text-foreground font-medium mb-1\">Efficiency Tip</p>\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  Mike Rodriguez has 20% faster completion time. Consider more assignments.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </motion.div>\r\n\r\n      {/* New Project Modal */}\r\n      <NewProjectModal\r\n        isOpen={isNewProjectModalOpen}\r\n        onClose={() => setIsNewProjectModalOpen(false)}\r\n        onSave={handleCreateProject}\r\n        customers={mockCustomers}\r\n        installers={mockInstallers}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AACA;AACA;AACA;AACA;AACA;AAOA;AAOA;AACA;AA/CA;;;;;;;;;;;;;;;AA2EA,yBAAyB;AACzB,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YACR,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA,QAAQ;QACR,WAAW;YACT,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,SAAS;QACT,aAAa;YAAC;SAAW;QACzB,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;QACX,gBAAgB;IAClB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YACR,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA,QAAQ;QACR,WAAW;YACT,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,SAAS;QACT,aAAa;YAAC;SAAS;QACvB,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YACR,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA,QAAQ;QACR,WAAW;QACX,SAAS;QACT,aAAa;YAAC;SAAS;QACvB,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YACR,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA,QAAQ;QACR,WAAW;YACT,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,SAAS;QACT,aAAa;YAAC;YAAU;SAAS;QACjC,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;QACX,gBAAgB;IAClB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YACR,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA,QAAQ;QACR,WAAW;YACT,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,SAAS;QACT,aAAa;YAAC;SAAW;QACzB,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;QACX,gBAAgB;IAClB;CACD;AAED,MAAM,YAAY;IAChB;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,4MAAA,CAAA,YAAS;QACf,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,oNAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,eAAe;IAErB,yCAAyC;IACzC,MAAM,gBAA4B;QAChC;YACE,IAAI;YACJ,WAAW;YACX,UAAU;YACV,OAAO;YACP,OAAO;YACP,SAAS;gBACP,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;YACA,aAAa;gBACX,sBAAsB;oBAAC;oBAAY;iBAAS;gBAC5C,iBAAiB;oBAAC;oBAAS;iBAAQ;gBACnC,QAAQ;gBACR,eAAe;YACjB;YACA,WAAW;YACX,WAAW;YACX,WAAW;QACb;KACD;IAED,MAAM,iBAA6B;QACjC;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,SAAS;gBACP,WAAW;gBACX,UAAU;gBACV,OAAO;YACT;YACA,WAAW;YACX,WAAW;QACb;KACD;IAED,8BAA8B;IAC9B,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB;YACrC,kEAAkE;YAClE,mCAAmC;YAEnC,oCAAoC;YACpC,yBAAyB;QAEzB,mDAAmD;QACnD,0BAA0B;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,aAAa,MAAM,CAAC,CAAA;QAC3C,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEzF,MAAM,gBAAgB,iBAAiB,SAAS,QAAQ,MAAM,CAAC,WAAW,OAAO,aAAa,WAAW;QAEzG,MAAM,iBAAiB,kBAAkB,SACnB,QAAQ,WAAW,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,WAAW,OAAO,cAAc,WAAW;QAEvG,OAAO,iBAAiB,iBAAiB;IAC3C,GAAG,IAAI,CAAC,CAAC,GAAG;QACV,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO;YACpE,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,IAAI;YACtD;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;IACvD,MAAM,oBAAoB,iBAAiB,KAAK,CAC9C,CAAC,cAAc,CAAC,IAAI,cACpB,cAAc;IAGhB,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB,CAAA,OAClB,KAAK,QAAQ,CAAC,aACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,aACzB;mBAAI;gBAAM;aAAU;IAE5B;IAEA,MAAM,kBAAkB;QACtB,IAAI,iBAAiB,MAAM,KAAK,kBAAkB,MAAM,EAAE;YACxD,oBAAoB,EAAE;QACxB,OAAO;YACL,oBAAoB,kBAAkB,GAAG,CAAC,CAAA,UAAW,QAAQ,EAAE;QACjE;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO,2NAAA,CAAA,cAAW;YACpB,KAAK;gBACH,OAAO,kNAAA,CAAA,aAAU;YACnB,KAAK;gBACH,OAAO,kMAAA,CAAA,OAAI;YACb,KAAK;gBACH,OAAO,oMAAA,CAAA,QAAK;YACd;gBACE,OAAO,oMAAA,CAAA,QAAK;QAChB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,WAAW,EAAE,MAAM,KAAK;IACzG;IAEA,2BAA2B;IAC3B,MAAM,gBAAgB;QACpB,WAAW,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACrD,YAAY,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACtD,eAAe,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACzD,aAAa,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IACzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAU;;;;;;0CAG/C,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAM,yBAAyB;;kDAExC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;kCAEjC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,eAAe,EAC9B,KAAK,KAAK,KAAK,UAAU,sBAAsB,iBAC/C;0DACA,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAC7B,KAAK,KAAK,KAAK,UAAU,qBAAqB,gBAC9C;;;;;;;;;;;4CAEH,KAAK,MAAM,kBACV,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SACE,KAAK,KAAK,KAAK,OAAO,YACtB,KAAK,KAAK,KAAK,SAAS,gBACxB,KAAK,KAAK,KAAK,UAAU,gBAAgB;gDAE3C,WAAU;0DAET,KAAK,MAAM;;;;;;;;;;;;;;;;;8CAKpB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC,KAAK,KAAK;;;;;;0DAC9D,8OAAC;gDAAE,WAAU;0DAAiC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;uBAhCzD,KAAK,KAAK;;;;;;;;;;0BAyCrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;;;;;;;sDAId,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAc,eAAe;;8DAC1C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAc;;;;;;sEAChC,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;;;;;;;;;;;;;sDAIlC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAe,eAAe;;8DAC3C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;;;;;;;;;;;;;sDAI/B,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAQ,eAAe;;8DACpC,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;;;;;;;;;;;;;;;;;;;8CAKnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,aAAa,UAAU,YAAY;oDAC5C,MAAK;oDACL,SAAS,IAAM,YAAY;oDAC3B,WAAU;8DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,aAAa,WAAW,YAAY;oDAC7C,MAAK;oDACL,SAAS,IAAM,YAAY;oDAC3B,WAAU;8DAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAIvB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAItC,iBAAiB,MAAM,GAAG,mBACzB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDACZ,iBAAiB,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUtC,aAAa,UACZ,aAAa;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,SAAS,iBAAiB,MAAM,KAAK,kBAAkB,MAAM,IAAI,kBAAkB,MAAM,GAAG;4DAC5F,iBAAiB;;;;;;;;;;;kEAGrB,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAC1D,8OAAC;wDAAG,WAAU;;;;;;;;;;;;;;;;;sDAGlB,8OAAC;sDACE,kBAAkB,GAAG,CAAC,CAAC,SAAS;gDAC/B,MAAM,aAAa,cAAc,QAAQ,MAAM;gDAC/C,MAAM,UAAU,UAAU,QAAQ,OAAO;gDACzC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oDAER,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO,QAAQ;oDAAK;oDAClC,WAAU;;sEAEV,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gEACP,SAAS,iBAAiB,QAAQ,CAAC,QAAQ,EAAE;gEAC7C,iBAAiB,IAAM,oBAAoB,QAAQ,EAAE;gEACrD,SAAS,CAAC,IAAwB,EAAE,eAAe;;;;;;;;;;;sEAGvD,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;;kFACC,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wEAC/B,WAAU;kFAET,QAAQ,IAAI;;;;;;kFAEf,8OAAC;wEAAE,WAAU;kFACV,QAAQ,WAAW,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;sEAIhC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;;kFACC,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,WAAW,EAAE,QAAQ,QAAQ,CAAC,EAAE,EAAE;wEACzC,WAAU;kFAET,QAAQ,QAAQ,CAAC,IAAI;;;;;;kFAExB,8OAAC;wEAAE,WAAU;kFAAiC,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;sEAGxE,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAS,sBAAsB,QAAQ,MAAM;gEAAG,WAAU;;kFAC/D,8OAAC;wEAAW,WAAU;;;;;;oEACrB,QAAQ,MAAM;;;;;;;;;;;;sEAGnB,8OAAC;4DAAG,WAAU;sEACX,QAAQ,SAAS,iBAChB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEAAC,WAAU;;0FAChB,8OAAC,kIAAA,CAAA,cAAW;gFAAC,KAAK,QAAQ,SAAS,CAAC,MAAM,IAAI;;;;;;0FAC9C,8OAAC,kIAAA,CAAA,iBAAc;gFAAC,WAAU;0FACvB,QAAQ,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;kFAG3D,8OAAC;wEAAK,WAAU;kFAA2B,QAAQ,SAAS,CAAC,IAAI;;;;;;;;;;;qFAGnE,8OAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;sEAGpD,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAW,CAAC,0BAA0B,EAAE,UAAU,qBAAqB,yBAAyB;;kFACnG,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,IAAI,KAAK,QAAQ,OAAO,EAAE,kBAAkB;oEAC5C,yBAAW,8OAAC,oNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAGvC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAU;;oEAA8B;oEAC1C,QAAQ,UAAU,CAAC,cAAc;;;;;;;;;;;;sEAGvC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kFACX,8OAAC,4IAAA,CAAA,sBAAmB;wEAAC,OAAO;wEAAC,SAAS,CAAC,IAAM,EAAE,eAAe;kFAC5D,cAAA,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAQ,MAAK;sFAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wEAAC,OAAM;;0FACzB,8OAAC,4IAAA,CAAA,mBAAgB;;kGACf,8OAAC,gMAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGlC,8OAAC,4IAAA,CAAA,mBAAgB;;kGACf,8OAAC,2MAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGnC,8OAAC,4IAAA,CAAA,mBAAgB;;kGACf,8OAAC,8MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGvC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0FACtB,8OAAC,4IAAA,CAAA,mBAAgB;gFAAC,WAAU;;kGAC1B,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;mDA5FtC,QAAQ,EAAE;;;;;4CAoGrB;;;;;;;;;;;;;;;;;0CAMN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAgC;4CACnC,CAAC,cAAc,CAAC,IAAI,eAAgB;4CAAE;4CAAK,KAAK,GAAG,CAAC,cAAc,cAAc,iBAAiB,MAAM;4CAAE;4CAAK,iBAAiB,MAAM;4CAAC;;;;;;;kDAGlJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;gDACzD,UAAU,gBAAgB;0DAC3B;;;;;;0DAID,8OAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,CAAC;oDAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;gDAAY,GAAG,CAAC,GAAG;oDACnD,MAAM,OAAO,IAAI;oDACjB,qBACE,8OAAC,kIAAA,CAAA,SAAM;wDAEL,SAAS,gBAAgB,OAAO,YAAY;wDAC5C,MAAK;wDACL,SAAS,IAAM,eAAe;wDAC9B,WAAU;kEAET;uDANI;;;;;gDASX;;;;;;0DAGF,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,YAAY,OAAO;gDAClE,UAAU,gBAAgB;0DAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBASX,cAAc;0BACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAET,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,QAAQ,SAAS,iBACpD,8OAAC,gIAAA,CAAA,OAAI;wBAAc,WAAU;;0CAC3B,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,SAAS,MAAM;;;;;;;;;;;;;;;;;0CAItB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,SAAS,GAAG,CAAC,CAAA;oCACZ,MAAM,aAAa,cAAc,QAAQ,MAAM;oCAC/C,MAAM,UAAU,UAAU,QAAQ,OAAO;oCACzC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAK;wCACnC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,QAAQ,IAAI;;;;;;sEAEf,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAS,sBAAsB,QAAQ,MAAM;4DAAG,WAAU;sEAC/D,cAAA,8OAAC;gEAAW,WAAU;;;;;;;;;;;;;;;;;8DAI1B,8OAAC;oDAAE,WAAU;8DACV,QAAQ,QAAQ,CAAC,IAAI;;;;;;8DAGxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,kBAAkB,EAAE,UAAU,qBAAqB,yBAAyB;;8EAC3F,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,IAAI,KAAK,QAAQ,OAAO,EAAE,kBAAkB;;;;;;;sEAE/C,8OAAC;4DAAK,WAAU;;gEAA8B;gEAC1C,QAAQ,UAAU,CAAC,cAAc;;;;;;;;;;;;;gDAItC,QAAQ,SAAS,kBAChB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,8OAAC,kIAAA,CAAA,cAAW;oEAAC,KAAK,QAAQ,SAAS,CAAC,MAAM,IAAI;;;;;;8EAC9C,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,QAAQ,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;sEAG3D,8OAAC;4DAAK,WAAU;sEACb,QAAQ,SAAS,CAAC,IAAI;;;;;;;;;;;;;;;;;;uCAvC1B,QAAQ,EAAE;;;;;gCA8CrB;;;;;;;uBA7DO;;;;;;;;;;0BAqEjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,aACE,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aACzB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO,IAC1E,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC;wCACJ,MAAM,UAAU,UAAU,QAAQ,OAAO;wCACzC,qBACE,8OAAC;4CAAqB,WAAU;;8DAC9B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,qBAAqB,EACpC,UAAU,mBAAmB,cAC7B;;;;;;sEACF,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAuC,QAAQ,IAAI;;;;;;8EAChE,8OAAC;oEAAE,WAAU;8EAAiC,QAAQ,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;;8DAGvE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAW,CAAC,oBAAoB,EAAE,UAAU,qBAAqB,mBAAmB;sEACpF,IAAI,KAAK,QAAQ,OAAO,EAAE,kBAAkB;;;;;;sEAE/C,8OAAC;4DAAE,WAAU;sEACV,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,IAAI,GAAG;;;;;;;;;;;;;2CAf1C,QAAQ,EAAE;;;;;oCAoBxB;;;;;;;;;;;;;;;;;kCAKR,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;8DAG7C,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAU,WAAU;;wDAAmB;sEACpD,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAIrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvD,8OAAC,yJAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,yBAAyB;gBACxC,QAAQ;gBACR,WAAW;gBACX,YAAY;;;;;;;;;;;;AAIpB", "debugId": null}}]}